{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"assess-web-app": {"projectType": "application", "schematics": {"@schematics/angular:component": {"skipTests": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/assess-web-app", "baseHref": "/", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", "src/_redirects", "src/firebase-messaging-sw.js", "src/manifest.json", {"glob": "**/*", "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "/assets/"}], "styles": ["node_modules/angular-calendar/css/angular-calendar.css", "src/custom-theme.scss", "src/styles.css"], "scripts": ["src/assets/js/face-api.min.js", "node_modules/apexcharts/dist/apexcharts.min.js"], "vendorChunk": false, "extractLicenses": false, "buildOptimizer": true, "sourceMap": false, "optimization": false, "namedChunks": true, "aot": true}, "configurations": {"development": {"buildOptimizer": false, "optimization": false, "sourceMap": true, "namedChunks": true, "extractLicenses": false, "vendorChunk": false, "aot": false}, "prod-rak": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod-rak.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "staging-rak": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging-rak.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "demo-gcp": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.demo-gcp.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "gcp-dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev-gcp.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "gcp-automation": {"baseHref": "/automation-daweb/", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.gcp-automation.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "gcp-onsite-byod": {"baseHref": "/byod-daweb/", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.gcp-onsite-byod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "gcp-release": {"baseHref": "/release-daweb/", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.gcp-release.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "gcp-sla": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.gcp-sla.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "gcp-staging": {"baseHref": "/staging-daweb/", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.gcp-staging.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "poc-upm": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.poc-upm.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "gcp-outcome": {"baseHref": "/outcome-daweb/", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.gcp-outcome.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "single-input": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.single-input.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "prod": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "gcp-upm": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod-gcp-upm.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "prod-upm": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod-upm.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "prod-sla": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod-sla.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "prod-sla-dhammam": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod-sla-dhammam.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "prod-sla-jeddah": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod-sla-jeddah.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}, "prod-scd": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod-scd.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "15kb"}]}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "assess-web-app:build"}, "configurations": {"development": {"browserTarget": "assess-web-app:build:development"}, "prod": {"browserTarget": "assess-web-app:build:prod"}, "prod-amc": {"browserTarget": "assess-web-app:build:prod-amc"}, "prod-upm": {"browserTarget": "assess-web-app:build:prod-upm"}, "demo": {"browserTarget": "assess-web-app:build:demo"}, "staging": {"browserTarget": "assess-web-app:build:staging"}, "dev": {"browserTarget": "assess-web-app:build:dev"}, "dev-arabic": {"browserTarget": "assess-web-app:build:dev-arabic"}, "staging-ecs": {"browserTarget": "assess-web-app:build:staging-ecs"}, "demo-ecs": {"browserTarget": "assess-web-app:build:demo-ecs"}, "onsite-byod-staging": {"browserTarget": "assess-web-app:build:onsite-byod-staging"}, "prod-ajman": {"browserTarget": "assess-web-app:build:prod-ajman"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "assess-web-app:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "/assets/"}], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "node_modules/ngx-extended-pdf-viewer/styles.css", "src/styles.css"], "stylePreprocessorOptions": {"includePaths": ["src"]}, "scripts": []}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "assess-web-app:serve"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": "8fe7787c-3aab-4701-9111-129ab418e572", "schematicCollections": ["@angular-eslint/schematics", "@angular-eslint/schematics"]}}