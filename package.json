{"name": "assess-web-app", "version": "9.6.17", "scripts": {"ng": "ng", "start": "node --max-old-space-size=4096 ./node_modules/@angular/cli/bin/ng serve", "start-dev": "node --max-old-space-size=4096 ./node_modules/@angular/cli/bin/ng serve --hmr --configuration development", "build:staging-rak": "node --max_old_space_size=4096 ./node_modules/@angular/cli/bin/ng build -c=staging-rak", "build:prod-rak": "node --max_old_space_size=4096 ./node_modules/@angular/cli/bin/ng build -c=prod-rak", "build:demo-gcp": "node --max_old_space_size=4096 ./node_modules/@angular/cli/bin/ng build -c=demo-gcp", "build:gcp-dev": "ng build -c=gcp-dev", "build:gcp-automation": "node --max_old_space_size=4096 ./node_modules/@angular/cli/bin/ng build -c=gcp-automation", "build:gcp-onsite-byod": "node --max_old_space_size=4096 ./node_modules/@angular/cli/bin/ng build -c=gcp-onsite-byod --sourceMap=false", "build:gcp-outcome": "node --max_old_space_size=4096 ./node_modules/@angular/cli/bin/ng build -c=gcp-outcome", "build:gcp-release": "node --max_old_space_size=4096 ./node_modules/@angular/cli/bin/ng build -c=gcp-release", "build:poc-upm": "node --max_old_space_size=4096 ./node_modules/@angular/cli/bin/ng build -c=poc-upm", "build:gcp-sla": "ng build -c=gcp-sla", "build:gcp-staging": "node --max_old_space_size=4096 ./node_modules/@angular/cli/bin/ng build -c=gcp-staging", "build:prod": "ng build -c=prod", "build:gcp-upm": "node --max_old_space_size=4096 ./node_modules/@angular/cli/bin/ng build -c=gcp-upm", "build:prod-sla": "node --max_old_space_size=4096 ./node_modules/@angular/cli/bin/ng build -c=prod-sla", "build:prod-sla-dhammam": "ng build -c=prod-sla-dhammam", "build:prod-sla-jeddah": "node --max_old_space_size=4096 ./node_modules/@angular/cli/bin/ng build -c=prod-sla-jeddah", "build:prod-upm": "node --max_old_space_size=6144 ./node_modules/@angular/cli/bin/ng build -c=prod-upm -- --max-workers=2", "build:prod-scd": "node --max_old_space_size=4096 ./node_modules/@angular/cli/bin/ng build -c=prod-scd", "build:single-input": "node --max_old_space_size=4096 ./node_modules/@angular/cli/bin/ng build -c=single-input", "start:staging": "ng serve -c=staging", "start:staging-ecs": "ng serve -c=staging-ecs", "start:demo-ecs": "ng serve -c=staging-ecs", "start:prod": "ng serve -c=prod", "start:dev": "ng serve -c=dev", "start:dev-arabic": "ng serve -c=dev-arabic", "start:demo": "ng serve -c=demo", "start:prod-ajman": "ng serve -c=prod-ajman", "test": "ng test", "lint": "node --max-old-space-size=4096  ./node_modules/.bin/ng lint", "e2e": "ng e2e", "format:write": "prettier --write src", "format:check": "prettier --list-different src"}, "private": true, "dependencies": {"@angular-devkit/schematics": "^16.2.16", "@angular-slider/ngx-slider": "^16.0.1", "@angular/animations": "^16.2.12", "@angular/cdk": "^16.2.14", "@angular/common": "^16.2.12", "@angular/compiler": "^16.2.12", "@angular/core": "^16.2.12", "@angular/forms": "^16.2.12", "@angular/localize": "^16.2.12", "@angular/material": "^16.2.14", "@angular/material-moment-adapter": "^16.2.14", "@angular/platform-browser": "^16.2.12", "@angular/platform-browser-dynamic": "^16.2.12", "@angular/router": "^16.2.12", "@ckeditor/ckeditor5-angular": "^5.2.0", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "angular-calendar": "^0.31.0", "apexcharts": "^3.54.1", "arabic-digits": "^1.1.0", "awesome-phonenumber": "^3.3.0", "crypto-js": "^4.1.1", "date-fns": "^2.29.2", "docx": "^9.1.0", "echarts": "^5.1.1", "exceljs": "^4.4.0", "file-saver": "^1.3.8", "html2canvas": "^1.0.0-rc.7", "html2pdf.js": "^0.10.3", "jspdf": "^2.5.2", "lodash": "^4.17.20", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "ng-apexcharts": "1.7.6", "ng-recaptcha": "^12.0.0", "ng2-pdf-viewer": "^9.1.5", "ng2-tooltip-directive-major-angular-updates": "^2.12.1", "ngx-amazing-time-picker": "^16.0.0", "ngx-daterangepicker-material": "^6.0.4", "ngx-echarts": "^15.0.3", "ngx-extended-pdf-viewer": "^21.4.6", "ngx-flexible-layout": "^16.0.1", "ngx-image-cropper": "^6.3.3", "ngx-infinite-scroll": "^16.0.0", "ngx-mat-timepicker": "^16.2.0", "ngx-material-timepicker": "^13.1.1", "ngx-toastr": "^15.2.2", "ngx-webcam": "^0.4.1", "npm": "^10.9.2", "resize-observer-polyfill": "^1.5.1", "rxjs": "^7.8.1", "socket.io-client": "^4.1.2", "tslib": "^2.0.0", "ua-parser-js": "^2.0.3", "zone.js": "~0.13.3"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.16", "@angular-eslint/builder": "15.2.1", "@angular-eslint/eslint-plugin": "^16.3.1", "@angular-eslint/eslint-plugin-template": "^15.2.1", "@angular-eslint/schematics": "^16.3.1", "@angular-eslint/template-parser": "^16.3.1", "@angular/cli": "^16.2.16", "@angular/compiler-cli": "^16.2.12", "@angular/language-service": "^16.2.12", "@types/gtag.js": "^0.0.7", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.20.55", "@typescript-eslint/eslint-plugin": "^5.48.2", "@typescript-eslint/parser": "^5.48.2", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^4.3.6", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "lint-staged": "^10.5.3", "prettier": "^3.3.2", "sass": "^1.81.0", "ts-node": "~8.3.0", "tslint-config-prettier": "^1.18.0", "typescript": "^4.9.5"}, "lint-staged": {"src/**/*.ts": ["eslint --fix", "prettier --write"], "src/**/*.{html,css,scss}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}