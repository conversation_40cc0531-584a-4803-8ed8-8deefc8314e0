:host {
  display: block;
  overflow: hidden;
  position: relative;
}

:host::ng-deep .digi-no-border ckeditor .ck.ck-editor__main > .ck-editor__editable {
  border-color: transparent;
  padding: 0;
}

:host::ng-deep .digi-no-border ckeditor .ck.ck-editor__main > .ck-editor__editable > * {
  pointer-events: none;
  user-select: none;
}

:host::ng-deep ckeditor {
  font-size: 14px;
  line-height: 20px;
  letter-spacing: normal;
}

:host::ng-deep .digi-no-border ckeditor .ck.ck-editor__top {
  display: none;
}

:host::ng-deep .digi-no-border ckeditor .ck.ck-editor__editable_inline > p {
  margin: 0;
}

:host::ng-deep .digi-no-border ckeditor .ck.ck-editor__main > .ck-editor__editable {
  background: transparent;
  padding: 0px 8px 2px 16px;
}

:host::ng-deep
  ckeditor
  .ck-rounded-corners
  .ck.ck-button:first-child
  .ck.ck-tooltip
  .ck-tooltip__text {
  left: -25%;
}

:host::ng-deep
  ckeditor
  .ck-rounded-corners
  .ck.ck-button:first-child
  .ck.ck-tooltip
  .ck-tooltip__text:after {
  left: 25%;
}

:host::ng-deep ckeditor .ck.ck-button .ck.ck-tooltip {
  display: none;
}

:host ::ng-deep .digi-ck-editor .ck-editor__editable_inline {
  min-height: 100px;
}

:host::ng-deep ckeditor .ck.ck-editor__main > .ck-content {
  min-height: 45px;
}

:host::ng-deep p,
:host::ng-deep ul,
:host::ng-deep ol,
:host::ng-deep dl,
:host::ng-deep h1,
:host::ng-deep h2,
:host::ng-deep h3,
:host::ng-deep h4,
:host::ng-deep h5,
:host::ng-deep h6,
.digi-normal-text {
  margin: 0 !important;
  line-height: 125%;
  word-break: break-word;
}

:host::ng-deep ckeditor .ck.ck-editor__main > .ck-content {
  padding: 8px;
  box-sizing: border-box;
}

.digi-equation-text {
  text-decoration: underline;
  color: #0064c8;
  cursor: pointer;
}

:host::ng-deep .ck.ck-reset.ck-dropdown__panel.ck-dropdown__panel_ne.ck-dropdown__panel-visible {
  top: 100%;
  bottom: auto;
}

:host::ng-deep .digi-bottom-toolbar .ck.ck-reset.ck-editor {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-direction: column-reverse;
  -moz-flex-direction: column-reverse;
  -ms-flex-direction: column-reverse;
  flex-direction: column-reverse;
}

:host::ng-deep .digi-bottom-toolbar .ck.ck-editor__top .ck-sticky-panel .ck-toolbar {
  border-radius: 8px !important;
  margin-top: 4px;
  border-bottom-width: 1px !important;
}

:host::ng-deep
  .ck.ck-reset.ck-dropdown__panel.ck-dropdown__panel_ne.ck-dropdown__panel-visible
  > div {
  background: #ffffff;
  box-shadow: 0 0 3px 0px #9e9e9e;
}

:host::ng-deep .digi-content .ck.ck-reset.ck-editor {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-direction: column-reverse;
  -moz-flex-direction: column-reverse;
  -ms-flex-direction: column-reverse;
  flex-direction: column-reverse;
}

:host::ng-deep .digi-control-width p {
  margin-right: 100px !important;
}

:host::ng-deep .digi-content .ck.ck-toolbar__separator {
  display: none !important;
}

:host::ng-deep .digi-content .ck.ck-editor__main > .ck-editor__editable:not(.ck-focused) {
  border: none !important;
}

:host::ng-deep .digi-content .ck.ck-toolbar {
  border: 1px solid transparent;
}

:host::ng-deep .digi-content .ck.ck-toolbar > .ck-toolbar__items > *:not(.ck-toolbar__line-break),
.ck.ck-toolbar > .ck-toolbar__grouped-dropdown {
  margin-bottom: var(--ck-spacing-small);
  margin-top: var(--ck-spacing-small);
  color: #6b7280;
  font-size: 12px;
}

:host::ng-deep .digi-content .ck-dropdown .ck-dropdown__button {
  width: 100%;
  color: #6b7280;
  font-size: 12px;
}
