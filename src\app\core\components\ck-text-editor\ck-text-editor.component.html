<div
  [ngClass]="{ 'digi-no-border': !options?.toolbar?.items?.length }"
  *ngIf="canShowCKEditor({ content: content }) || options?.toolbar?.items?.length; else showText"
>
  <ckeditor
    *ngIf="content !== undefined && content !== null"
    class="digi-ck-editor"
    [ngClass]="{
      'digi-bottom-toolbar': isIndependentAssessment,
      'digi-hidden-toolbar': isIndependentAssessment && !isFocused,
      'digi-content': topicContent,
      'digi-control-width': canControlWidth
    }"
    [config]="options"
    [editor]="editor"
    [disabled]="!isEditable"
    [(ngModel)]="content"
    (focus)="onFocus()"
    (blur)="focusOutEditor.emit(content)"
    (ngModelChange)="onChangeText()"
  ></ckeditor>
</div>

<ng-template #showText>
  <div
    *ngIf="!hasFormula({ content: content }); else showEquationText"
    class="digi-normal-text digi-fs-16 digi-pl-5 digi-pr-5"
    [innerHTML]="content"
  ></div>

  <ng-template #showEquationText>
    <div class="digi-fs-16 digi-width-fit-content">
      <div [digiMathjax]="content"></div>
    </div>
  </ng-template>
</ng-template>
