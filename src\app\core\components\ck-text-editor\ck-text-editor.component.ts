import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  Renderer2,
  SimpleChanges
} from '@angular/core'
import { MatDialog } from '@angular/material/dialog'

import { Observable, Subject, Subscription } from 'rxjs'
import { debounceTime } from 'rxjs/operators'

import { ImageshowPopupComponent } from '@appcore/app/pages/imageshow-popup/imageshow-popup.component'
import { SettingsService, UtilService } from '@appcore/app/services'

import { toolbar } from './cq-editor-toolbar'
import * as classicEditor from './js/ckeditor.js'

@Component({
  selector: 'digi-ck-text-editor',
  templateUrl: './ck-text-editor.component.html',
  styleUrls: ['./ck-text-editor.component.css']
})
export class CkTextEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input()
  content = ''

  @Input()
  isEditable = true

  @Input()
  options = toolbar.basicToolbar

  @Input()
  isQuestionCard = false

  @Input()
  isIndependentAssessment = false

  @Input()
  topicContent = false

  @Input()
  resetText$: Observable<boolean | null>

  @Input()
  canEmitOnDestroy = false

  @Input()
  isPreview = false

  @Input()
  canControlWidth = false

  @Output()
  editorChange = new EventEmitter<string>()

  @Output()
  editorFocus = new EventEmitter()

  @Output()
  focusOutEditor = new EventEmitter()

  updateText: Subject<string> = new Subject()
  editor = classicEditor
  isFocused = false
  private resetTextSubscription: Subscription

  constructor(
    private settingService: SettingsService,
    private utilsService: UtilService,
    private dialog: MatDialog,
    private settingsService: SettingsService,
    private renderer: Renderer2, // Inject Renderer2
    private el: ElementRef
  ) {}

  ngOnInit() {
    this.resetTextSubscription = this.resetText$?.pipe().subscribe(() => {
      this.content = ''
      this.updateText.next('')
    })

    this.renderer.listen(this.el.nativeElement, 'keydown', (event: KeyboardEvent) => {
      if (event.key === 'Tab') {
        event.preventDefault()
      }
    })

    this.renderer.listen('document', 'click', (event: Event) => {
      this.onClickOutside(event)
    })

    this.renderer.listen(this.el.nativeElement, 'dragstart', (event: DragEvent) => {
      event.preventDefault()
    })

    this.updateText.pipe(debounceTime(500)).subscribe(() => this.editorChange.emit(this.content))
  }

  onClickOutside(event: Event): void {
    const targetElement = event.target as HTMLElement

    if (
      this.el.nativeElement.contains(targetElement) ||
      targetElement.closest('.wrs_modal_dialogContainer')
    ) {
      return
    }

    this.isFocused = false
  }

  ngOnChanges(changes: SimpleChanges) {
    const { options } = changes

    if (options?.currentValue) {
      this.options = this.setOptions({ options: options?.currentValue })
    }
  }

  private setOptions({ options }: { options }) {
    options.language = this.settingService.options.language
    options.direction = this.settingService.options.dir

    return options
  }

  onChangeText() {
    if (this.content.includes('<figure')) {
      // remove the image tag from content
      return setTimeout(() => {
        this.content = this.content.replace(/<\/?[^>]+(>|$)/gi, '')
      })
    }

    this.editorFocus.emit()
    this.updateText.next('')
  }

  canShowCKEditor({ content }: { content: string }) {
    if (!content) {
      return
    }

    const hasFormula = this.hasFormula({ content })

    if (hasFormula && this.isPreview) {
      return false
    }

    return (this.hasFormula({ content }) && !this.isQuestionCard) || this.isEditable
  }

  hasFormula({ content }: { content: string }) {
    if (!content) {
      return
    }

    return this.utilsService.hasFormula({ content })
  }

  onClickViewEquation({ content }: { content: string }) {
    event.stopPropagation()

    this.dialog.open(ImageshowPopupComponent, {
      disableClose: true,
      width: '100%',
      maxWidth: '650px',
      panelClass: 'dialog-fix',
      direction: this.settingsService.getOptions().dir,
      data: {
        content
      }
    })
  }

  onFocus() {
    this.isFocused = true
    this.editorFocus.emit()
  }

  ngOnDestroy(): void {
    this.resetTextSubscription?.unsubscribe()

    if (this.canEmitOnDestroy) {
      this.editorChange.emit(this.content)
    }
  }
}
