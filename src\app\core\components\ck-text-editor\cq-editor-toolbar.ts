export const toolbar = {
  basicToolbar: {
    toolbar: {
      items: [
        'undo',
        'redo',
        '|',
        'bold',
        'italic',
        '|',
        'numberedList',
        'bulletedList',
        '|',
        'MathType',
        'ChemType',
        'subscript',
        'superscript',
        'SpecialCharacters'
      ]
    },
    htmlAllowedTags: ['.*'],
    htmlAllowedAttrs: ['.*'],
    placeholder: 'Enter text here...'
  },
  noToolbar: {
    toolbar: {
      items: []
    },
    htmlAllowedTags: ['.*'],
    htmlAllowedAttrs: ['.*'],
    placeholder: ''
  },
  choiceToolbar: {
    toolbar: {
      items: [
        'undo',
        'redo',
        '|',
        'bold',
        'italic',
        '|',
        'MathType',
        'ChemType',
        'subscript',
        'superscript'
      ]
    },
    htmlAllowedTags: ['.*'],
    htmlAllowedAttrs: ['.*'],
    placeholder: ''
  },
  formattingToolbar: {
    toolbar: {
      items: ['undo', 'redo', '|', 'bold', 'italic', '|', 'subscript', 'superscript']
    },
    htmlAllowedTags: ['.*'],
    htmlAllowedAttrs: ['.*'],
    placeholder: ''
  }
}
