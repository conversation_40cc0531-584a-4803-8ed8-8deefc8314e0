.digi-table-container {
  overflow: auto;
  width: 100%;
  position: relative;
  padding: 0 0 0;
  box-sizing: border-box;
  max-height: calc(100vh - 80px);
}

.digi-table-container * {
  box-sizing: border-box;
}
.thead {
  width: 100%;
  border-bottom: 2px solid #56ccf2;
  width: max-content;
  width: fit-content;
  background-color: white;
}
.tbody {
  background-color: white;
  width: fit-content;
  width: max-content;
}

.td {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  font-family: inherit;
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 16px;
  padding: 5px 15px;
  max-width: 240px;
  min-width: 240px;
  overflow: hidden;
}

.thead .td {
  padding: 15px 15px;
  color: #3a3a3a;
  font-weight: 500;
  line-height: 20px;
}

.td.col-span {
  max-width: 960px;
  min-width: 960px;
  justify-content: flex-start;
}

.tbody .td {
  padding: 15px 15px;
}

.td0 {
  max-width: 400px;
  min-width: 400px;
}

.tbody .td0 {
  justify-content: flex-start;
}

.td3 {
  background: rgba(0, 0, 0, 0.12);
}

.row.part1,
.row.part2 {
  background: #f9f9fa;
  cursor: pointer;
  border-bottom: 1px solid rgb(0 0 0 / 25%);
}

.row.part3 {
  border-bottom: 1px solid rgb(0 0 0 / 25%);
}

.digi-toggle .row.part2 {
  display: none !important;
}
.digi-toggle.active .row.part2 {
  display: flex !important;
}

.digi-no-data-message {
  background: white;
  padding: 50px 15px 50px 45%;
  font-size: 16px;
}

.row {
  display: flex;
}

.digi-check-icon {
  display: flex;
  align-items: center;
}

.digi-check-icon mat-icon {
  color: #ff0000;
  font-size: 22px;
  line-height: 22px;
  width: 22px;
  height: 22px;
}

.digi-check-icon.active mat-icon {
  color: #51907f;
}

.digi-pending {
  color: #ff0000;
  text-transform: capitalize;
}

.digi-arrow-icon {
  cursor: pointer;
  padding: 5px;
  margin-right: 5px;
}

:host ::ng-deep .material-icons {
  font-size: 22px;
}

/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
:host ::ng-deep .mat-mdc-checkbox:not(.mat-checkbox-disabled) .mat-checkbox-frame {
  border-color: #0064c8 !important;
}

.course-name {
  padding-left: 10px;
}

.digi-table-sticky-header {
  position: sticky;
  top: 0px;
  z-index: 1;
}

.digi-bracket-text {
  font-style: italic;
  font-size: 14px;
  margin-left: 10px;
  text-align: center;
}

::ng-deep [dir='rtl'] .digi-bracket-text {
  margin-right: 10px;
  margin-left: 0;
  font-style: normal;
}

.td.td7 {
  max-width: 260px;
}

.mat-table-container {
  margin-top: 20px;
  background: white;
  border-radius: 4px;
  overflow: auto;
}

.mat-table-container table {
  width: 100%;
  min-width: min-content;
}

.mat-table-container th.mat-header-cell {
  background-color: #f3f4f6;
  color: #3a3a3a;
  font-weight: 500;
  font-size: 16px;
  padding: 15px;
  border-bottom: 2px solid #56ccf2;
}

.mat-mdc-table .mat-mdc-header-cell {
  background-color: #f3f4f6;
}

.mat-table-container td.mat-cell {
  padding: 15px;
  font-size: 16px;
  border-bottom: 1px solid #d1d5db;
}

.mat-table-container tr.mat-row:hover {
  background-color: #f5f5f5;
}

.mat-table-container .mat-paginator {
  background-color: #f9f9fa;
}

.course-name {
  font-weight: 500;
  color: #3a3a3a;
}

.digi-color-blue {
  color: #0064c8;
}

.digi-icon-info {
  --icon-size: 18px;
  width: var(--icon-size);
  height: var(--icon-size);
  font-size: var(--icon-size);
  line-height: var(--icon-size);
  color: #0064c8;
}

.digi-label-info-container {
  display: inline-flex;
  align-items: center;
  flex-direction: row;
  gap: 0px;
}

.mat-mdc-header-cell,
.mat-mdc-cell {
  padding: 0 10px;
  box-sizing: content-box;
  min-width: var(--width);
  max-width: var(--width);
}

.mat-column-select {
  --width: 30px;
}

.mat-column-sno {
  --width: 45px;
}

.mat-column-listOfCourses {
  min-width: auto;
  max-width: none;
  width: auto;
}

.mat-column-examType {
  min-width: auto;
  max-width: none;
  width: auto;
}

.mat-column-scheduledExamDateTime {
  --width: 125px;
}

.mat-column-studentsUploadedLastPublish {
  --width: 110px;
}

.mat-column-studentsUploadedRegistered {
  --width: 75px;
}

.mat-column-studentsSentUploaded {
  --width: 100px;
}

.mat-column-notificationsProctor {
  --width: 100px;
}

.mat-column-notificationsAssessmentAuthor {
  --width: 130px;
}

.mat-column-studentGroup {
  --width: 120px;
}

.digi-text-truncate-ellipsis.digi-exam-type-content {
  --text-line-clamp: 2;
}

.digi-text-truncate-ellipsis.digi-course-name-content {
  --text-line-clamp: 1;
}

:host ::ng-deep digi-student-grouping-chip .student-grouping-chip-container {
  flex-wrap: wrap !important;
  gap: 5px;
}

:host ::ng-deep digi-student-grouping-chip .student-grouping-chip-container > div {
  gap: 5px;
}

:host ::ng-deep digi-student-grouping-chip .student-grouping-chip-container > div > div {
  margin: 0 !important;
}
