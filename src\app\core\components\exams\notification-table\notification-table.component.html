<div class="mat-table-container">
  <table mat-table [dataSource]="tableData" class="mat-elevation-z">
    <!-- Checkbox Column -->
    <ng-container matColumnDef="select">
      <th mat-header-cell *matHeaderCellDef>
        <mat-checkbox
          class="digi-checkbox-no-padding"
          [checked]="allSelected"
          [indeterminate]="someSelected"
          (change)="toggleAll($event.checked)"
        >
        </mat-checkbox>
      </th>
      <td mat-cell *matCellDef="let element">
        <mat-checkbox
          class="digi-checkbox-no-padding"
          [checked]="isSelected(element)"
          (change)="toggleRow(element, $event.checked)"
        >
        </mat-checkbox>
      </td>
    </ng-container>

    <!-- List of Courses Column -->
    <ng-container matColumnDef="listOfCourses">
      <th mat-header-cell *matHeaderCellDef>
        {{ 'assessment.itemCreate.courseName' | translate }}
      </th>
      <td mat-cell *matCellDef="let element">
        <div
          fxLayout="column"
          class="digi-hierarchy-details"
          maxWidth="50vw"
          contentType="template"
          [tooltip]="courseDetails"
        >
          <span class="digi-fw-500 digi-text-truncate-ellipsis digi-course-name-content">
            {{ element.hierarchy?.course?.name }} - {{ element.hierarchy?.course?.code }}
          </span>
          <span
            *ngIf="element?.hierarchy"
            class="digi-notification-table-hierarchy-text digi-text-truncate-ellipsis digi-course-name-content"
            [innerHTML]="getCourseDetails({ hierarchy: element.hierarchy }) | safeHtml"
          ></span>
        </div>
        <ng-template #courseDetails>
          <span class="digi-fw-500">
            {{ element.hierarchy?.course?.name }} - {{ element.hierarchy?.course?.code }}
          </span>
          <br />
          <span
            *ngIf="element?.hierarchy"
            class="digi-notification-table-hierarchy-text"
            [innerHTML]="getCourseDetails({ hierarchy: element.hierarchy }) | safeHtml"
          ></span>
        </ng-template>
      </td>
    </ng-container>

    <!-- Exam Details Column -->
    <ng-container matColumnDef="examDetails">
      <th mat-header-cell *matHeaderCellDef>
        <div class="digi-label-info-container">
          <span class="digi-th-label">
            {{ 'assessment.createItem.examDetails' | translate }}
          </span>
          <span
            class="digi-icon-info-container"
            maxWidth="50vw"
            contentType="template"
            [tooltip]="studentGroupTooltip"
          >
            <mat-icon class="digi-icon-info">info</mat-icon>
          </span>

          <ng-template #studentGroupTooltip>
            {{ 'common.examType' | translate | titlecase }},
            {{ 'common.category' | translate | titlecase }},
            {{ 'common.attemptType' | translate | titlecase }}
          </ng-template>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        <span
          class="digi-text-truncate-ellipsis digi-exam-type-content"
          contentType="template"
          maxWidth="50vw"
          [tooltip]="examTypeContent"
        >
          <ng-container [ngTemplateOutlet]="examTypeContent"></ng-container>
        </span>
        <ng-template #examTypeContent>
          {{ element?.exam?.examType?.name }} - {{ element?.exam?.examType?.code }},
          {{ element?.exam?.examType?.examCategory?.name }},
          {{ element?.exam?.attemptType?.name }}
        </ng-template>
      </td>
    </ng-container>

    <!-- Student Group Column -->
    <ng-container matColumnDef="studentGroup">
      <th mat-header-cell *matHeaderCellDef>
        {{ 'exams.studentGrouping.studentGroups' | translate }}
      </th>
      <td mat-cell *matCellDef="let element">
        <div fxLayout="column">
          <div *ngIf="element?.groups?.length">
            <digi-student-grouping-chip [groups]="element?.groups"></digi-student-grouping-chip>
          </div>
          <div *ngIf="!element?.groups?.length" class="digi-enable-text digi-fs-14 digi-fw-400">
            {{ 'independentAssessmentAuthoring.noGroups' | translate | titlecase }}
          </div>
        </div>
      </td>
    </ng-container>

    <!-- Scheduled Exam Date Time Column -->
    <ng-container matColumnDef="scheduledExamDateTime">
      <th mat-header-cell *matHeaderCellDef>
        <div class="digi-label-info-container">
          <span class="digi-th-label">
            {{ 'independentAssessmentAuthoring.examDateAndTime' | translate }}
          </span>
          <span
            class="digi-icon-info-container"
            maxWidth="50vw"
            [tooltip]="'exams.sendNotification.exactExamStartAndEndTime' | translate"
          >
            <mat-icon class="digi-icon-info">info</mat-icon>
          </span>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        <div fxLayout="column">
          <span *ngIf="element.date">
            {{ element.date | localeDate: { month: '2-digit', day: '2-digit', year: 'numeric' } }}
          </span>
          <span *ngIf="!element.date" class="digi-pending">
            {{ 'exams.sendNotification.notScheduled' | translate }}
          </span>
          <span *ngIf="element.date && element?.session" class="digi-fs-12 digi-color-gray">
            {{ element?.session | formatSession: true }}
          </span>
        </div>
      </td>
    </ng-container>

    <!-- Students Uploaded Last Publish Column -->
    <ng-container matColumnDef="studentsUploadedLastPublish">
      <th mat-header-cell *matHeaderCellDef>
        <div class="digi-label-info-container">
          <span class="digi-th-label">
            {{ 'exams.sendNotification.newStudentsUploaded' | translate }}
          </span>
          <span
            class="digi-icon-info-container"
            maxWidth="50vw"
            [tooltip]="'exams.sendNotification.lastNotificationPublish' | translate"
          >
            <mat-icon class="digi-icon-info">info</mat-icon>
          </span>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        <span *ngIf="element.date" class="digi-fw-500">
          {{ element.noOfStudentsUploadedSinceLastPublish | localeNumber }}
        </span>
        <span *ngIf="!element.date">-</span>
      </td>
    </ng-container>

    <!-- Students Uploaded Registered Column -->
    <ng-container matColumnDef="studentsUploadedRegistered">
      <th mat-header-cell *matHeaderCellDef>
        <div class="digi-label-info-container">
          <span class="digi-th-label">
            {{ 'exams.studentGrouping.totalStudents' | translate }}
          </span>
          <span
            class="digi-icon-info-container"
            maxWidth="50vw"
            [tooltip]="'exams.sendNotification.numberOfStudentsUploadedRegistered' | translate"
          >
            <mat-icon class="digi-icon-info">info</mat-icon>
          </span>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        <span *ngIf="element.date">
          {{ element.notificationsToStudentsCount?.uploaded | localeNumber }}/{{
            element.totalNoOfStudentsExpected | localeNumber
          }}
        </span>
        <span *ngIf="!element.date">-</span>
      </td>
    </ng-container>

    <!-- Students Sent Uploaded Column -->
    <ng-container matColumnDef="studentsSentUploaded">
      <th mat-header-cell *matHeaderCellDef>
        <div class="digi-label-info-container">
          <span class="digi-th-label">
            {{ 'exams.sendNotification.studentNotifications' | translate }}
          </span>
          <span
            class="digi-icon-info-container"
            maxWidth="50vw"
            [tooltip]="'exams.sendNotification.notificationStudents' | translate"
          >
            <mat-icon class="digi-icon-info">info</mat-icon>
          </span>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        <span
          *ngIf="element.date"
          style="cursor: pointer"
          (click)="handleSendList(element.notificationsToStudentsCount)"
        >
          <span [ngClass]="{ 'digi-color-blue': element.notificationsToStudentsCount?.sent }">
            {{ element.notificationsToStudentsCount?.sent | localeNumber }}
          </span>
          <span>/{{ element.notificationsToStudentsCount?.uploaded | localeNumber }}</span>
        </span>
        <span *ngIf="!element.date">-</span>
      </td>
    </ng-container>

    <!-- Notifications Proctor Column -->
    <ng-container matColumnDef="notificationsProctor">
      <th mat-header-cell *matHeaderCellDef>
        <div class="digi-label-info-container">
          <span class="digi-th-label">
            {{ 'exams.sendNotification.proctorNotifications' | translate }}
          </span>
          <span
            class="digi-icon-info-container"
            maxWidth="50vw"
            [tooltip]="'exams.sendNotification.notificationProctor' | translate"
          >
            <mat-icon class="digi-icon-info">info</mat-icon>
          </span>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        <span
          *ngIf="element.date"
          fxLayout="row"
          fxLayoutGap="5px"
          class="digi-check-icon"
          [ngClass]="{ active: getIconStatus(element?.notificationsToProctorsCount) }"
        >
          <mat-icon *ngIf="showIcon(element?.notificationsToProctorsCount)">check_circle</mat-icon>
          <span *ngIf="getCount(element?.notificationsToProctorsCount)">
            {{ element?.notificationsToProctorsCount?.sent | localeNumber }}/{{
              element?.notificationsToProctorsCount?.total | localeNumber
            }}
          </span>
        </span>
        <span *ngIf="!element.date">-</span>
      </td>
    </ng-container>

    <!-- Notifications Assessment Author Column -->
    <ng-container matColumnDef="notificationsAssessmentAuthor">
      <th mat-header-cell *matHeaderCellDef>
        <div class="digi-label-info-container">
          <span class="digi-th-label">
            {{ 'exams.sendNotification.authorNotifications' | translate }}
          </span>
          <span
            class="digi-icon-info-container"
            maxWidth="50vw"
            [tooltip]="'exams.sendNotification.notificationAssessmentAuthor' | translate"
          >
            <mat-icon class="digi-icon-info">info</mat-icon>
          </span>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        <div fxLayout="column" fxLayoutAlign="start center" class="digi-pt-5 digi-pb-5">
          <div
            *ngIf="element.date"
            fxLayout="row"
            fxLayoutGap="5px"
            class="digi-check-icon"
            [ngClass]="{ active: getIconStatus(element.notificationsToAssessmentAuthorsCount) }"
          >
            <mat-icon *ngIf="showIcon(element.notificationsToAssessmentAuthorsCount)">
              check_circle
            </mat-icon>
            <span *ngIf="getCount(element.notificationsToAssessmentAuthorsCount)">
              {{ element.notificationsToAssessmentAuthorsCount?.sent | localeNumber }}/{{
                element.notificationsToAssessmentAuthorsCount?.total | localeNumber
              }}
            </span>
          </div>
          <div *ngIf="element.isPublished" class="digi-bracket-text">
            {{ 'exams.sendNotification.assessmentPublished' | translate }}
          </div>
          <span *ngIf="!element.date && !element.isPublished">-</span>
        </div>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>

  <div fxLayout="row" fxLayoutAlign="center center" class="digi-p-20">
    <div *ngIf="!tableData?.data?.length && !isLoading">
      {{ 'exams.sendNotification.noCourse' | translate }}
    </div>
    <div *ngIf="!tableData?.data?.length && isLoading">
      {{ 'common.loading' | translate }}
    </div>
  </div>
</div>
