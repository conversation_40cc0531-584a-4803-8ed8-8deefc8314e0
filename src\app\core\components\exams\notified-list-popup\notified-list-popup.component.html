<div class="digi-contaienr">
  <div fxLayout="row" fxLayoutAlign="none center" class="digi-mb-10">
    <h4 fxFlex class="digi-notification-title digi-m-0">
      {{ 'exams.sendNotification.notified' | translate | titlecase }}
    </h4>
    <mat-icon class="digi-close-icon" (click)="handleClose()">clear</mat-icon>
  </div>
  <div class="digi-name-list">
    <div *ngFor="let name of data" class="digi-name">
      {{ getFullName({ name }) }} - {{ name?.academicNo }}
    </div>
  </div>
</div>
