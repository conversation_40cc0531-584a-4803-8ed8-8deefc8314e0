import { Component, Inject } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'

import { IName, INotifiedStudent } from '@appcore/models/students'
import { GlobalService } from '@appcore/services/global.service'

@Component({
  selector: 'digi-notified-list-popup',
  templateUrl: './notified-list-popup.component.html',
  styleUrls: ['./notified-list-popup.component.css']
})
export class NotifiedListPopupComponent {
  constructor(
    private dialogRef: MatDialogRef<NotifiedListPopupComponent>,
    private globalService: GlobalService,
    @Inject(MAT_DIALOG_DATA) public data: INotifiedStudent[]
  ) {}

  handleClose() {
    this.dialogRef.close()
  }

  getFullName({ name }: { name: INotifiedStudent }) {
    return this.globalService.getFullName(name as IName)
  }
}
