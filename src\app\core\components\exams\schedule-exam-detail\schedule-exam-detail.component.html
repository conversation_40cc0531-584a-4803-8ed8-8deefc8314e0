<div class="digi-table-container">
  <table
    mat-table
    [dataSource]="dataSource"
    multiTemplateDataRows
    class="mat-elevation-z digi-table"
  >
    <!-- Course Details Column -->
    <ng-container matColumnDef="courseDetails">
      <th mat-header-cell *matHeaderCellDef>
        <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="10px">
          <div class="digi-collapse-button-container"></div>
          <span class="digi-course-details-label">
            {{ 'scheduleExamDetail.courseDetails' | translate | titlecase }}
          </span>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="10px">
          <div class="digi-collapse-button-container">
            <button
              mat-icon-button
              class="digi-collapse-button"
              (click)="
                expandedElementId = expandedElementId === element?._id ? null : element?._id;
                $event.stopPropagation()
              "
            >
              <mat-icon>
                {{
                  expandedElementId === element?._id
                    ? 'keyboard_arrow_down'
                    : 'keyboard_arrow_right'
                }}
              </mat-icon>
            </button>
          </div>
          <div
            fxLayout="column"
            class="digi-mb-5"
            contentType="template"
            maxWidth="50vw"
            [tooltip]="courseHeirachyContent"
          >
            <div fxLayout="row" fxLayoutAlign="start none" fxLayoutGap="5px">
              <span class="digi-course-label">
                <span class="digi-text-clip">
                  <ng-container *ngTemplateOutlet="courseName"></ng-container>
                </span>
              </span>
              <span class="digi-setting-icon-container">
                <mat-icon
                  class="digi-setting-icon digi-cursor"
                  (click)="onClickAssessmentSettings({ course: element })"
                >
                  settings
                </mat-icon>
              </span>
            </div>
            <div class="digi-hierarchy-label">
              <div class="digi-text-clip">
                <ng-container *ngTemplateOutlet="hierarchyContent"></ng-container>
              </div>
            </div>

            <ng-template #courseHeirachyContent>
              <span class="digi-text-left">
                <ng-container *ngTemplateOutlet="courseName"></ng-container><br />
                <ng-container *ngTemplateOutlet="hierarchyContent"></ng-container>
              </span>
            </ng-template>
            <ng-template #hierarchyContent>
              <span [innerHTML]="getHierarchy({ course: element })"></span>
            </ng-template>
            <ng-template #courseName>
              {{ element?.courseCode || '-' }} - {{ element?.courseName || '-' }}
            </ng-template>
          </div>
        </div>
      </td>
    </ng-container>

    <!-- Schedule On Column -->
    <ng-container matColumnDef="startTime">
      <th mat-header-cell *matHeaderCellDef>
        {{ 'scheduleExamDetail.dateAndTime' | translate | titlecase }}
      </th>
      <td mat-cell *matCellDef="let element">
        <div fxLayout="column">
          <div fxLayout="row" fxLayoutAlign="flex-start center" fxLayoutGap="5px">
            <span class="digi-session-label">
              {{ element?.session?.slotStartEndTime || '-' }}
            </span>
            <div fxLayoutAlign="center center">
              <mat-icon
                *ngIf="element?.isAssignedSlotTime"
                class="digi-info-icon"
                [tooltip]="$any(sessionDetails)"
                [contentType]="'template'"
                [tooltipClass]="'digi-session-tooltip'"
              >
                info
              </mat-icon>
            </div>

            <ng-template #sessionDetails>
              <div class="digi-p-5" fxLayout="column" fxLayoutAlign="start start">
                <div class="digi-text-session">
                  {{ 'dashboard.exams.currentExamSession.sessionTiming' | translate }}
                </div>
                <div class="digi-fs-14">
                  {{ element?.session?.startEndTime || '-' }}
                </div>
              </div>
            </ng-template>
          </div>
          <div *ngIf="element?.dateString" class="digi-time-label">
            {{ element?.dateString || '-' }}
          </div>
        </div>
      </td>
    </ng-container>

    <!-- groups -->
    <ng-container matColumnDef="groups">
      <th mat-header-cell *matHeaderCellDef>
        {{ 'scheduleExamDetail.groups' | translate | titlecase }}
      </th>
      <td mat-cell *matCellDef="let element">
        <digi-student-grouping-chip
          *ngIf="element?.groups?.length"
          [groups]="element.groups"
        ></digi-student-grouping-chip>
      </td>
    </ng-container>

    <!-- Students Column -->
    <ng-container matColumnDef="students">
      <th mat-header-cell *matHeaderCellDef>
        <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="5px">
          <span class="digi-students-label">
            {{ 'scheduleExamDetail.students' | translate | titlecase }}
          </span>
          <span
            class="digi-info-icon-container"
            [tooltip]="'scheduleExamDetail.noStudentsImportedPlanned' | translate | titlecase"
          >
            <mat-icon class="digi-info-icon">info</mat-icon>
          </span>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        <span class="digi-students-label">
          {{ element?.students?.male?.scheduledCount + element?.students?.female?.scheduledCount }}/
          {{ element?.students?.male?.totalCount + element?.students?.female?.totalCount }}
        </span>
      </td>
    </ng-container>

    <!-- Proctors Column -->
    <ng-container matColumnDef="proctors">
      <th mat-header-cell *matHeaderCellDef>
        <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="5px">
          <span class="digi-proctor-label">
            {{ 'scheduleExamDetail.proctors' | translate | titlecase }}
          </span>
          <span
            class="digi-info-icon-container"
            [tooltip]="'scheduleExamDetail.noProctorsTestcenters' | translate | titlecase"
          >
            <mat-icon class="digi-info-icon">info</mat-icon>
          </span>
        </div>
      </th>
      <td mat-cell *matCellDef="let element">
        <span class="digi-proctor-value">
          {{ element?.proctors?.assignedCount }}/{{ element?.proctors?.requiredCount }}
        </span>
      </td>
    </ng-container>

    <!-- Assessment Author Column -->
    <ng-container matColumnDef="assessmentStatus">
      <th mat-header-cell *matHeaderCellDef>
        {{ 'scheduleExamDetail.assessmentStatus' | translate | titlecase }}
      </th>
      <td mat-cell *matCellDef="let element">
        <div fxLayout="column" fxLayoutGap="5px" class="digi-author-status-container">
          <div class="digi-author-label">
            {{ element?.assessment?.author || '-' }}
          </div>
          <div
            fxLayout="row"
            fxLayoutAlign="start center"
            fxLayoutGap="5px"
            class="digi-author-status"
            [ngClass]="{
              green: element?.assessment?.status === 'Published',
              yellow: element?.assessment?.status !== 'Published'
            }"
          >
            <span class="digi-author-status-text">
              {{ element?.assessment?.statusText | titlecase }}
            </span>
            <mat-icon
              *ngIf="element?.assessment?.publishedDate"
              class="digi-info-icon"
              [tooltip]="$any(publishedAt)"
              [contentType]="'template'"
            >
              info
            </mat-icon>
            <ng-template #publishedAt>
              <div class="digi-p-5" fxLayout="column" fxLayoutAlign="start start">
                <div class="digi-text-session">
                  {{ 'scheduleExamDetail.publishedAt' | translate }}
                </div>
                <div class="digi-fs-14">
                  {{ element?.assessment?.publishedDate | date: 'dd-MM-yyyy hh:mm a' }}
                </div>
              </div>
            </ng-template>
          </div>
        </div>
      </td>
    </ng-container>

    <ng-container matColumnDef="scheduleStatus">
      <th mat-header-cell *matHeaderCellDef aria-label="row actions">
        {{ 'scheduleExamDetail.scheduleStatus' | translate | titlecase }}
      </th>
      <td mat-cell *matCellDef="let element">
        <div fxLayout="column" fxLayoutGap="5px">
          <div class="digi-author-label">
            {{ element?.scheduled?.by?.name }}
          </div>
          <div
            *ngIf="element?.status"
            fxLayout="row"
            fxLayoutAlign="start center"
            fxLayoutGap="5px"
            class="digi-status-container"
          >
            <span class="digi-status-tag" [ngClass]="element?.statusClass">
              {{ element?.statusText || '-' }}
            </span>
            <mat-icon
              *ngIf="element?.publishedAt"
              class="digi-info-icon"
              [tooltip]="$any(publishedAt)"
              [contentType]="'template'"
            >
              info
            </mat-icon>
            <ng-template #publishedAt>
              <div class="digi-p-5" fxLayout="column" fxLayoutAlign="start start">
                <div class="digi-text-session">
                  {{ 'scheduleExamDetail.publishedAt' | translate }}
                </div>
                <div class="digi-fs-14">
                  {{ element?.publishedAt | date: 'dd-MM-yyyy hh:mm a' }}
                </div>
              </div>
            </ng-template>
          </div>
        </div>
      </td>
    </ng-container>

    <ng-container matColumnDef="actions">
      <th mat-header-cell *matHeaderCellDef aria-label="row actions">
        {{ 'scheduleExamDetail.actions' | translate | titlecase }}
      </th>
      <td mat-cell *matCellDef="let element">
        <!--
       To do later: publish button
      <button mat-raised-button color="primary">
        <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="5px">
          <mat-icon>publish</mat-icon>
          <span>{{ 'scheduleExamDetail.publish' | translate }}</span>
        </div>
      </button> -->

        <button
          *ngIf="!element?.date"
          class="digi-pointer-auto"
          mat-stroked-button
          color="primary"
          [disabled]="!hasSchedulePermission"
          [display]="!hasSchedulePermission"
          [tooltip]="'common.youDontHaveAccessToSchedule' | translate | titlecase"
          (click)="onClickSchedule({ course: element })"
        >
          {{ 'scheduleExamDetail.schedule' | translate | titlecase }}
        </button>

        <button
          *ngIf="element?.date"
          mat-stroked-button
          color="secondary"
          (click)="onClickView({ course: element })"
        >
          <span>{{ 'scheduleExamDetail.view' | translate | titlecase }}</span>
        </button>
      </td>
    </ng-container>

    <ng-container matColumnDef="tribleDots">
      <th mat-header-cell *matHeaderCellDef aria-label="row actions"></th>
      <td mat-cell *matCellDef="let element">
        <button mat-icon-button [matMenuTriggerFor]="menu" class="digi-dots-button digi-cursor">
          <mat-icon>more_vert</mat-icon>
        </button>

        <mat-menu #menu="matMenu">
          <ng-container *ngFor="let option of menuOptions">
            <ng-container *ngIf="option?.hasPermission">
              <button
                *ngIf="option?.action === 'downloadTemplate' || element?.date"
                mat-menu-item
                (click)="onClickMenuOption({ action: option.action, course: element })"
              >
                <span>{{ option.label | translate | titlecase }}</span>
              </button>
            </ng-container>
          </ng-container>
        </mat-menu>
      </td>
    </ng-container>

    <!-- Expanded Content Column -->
    <ng-container matColumnDef="expandedDetail">
      <td mat-cell *matCellDef="let element" [attr.colspan]="columnsToDisplay.length">
        <div
          class="exam-schedule-detail"
          [@detailExpand]="element?._id == expandedElementId ? 'expanded' : 'collapsed'"
        >
          <digi-schedule-test-center-detail
            *ngIf="element?._id == expandedElementId"
            [exam]="element?.exam"
            [courseData]="element"
            (changeStudentTc)="onChangeStudentTc({ testCenters: $event, course: element })"
            (updateCourseStudentsCount)="onUpdateCourseStudentsCount($event)"
          ></digi-schedule-test-center-detail>
        </div>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="columnsToDisplay"></tr>
    <tr
      mat-row
      *matRowDef="let element; columns: columnsToDisplay"
      class="exam-schedule-row"
      [ngClass]="{
        'exam-schedule-expanded-row': expandedElementId === element?._id
      }"
    ></tr>
    <tr
      mat-row
      *matRowDef="let row; columns: ['expandedDetail']"
      class="exam-detail-row"
      [ngClass]="{
        active: expandedElementId === row?._id
      }"
    ></tr>
  </table>
</div>

<div
  *ngIf="!dataSource?.data?.length"
  fxLayout="column"
  fxLayoutAlign="center center"
  class="digi-p-25"
>
  <span class="digi-text-gray-500">{{ 'common.noDataFound' | translate | titlecase }}</span>
</div>

<digi-custom-slider [sliderId]="'schedule-slider'" (closeBackPanel)="onClickSchedule()">
  <digi-schedule-slider
    *ngIf="showSchedule"
    [selectedCourse]="selectedCourse"
    (clickClose)="onClickSchedule()"
  ></digi-schedule-slider>
</digi-custom-slider>
