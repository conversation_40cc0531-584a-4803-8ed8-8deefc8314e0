import {
  Component,
  EventEmitter,
  Input,
  OnC<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { MatTableDataSource } from '@angular/material/table'
import { animate, state, style, transition, trigger } from '@angular/animations'

import { TranslateService } from '@ngx-translate/core'

import { AssessmentConfigurationPopupComponent } from '../../assessment/assessment-global-settings/assessment-configuration-popup/assessment-configuration-popup.component'
import { ConfirmDialogComponent } from '../../confirm-dialogbox/confirm.component'
import { ImportCsvDialogComponent } from '@appcore/app/core/components/shared-component'
import { AutoAssignTcPopupComponent } from '../table/exammgt/dashboard/unscheduled-course-group-table/auto-assign-tc-popup/auto-assign-tc-popup.component'
import { PlannedCountExistComponent } from '../table/exammgt/dashboard/unscheduled-course-group-table/planned-count-exist-popup/planned-count-exist-popup.component'
import { UploadStudentErrorPopupComponent } from '../table/exammgt/dashboard/unscheduled-course-group-table/upload-student-error-popup'
import { ScheduleCourseDetailPopupComponent } from '../schedule-course-detail/schedule-course-detail.component'

import { ExamScheduleService } from '@appcore/app/pages/exams/exam-schedule.service'
import { ProfileSettingsService } from '@appcore/app/pages/settings/profile-settings/profile-settings.service'
import {
  AuthService,
  CsvService,
  ErrorHandlerService,
  GlobalService,
  NameConversionService,
  RouteService,
  SettingsService,
  UtilService
} from '@appcore/app/services'

import {
  IScheduleExamFilterOptions,
  IScheduleExamReadinessCourseApiParams,
  IScheduleExamTableData,
  IScheduleExamTableDataResponse,
  IScheduleExamTimeFormat,
  IScheduleStudentUploadPayload,
  IScheduleTestCenterDetail,
  IScheduleUploadError
} from './schedule-exam-detail.interface'

import {
  DEFAULT_SELECTED_FILTER,
  DIGICLASS,
  EPermissionType,
  ROUTES,
  SCHEDULED_STUDENT_GROUPING
} from '@appcore/app/core/constants'
import { IndependentAssessmentService } from '@appcore/app/pages/assessment'
import { ActivatedRoute, Router } from '@angular/router'
import { catchError, map, of } from 'rxjs'
import { environment } from 'src/environments/environment'
import { IExamReadinessCourse } from '../../../../models'
import { UploadLogPopupComponent } from '../table/exammgt/dashboard/unscheduled-course-group-table/upload-log-popup'

@Component({
  selector: 'digi-schedule-exam-detail',
  templateUrl: './schedule-exam-detail.component.html',
  styleUrls: ['./schedule-exam-detail.component.css'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)'))
    ])
  ]
})
export class ScheduleExamDetailComponent implements OnChanges, OnDestroy, OnInit {
  @Input()
  filterOptions: IScheduleExamFilterOptions

  @Input()
  programData: IScheduleExamTableData[]

  @Input()
  isPreviousExam = false

  @Input()
  examCategory: string

  @Input()
  courseGroupId: string

  @Input()
  examType: string

  @Input()
  columnsToDisplay: string[] = []

  @Input()
  searchText: string

  @Output()
  exameTimeTableEvent = new EventEmitter()

  @Output()
  courseGroupLength = new EventEmitter()

  @Output()
  isEditable = new EventEmitter<boolean>()

  @Output()
  refreshChipsFilter = new EventEmitter<void>()

  dataSource: MatTableDataSource<IScheduleExamTableData> = new MatTableDataSource()
  expandedElementId: string | null
  isOnsiteWithProctor = false
  courseGroups: IScheduleExamTableData[] = []
  canUseExternalGroup = false
  uploadStudentsList: string[] = []
  menuOptions = [
    {
      label: 'scheduleExamDetail.uploadStudents',
      action: 'importStudents',
      key: 'upload-students',
      hasPermission: true,
      subTabName: 'scheduled-course'
    },
    {
      label: 'scheduleExamDetail.downloadTemplate',
      action: 'downloadTemplate',
      key: 'download-template',
      hasPermission: true,
      subTabName: 'scheduled-course'
    },
    {
      label: 'scheduleExamDetail.reschedule',
      action: 'reschedule',
      key: 'reschedule',
      hasPermission: true,
      subTabName: ''
    },
    {
      label: 'scheduleExamDetail.cancel',
      action: 'cancel',
      key: 'cancel',
      hasPermission: true,
      subTabName: ''
    },
    {
      label: 'common.viewLog',
      action: 'viewLog',
      key: 'view-log',
      hasPermission: true,
      subTabName: 'scheduled-course'
    }
  ]
  selectedGender: string
  fileName: string
  selectedTestCenterIds: string[] = []
  errorType: string
  selectedCourse: IScheduleExamTableData
  canEnableSis = environment.canEnableSis
  hasSchedulePermission = false

  constructor(
    private examScheduleService: ExamScheduleService,
    private errorHandlerService: ErrorHandlerService,
    private utilsService: UtilService,
    private globalService: GlobalService,
    private translateService: TranslateService,
    private nameConversionService: NameConversionService,
    private dialog: MatDialog,
    private settingsService: SettingsService,
    private utilService: UtilService,
    private csvService: CsvService,
    private profileSettingsService: ProfileSettingsService,
    private routeService: RouteService,
    private independentAssessmentService: IndependentAssessmentService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    if (this.canEnableSis) {
      this.menuOptions.push({
        label: 'independentAssessmentAuthoring.uploadSisStudents',
        action: 'uploadSisStudents',
        key: 'upload-sis-students',
        hasPermission: true,
        subTabName: ''
      })
    }

    this.menuOptions.forEach((option) => {
      if (option?.action === 'uploadSisStudents') {
        return (option.hasPermission = true)
      }

      option.hasPermission = this.authService.checkHasPermission({
        permissionType: EPermissionType.ACTION,
        moduleName: ROUTES.MODULE_EXAM,
        pageName: ROUTES.PAGE_EXAM_DASHBOARD,
        tabName: ROUTES.TAB_EXAM_ONGOING_EXAM,
        ...(option.subTabName && { subTabName: option.subTabName }),
        actionName: option.key
      })
    })

    this.hasSchedulePermission = this.authService.checkHasPermission({
      permissionType: EPermissionType.ACTION,
      moduleName: ROUTES.MODULE_EXAM,
      pageName: ROUTES.PAGE_EXAM_DASHBOARD,
      tabName: ROUTES.TAB_EXAM_ONGOING_EXAM,
      subTabName: ROUTES.SUB_TAB_UNSCHEDULED,
      actionName: 'schedule'
    })
  }

  ngOnChanges(changes: SimpleChanges) {
    const { filterOptions } = changes

    if (filterOptions?.currentValue) {
      setTimeout(() => this.fetchProgramData())
    }
  }

  ngOnDestroy(): void {
    this.examScheduleService.showSchedule = false
  }

  private fetchProgramData() {
    if (!this.filterOptions?.courseFilter || !this.filterOptions?.examId) {
      this.isEditable.emit(false)
      return (this.dataSource.data = [])
    }

    if (this.showSchedule) {
      return
    }

    this.getExamScheduleDetail()
  }

  getExamScheduleDetail() {
    this.examScheduleService
      .getExamScheduleDetail({
        params: this.examScheduleDetailParams,
        isPreviousExam: this.isPreviousExam
      })
      .subscribe({
        next: ({ data }) => {
          const {
            courseGroups,
            exam: { isEditable }
          } = data as unknown as IScheduleExamTableDataResponse

          courseGroups?.forEach((program) => {
            program.collapse = false

            const scheduleStatus = program?.status
            const startTime =
              this.utilsService.formatCustomTime({
                time: program?.session?.start as unknown as IScheduleExamTimeFormat
              }) || ''
            const slotEndTime =
              this.utilsService.formatCustomTime({
                time: program?.session?.slotEnd as unknown as IScheduleExamTimeFormat
              }) || ''
            const actualStartTime =
              this.utilsService.formatCustomTime({
                time: program?.session?.actualStart as unknown as IScheduleExamTimeFormat
              }) || ''
            const endTime =
              this.utilsService.formatCustomTime({
                time: program?.session?.end as unknown as IScheduleExamTimeFormat
              }) || ''
            const assessmentStatus = program?.assessment?.status

            Object.assign(program, {
              statusText: this.getStatusTag({ status: scheduleStatus })?.statusText || '',
              statusClass: this.getStatusTag({ status: scheduleStatus })?.statusClass || ''
            })

            Object.assign(program.session, {
              startEndTime: `${startTime} - ${slotEndTime}`,
              slotStartEndTime: `${actualStartTime} - ${endTime}`
            })

            Object.assign(program.assessment, {
              statusText: this.getStatusTag({ status: assessmentStatus })?.statusText || '',
              statusClass: this.getStatusTag({ status: assessmentStatus })?.statusClass || ''
            })

            program.isAssignedSlotTime = !!(
              program?.session?.slotEnd && Object.keys(program.session.slotEnd).length
            )
          })

          this.dataSource.data = courseGroups
          this.courseGroups = [...courseGroups]
          this.isEditable.emit(isEditable)
          this.onSearchCourse({ searchText: this.searchText })
        },
        error: (err) => this.errorHandlerService.errorLog(err)
      })
  }

  private get examScheduleDetailParams(): IScheduleExamReadinessCourseApiParams {
    return {
      id: this.filterOptions?.examId,
      viewFilter: this.filterOptions?.courseFilter ?? DEFAULT_SELECTED_FILTER,
      programFilter: this.filterOptions?.programCommonFilter ?? DEFAULT_SELECTED_FILTER,
      termFilter: this.filterOptions?.termCommonFilter ?? DEFAULT_SELECTED_FILTER,
      levelFilter: this.filterOptions?.levelCommonFilter ?? DEFAULT_SELECTED_FILTER,
      yearFilter: this.filterOptions?.yearCommonFilter ?? DEFAULT_SELECTED_FILTER,
      curriculumFilter: this.filterOptions?.curriculumCommonFilter ?? DEFAULT_SELECTED_FILTER,
      rotationalGroupFilter:
        this.filterOptions?.rotationGroupCommonFilter ?? DEFAULT_SELECTED_FILTER,
      type: this.isPreviousExam ? 'previousExam' : 'onGoingExam',
      examCategory: this.examCategory,
      ...(this.courseGroupId && { courseGroupId: this.courseGroupId })
    }
  }

  getStatusTag({ status }: { status: string }): { statusText: string; statusClass: string } {
    if (!status) {
      return { statusText: '', statusClass: '' }
    }

    const statusClass = this.nameConversionService.convertToKebabCase(status) ?? ''
    const statusText = statusClass.split('-').join(' ')

    return { statusText, statusClass }
  }

  onClickAssessmentSettings({ course }: { course: IScheduleExamTableData }) {
    this.examScheduleService
      .getAssessmentSettings({ params: { examCourseId: course._id } })
      .subscribe(({ data }) => {
        this.dialog.open(AssessmentConfigurationPopupComponent, {
          panelClass: ['dialog-padding-top-none', 'digi-white-bg', 'dialog-fix'],
          width: '100%',
          maxWidth: '70%',
          height: '100%',
          maxHeight: '95%',
          data: {
            version: data,
            isViewOnly: true,
            config: { examTypeName: course?.exam?.examType?.name },
            isTestSpecEnabled: course?.assessment?.isTestSpecEnabled,
            isSchedulePage: true
          },
          direction: this.settingsService.getOptions().dir
        })
      })
  }

  onClickSchedule({ course }: { course?: IScheduleExamTableData } = {}) {
    if (course) {
      this.selectedCourse = course
    }

    this.utilsService.customSliderToggle({ id: 'schedule-slider', customWidth: '95%' })
    this.examScheduleService.showSchedule = !this.examScheduleService.showSchedule

    if (this.examScheduleService.canRefreshExamSchedule) {
      this.examScheduleService.canRefreshExamSchedule = false
    }

    if (!this.examScheduleService.showSchedule) {
      this.getExamScheduleDetail()
      this.refreshChipsFilter.emit()
    }

    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { examTimeDetails: null },
      queryParamsHandling: 'merge'
    })
  }

  onSearchCourse({ searchText }: { searchText: string }) {
    const searchLower = searchText?.toLowerCase()?.trim()

    this.dataSource.data = this.courseGroups.filter((item: IScheduleExamTableData) => {
      return (
        item?.courseName?.toString().toLowerCase().includes(searchLower) ||
        item?.courseCode?.toString().toLowerCase().includes(searchLower)
      )
    })
  }

  onClickMenuOption({ action, course }: { action: string; course?: IScheduleExamTableData }) {
    switch (action) {
      case 'importStudents':
        this.handleImportStudents({ course })
        break

      case 'downloadTemplate':
        this.handleDownloadTemplate({ course })
        break

      case 'reschedule':
        this.confirmUpdateExam({ groupId: course?._id, action })
        break

      case 'cancel':
        this.confirmUpdateExam({ groupId: course?._id, action })
        break

      case 'uploadSisStudents':
        this.handleUploadSisStudents({ course })
        break

      case 'viewLog':
        this.handleViewLog({ course })
        break

      default:
        console.warn('Unknown menu action:', action)
    }
  }

  handleViewLog({ course }: { course?: IScheduleExamTableData }) {
    this.dialog.open(UploadLogPopupComponent, {
      maxWidth: '95vw',
      maxHeight: '95vh',
      data: {
        courseId: course?._id
      },
      panelClass: 'dialog-fix',
      direction: this.settingsService.getOptions().dir
    })
  }

  private handleUploadSisStudents({ course }: { course?: IScheduleExamTableData }) {
    if (!course) {
      return
    }

    this.independentAssessmentService
      .getSisStudent({ courseId: course?._id, type: 'schedule', isIndependentAssessment: false })
      .subscribe(
        ({ message }) => {
          this.globalService.showSuccess(message, '')
          this.handleImportStudents({ course, isSis: true })
        },
        (err) => this.errorHandlerService.errorLog(err)
      )
  }

  private handleDownloadTemplate({ course }: { course?: IScheduleExamTableData }) {
    if (!course) {
      return
    }

    const courseCode = course.courseCode
    const academicNo = this.translateService.instant('exams.uploadPermittedStudents.academicNo')
    const studentColumn = [academicNo]
    const templateToExcel: string[][] = [studentColumn, []]

    this.utilService.exportTemplateAsExcel({
      templateToExcel,
      fileName: course.courseHierarchyName,
      sheetName: courseCode
    })
  }

  private confirmUpdateExam({ groupId, action }: { groupId?: string; action: string }) {
    if (!groupId) {
      return
    }

    const messageKey =
      action === 'reschedule'
        ? 'scheduleExamDetail.areYouSureToReschedule'
        : 'scheduleExamDetail.areYouSureToCancel'

    this.dialog
      .open(ConfirmDialogComponent, {
        width: '480px',
        data: {
          message: this.translateService.instant(messageKey, { action }),
          type: action
        },
        panelClass: 'dialog-fix',
        direction: this.settingsService.getOptions().dir
      })
      .afterClosed()
      .subscribe((result) => {
        if (!result) {
          return
        }
        this.updateExam({ groupId, action })
      })
  }

  private updateExam({ groupId, action }: { groupId: string; action: string }) {
    const payload = { cancelExam: action === 'cancel' }

    this.examScheduleService.rescheduleExam({ action, groupId, payload }).subscribe({
      next: (res) => {
        this.globalService.showSuccess(res.message, '')
        this.fetchProgramData()
        this.refreshChipsFilter.emit()
      },
      error: (err) => this.errorHandlerService.errorLog(err)
    })
  }

  private getStudentsForIndependentAssessment({ examCourseId }: { examCourseId: string }) {
    return this.independentAssessmentService
      .getStudentByTcDetails({
        payload: {
          examCourseId,
          pageNo: 1,
          limit: 10,
          search: '',
          isInit: true,
          isRefreshStudentList: false,
          academicNos: [],
          isThrowError: false
        },
        type: 'exam-schedule'
      })
      .pipe(
        map(({ data }) => ({
          students: data?.students,
          isError: false
        })),
        catchError((error) => {
          this.errorHandlerService.errorLog(error)
          return of({ isError: true, students: [] })
        })
      )
  }

  private handleImportStudents({
    course,
    isSis = false
  }: {
    course?: IScheduleExamTableData
    isSis?: boolean
  }) {
    if (!course) {
      return
    }

    this.getStudentsForIndependentAssessment({ examCourseId: course?._id }).subscribe(
      ({ students, isError }) => {
        if (isError) {
          return
        }

        if (students?.length) {
          return this.openAutoAssignPopup({ type: 'schedule', course, isUpload: true })
        } else {
          if (isSis) {
            return
          }

          this.csvService.isResponse = false
          this.csvService.isLoading = false
          this.csvService.importedFileName = 'exams.uploadPermittedStudents.csvFilesSelectedZero'
          this.csvService.studentImportResponse = {
            data: {},
            message: ''
          }
          this.getGroupSettings()

          this.dialog
            .open(ImportCsvDialogComponent, {
              width: '400px',
              data: {
                dialogTitle: 'exams.uploadPermittedStudents.selectStudentPermitted',
                dialogHint: this.csvService.importedFileName,
                type: 'scheduleAssessment',
                examCourseId: course?._id,
                isCustomGroup: true,
                canAllowToUploadWithoutGroup: true,
                hideBulkUpload: false
              },
              panelClass: 'dialog-fix',
              direction: this.settingsService.getOptions().dir
            })
            .afterClosed()
            .subscribe((result) => {
              if (!result) {
                return
              }

              if (result?.type === DIGICLASS) {
                return this.routeService.transitionToImportFromDigiClass({
                  id: course?._id,
                  name: course?.courseName,
                  courseName: course?.courseName
                })
              }

              if (result?.type === SCHEDULED_STUDENT_GROUPING) {
                this.uploadStudentsList = []

                return this.openAutoAssignPopup({ type: result?.type.toLowerCase(), course })
              }

              if (Array.isArray(result)) {
                return this.validateFileName({
                  files: result[0],
                  course
                })
              }

              this.validateFileName({
                files: {
                  name: course.courseHierarchyName,
                  students: [{ academicNo: result?.academicNo }]
                },
                type: result?.type,
                course
              })
              this.selectedGender = result?.gender
            })
        }
      }
    )
  }

  private validateFileName({
    files,
    type,
    course
  }: {
    files?: { name: string; students: { academicNo: string }[] }
    type?: string
    course: IScheduleExamTableData
  }) {
    this.uploadStudentsList = this.getStudents({ students: files?.students })

    this.fileName = files?.name ?? ''

    if (this.fileName?.replace(' ', '') !== course.courseHierarchyName?.replace(' ', '')) {
      this.openFileMismatchPopup({ course })

      return
    }

    if (!this.uploadStudentsList.length) {
      return this.globalService.showError(
        this.translateService.instant('exams.uploadStudentError.importedDataIsEmpty'),
        ''
      )
    }

    type && type === 'INDIVIDUAL'
      ? this.uploadStudents({ uploadType: type, course })
      : this.openAutoAssignPopup({ type: 'schedule', course })
  }

  private getGroupSettings() {
    this.profileSettingsService.getCanUseExternalGroup().subscribe(
      ({ data }) => (this.canUseExternalGroup = data?.canUseExternalGroup),
      (err) => this.errorHandlerService.errorLog(err)
    )
  }

  private getStudents({ students }: { students?: { academicNo: string }[] }) {
    if (!students?.length) {
      return []
    }

    const studentAcademicNo = students.map((item) => {
      return item.academicNo.trim()
    })

    return studentAcademicNo
  }

  private openAutoAssignPopup({
    type,
    course,
    isUpload = false
  }: {
    type: string
    course: IScheduleExamTableData
    isUpload?: boolean
  }) {
    let data

    if (isUpload) {
      data = {
        type: 'independent-assessment',
        academicNos: [],
        course: {
          _id: course?._id,
          name: course?.courseHierarchyName,
          session: course?.session,
          date: course?.date,
          examType: course?.exam?.examType?.name
        },
        canUseExternalGroup: false,
        proctoringType: 'onsite_with_proctor',
        isExamSchedule: true
      }
    } else {
      data = {
        type,
        academicNos: this.uploadStudentsList,
        fileName: this.fileName || course?.courseName,
        examType: this.examType || course?.exam?.examType?.name,
        course,
        canUseExternalGroup: this.canUseExternalGroup
      }
    }

    this.dialog
      .open(AutoAssignTcPopupComponent, {
        width: '75vw',
        panelClass: 'dialog-fix',
        direction: this.settingsService.getOptions().dir,
        disableClose: true,
        data
      })
      .afterClosed()
      .subscribe((result) => {
        if (!result) {
          return
        }

        this.fetchProgramData()
      })
  }

  private openFileMismatchPopup({ course }: { course: IScheduleExamTableData }) {
    this.dialog.open(PlannedCountExistComponent, {
      data: {
        errorType: 'filenameMismatch',
        course
      },
      panelClass: 'dialog-fix',
      direction: this.settingsService.getOptions().dir
    })
  }

  private uploadStudents({
    selectedTestCenterIds,
    isStudentAlreadyScheduledInSameDateAndTime,
    uploadType,
    course
  }: {
    selectedTestCenterIds?: string[]
    isStudentAlreadyScheduledInSameDateAndTime?: boolean
    uploadType?: 'BULK' | 'INDIVIDUAL'
    course: IScheduleExamTableData
  }) {
    if (!this.uploadStudentsList.length) {
      return
    }

    const payload: IScheduleStudentUploadPayload = {
      autoAssignAcademicNos: this.uploadStudentsList,
      courseId: course._id,
      file: {
        name: this.fileName,
        newTcs: [],
        testCenters: []
      },
      isAutoAssignTc: true,
      importFrom: this.canUseExternalGroup ? 'DC' : 'DA',
      isUploadIndividualStudent:
        uploadType === 'INDIVIDUAL' || isStudentAlreadyScheduledInSameDateAndTime
    }

    if (selectedTestCenterIds?.length) {
      this.selectedTestCenterIds = selectedTestCenterIds
    }

    if (this.selectedTestCenterIds?.length) {
      Object.assign(payload.file, {
        newTcs: this.selectedTestCenterIds
      })
    }

    if (isStudentAlreadyScheduledInSameDateAndTime) {
      Object.assign(payload, {
        isStudentAlreadyScheduledInSameDateAndTime
      })
    }

    this.examScheduleService.uploadStudentList({ payload }).subscribe(
      ({ data, message }) => {
        if (data?.students || data?.alreadyScheduledTcs || data?.pending) {
          data.message = message
          this.errorType = data?.pending
            ? 'countExisting'
            : data?.alreadyScheduledTcs
              ? 'sameTCs'
              : 'studentData'
          this.uploadErrorPopup({ errorList: data, course })

          return
        }

        this.globalService.showSuccess(message, '')
        this.fetchProgramData()
        this.selectedTestCenterIds = []
      },
      (err) => this.errorHandlerService.errorLog(err)
    )
  }

  uploadErrorPopup({
    errorList,
    course
  }: {
    errorList: IScheduleUploadError
    course: IScheduleExamTableData
  }) {
    this.dialog
      .open(UploadStudentErrorPopupComponent, {
        data: {
          errorType: this.errorType,
          course,
          errorList,
          currentUploadCount: this.uploadStudentsList.length,
          gender: this.selectedGender,
          examType: this.examType
        },
        panelClass: 'dialog-fix',
        direction: this.settingsService.getOptions().dir
      })
      .afterClosed()
      .subscribe((result) => {
        if (!result) {
          return
        }

        let newResult
        if (result?.isStudentAlreadyScheduledInSameDateAndTime || result?.testCenterIds?.length) {
          newResult = {
            selectedTestCenterIds: result?.testCenterIds,
            isStudentAlreadyScheduledInSameDateAndTime:
              result?.isStudentAlreadyScheduledInSameDateAndTime
          }
        }

        this.errorType = ''
        this.uploadStudents({
          selectedTestCenterIds: newResult ? newResult?.selectedTestCenterIds : result,
          isStudentAlreadyScheduledInSameDateAndTime:
            newResult?.isStudentAlreadyScheduledInSameDateAndTime,
          uploadType: 'INDIVIDUAL',
          course
        })
      })
  }

  onClickView({ course }: { course: IScheduleExamTableData }) {
    this.openScheduleCourseDetail({ course })
  }

  private openScheduleCourseDetail({ course }: { course: IScheduleExamTableData }) {
    this.dialog.open(ScheduleCourseDetailPopupComponent, {
      data: course,
      panelClass: 'dialog-fix',
      maxHeight: '95%',
      direction: this.settingsService.getOptions().dir
    })
  }

  get showSchedule() {
    return this.examScheduleService.showSchedule
  }

  getHierarchy({ course }: { course: IScheduleExamTableData }) {
    return this.utilService.getCourseDetails({ hierarchy: course?.hierarchy })
  }

  onChangeStudentTc({
    testCenters,
    course
  }: {
    testCenters: IScheduleTestCenterDetail[]
    course: IScheduleExamTableData
  }) {
    course.testCenters = testCenters.map((testCenter) => {
      return {
        ...testCenter,
        studentsCount: testCenter?.students?.length
      }
    })
  }

  onUpdateCourseStudentsCount({
    allTcStudents,
    courseDetailId
  }: {
    allTcStudents: { maleCount: number; femaleCount: number }
    courseDetailId: string
  }) {
    const selectedCourse = this.dataSource?.data?.find((course) => course?._id === courseDetailId)

    if (selectedCourse) {
      selectedCourse.students.male.scheduledCount = allTcStudents.maleCount
      selectedCourse.students.female.scheduledCount = allTcStudents.femaleCount
    }
  }
}
