import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild
} from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { MatTableDataSource } from '@angular/material/table'
import { TranslateService } from '@ngx-translate/core'

import { AssignInvigilatorDialogComponent } from '@appcore/app/core/components/exams/assign-invigilator-dialog/assign-invigilator-dialog.component'
import { ChangeTestCenterComponent } from '@appcore/app/core/components/shared-component'
import { DigiExportComponent } from '@appcore/shared/digi-export/digi-export.component'
import { ConfirmDialogComponent } from '@appcore/shared/confirm-dialogbox'

import { ExamScheduleService } from '@appcore/app/pages/exams/exam-schedule.service'
import { ErrorHandlerService } from '@appcore/app/services/error-handler.service'
import { GlobalService } from '@appcore/app/services/global.service'
import { SettingsService } from '@appcore/app/services/settings.service'

import {
  IScheduleAssignInvigilatorRequest,
  IScheduleExam,
  IScheduleExamTableData,
  IScheduleTestCenterCapacity,
  IScheduleTestCenterDetail,
  IScheduleTestCenterFullName,
  IScheduleTestCenterInvigilator,
  IScheduleTestCenterStudent
} from '../schedule-exam-detail/schedule-exam-detail.interface'
import { ITimeFormat } from '../../../../models/exam-center-management'

import { EPermissionType, ROUTES } from '@appcore/constants'
import { SearchComponent } from '../../digi-search/digi-search.component'
import { AuthService } from '@appcore/app/services/auth.service'
import { UtilService } from '@appcore/app/services'

type TabKey = 'students' | 'proctors'

@Component({
  selector: 'digi-schedule-student-proctor-table',
  templateUrl: './schedule-student-proctor-table.component.html',
  styleUrls: ['./schedule-student-proctor-table.component.css']
})
export class ScheduleStudentProctorTableComponent implements OnInit, OnChanges {
  @ViewChild('searchInput')
  searchInput: SearchComponent

  @Input()
  students: IScheduleTestCenterStudent[] = []

  @Input()
  proctors: IScheduleTestCenterInvigilator[] = []

  @Input()
  canAllocateSeats = false

  @Input()
  exam: IScheduleExam

  @Input()
  testCenterDetail: IScheduleTestCenterDetail

  @Input()
  courseData: IScheduleExamTableData

  @Output()
  changeStudentTc: EventEmitter<boolean> = new EventEmitter()

  @Output()
  updateStudents: EventEmitter<{ students: IScheduleTestCenterStudent[]; testCenterId: string }> =
    new EventEmitter()

  displayedColumns: string[] = []
  dataSource: MatTableDataSource<IScheduleTestCenterStudent | IScheduleTestCenterInvigilator> =
    new MatTableDataSource()
  activeTab: TabKey = 'students'
  removedStudentsCount = 0
  hasExportStudentPermission = false
  hasRemoveStudentPermission = false
  hasChangeTestCenterPermission = false
  hasEditInvigilatorPermission = false
  hasExportInvigilatorPermission = false

  constructor(
    private globalService: GlobalService,
    private dialog: MatDialog,
    private translate: TranslateService,
    private settingsService: SettingsService,
    private examScheduleService: ExamScheduleService,
    private errorHandler: ErrorHandlerService,
    private authService: AuthService,
    private cd: ChangeDetectorRef,
    private utilService: UtilService
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    const { students } = changes

    if (students?.currentValue) {
      this.sortTableData({ data: students?.currentValue })
    }
  }

  ngOnInit() {
    this.hasExportStudentPermission = this.authService.checkHasPermission({
      permissionType: EPermissionType.ACTION,
      moduleName: ROUTES.MODULE_EXAM,
      pageName: ROUTES.PAGE_EXAM_DASHBOARD,
      tabName: ROUTES.TAB_EXAM_ONGOING_EXAM,
      subTabName: ROUTES.SUB_TAB_ALL_COURSE_GROUP,
      actionName: ROUTES.ACTION_EXPORT_STUDENT
    })
    this.hasRemoveStudentPermission = this.authService.checkHasPermission({
      permissionType: EPermissionType.ACTION,
      moduleName: ROUTES.MODULE_EXAM,
      pageName: ROUTES.PAGE_EXAM_DASHBOARD,
      tabName: ROUTES.TAB_EXAM_ONGOING_EXAM,
      subTabName: ROUTES.SUB_TAB_ALL_COURSE_GROUP,
      actionName: ROUTES.ACTION_REMOVE_STUDENT
    })
    this.hasChangeTestCenterPermission = this.authService.checkHasPermission({
      permissionType: EPermissionType.ACTION,
      moduleName: ROUTES.MODULE_EXAM,
      pageName: ROUTES.PAGE_EXAM_DASHBOARD,
      tabName: ROUTES.TAB_EXAM_ONGOING_EXAM,
      subTabName: ROUTES.SUB_TAB_ALL_COURSE_GROUP,
      actionName: ROUTES.ACTION_CHANGE_TC
    })
    this.hasEditInvigilatorPermission = this.authService.checkHasPermission({
      permissionType: EPermissionType.ACTION,
      moduleName: ROUTES.MODULE_EXAM,
      pageName: ROUTES.PAGE_EXAM_DASHBOARD,
      tabName: ROUTES.TAB_EXAM_ONGOING_EXAM,
      subTabName: ROUTES.SUB_TAB_ALL_COURSE_GROUP,
      actionName: ROUTES.ACTION_EDIT_INVIGILATOR
    })
    this.hasExportInvigilatorPermission = this.authService.checkHasPermission({
      permissionType: EPermissionType.ACTION,
      moduleName: ROUTES.MODULE_EXAM,
      pageName: ROUTES.PAGE_EXAM_DASHBOARD,
      tabName: ROUTES.TAB_EXAM_ONGOING_EXAM,
      subTabName: ROUTES.SUB_TAB_ALL_COURSE_GROUP,
      actionName: ROUTES.ACTION_EXPORT_INVIGILATOR
    })
    this.onClickTab({ tabKey: this.activeTab, isFirstChange: true })
  }

  private sortTableData({ data }: { data: IScheduleTestCenterStudent[] }) {
    if (!data?.length) {
      return
    }

    this.utilService?.sortStudentsByAcademicNo({ students: data as IScheduleTestCenterStudent[] })
  }

  onClickTab({ tabKey, isFirstChange = false }: { tabKey: TabKey; isFirstChange?: boolean }) {
    this.activeTab = tabKey

    if (!isFirstChange) {
      this.searchInput.reset()
    }

    if (tabKey === 'students') {
      this.setStudents()
    } else {
      this.setProctors()
    }
  }

  onSearchPerson({ searchText }: { searchText: string }) {
    if (!searchText) {
      this.onClickTab({ tabKey: this.activeTab })
      return
    }

    const searchLower = searchText.toLowerCase()

    if (this.activeTab === 'students') {
      const filteredData = this.students.filter((student: IScheduleTestCenterStudent) => {
        const fullName = this.getFullName({ name: student?.name })
        return String(fullName)?.toLowerCase()?.includes(searchLower)
      })
      this.dataSource.data = filteredData
    } else {
      const filteredData = this.proctors.filter((proctor: IScheduleTestCenterInvigilator) => {
        const fullName = this.getFullName({ name: proctor?.name })
        return String(fullName)?.toLowerCase()?.includes(searchLower)
      })
      this.dataSource.data = filteredData
    }
  }

  private setStudents() {
    this.dataSource.data = [...this.students]
    const columns = [
      'checkbox',
      'academicNo',
      'studentName',
      'gender',
      'studentGroup',
      'studentActions'
    ]

    if (
      !this.hasExportStudentPermission &&
      !this.hasRemoveStudentPermission &&
      !this.hasChangeTestCenterPermission
    ) {
      columns.splice(columns.length - 1, 1)
    }

    if (this.canAllocateSeats) {
      columns.splice(columns.length - 1, 0, 'seatNo')
    }

    this.displayedColumns = columns
  }

  private setProctors() {
    this.dataSource.data = [...this.proctors]
    this.displayedColumns = ['employeeId', 'proctorName', 'role']
  }

  getFullName({ name }: { name: IScheduleTestCenterFullName }) {
    if (!name) {
      return
    }

    return this.globalService.getFullName(name)
  }

  openStudents(): void {
    const exportData = {
      testCenterStudentData: this.students,
      courseData: this.courseData,
      programDetails: this.courseData,
      testcenterdetails: this.testCenterDetail,
      title: 'common.studentListPreview',
      type: 'student',
      proctoringType: this.exam?.examType?.locationAndProctoringType,
      canAllocateSeats: this.canAllocateSeats
    }
    this.dialog.open(DigiExportComponent, {
      maxWidth: '90%',
      maxHeight: '90%',
      width: '100%',
      panelClass: ['full-screen-modal', 'dialog-fix'],
      data: exportData,
      disableClose: true,
      direction: this.settingsService?.getOptions()?.dir
    })
  }

  get examType() {
    return this.exam?.examType?.locationAndProctoringType
  }

  onClickRemoveStudent({ student }: { student?: IScheduleTestCenterStudent }) {
    this.dialog
      .open(ConfirmDialogComponent, {
        data: {
          title: 'exams.examsManagement.areYouSure',
          message: this.translate.instant('exams.examsManagement.areYouSureMessage', {
            testCenterName: this.testCenterDetail?.name
          }),
          actionBtn: 'common.yes'
        },
        panelClass: 'dialog-fix',
        disableClose: true,
        direction: this.settingsService.getOptions().dir
      })
      .afterClosed()
      .subscribe((result: boolean) => {
        if (!result) {
          return
        }

        this.removeStudent({ student })
      })
  }

  private removeStudent({ student }: { student?: IScheduleTestCenterStudent }) {
    this.examScheduleService
      .removeStudent({
        id: this.courseData?._id,
        testCenterId: this.testCenterDetail?._id,
        payload: {
          students: student ? [String(student?.academicNo)] : this.payload,
          removeByAcademicNo: true
        }
      })
      .subscribe(
        ({ message }) => {
          this.removedStudentsCount = this.selectedStudents?.length
          this.removeStudentFromTable({ student })
          this.globalService.showSuccess(message)
          if (!this.dataSource?.data?.length) {
            this.changeStudentTc.emit(true)
          }
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  get selectedStudents(): IScheduleTestCenterStudent[] {
    const data = this.dataSource.data as IScheduleTestCenterStudent[]

    return data.reduce((acc: IScheduleTestCenterStudent[], student) => {
      if (student?.isSelected) {
        acc.push(student)
      }

      return acc
    }, [])
  }

  get payload(): string[] {
    const data = this.dataSource.data as IScheduleTestCenterStudent[]
    return data.reduce((acc: string[], student) => {
      if (student?.isSelected) {
        acc.push(String(student?.academicNo))
      }

      return acc
    }, [])
  }

  private removeStudentFromTable({ student }: { student?: IScheduleTestCenterStudent }) {
    const data = this.dataSource.data as IScheduleTestCenterStudent[]
    if (student) {
      const removeIndex = data.findIndex(({ _id }) => _id === student?._id)
      data.splice(removeIndex, 1)
    } else {
      this.selectedStudents.forEach(({ _id }) => {
        const removeIndex = data.findIndex(({ _id: studentId }) => _id === studentId)
        data.splice(removeIndex, 1)
      })
    }

    this.dataSource.data = data
    this.updateStudents.emit({
      students: data,
      testCenterId: this.testCenterDetail?._id
    })
    this.changeStudentTc.emit(true)
  }

  onChangeSelectStudent({
    student,
    isChecked
  }: {
    student?: IScheduleTestCenterStudent
    isChecked
  }) {
    student
      ? (student.isSelected = !student.isSelected)
      : (this.dataSource.data as IScheduleTestCenterStudent[]).forEach(
          (row) => (row.isSelected = isChecked)
        )
  }

  get isAllStudentsSelected() {
    return (this.dataSource.data as IScheduleTestCenterStudent[]).every(
      ({ isSelected }) => isSelected
    )
  }

  get isSomeStudentsSelected() {
    return (this.dataSource.data as IScheduleTestCenterStudent[]).some(
      ({ isSelected }) => isSelected
    )
  }

  onClickChangeStudentTestCenter({ student }: { student: IScheduleTestCenterStudent }) {
    this.examScheduleService
      .getTestCenterAvailability({
        payload: {
          examCourseId: this.courseData?._id,
          startTime: this.courseData?.session?.start as ITimeFormat,
          endTime: this.courseData?.session?.end as ITimeFormat,
          date: this.courseData?.date,
          canMoveStudent: true,
          gender: this.globalService.getGenderChar({ gender: student?.gender }),
          testCenterId: this.testCenterDetail?._testCenter,
          isUploadStudent: false
        }
      })
      .subscribe(
        ({ data }) => {
          this.openChangeTestCenterDialog({ student, testCenters: data.testCenter })
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  private openChangeTestCenterDialog({
    student,
    testCenters
  }: {
    student: IScheduleTestCenterStudent
    testCenters: IScheduleTestCenterCapacity[]
  }) {
    this.dialog
      .open(ChangeTestCenterComponent, {
        data: {
          title: 'exams.examsManagement.testTable.changeTestCenter',
          message: 'exams.examsManagement.testTable.selectedStudentsTC',
          studentId: student._id,
          examCourseGroupId: this.courseData?._id,
          currentTestCenterId: this.testCenterDetail?._testCenter,
          testCenters
        },
        panelClass: 'dialog-fix',
        disableClose: true,
        direction: this.settingsService?.getOptions()?.dir
      })
      .afterClosed()
      .subscribe((result: boolean) => {
        if (!result) {
          return
        }

        this.removeStudentFromTable({ student })
      })
  }

  onClickEditInvigilator() {
    this.openAssignInvigilatorPopup()
  }

  private openAssignInvigilatorPopup() {
    const group = this.getCourseGroup()

    this.dialog
      .open(AssignInvigilatorDialogComponent, {
        maxWidth: '1000px',
        disableClose: true,
        data: {
          group,
          testCenter: this.testCenterDetail,
          count: this.courseData?.requiredInvigilatorsCount,
          actionType: ROUTES.ACTION_EDIT_INVIGILATOR,
          invigilators: this.examScheduleService.getInvigilators({
            testCenter: this.testCenterDetail
          })
        },
        panelClass: 'dialog-fix',
        direction: this.settingsService.getOptions().dir
      })
      .afterClosed()
      .subscribe((payload: IScheduleAssignInvigilatorRequest) => {
        if (!payload) {
          return
        }

        this.assignInvigilator({
          testCenterId: this.testCenterDetail._testCenter,
          groupId: payload.groupId
        })
      })
  }

  private getCourseGroup() {
    const {
      _id,
      courseName: name,
      date,
      students: {
        male: { totalCount: maleStudents },
        female: { totalCount: femaleStudents }
      },
      session: {
        start: { format: startFormat, minute: startMinute, hour: startHour },
        end: { format: endFormat, minute: endMinute, hour: endHour }
      }
    } = this.courseData as IScheduleExamTableData

    return {
      time: {
        start: {
          format: startFormat,
          hour: startHour,
          minute: startMinute
        },
        end: {
          format: endFormat,
          hour: endHour,
          minute: endMinute
        }
      },
      _id,
      date,
      femaleStudents,
      maleStudents,
      name
    }
  }

  private assignInvigilator({ testCenterId, groupId }: { testCenterId: string; groupId: string }) {
    this.examScheduleService.updateInvigilators({ testCenterId, groupId }).subscribe(
      ({ message }) => {
        this.globalService.showSuccess(message, '')
      },
      (err) => this.errorHandler.errorLog(err)
    )
  }
}
