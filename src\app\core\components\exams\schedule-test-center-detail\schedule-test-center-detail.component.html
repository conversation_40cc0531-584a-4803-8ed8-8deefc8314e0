<div fxLayout="row">
  <div class="digi-cross-container">
    <div class="digi-cross-border"></div>
    <div class="digi-cross-text">{{ 'scheduleExamDetail.testCenters' | translate }}</div>
  </div>

  <div fxFlex="100%" class="digi-p-5">
    <div class="digi-table-container">
      <div fxLayout="row" fxLayoutAlign="start center" class="digi-tc-title-container digi-mb-5">
        <div class="digi-tc-title" fxFlex>
          {{ 'scheduleExamDetail.listOfTestCenters' | translate }} ({{ dataSource.data.length }})
        </div>
        <div class="digi-tc-search-container">
          <digi-search
            width="210px"
            [text]="'scheduleExamDetail.searchTestCenter' | translate | titlecase"
            [filter]="true"
            [minChar]="0"
            (search)="onSearchTestCenter({ searchText: $event })"
          ></digi-search>
        </div>
      </div>

      <table mat-table [dataSource]="dataSource" multiTemplateDataRows class="mat-elevation-z">
        <ng-container matColumnDef="testCenterDetails">
          <th mat-header-cell *matHeaderCellDef>
            {{ 'scheduleExamDetail.testCenterDetails' | translate }}
          </th>
          <td mat-cell *matCellDef="let element">
            <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="10px">
              <div class="digi-collapse-button-container">
                <button
                  mat-icon-button
                  class="digi-collapse-button"
                  (click)="
                    $event.stopPropagation();
                    expandedElementId = expandedElementId === element?._id ? null : element?._id
                  "
                >
                  <mat-icon>
                    {{
                      expandedElementId === element?._id
                        ? 'keyboard_arrow_down'
                        : 'keyboard_arrow_right'
                    }}
                  </mat-icon>
                </button>
              </div>
              <div fxLayout="column">
                <span class="digi-tc-name">{{ element?.name }}</span>
                <span class="digi-tc-label">
                  <span>
                    {{ getTranslatedGenderCode({ genderCode: element?.gender })?.name | translate }}
                    {{ 'common.testCenter' | translate }}
                  </span>
                  <span class="digi-tc-separator"></span>
                  <span>
                    {{ element?.allocatedSeats }}
                    {{ 'scheduleExamDetail.seats' | translate }}
                  </span>
                </span>
              </div>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="students">
          <th mat-header-cell *matHeaderCellDef>Students</th>
          <td mat-cell *matCellDef="let element">
            <span class="digi-tc-students">{{ element?.students?.length || '-' }}</span>
          </td>
        </ng-container>

        <ng-container matColumnDef="primaryProctor">
          <th mat-header-cell *matHeaderCellDef>
            {{ 'scheduleExamDetail.primaryProctor' | translate }}
          </th>
          <td mat-cell *matCellDef="let element">
            <span class="digi-tc-primary-proctor">
              {{
                getFullName({
                  type: 'primaryInvigilator',
                  invigilators: element?.invigilators
                })
              }}
            </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="secondaryProctor">
          <th mat-header-cell *matHeaderCellDef>
            {{ 'scheduleExamDetail.secondaryProctor' | translate }}
          </th>
          <td mat-cell *matCellDef="let element">
            <span class="digi-tc-secondary-proctor">
              {{
                getFullName({
                  type: 'secondaryInvigilators',
                  invigilators: element?.invigilators
                })
              }}
            </span>
            <span
              *ngIf="element?.invigilators?.length > 2"
              class="digi-blue digi-fw-500 digi-ml-5 digi-cursor"
              [tooltip]="
                getFullName({
                  type: 'secondaryInvigilators',
                  invigilators: element?.invigilators,
                  isShowMore: true
                })
              "
              >+{{ element?.invigilators?.length - 2 | arabicNumber }}</span
            >
          </td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>
            <ng-container
              *digiAccessControl="
                'actions_export-invigilator^tabs_ongoing-exam_subTabs_all-course-group'
              "
            >
              <div
                fxLayout="row"
                fxLayoutAlign="start center"
                class="digi-export-container digi-cursor"
                id="exit-to-app"
                (click)="openInvigilator()"
              >
                <span class="digi-export-icon-container">
                  <mat-icon class="digi-export-icon">ios_share</mat-icon>
                </span>
                <span class="digi-export-label">{{ 'scheduleExamDetail.export' | translate }}</span>
              </div>
            </ng-container>
          </th>
          <td mat-cell *matCellDef="let element"></td>
        </ng-container>

        <!-- Expanded Content Column -->
        <ng-container matColumnDef="expandedDetail">
          <td mat-cell *matCellDef="let element" [attr.colspan]="columnsToDisplay.length">
            <div
              class="test-center-schedule-detail"
              [@detailExpand]="element?._id === expandedElementId ? 'expanded' : 'collapsed'"
            >
              <digi-schedule-student-proctor-table
                *ngIf="expandedElementId === element?._id"
                [students]="element?.students"
                [proctors]="element?.invigilators"
                [canAllocateSeats]="canAllocateSeats"
                [exam]="exam"
                [testCenterDetail]="element"
                [courseData]="courseData"
                (changeStudentTc)="onChangeStudentTc()"
                (updateStudents)="onUpdateTestCenterStudents($event)"
              ></digi-schedule-student-proctor-table>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="columnsToDisplay"></tr>
        <tr
          mat-row
          *matRowDef="let element; columns: columnsToDisplay"
          class="test-center-schedule-row"
          [class.test-center-schedule-expanded-row]="expandedElementId === element?._id"
        ></tr>
        <tr
          mat-row
          *matRowDef="let row; columns: ['expandedDetail']"
          class="exam-detail-row"
          [ngClass]="{
            active: expandedElementId === row?._id
          }"
        ></tr>
      </table>
    </div>

    <div
      *ngIf="!dataSource?.data?.length"
      fxLayout="column"
      fxLayoutAlign="center center"
      class="digi-p-25"
    >
      <span class="digi-text-gray-500">{{ 'common.noDataFound' | translate | titlecase }}</span>
    </div>
  </div>
</div>
