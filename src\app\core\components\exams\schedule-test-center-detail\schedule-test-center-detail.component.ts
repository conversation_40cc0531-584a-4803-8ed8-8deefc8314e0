import { Component, EventEmitter, Input, Output } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { MatTableDataSource } from '@angular/material/table'
import { animate, state, style, transition, trigger } from '@angular/animations'

import { ExamScheduleService } from '@appcore/app/pages/exams/exam-schedule.service'
import { GlobalService } from '@appcore/app/services'
import { ErrorHandlerService } from '@appcore/app/services/error-handler.service'
import { SettingsService } from '@appcore/app/services/settings.service'

import { DigiExportComponent } from '@appcore/shared/digi-export/digi-export.component'

import {
  IScheduleExam,
  IScheduleExamTableData,
  IScheduleTestCenterDetail,
  IScheduleTestCenterInvigilator,
  IScheduleTestCenterStudent
} from '../schedule-exam-detail/schedule-exam-detail.interface'
import { EGender } from '@appcore/app/core/constants'
import { UtilService } from '@appcore/app/services/util.service'
import { TranslateService } from '@ngx-translate/core'

@Component({
  selector: 'digi-schedule-test-center-detail',
  templateUrl: './schedule-test-center-detail.component.html',
  styleUrls: ['./schedule-test-center-detail.component.css'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)'))
    ])
  ]
})
export class ScheduleTestCenterDetailComponent {
  @Input()
  exam: IScheduleExam

  @Input()
  courseData: IScheduleExamTableData

  @Output()
  changeStudentTc: EventEmitter<IScheduleTestCenterDetail[]> = new EventEmitter()

  @Output()
  updateCourseStudentsCount: EventEmitter<{
    allTcStudents: { maleCount: number; femaleCount: number }
    courseDetailId: string
  }> = new EventEmitter()

  dataSource: MatTableDataSource<IScheduleTestCenterDetail> = new MatTableDataSource()
  columnsToDisplay = [
    'testCenterDetails',
    'students',
    'primaryProctor',
    'secondaryProctor',
    'actions'
  ]
  expandedElementId = ''
  canAllocateSeats = false
  testCenters: IScheduleTestCenterDetail[] = []

  constructor(
    private examScheduleService: ExamScheduleService,
    private globalService: GlobalService,
    private errorHandler: ErrorHandlerService,
    private dialog: MatDialog,
    private settingsService: SettingsService,
    private utilService: UtilService,
    private translateService: TranslateService
  ) {}

  ngOnInit() {
    this.getTestCenterDetail({ courseDetailId: this.courseData?._id })
    this.loadCanAllocateSeats()
  }

  private loadCanAllocateSeats(): void {
    this.examScheduleService.getCanAllocateSeats().subscribe({
      next: ({ data }) => {
        this.canAllocateSeats = data
      },
      error: (err) => this.errorHandler.errorLog(err)
    })
  }

  private getTestCenterDetail({
    courseDetailId,
    isEmit
  }: {
    courseDetailId: string
    isEmit?: boolean
  }) {
    this.examScheduleService
      .getTestCenterDetail({
        courseDetailId,
        getTestCenters: true,
        getAssessmentAuthors: <AUTHORS>
      })
      .subscribe(({ data: { testCenters } }) => {
        this.dataSource.data = testCenters
        this.testCenters = [...testCenters]

        if (isEmit) {
          this.changeStudentTc.emit(testCenters)
        }
      })
  }

  getFullName({
    type,
    invigilators,
    isShowMore = false
  }: {
    type: string
    invigilators: IScheduleTestCenterInvigilator[]
    isShowMore?: boolean
  }) {
    if (!invigilators?.length) {
      return
    }

    const primaryInvigilator = invigilators?.find((inv) => inv?.type === 'invigilator1')
    const secondaryInvigilators = invigilators?.filter((inv) => inv?.type === 'invigilator2')

    if (type === 'primaryInvigilator') {
      return primaryInvigilator
        ? this.globalService.getFullName(primaryInvigilator?.name)
        : this.translateService.instant('scheduleExamDetail.notAssignedYet')
    }

    if (type === 'secondaryInvigilators') {
      if (!secondaryInvigilators?.length) {
        return this.translateService.instant('scheduleExamDetail.notAssignedYet')
      }

      if (isShowMore && secondaryInvigilators.length > 1) {
        return secondaryInvigilators
          .map((inv) => this.globalService.getFullName(inv?.name))
          .join(', ')
      }

      return this.globalService.getFullName(secondaryInvigilators[0]?.name)
    }
  }

  private sortInvigilators({ testCenters = [] }: { testCenters: IScheduleTestCenterDetail[] }) {
    if (!testCenters?.length) {
      return
    }

    testCenters.forEach((testCenter) => {
      testCenter.invigilators.sort((a, b) => {
        if (a.type?.toLowerCase() === 'invigilator1' && b.type?.toLowerCase() !== 'invigilator1') {
          return -1
        } else if (
          a.type?.toLowerCase() !== 'invigilator1' &&
          b.type?.toLowerCase() === 'invigilator1'
        ) {
          return 1
        }
        return 0
      })
    })
  }

  openInvigilator(): void {
    const exportData = {
      testCenter: this.dataSource?.data,
      courseData: this.courseData,
      programDetails: this.courseData,
      title: 'common.invigilatorListPreview',
      type: 'invigilator'
    }

    this.sortInvigilators({ testCenters: exportData?.testCenter })

    this.dialog
      .open(DigiExportComponent, {
        maxWidth: '90%',
        maxHeight: '90%',
        panelClass: 'dialog-fix',
        data: exportData,
        direction: this.settingsService.getOptions().dir
      })
      .afterClosed()
      .subscribe((result) => {})
  }

  onSearchTestCenter({ searchText }: { searchText: string }) {
    const searchLower = searchText.toLowerCase()

    this.dataSource.data = this.testCenters.filter((item: IScheduleTestCenterDetail) => {
      return item?.name?.toString().toLowerCase().includes(searchLower)
    })
  }

  onChangeStudentTc() {
    this.getTestCenterDetail({ courseDetailId: this.courseData?._id, isEmit: true })
  }

  getTranslatedGenderCode({ genderCode }: { genderCode: EGender }) {
    return this.utilService.getTranslatedGenderCode({ genderCode: genderCode as EGender })
  }

  onUpdateTestCenterStudents({
    students,
    testCenterId
  }: {
    students: IScheduleTestCenterStudent[]
    testCenterId: string
  }) {
    const selectedTestCenter = this.dataSource?.data?.find(
      (testCenter) => testCenter?._id === testCenterId
    )

    if (selectedTestCenter) {
      selectedTestCenter.students = students

      const allTcStudents = this.testCenters.reduce(
        (acc, testCenter) => {
          const maleCount = testCenter.students.filter((student) => student.gender === 'M').length
          const femaleCount = testCenter.students.filter((student) => student.gender === 'F').length
          return {
            maleCount: acc.maleCount + maleCount,
            femaleCount: acc.femaleCount + femaleCount
          }
        },
        { maleCount: 0, femaleCount: 0 }
      )

      this.updateCourseStudentsCount.emit({ allTcStudents, courseDetailId: this.courseData?._id })
    }
  }
}
