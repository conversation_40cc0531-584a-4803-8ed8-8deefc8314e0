import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core'
import { MatDialog } from '@angular/material/dialog'

import { TranslateService } from '@ngx-translate/core'

import { ChangeTestCenterComponent } from '@appcore/app/core/components/shared-component'
import { EGender, EPermissionType, ROUTES } from '@appcore/app/core/constants'
import { ITestCenterCapacity } from '@appcore/app/models/exam-center-management'
import { ExamScheduleService } from '@appcore/app/pages/exams/exam-schedule.service'
import { AuthService, ErrorHandlerService, GlobalService, UtilService } from '@appcore/app/services'
import { SettingsService } from '@appcore/app/services/settings.service'
import {
  IExamReadiness,
  IExamReadinessCourse,
  IExamReadinessStudent,
  IExamReadinessStudentHeaderData,
  IExportDeatails
} from '@appcore/models/exam-readiness/exam-readiness-on-going.interface'
import { ConfirmDialogComponent } from '@appcore/shared/confirm-dialogbox'
import { DigiExportComponent } from '@appcore/shared/digi-export/digi-export.component'
import { IScheduleExamTimeFormat } from '../../../../schedule-exam-detail/schedule-exam-detail.interface'

@Component({
  selector: 'digi-student-exam-table',
  templateUrl: './student-exam-table.component.html',
  styleUrls: ['./student-exam-table.component.css']
})
export class StudentExamTableComponent implements OnInit, OnChanges {
  @Input()
  testCenterStudentData: IExamReadinessStudent[]

  @Input()
  programDetails: IExamReadiness

  @Input()
  courseData: IExamReadinessCourse

  @Input()
  testcenterdetails: IExamReadinessCourse

  @Input()
  yearAndLevelData: IExamReadiness

  @Input()
  tabName: string

  @Input()
  isPreviousExam = false

  @Input()
  examType: string

  @Output()
  changeStudentTc: EventEmitter<boolean> = new EventEmitter()

  headerData: IExamReadinessStudentHeaderData[] = []
  exportData: IExportDeatails

  removedStudentsCount = 0
  examLocation: string
  hasOverAllActionsPermission = false
  hasIndividualActionsPermission = false
  canAllocateSeats = false

  constructor(
    public dialog: MatDialog,
    private globalService: GlobalService,
    private examScheduleService: ExamScheduleService,
    private errorHandler: ErrorHandlerService,
    private settingsService: SettingsService,
    private translate: TranslateService,
    private authService: AuthService,
    private utilService: UtilService
  ) {}

  ngOnInit() {
    this.headerData = this.getHeaderData()
    this.examLocation = this.examType
    this.checkPermission()
    this.loadCanAllocateSeats()
  }

  private loadCanAllocateSeats(): void {
    this.examScheduleService.getCanAllocateSeats().subscribe({
      next: ({ data }) => {
        this.canAllocateSeats = data
        this.headerData = this.getHeaderData()
      },
      error: (err) => this.errorHandler.errorLog(err)
    })
  }

  ngOnChanges(changes: SimpleChanges) {
    const { testCenterStudentData } = changes

    if (testCenterStudentData?.currentValue) {
      testCenterStudentData?.currentValue.sort((prev, curr) => {
        return prev.academicNo.localeCompare(curr.academicNo)
      })
    }
  }

  get hasDuplicateStudents() {
    return this.testCenterStudentData.some((student) => student.isDuplicateStudentInSameSession)
  }

  private checkPermission() {
    this.hasOverAllActionsPermission = this.authService.checkHasPermission({
      permissionType: EPermissionType.ACTION,
      moduleName: ROUTES.MODULE_EXAM,
      pageName: ROUTES.PAGE_EXAM_DASHBOARD,
      tabName: ROUTES.TAB_EXAM_ONGOING_EXAM,
      subTabName: ROUTES.SUB_TAB_ALL_COURSE_GROUP,
      actionNames: [ROUTES.ACTION_REMOVE_STUDENT, ROUTES.ACTION_EXPORT_STUDENT]
    })

    this.hasIndividualActionsPermission = this.authService.checkHasPermission({
      permissionType: EPermissionType.ACTION,
      moduleName: ROUTES.MODULE_EXAM,
      pageName: ROUTES.PAGE_EXAM_DASHBOARD,
      tabName: ROUTES.TAB_EXAM_ONGOING_EXAM,
      subTabName: ROUTES.SUB_TAB_ALL_COURSE_GROUP,
      actionNames: [ROUTES.ACTION_REMOVE_STUDENT, ROUTES.ACTION_CHANGE_TC]
    })
  }

  private getHeaderData() {
    const header = {
      serialNumber: '',
      academicNumber: 'common.academicNo',
      firstName: 'common.firstName',
      middleName: 'common.middleName',
      lastName: 'common.lastName',
      icon: ''
    }

    if (this.canAllocateSeats) {
      Object.assign(header, {
        seatNo: 'dashboard.exams.seatNo'
      })
    }

    return [header]
  }

  openStudents(): void {
    const startTime =
      this.utilService.formatCustomTime({
        time: this.courseData?.session?.start as unknown as IScheduleExamTimeFormat
      }) || ''
    const slotEndTime =
      this.utilService.formatCustomTime({
        time: this.courseData?.session?.slotEnd as unknown as IScheduleExamTimeFormat
      }) || ''

    const endTime =
      this.utilService.formatCustomTime({
        time: this.courseData?.session?.end as unknown as IScheduleExamTimeFormat
      }) || ''

    Object.assign(this.courseData.session, {
      startEndTime: `${startTime} - ${slotEndTime || endTime}`
    })

    const exportData = {
      testCenterStudentData: this.testCenterStudentData,
      courseData: this.courseData,
      programDetails: this.programDetails,
      testcenterdetails: this.testcenterdetails,
      title: 'common.studentListPreview',
      type: 'student',
      proctoringType: this.examLocation,
      canAllocateSeats: this.canAllocateSeats
    }
    this.exportData = exportData
    this.dialog.open(DigiExportComponent, {
      maxWidth: '90%',
      maxHeight: '90%',
      width: '100%',
      panelClass: ['full-screen-modal', 'dialog-fix'],
      data: this.exportData,
      disableClose: true,
      direction: this.settingsService?.getOptions()?.dir
    })
  }

  onClickRemoveStudent({ student }: { student?: IExamReadinessStudent }) {
    this.dialog
      .open(ConfirmDialogComponent, {
        data: {
          title: 'exams.examsManagement.areYouSure',
          message: this.translate.instant('exams.examsManagement.areYouSureMessage', {
            testCenterName: this.testcenterdetails?.name
          }),
          actionBtn: 'common.yes'
        },
        panelClass: 'dialog-fix',
        disableClose: true,
        direction: this.settingsService.getOptions().dir
      })
      .afterClosed()
      .subscribe((result: boolean) => {
        if (!result) {
          return
        }

        this.removeStudent({ student })
      })
  }

  onClickChangeStudentTestCenter({ student }: { student?: IExamReadinessStudent }) {
    this.examScheduleService
      .getTestCenterAvailability({
        payload: {
          examCourseId: this.courseData?._id,
          startTime: this.courseData?.session.start,
          endTime: this.courseData?.session.end,
          date: this.courseData?.date,
          canMoveStudent: true,
          gender: this.globalService.getGenderChar({ gender: student?.gender }),
          testCenterId: this.testcenterdetails?._testCenter,
          isUploadStudent: false
        }
      })
      .subscribe(
        ({ data }) => {
          this.openChangeTestCenterDialog({ student, testCenters: data.testCenter })
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  private openChangeTestCenterDialog({
    student,
    testCenters
  }: {
    student: IExamReadinessStudent
    testCenters: ITestCenterCapacity[]
  }) {
    this.dialog
      .open(ChangeTestCenterComponent, {
        data: {
          title: 'exams.examsManagement.testTable.changeTestCenter',
          message: 'exams.examsManagement.testTable.selectedStudentsTC',
          studentId: student._id,
          examCourseGroupId: this.courseData?._id,
          currentTestCenterId: this.testcenterdetails._testCenter,
          testCenters
        },
        panelClass: 'dialog-fix',
        disableClose: true,
        direction: this.settingsService?.getOptions()?.dir
      })
      .afterClosed()
      .subscribe((result: boolean) => {
        if (!result) {
          return
        }

        this.changeStudentTc.emit(true)
      })
  }

  private removeStudent({ student }: { student: IExamReadinessStudent }) {
    this.examScheduleService
      .removeStudent({
        id: this.courseData?._id,
        testCenterId: this.testcenterdetails?._id,
        payload: {
          students: student ? [student?.mobile] : this.payload
        }
      })
      .subscribe(
        ({ message }) => {
          this.removedStudentsCount = this.selectedStudents?.length
          this.updateCount({ student })
          this.globalService.showSuccess(message)

          if (!this.testCenterStudentData.length) {
            this.changeStudentTc.emit(true)
          }
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  get selectedStudents(): IExamReadinessStudent[] {
    return this.testCenterStudentData.reduce((acc: IExamReadinessStudent[], student) => {
      if (student?.isSelected) {
        acc.push(student)
      }

      return acc
    }, [])
  }

  get payload(): string[] {
    return this.testCenterStudentData.reduce((acc: string[], student) => {
      if (student?.isSelected) {
        acc.push(student?.mobile)
      }

      return acc
    }, [])
  }

  onChangeSelectStudent({ student, isChecked }: { student?: IExamReadinessStudent; isChecked }) {
    student
      ? (student.isSelected = !student.isSelected)
      : this.testCenterStudentData.forEach(
          (testCenterStudentData) => (testCenterStudentData.isSelected = isChecked)
        )
  }

  get isAllStudentsSelected() {
    return this.testCenterStudentData.every(({ isSelected }) => isSelected)
  }

  get isSomeStudentsSelected() {
    return this.testCenterStudentData.some(({ isSelected }) => isSelected)
  }

  private updateCount({ student }: { student: IExamReadinessStudent }) {
    const removedLength = student ? 1 : this.removedStudentsCount
    const maleCount = 0
    const femaleCount = 0
    const selectedGender = this.globalService
      .getGender(this.testcenterdetails.gender, false)
      ?.toLowerCase()
    this.programDetails.student.allocatedCount -= removedLength
    this.yearAndLevelData.student.allocatedCount -= removedLength

    switch (selectedGender) {
      case 'male':
      case 'female':
        this.courseData.students[selectedGender].scheduledCount -= removedLength

        break

      case 'common':
        if (['M', 'F'].includes(this.testcenterdetails.commonActAs)) {
          this.courseData.students[
            this.utilService
              .getTranslatedGenderCode({
                genderCode: this.testcenterdetails.commonActAs as EGender
              })
              .name.toLowerCase()
          ].scheduledCount -= removedLength
        }

        if (this.testcenterdetails.commonActAs === 'MIXED') {
          this.updateMixedStudentsCount({ student, maleCount, femaleCount })
        }

        break

      case 'mixed':
        this.updateMixedStudentsCount({ student, maleCount, femaleCount })

        break
    }

    this.removeStudentFromTable({ student })
  }

  private updateMixedStudentsCount({
    student,
    maleCount,
    femaleCount
  }: {
    student: IExamReadinessStudent
    maleCount: number
    femaleCount: number
  }) {
    maleCount =
      student?.gender === 'M'
        ? 1
        : this.selectedStudents.filter((selectedStudent) => selectedStudent.gender === 'M')?.length
    femaleCount =
      student?.gender === 'F'
        ? 1
        : this.selectedStudents.filter((selectedStudent) => selectedStudent.gender === 'F')?.length

    if (maleCount) {
      this.courseData.students.male.scheduledCount -= maleCount
    }

    if (femaleCount) {
      this.courseData.students.female.scheduledCount -= femaleCount
    }
  }

  private removeStudentFromTable({ student }: { student: IExamReadinessStudent }) {
    if (student) {
      const removeIndex = this.testCenterStudentData.findIndex(({ _id }) => _id === student?._id)
      this.testCenterStudentData.splice(removeIndex, 1)
    } else {
      this.selectedStudents.forEach(({ _id }) => {
        const removeIndex = this.testCenterStudentData.findIndex(
          ({ _id: studentId }) => _id === studentId
        )
        this.testCenterStudentData.splice(removeIndex, 1)
      })
    }
  }
}
