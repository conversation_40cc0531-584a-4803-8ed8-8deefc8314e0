import { Component, Inject, OnInit } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog'

import { TranslateService } from '@ngx-translate/core'
import { cloneDeep } from 'lodash'

import { ConfirmDialogComponent } from '@appcore/app/core/components/confirm-dialogbox/confirm.component'
import { EGender } from '@appcore/app/core/constants'
import { AutoAssignTestCenterType } from '@appcore/app/core/constants'
import {
  IAssignManualEvaluatorPopup,
  IAutoAssignedStudentsPayload,
  IEvaluatorsFullName,
  IPagination,
  IStudentWithGroupPayload,
  IUnAssignCount,
  IUnAssignedPayload
} from '@appcore/app/models'
import {
  IAlreadyScheduledTCs,
  IAutoAssignTcPopup,
  IInstitutionCalendar,
  IPlannedStudent,
  ITotalCount,
  IUploadError,
  IUploadStudentList,
  IUploadStudents,
  IUploadStudentTestCenter,
  IUploadTestCenter
} from '@appcore/app/models/exam-readiness/exam-readiness-on-going.interface'
import { IndependentAssessmentService } from '@appcore/app/pages/assessment'
import { ExamScheduleService } from '@appcore/app/pages/exams/exam-schedule.service'
import {
  ErrorHandlerService,
  GlobalService,
  SettingsService,
  UtilService
} from '@appcore/app/services'

import { PlannedCountExistComponent } from '../planned-count-exist-popup'
import { TcAssignErrorPopupComponent } from '../tc-assign-error-popup'
import { ToastrService } from 'ngx-toastr'

@Component({
  selector: 'digi-auto-assign-tc-popup',
  templateUrl: './auto-assign-tc-popup.component.html',
  styleUrls: ['./auto-assign-tc-popup.component.css']
})
export class AutoAssignTcPopupComponent implements OnInit {
  importedStudents: IUploadStudents[] = []
  selectedImportedStudents: IUploadStudents[] = []
  plannedStudentsCount: IPlannedStudent
  importedStudentsTotalCount: ITotalCount
  testCenters: IUploadStudentTestCenter[] = []
  pagination: IPagination = { currentPage: 1, perPage: 10, totalPages: 1 }
  selectedGender = EGender.M
  search = ''
  selectedTc: IUploadStudentTestCenter
  selectedScheduledStudents: IUploadStudents[] = []
  errorType = ''
  initUploadStudents: string[] = []
  isAutoAssignTc = false
  scheduledPagination: IPagination = { currentPage: 1, perPage: 10, totalPages: 1 }
  perPages = [10, 25, 50, 75, 100]
  scheduledGender: string
  importStudentSerialNumbers: number[] = []
  scheduledStudentSerialNumbers: number[] = []
  testCenterData: IAutoAssignTcPopup
  studentGroupData: IAssignManualEvaluatorPopup
  studentsTotalCount: IUnAssignCount = {
    all: 0,
    male: 0,
    female: 0
  }
  genderCountPlanned: IUnAssignCount
  genderCountUnplanned: IUnAssignCount
  selectedEvaluator: IEvaluatorsFullName
  assignedStudents: IUploadStudents[] = []
  assignedGender = EGender.M
  institutionCalendar: IInstitutionCalendar
  readonly autoAssignTestCenterType = AutoAssignTestCenterType

  constructor(
    private dialogRef: MatDialogRef<AutoAssignTcPopupComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: IAutoAssignTcPopup | IAssignManualEvaluatorPopup,
    private examScheduleService: ExamScheduleService,
    private errorHandlerService: ErrorHandlerService,
    private utilService: UtilService,
    private dialog: MatDialog,
    private settingsService: SettingsService,
    private translateService: TranslateService,
    private globalService: GlobalService,
    private independentAssessmentService: IndependentAssessmentService,
    private toastService: ToastrService
  ) {}

  ngOnInit() {
    this.testCenterData = this.data as IAutoAssignTcPopup

    switch (this.data?.type) {
      case AutoAssignTestCenterType.SCHEDULE:
        this.initTestCenter()
        break

      case AutoAssignTestCenterType.STUDENT_GROUPING:
        this.initStudentGrouping()
        break

      case AutoAssignTestCenterType.SCHEDULED_STUDENT_GROUPING:
        this.getStudentWithGrouping({ isInit: true })
        break

      case AutoAssignTestCenterType.INDEPENDENT_ASSESSMENT:
        this.getStudentsForIndependentAssessment({ isInit: true })
        break
    }
  }

  private getStudentWithGrouping({
    isInit = false,
    isRefreshStudentList = false
  }: {
    isInit?: boolean
    isRefreshStudentList?: boolean
  }) {
    const payload: Partial<IStudentWithGroupPayload> = {
      gender: this.selectedGender,
      examCourseId: this.testCenterData.course._id,
      pageNo: this.pagination?.currentPage,
      limit: this.pagination?.perPage,
      search: this.search,
      isInit,
      academicNos: isRefreshStudentList ? [] : this.testCenterData.academicNos || [],
      isRefreshStudentList // need to remove true after testing
    }

    this.examScheduleService.getStudentsWithGroup({ payload }).subscribe(
      ({ data, message }) => {
        if (!data.academicNos?.length && isInit) {
          this.dialog.closeAll()

          return this.globalService.showError(
            this.translateService.instant('exams.studentGrouping.noStudentFound')
          )
        }

        if (isInit) {
          if (data?.inActiveStudents?.length) {
            this.showInActiveStudentsPopUp({ students: data.inActiveStudents })
              .afterClosed()
              .subscribe((res) => {
                if (res) {
                  this.testCenterData.academicNos = data.academicNos
                  this.initUploadStudents = [...this.testCenterData.academicNos]
                  this.institutionCalendar = data.institutionCalendar
                  data.inActiveStudents = []
                  this.formatStudentAndTcDetails({ data, isInit })

                  return
                }

                const inActiveAcademicNos = data.inActiveStudents.map(
                  (student) => student.academicNo
                )
                this.testCenterData.academicNos = data.academicNos.filter(
                  (academicNo) => !inActiveAcademicNos.includes(academicNo)
                )
                this.initUploadStudents = [...this.testCenterData.academicNos]

                if (!this.initUploadStudents.length) {
                  this.dialogRef.close()
                  this.globalService.showError(
                    this.translateService.instant('exams.studentGrouping.noStudentFound')
                  )

                  return
                }

                this.getStudentWithGrouping({})
              })

            this.institutionCalendar = data.institutionCalendar
            this.formatStudentAndTcDetails({ data, isInit })

            return
          }

          this.testCenterData.academicNos = data.academicNos
          this.initUploadStudents = [...this.testCenterData.academicNos]
        }

        this.institutionCalendar = data.institutionCalendar
        this.formatStudentAndTcDetails({ data, isInit: true })

        if (isRefreshStudentList) {
          this.toastService.success(message)
        }
      },
      (err) => this.errorHandlerService.errorLog(err)
    )
  }

  private showInActiveStudentsPopUp({ students }: { students: IUploadStudents[] }) {
    return this.dialog.open(TcAssignErrorPopupComponent, {
      panelClass: 'dialog-fix',
      maxWidth: '40vw',
      maxHeight: '90vh',
      direction: this.settingsService.getOptions().dir,
      disableClose: true,
      data: {
        type: 'inactiveError',
        message: this.translateService.instant('exams.uploadStudentError.inactiveStudentMessage'),
        students
      }
    })
  }

  private initStudentGrouping() {
    this.studentGroupData = this.data as IAssignManualEvaluatorPopup
    this.selectedEvaluator = this.studentGroupData?.assignEvaluators[0]
    this.getStudentsCount()
    this.getUnassignedStudents()
    this.getAssignedStudents()
  }

  private getStudentsCount() {
    this.examScheduleService
      .getStudentsCount({
        groupId: this.studentGroupData?.group?._id,
        studentGroupId: this.studentGroupData?.studentGroupId
      })
      .subscribe(
        ({ data }) => {
          this.studentsTotalCount = data?.totalCount
        },
        (err) => this.errorHandlerService.errorLog(err)
      )
  }

  private getUnassignedStudents() {
    const params: IUnAssignedPayload = {
      groupId: this.studentGroupData?.group?._id,
      studentGroupId: this.studentGroupData?.studentGroupId,
      search: this.search,
      gender: this.selectedGender,
      pageNo: this.pagination?.currentPage,
      limit: this.pagination?.perPage
    }

    this.examScheduleService.getUnassignedStudents({ params }).subscribe(
      ({ data }) => {
        this.importedStudents = data?.data
        this.genderCountPlanned = data?.unAssignCount
        this.setPagination({
          pagination: {
            currentPage: data?.currentPage,
            totalPages: data?.totalPages
          }
        })
        this.setImportedSerialnumber()
      },
      (err) => this.errorHandlerService.errorLog(err)
    )
  }

  private getAssignedStudents() {
    const params: IUnAssignedPayload = {
      groupId: this.studentGroupData?.group?._id,
      studentGroupId: this.studentGroupData?.studentGroupId,
      evaluatorId: this.selectedEvaluator?._id,
      search: this.search,
      gender: this.assignedGender,
      pageNo: this.scheduledPagination?.currentPage,
      limit: this.scheduledPagination?.perPage
    }

    this.examScheduleService.getAssignedStudents({ params }).subscribe(
      ({ data }) => {
        this.assignedStudents = data?.data
        this.genderCountUnplanned = data?.totalCount
        this.setSchedulePagination({
          pagination: {
            currentPage: data?.currentPage,
            totalPages: data?.totalPages
          }
        })
      },
      (err) => this.errorHandlerService.errorLog(err)
    )
  }

  private initTestCenter() {
    this.initUploadStudents = [...this.testCenterData.academicNos]
    this.studentList({ isInit: true })
  }

  private studentList({ isInit = false }: { isInit?: boolean }) {
    const { course, academicNos, fileName } = this.testCenterData
    const payload = {
      courseId: course._id,
      academicNos,
      fileName,
      limit: this.pagination.perPage,
      pageNo: this.pagination.currentPage,
      gender: this.selectedGender,
      search: this.search
    }
    this.examScheduleService.getStudentByTcDetails({ payload }).subscribe(
      ({ data }) => {
        this.formatStudentAndTcDetails({ data, isInit })
      },
      (err) => {
        this.errorHandlerService.errorLog(err)
        this.importedStudents = []
      }
    )
  }

  private formatStudentAndTcDetails({
    data,
    isInit
  }: {
    data: IUploadStudentList
    isInit: boolean
  }) {
    if (data?.status === false) {
      this.dialogRef.close()
      this.openStudentErrorPopUp({ errorStudents: data?.students })

      return
    }

    this.importedStudents = data?.students
    if (!this.testCenters.length) {
      this.testCenters = data?.testCenters
      this.plannedStudentsCount = data?.count?.planned
      Object.assign(this.plannedStudentsCount, {
        male: { ...this.plannedStudentsCount.male, remaining: 0 },
        female: { ...this.plannedStudentsCount.female, remaining: 0 }
      })

      this.plannedStudentsCount.male.initUpload = cloneDeep(this.plannedStudentsCount.male.uploaded)
      this.plannedStudentsCount.female.initUpload = cloneDeep(
        this.plannedStudentsCount.female.uploaded
      )
      this.formatTc()
    }

    this.importedStudentsTotalCount = data?.count?.totalCount
    this.setPagination({ pagination: data?.count?.pagination })
    this.calculateScheduledStudentsCount()
    this.checkIsTestCenterCountExist({ totalCount: cloneDeep(data.count.totalCount) })

    if (
      isInit &&
      this.checkIsTestCenterCountExist({ totalCount: data.count.totalCount }) &&
      !data?.inActiveStudents?.length
    ) {
      this.openPlanCountExistPopup()
    }
  }

  private checkIsTestCenterCountExist({ totalCount }: { totalCount: ITotalCount }) {
    let isCountExist = false
    const { male = 0, female = 0 } = totalCount
    const maleTcCount = this.filterTestCenterGender({ gender: 'M' })
    const femaleTcCount = this.filterTestCenterGender({ gender: 'F' })
    let mixedTcCount = this.filterTestCenterGender({ gender: 'MIXED' })

    if (maleTcCount < male) {
      const pendingSeats = male - maleTcCount

      const pendingMixedTcCount = this.mixedTcCount({
        mixedTcCount,
        pendingSeats,
        gender: 'male',
        tcCount: maleTcCount,
        totalCount: male
      })

      mixedTcCount = pendingMixedTcCount.mixedTcCount

      if (pendingMixedTcCount.isExist) {
        isCountExist = true
      }
    }

    if (femaleTcCount < female) {
      const pendingSeats = female - femaleTcCount

      const pendingMixedTcCount = this.mixedTcCount({
        mixedTcCount,
        pendingSeats,
        gender: 'female',
        tcCount: femaleTcCount,
        totalCount: female
      })

      mixedTcCount = pendingMixedTcCount.mixedTcCount

      if (pendingMixedTcCount.isExist) {
        isCountExist = true
      }
    }

    return isCountExist
  }

  private mixedTcCount({
    mixedTcCount,
    pendingSeats,
    gender,
    tcCount,
    totalCount
  }: {
    mixedTcCount: number
    pendingSeats: number
    gender: string
    tcCount: number
    totalCount: number
  }) {
    if (mixedTcCount >= pendingSeats) {
      mixedTcCount -= pendingSeats

      return { mixedTcCount, isExist: false }
    } else {
      this.plannedStudentsCount[gender].remaining = totalCount - (tcCount + mixedTcCount)
      mixedTcCount = 0

      return { mixedTcCount, isExist: true }
    }
  }

  private filterTestCenterGender({ gender }: { gender: string }) {
    return this.testCenters
      ?.filter((testCenter) => testCenter.gender === gender)
      ?.reduce((acc, tc) => acc + (tc.allocatedSeats - tc.uploaded), 0)
  }

  private openStudentErrorPopUp({ errorStudents }: { errorStudents: IUploadStudents[] }) {
    return this.dialog
      .open(TcAssignErrorPopupComponent, {
        width: '30vw',
        panelClass: 'dialog-fix',
        direction: this.settingsService.getOptions().dir,
        disableClose: true,
        data: {
          type: 'errorStudent',
          errorStudents,
          isInvalidStudents: !errorStudents?.some(({ isInvalid = false }) => !isInvalid),
          course: this.testCenterData?.course,
          fileName:
            this.testCenterData?.fileName ||
            this.translateService.instant('exams.uploadPermittedStudents.errorStudentList')
        }
      })
      .afterClosed()
      .subscribe((isProceed: boolean) => {
        if (!isProceed) {
          return
        }

        this.verifyUploadStudent({ canSkipInvalidStudents: true })
      })
  }

  private formatTc() {
    this.testCenters.forEach((testCenter) => {
      testCenter.students = []
      if (testCenter.gender === EGender.COMMON) {
        testCenter.actAsLabel = this.translateService.instant('exams.assignDateTimePicker.actAs', {
          gender:
            testCenter.commonActAs.toLowerCase() === EGender.M.toLowerCase()
              ? this.translateService.instant('common.male')
              : testCenter.commonActAs.toLowerCase() === EGender.F.toLowerCase()
                ? this.translateService.instant('common.female')
                : this.translateService.instant('common.mixed')
        })
      }
      testCenter.isNewTc = testCenter.isNewTc ?? false
      testCenter.count = { male: 0, female: 0 }
      testCenter.initTcUploadCount = cloneDeep(testCenter.uploaded)
      if (!testCenter?.originalSeat) {
        testCenter.originalSeat = cloneDeep(
          testCenter.allocatedSeats - testCenter.initTcUploadCount
        )
      }
    })
  }

  private openPlanCountExistPopup() {
    this.dialog
      .open(PlannedCountExistComponent, {
        data: {
          dataType: this.data?.type,
          errorType: 'countExisting',
          course: this.testCenterData.course,
          plannedStudentsCount: this.plannedStudentsCount,
          importedStudentsTotalCount: this.importedStudentsTotalCount,
          gender: this.selectedGender,
          examType: this.testCenterData.examType
        },
        panelClass: 'dialog-fix',
        direction: this.settingsService.getOptions().dir
      })
      .afterClosed()
      .subscribe((result) => {
        if (!result) {
          return this.dialog.closeAll()
        }

        this.calculateNewTcUseableCapacity({ testCenters: result })

        const selectedCountExistTc = result
        selectedCountExistTc.forEach((tc) => {
          const isTcExist = this.testCenters.find((testCenter) => testCenter._testCenter === tc._id)
          if (isTcExist) {
            isTcExist.allocatedSeats = isTcExist.allocatedSeats + tc.usableCapacity
            isTcExist.isTcExist = true
            isTcExist.newMaleCount = tc?.newMaleCount ?? 0
            isTcExist.newFemaleCount = tc?.newFemaleCount ?? 0
          } else {
            this.testCenters.push({
              allocatedSeats: tc.usableCapacity,
              gender: tc.gender,
              name: tc.name,
              uploaded: 0,
              _testCenter: tc._id,
              ...(tc.commonActAs && { commonActAs: tc.commonActAs.gender }),
              isDisabled: false,
              students: [],
              isNewTc: true,
              count: { male: 0, female: 0 },
              initTcUploadCount: 0,
              newMaleCount: tc?.newMaleCount ?? 0,
              newFemaleCount: tc?.newFemaleCount ?? 0
            })
          }
          this.formatTc()
        })
        this.globalService.showSuccess(
          this.translateService.instant('exams.uploadStudentError.testCenterAddedSuccessfully'),
          ''
        )

        this.testCenters = [...this.testCenters]
      })
  }

  private calculateNewTcUseableCapacity({ testCenters }: { testCenters: IUploadTestCenter[] }) {
    const { male, female } = this.plannedStudentsCount
    testCenters.forEach((testCenter) => {
      const gender = this.globalService.getGender(testCenter.gender, false)
      if (['male', 'female'].includes(gender)) {
        if (testCenter.usableCapacity < this.plannedStudentsCount[gender].remaining) {
          this.plannedStudentsCount[gender].remaining -= testCenter.usableCapacity

          return
        }

        testCenter.usableCapacity = this.plannedStudentsCount[gender].remaining
        this.plannedStudentsCount[gender].remaining = 0
      }

      if (gender === 'mixed') {
        const totalRemaining = male.remaining + female.remaining
        if (testCenter.usableCapacity < totalRemaining) {
          if (testCenter.usableCapacity < male.remaining) {
            male.remaining -= testCenter.usableCapacity
            testCenter.newMaleCount = male.remaining

            return
          }

          if (testCenter.usableCapacity < female.remaining) {
            female.remaining -= testCenter.usableCapacity
            testCenter.newFemaleCount = female.remaining

            return
          }
        }

        testCenter.newMaleCount = male.remaining
        testCenter.newFemaleCount = female.remaining
        testCenter.usableCapacity = totalRemaining
      }
    })
  }

  private calculateScheduledStudentsCount() {
    this.plannedStudentsCount.male.uploaded = 0
    this.plannedStudentsCount.female.uploaded = 0
    this.testCenters?.forEach((testCenter) => {
      this.plannedStudentsCount.male.uploaded +=
        testCenter?.students.filter((student) => student.gender === EGender.M).length || 0
      this.plannedStudentsCount.female.uploaded +=
        testCenter?.students.filter((student) => student.gender === EGender.F).length || 0
    })

    this.plannedStudentsCount.male.uploaded += this.plannedStudentsCount.male.initUpload
    this.plannedStudentsCount.female.uploaded += this.plannedStudentsCount.female.initUpload
  }

  private setPagination({ pagination }: { pagination: IPagination }) {
    this.pagination = {
      currentPage: pagination?.currentPage,
      totalPages: pagination?.totalPages,
      perPage: this.pagination?.perPage
    }

    this.setImportedSerialnumber()
  }

  private setSchedulePagination({ pagination }: { pagination: IPagination }) {
    if (this.data?.type === AutoAssignTestCenterType.STUDENT_GROUPING) {
      this.scheduledPagination = {
        currentPage: pagination?.currentPage,
        totalPages: pagination?.totalPages,
        perPage: this.scheduledPagination?.perPage
      }

      this.setScheduledSerialnumber()
    }
  }

  onChangeSelectedStudents({
    students,
    isPlanned
  }: {
    students: IUploadStudents[]
    isPlanned: boolean
  }) {
    this.scheduledGender = ''
    if (isPlanned) {
      return (this.selectedImportedStudents = students)
    }

    this.selectedScheduledStudents = students
  }

  onClickMoveStudent({ isPlanned }: { isPlanned: boolean }) {
    ;[
      AutoAssignTestCenterType.SCHEDULE,
      AutoAssignTestCenterType.SCHEDULED_STUDENT_GROUPING,
      AutoAssignTestCenterType.INDEPENDENT_ASSESSMENT
    ].includes(this.data?.type as AutoAssignTestCenterType)
      ? this.moveStudentsToTC({ isPlanned })
      : this.assignEvaluators({ isAutoAssign: false })
  }

  private moveStudentsToTC({ isPlanned }: { isPlanned: boolean }) {
    if (!this.selectedTc) {
      return
    }

    const {
      count: { male = 0, female = 0 },
      gender,
      commonActAs,
      allocatedSeats,
      uploaded,
      students
    } = this.selectedTc

    if (
      (this.selectedTc.gender === EGender.MIXED || this.selectedTc.commonActAs === EGender.MIXED) &&
      isPlanned
    ) {
      if (
        (this.selectedGender === EGender.M && !male) ||
        (this.selectedGender === EGender.F && !female)
      ) {
        return this.openErrorPopup({ type: 'genderMismatch' })
      }

      if (this.selectedGender === EGender.M && isPlanned) {
        const maleStudent = students.filter((student) => student.gender === EGender.M).length
        if (male === maleStudent) {
          return this.openErrorPopup({ type: 'testCenterExist' })
        }

        if (male < maleStudent + this.selectedImportedStudents.length) {
          return this.openErrorPopup({ type: 'testCenterLimitExceed', tcCount: male })
        }
      }

      if (this.selectedGender === EGender.F && isPlanned) {
        const femaleStudent = students.filter((student) => student.gender === EGender.F).length
        if (female === femaleStudent) {
          return this.openErrorPopup({ type: 'testCenterExist' })
        }

        if (female < femaleStudent + this.selectedImportedStudents.length) {
          return this.openErrorPopup({ type: 'testCenterLimitExceed', tcCount: female })
        }
      }
    }

    if (
      (gender === EGender.COMMON ? commonActAs : gender) !== this.selectedGender &&
      gender !== EGender.MIXED &&
      commonActAs !== EGender.MIXED &&
      isPlanned
    ) {
      return this.openErrorPopup({ type: 'genderMismatch' })
    }

    if (allocatedSeats === uploaded && isPlanned) {
      return this.openErrorPopup({ type: 'testCenterExist' })
    }

    if (allocatedSeats < uploaded + this.selectedImportedStudents.length && isPlanned) {
      return this.openErrorPopup({ type: 'testCenterLimitExceed', tcCount: allocatedSeats })
    }

    isPlanned
      ? this.moveImportStudentToScheduleStudent()
      : this.moveScheduleStudentToImportStudent()
  }

  private moveImportStudentToScheduleStudent() {
    if (!this.selectedImportedStudents.length) {
      return
    }

    this.importedStudents = this.filterStudentInList({
      filterStudents: this.importedStudents,
      selectedStudents: this.selectedImportedStudents
    })

    this.reArrangeStudent({
      moveType: 'importedStudent',
      selectedStudents: this.selectedImportedStudents
    })
    this.scheduledGender = cloneDeep(this.selectedGender)
  }

  private moveScheduleStudentToImportStudent() {
    if (!this.selectedScheduledStudents.length) {
      return
    }

    this.importedStudents = [...this.importedStudents, ...this.selectedScheduledStudents]
    this.selectedTc.students = this.filterStudentInList({
      filterStudents: this.selectedTc.students,
      selectedStudents: this.selectedScheduledStudents
    })

    this.reArrangeStudent({
      moveType: 'scheduledStudent',
      selectedStudents: this.selectedScheduledStudents
    })
  }

  private filterStudentInList({
    filterStudents,
    selectedStudents
  }: {
    filterStudents: IUploadStudents[]
    selectedStudents: IUploadStudents[]
  }) {
    return filterStudents.filter((student) => !selectedStudents.includes(student))
  }

  private reArrangeStudent({
    moveType,
    selectedStudents
  }: {
    moveType: string
    selectedStudents: IUploadStudents[]
  }) {
    let count = 0
    selectedStudents.forEach((student) => {
      if (moveType === 'importedStudent') {
        this.selectedTc.students.push(student)
        const index = this.testCenterData.academicNos.findIndex(
          (academicNo) => academicNo === student.academicNo
        )
        this.testCenterData.academicNos.splice(index, 1)
        count = -1
        this.setImportedSerialnumber()
      } else {
        this.testCenterData.academicNos.unshift(student.academicNo)
        count = 1
        this.setScheduledSerialnumber()
      }

      const genderCode = (
        student?.gender?.toLowerCase() === 'male'
          ? 'M'
          : student?.gender?.toLowerCase() === 'female'
            ? 'F'
            : student?.gender
      ) as EGender
      const gender = this.utilService.getTranslatedGenderCode({ genderCode }).name.toLowerCase()

      this.importedStudentsTotalCount[gender] = this.importedStudentsTotalCount[gender] + count
    })

    this.testCenters = [...this.testCenters]
    this.reCallStudentList({})
    this.selectedImportedStudents = []
    this.selectedScheduledStudents = []
  }

  private openErrorPopup({
    type,
    students,
    message,
    tcCount
  }: {
    type: string
    students?: IAlreadyScheduledTCs[]
    message?: string
    tcCount?: number
  }) {
    return this.dialog.open(TcAssignErrorPopupComponent, {
      width: type === 'sameTCs' ? '40vw' : '30vw',
      panelClass: 'dialog-fix',
      direction: this.settingsService.getOptions().dir,
      disableClose: true,
      data: {
        type,
        testCenter: this.selectedTc,
        gender: this.selectedGender,
        importedStudentsTotalCount: this.importedStudentsTotalCount,
        students,
        message,
        tcCount
      }
    })
  }

  onChangePagination({
    page,
    type,
    isScheduled = false
  }: {
    page: number
    type: 'perPage' | 'currentPage'
    isScheduled?: boolean
  }) {
    if (isScheduled) {
      this.selectedScheduledStudents = []
      this.scheduledPagination[type] = page
      if (type === 'perPage') {
        this.scheduledPagination.currentPage = 1
      }

      this.data?.type === AutoAssignTestCenterType.STUDENT_GROUPING
        ? this.getAssignedStudents()
        : this.setScheduledSerialnumber()

      return
    }

    this.pagination[type] = page
    if (type === 'perPage') {
      this.pagination.currentPage = 1
    }
    this.selectedImportedStudents = []
    this.reCallStudentList({})
    this.setImportedSerialnumber()
  }

  onChangeGender({ gender, isPlanned }: { gender: EGender; isPlanned: boolean }) {
    if (!isPlanned) {
      this.scheduledPagination.currentPage = 1
      this.scheduledPagination.perPage = 10
      this.selectedScheduledStudents = []
      this.assignedStudents = []

      if (this.data?.type === AutoAssignTestCenterType.STUDENT_GROUPING) {
        this.assignedGender = gender
        this.getAssignedStudents()
      }

      return
    }

    this.selectedImportedStudents = []
    this.selectedGender = gender
    this.pagination.currentPage = 1
    this.pagination.perPage = 10
    this.reCallStudentList({ isPlanned })
  }

  onChangeSearch({ text, isPlanned }: { text: string; isPlanned: boolean }) {
    this.search = text

    if (isPlanned) {
      this.pagination.currentPage = 1
      this.pagination.perPage = 10
      this.reCallStudentList({ isPlanned })
    } else {
      this.scheduledPagination.currentPage = 1
      this.scheduledPagination.perPage = 10
      this.reCallStudentList({ isPlanned })
    }
  }

  onChangeTc({ testCenter }: { testCenter: IUploadStudentTestCenter }) {
    this.selectedScheduledStudents = []
    this.selectedTc = testCenter
    this.scheduledPagination.currentPage = 1
    this.scheduledPagination.perPage = 10
    this.setScheduledSerialnumber()
  }

  onClickCancel() {
    this.dialogRef.close()
  }

  onClickDone() {
    this.isAutoAssignTc = false
    this.verifyUploadStudent()
  }

  onClickAutoAssignTc() {
    this.isAutoAssignTc = true
    const hasMixedTc = this.testCenters.some(
      (tc) => tc.gender === EGender.MIXED && tc.originalSeat !== 0
    )
    this.autoAssignConfirmation({ hasMixedTc })
      .afterClosed()
      .subscribe((result) => {
        if (!result) {
          return
        }

        this.verifyUploadStudent()
      })
  }

  private autoAssignConfirmation({ hasMixedTc }: { hasMixedTc: boolean }) {
    return this.dialog.open(TcAssignErrorPopupComponent, {
      panelClass: 'dialog-fix',
      direction: this.settingsService.getOptions().dir,
      disableClose: true,
      maxWidth: '38vw',
      data: {
        hasMixedTc,
        testCenters: this.testCenters,
        plannedStudentsCount: this.plannedStudentsCount,
        initUploadStudent: this.initUploadStudents.length,
        importedStudentsTotalCount: this.importedStudentsTotalCount
      }
    })
  }

  private verifyUploadStudent({
    canSkipInvalidStudents = false
  }: {
    canSkipInvalidStudents?: boolean
  } = {}) {
    const payload = {
      isAutoAssignTc: this.isAutoAssignTc,
      file: this.getTestCenters(),
      courseId: this.testCenterData.course._id,
      importFrom: this.data?.canUseExternalGroup ? 'DC' : 'DA',
      ...(this.data?.proctoringType && { proctoringType: this.data?.proctoringType }),
      ...(this.isAutoAssignTc && { autoAssignAcademicNos: this.initUploadStudents }),
      ...(canSkipInvalidStudents && { canSkipInvalidStudents })
    }

    this.examScheduleService
      .verifyStudentUpload({
        payload,
        isIAA: this.data?.type === AutoAssignTestCenterType.INDEPENDENT_ASSESSMENT
      })
      .subscribe(
        ({ data, message }) => {
          if (!data.status) {
            if (this.showImportStudentError({ message, data })) {
              return
            }

            this.globalService.showError(message, '')

            return
          }

          this.uploadStudent({ canSkipInvalidStudents })
        },
        (err) => this.errorHandlerService.errorLog(err)
      )
  }

  private uploadStudent({
    isStudentAlreadyScheduledInSameDateAndTime,
    canSkipInvalidStudents = false
  }: {
    isStudentAlreadyScheduledInSameDateAndTime?: boolean
    canSkipInvalidStudents?: boolean
  }) {
    const payload = {
      isAutoAssignTc: this.isAutoAssignTc,
      file: this.getTestCenters(),
      courseId: this.testCenterData.course._id,
      importFrom: this.data?.canUseExternalGroup ? 'DC' : 'DA',
      ...(this.data?.proctoringType && { proctoringType: this.data?.proctoringType }),
      ...(this.isAutoAssignTc && { autoAssignAcademicNos: this.initUploadStudents }),
      ...(isStudentAlreadyScheduledInSameDateAndTime && {
        isStudentAlreadyScheduledInSameDateAndTime
      }),
      ...(canSkipInvalidStudents && { canSkipInvalidStudents }),
      ...(this.testCenterData.isDC && { importFrom: 'DC' })
    }
    this.examScheduleService
      .uploadStudent({
        payload,
        isIAA: this.data?.type === AutoAssignTestCenterType.INDEPENDENT_ASSESSMENT
      })
      .subscribe(
        ({ data, message }) => {
          if (!data.status) {
            if (this.showImportStudentError({ message, data })) {
              return
            }

            this.globalService.showError(message, '')

            return
          }

          this.globalService.showSuccess(message, '')
          this.dialogRef.close(true)
        },
        (err) => this.errorHandlerService.errorLog(err)
      )
  }

  private showImportStudentError({ message, data }: { message: string; data: IUploadError }) {
    if (data?.alreadyScheduledTcs) {
      this.openErrorPopup({
        type: 'sameTCs',
        students: data.alreadyScheduledTcs,
        message
      })
        .afterClosed()
        .subscribe((result) => {
          if (!result) {
            return
          }

          this.uploadStudent({
            isStudentAlreadyScheduledInSameDateAndTime: true
          })
        })

      return true
    }

    if (data?.students?.length) {
      // comment below line for restrict close popup for handle invalid students
      // this.dialogRef.close()
      this.openStudentErrorPopUp({ errorStudents: data?.students })

      return true
    }

    return false
  }

  private getTestCenters() {
    const testCenters = this.testCenters.map((testCenter) => {
      if (!testCenter.isNewTc || testCenter.isTcExist) {
        return this.getTestCenter({ testCenter })
      }
    })
    const newTcs = this.testCenters.map((testCenter) => {
      if (testCenter.isNewTc || testCenter.isTcExist) {
        return this.getTestCenter({ testCenter, isNew: true })
      }
    })

    return {
      testCenters: testCenters.filter((testCenter) =>
        this.isAutoAssignTc ? testCenter !== undefined : testCenter !== undefined
      ),
      newTcs: newTcs.filter((newTc) =>
        this.isAutoAssignTc ? newTc !== undefined : newTc !== undefined && newTc?.students?.length
      ),
      name: this.testCenterData.course.name
    }
  }

  private getTestCenter({
    testCenter,
    isNew = false
  }: {
    testCenter: IUploadStudentTestCenter
    isNew?: boolean
  }) {
    const { gender, isNewTc, commonActAs, _testCenter, students, count } = testCenter

    if (this.isAutoAssignTc && gender !== EGender.MIXED && !isNewTc && !isNew) {
      return undefined
    }

    return {
      _testCenter,
      ...(!this.isAutoAssignTc && { gender }),
      ...(commonActAs && !this.isAutoAssignTc && { commonActAs }),
      ...(!this.isAutoAssignTc && {
        students: testCenter?.isTcExist
          ? this.getPayloadStudents({ testCenter, isNew })
          : students.map((student) => student.academicNo)
      }),
      ...(this.isAutoAssignTc &&
        gender === EGender.MIXED && { count: this.AutoAssignMixedTcCount({ testCenter, isNew }) })
    }
  }

  private AutoAssignMixedTcCount({
    testCenter,
    isNew
  }: {
    testCenter: IUploadStudentTestCenter
    isNew: boolean
  }) {
    const {
      isNewTc = false,
      newMaleCount = 0,
      newFemaleCount = 0,
      count,
      isTcExist = false
    } = testCenter

    if ((!isTcExist && !isNewTc) || (isNewTc && !isTcExist)) {
      return count
    }

    if (isTcExist && !isNew) {
      return { male: count.male - newMaleCount, female: count.female - newFemaleCount }
    }

    if (isTcExist && isNew) {
      return { male: newMaleCount, female: newFemaleCount }
    }
  }

  private getPayloadStudents({
    testCenter,
    isNew
  }: {
    testCenter: IUploadStudentTestCenter
    isNew: boolean
  }) {
    return testCenter.students
      .filter((student, index) =>
        isNew ? index + 1 > testCenter.originalSeat : index + 1 <= testCenter.originalSeat
      )
      .map((student) => student.academicNo)
  }

  private setScheduledSerialnumber() {
    this.scheduledStudentSerialNumbers = this.generateSerialNumbers({
      currentPage: this.scheduledPagination.currentPage,
      perPage: this.scheduledPagination.perPage
    })
  }

  private setImportedSerialnumber() {
    this.importStudentSerialNumbers = this.generateSerialNumbers({
      currentPage: this.pagination.currentPage,
      perPage: this.pagination.perPage
    })
  }

  private generateSerialNumbers({
    currentPage,
    perPage
  }: {
    currentPage: number
    perPage: number
  }) {
    const startSerialNumber = (currentPage - 1) * perPage + 1
    const endSerialNumber = startSerialNumber + perPage - 1 // TODO: if need in feature we use it

    return Array.from({ length: perPage }, (_, index) => startSerialNumber + index)
  }

  setTotalPages({ totalPages }: { totalPages: number }) {
    this.scheduledPagination.totalPages = totalPages

    if (this.scheduledPagination.currentPage > totalPages) {
      this.scheduledPagination.currentPage = 1
    }

    this.setScheduledSerialnumber()
  }

  onClickAutoAssignEvaluators() {
    this.dialog
      .open(ConfirmDialogComponent, {
        panelClass: 'dialog-fix',
        disableClose: true,
        data: {
          customTitle: this.translateService.instant('assessment.dashboard.confirmationRequired'),
          customMessage: this.translateService.instant(
            'exams.studentGrouping.messageAutoAssignEvaluators'
          ),
          actionBtn: 'common.confirm'
        },
        direction: this.settingsService.getOptions().dir
      })
      .afterClosed()
      .subscribe((result) => {
        if (!result) {
          return
        }

        this.assignEvaluators({ isAutoAssign: true })
      })
  }

  private assignEvaluators({ isAutoAssign }: { isAutoAssign: boolean }) {
    const payload: IAutoAssignedStudentsPayload = this.getAssignEvaluatorsPayload({ isAutoAssign })

    this.examScheduleService.assignEvaluators({ payload }).subscribe(
      ({ message }) => {
        this.globalService.showSuccess(message)

        if (isAutoAssign) {
          return this.dialogRef.close()
        }

        this.refreshStudentsTable()
      },
      (err) => this.errorHandlerService.errorLog(err)
    )
  }

  private refreshStudentsTable() {
    this.selectedScheduledStudents = []
    this.selectedImportedStudents = []
    this.getUnassignedStudents()
    this.getAssignedStudents()
  }

  private getAssignEvaluatorsPayload({
    isAutoAssign
  }: {
    isAutoAssign: boolean
  }): IAutoAssignedStudentsPayload {
    const payload: IAutoAssignedStudentsPayload = {
      groupId: this.studentGroupData?.group?._id,
      studentGroupId: this.studentGroupData?.studentGroupId,
      isAutoAssign,
      assignStudents: [],
      unAssignStudents: []
    }

    if (!isAutoAssign) {
      if (this.selectedImportedStudents?.length) {
        payload.assignStudents = [
          {
            evaluatorId: this.selectedEvaluator?._id,
            academicNos: this.selectedImportedStudents.map(({ academicNo }) => academicNo)
          }
        ]
      }

      if (this.selectedScheduledStudents?.length) {
        payload.unAssignStudents = this.selectedScheduledStudents.map(
          ({ academicNo }) => academicNo
        )
      }
    }

    return payload
  }

  onChangeEvaluator({ selectedEvaluator }: { selectedEvaluator: IEvaluatorsFullName }) {
    this.selectedEvaluator = selectedEvaluator
    this.scheduledPagination.currentPage = 1
    this.scheduledPagination.perPage = 10
    this.selectedScheduledStudents = []
    this.assignedStudents = []
    this.getAssignedStudents()
  }

  isArrowsDisabled({ isLeftArrow }: { isLeftArrow: boolean }): boolean {
    if (isLeftArrow) {
      return [
        AutoAssignTestCenterType.SCHEDULE,
        AutoAssignTestCenterType.SCHEDULED_STUDENT_GROUPING,
        AutoAssignTestCenterType.INDEPENDENT_ASSESSMENT
      ].includes(this.data?.type as AutoAssignTestCenterType)
        ? !(this.selectedTc && this.selectedImportedStudents?.length)
        : !this.selectedImportedStudents.length
    } else {
      return [
        AutoAssignTestCenterType.SCHEDULE,
        AutoAssignTestCenterType.SCHEDULED_STUDENT_GROUPING,
        AutoAssignTestCenterType.INDEPENDENT_ASSESSMENT
      ].includes(this.data?.type as AutoAssignTestCenterType)
        ? !(this.selectedTc && this.selectedScheduledStudents?.length)
        : !this.selectedScheduledStudents.length
    }
  }

  private reCallStudentList({ isPlanned }: { isPlanned?: boolean }) {
    switch (this.data?.type) {
      case AutoAssignTestCenterType.STUDENT_GROUPING:
        isPlanned ? this.getUnassignedStudents() : this.getAssignedStudents()
        break

      case AutoAssignTestCenterType.SCHEDULE:
        this.studentList({})
        break

      case AutoAssignTestCenterType.SCHEDULED_STUDENT_GROUPING:
        this.getStudentWithGrouping({})
        break

      case AutoAssignTestCenterType.INDEPENDENT_ASSESSMENT:
        this.getStudentsForIndependentAssessment({})
        break
    }
  }

  onClickRefreshStudentList() {
    if (this.data?.type === AutoAssignTestCenterType.INDEPENDENT_ASSESSMENT) {
      this.getStudentsForIndependentAssessment({ isRefreshStudentList: true })
      return
    }

    this.getStudentWithGrouping({ isRefreshStudentList: true })
  }

  get getInstitutionName() {
    return this.utilService.getInstitutionNameWithDate({
      institutionCalendar: this.institutionCalendar
    })
  }

  private getStudentsForIndependentAssessment({
    isInit = false,
    isRefreshStudentList = false,
    isThrowError = false
  }: {
    isInit?: boolean
    isRefreshStudentList?: boolean
    isThrowError?: boolean
  }) {
    this.independentAssessmentService
      .getStudentByTcDetails({
        payload: {
          examCourseId: this.testCenterData.course._id,
          gender: this.selectedGender,
          pageNo: this.pagination?.currentPage,
          limit: this.pagination?.perPage,
          search: this.search,
          isInit,
          isRefreshStudentList,
          academicNos: isRefreshStudentList ? [] : this.testCenterData.academicNos || [],
          isThrowError
        },
        type: this.data.isExamSchedule ? 'exam-schedule' : 'independent-assessment'
      })
      .subscribe(
        ({ data, message }) => {
          if (data?.inActiveStudents?.length) {
            this.showInActiveStudentsPopUp({ students: data.inActiveStudents })
              .afterClosed()
              .subscribe((res) => {
                if (res) {
                  this.testCenterData.academicNos = data.academicNos
                  this.initUploadStudents = [...this.testCenterData.academicNos]
                  this.institutionCalendar = data.institutionCalendar
                  data.inActiveStudents = []
                  this.formatStudentAndTcDetails({ data, isInit })

                  return
                }

                const inActiveAcademicNos = data.inActiveStudents.map(
                  (student) => student.academicNo
                )
                this.testCenterData.academicNos = data.academicNos.filter(
                  (academicNo) => !inActiveAcademicNos.includes(academicNo)
                )
                this.initUploadStudents = [...this.testCenterData.academicNos]

                if (!this.initUploadStudents.length) {
                  this.dialogRef.close()
                  this.globalService.showError(
                    this.translateService.instant('exams.studentGrouping.noStudentFound')
                  )

                  return
                }

                this.getStudentsForIndependentAssessment({})
              })
          }

          if (data.academicNos?.length) {
            this.testCenterData.academicNos = data.academicNos
            this.initUploadStudents = [...this.testCenterData.academicNos]
          }

          if (!this.initUploadStudents.length) {
            this.dialogRef.close()
            this.globalService.showError(
              this.translateService.instant('exams.studentGrouping.noStudentFound')
            )

            return
          }

          this.formatStudentAndTcDetails({ data, isInit })

          if (isRefreshStudentList) {
            this.toastService.success(message)
          }
        },
        (err) => {
          this.errorHandlerService.errorLog(err)
          this.importedStudents = []
        }
      )
  }
}
