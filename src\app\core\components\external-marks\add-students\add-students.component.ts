import {
  Component,
  EventEmitter,
  Input,
  OnChang<PERSON>,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core'
import { MatDialog } from '@angular/material/dialog'

import { TranslateService } from '@ngx-translate/core'
import { ToastrService } from 'ngx-toastr'

import { DIGICLASS, SCHEDULED_STUDENT_GROUPING } from '@appcore/app/core/constants'
import {
  IExternalExam,
  IExternalExamSession,
  IName,
  IPagination,
  IStudentGroup
} from '@appcore/app/models'
import {
  IUploadError,
  IUploadStudents
} from '@appcore/app/models/exam-readiness/exam-readiness-on-going.interface'
import { ExternalMarkService } from '@appcore/app/pages/external-marks/external-marks.service'
import {
  CsvService,
  ErrorHandlerService,
  EventsService,
  GlobalService,
  RouteService,
  SettingsService
} from '@appcore/app/services'

import { TcAssignErrorPopupComponent } from '../../exams/table/exammgt/dashboard/unscheduled-course-group-table/tc-assign-error-popup'
import { UploadStudentErrorPopupComponent } from '../../exams/table/exammgt/dashboard/unscheduled-course-group-table/upload-student-error-popup'
import { ConfirmDialogComponent, ImportCsvDialogComponent } from '../../shared-component'
import { IStudentList } from '../external-mark-interface'

@Component({
  selector: 'digi-app-add-students',
  templateUrl: './add-students.component.html',
  styleUrls: ['./add-students.component.css']
})
export class AddStudentsComponent implements OnInit, OnDestroy, OnChanges {
  @Input()
  externalExam: IExternalExam

  @Input()
  externalExamSession: IExternalExamSession

  @Input()
  revisionNumber: number

  @Input()
  studentAction: { canUpload: boolean }

  @Input()
  evaluationNumber: number

  @Input()
  searchText: string

  @Output()
  changeStudentsList: EventEmitter<{ notEmpty: boolean; students: IStudentList[] }> =
    new EventEmitter<{ notEmpty: boolean; students: IStudentList[] }>()

  @Output()
  moveStudent: EventEmitter<string[]> = new EventEmitter<string[]>()

  displayedColumns: string[] = ['action', 'sNo', 'academic', 'gender', 'name', 'group', 'delete']
  dataSource: IStudentList[] = []
  pagination: IPagination = { pageNo: 1, limit: 5, perPage: 5 }
  dialogTitle = 'exams.uploadPermittedStudents.selectStudentPermitted'
  isSelectedAll = false
  totalStudents: number
  students: string[]
  type: string
  fileName: string
  group: IStudentGroup
  canAddInactiveStudents: boolean

  constructor(
    private dialog: MatDialog,
    private csvService: CsvService,
    private settingsService: SettingsService,
    private externalMarkService: ExternalMarkService,
    private errorHandlerService: ErrorHandlerService,
    private globalService: GlobalService,
    private toastrService: ToastrService,
    private eventService: EventsService,
    private translate: TranslateService,
    private routeService: RouteService
  ) {}

  ngOnInit(): void {
    this.initEvents()
    this.initStudentList()
  }

  ngOnChanges(changes: SimpleChanges): void {
    const { searchText } = changes

    if (!searchText?.firstChange && (searchText.currentValue || searchText.currentValue === '')) {
      Object.assign(this.pagination, {
        pageNo: 1,
        currentPage: 1
      })
      this.initStudentList()
    }
  }

  private initEvents() {
    void this.eventService.listen('students:add', ({ type }) => {
      switch (type) {
        case 'template':
          this.onClickImportTemplate()
          break

        case 'refresh':
          this.initStudentList()
          break

        case 'move':
          this.moveStudents()
          break

        case 'sis':
          this.uploadStudentsViaSis()
          break

        default:
          this.initStudentList()
          break
      }
    })
  }

  onClickUploadStudentsViaSis() {
    this.uploadStudentsViaSis()
  }

  private uploadStudentsViaSis({
    canAddInactiveStudents
  }: { canAddInactiveStudents?: boolean } = {}) {
    this.externalMarkService
      .uploadStudentsForExternalExams({
        courseGroupId: this.externalExam?._id,
        courseReportId: this.externalExam?.courseReportId,
        sessionId: this.externalExamSession?._id,
        testCenterId: this.externalExamSession?.testCenters[0]?._id,
        revisionNumber: this.revisionNumber,
        evaluationNumber: this.evaluationNumber,
        type: 'SIS',
        ...(canAddInactiveStudents !== undefined && { canAddInactiveStudents })
      })
      .subscribe(
        ({ data, message }) => {
          const hasInactiveStudents = data?.students?.some((student) => !student?.isActive)

          if (hasInactiveStudents) {
            return this.showInActiveStudentsPopUp({
              students: data?.students.filter((student) => !student?.isActive)
            })
              .afterClosed()
              .subscribe((res) => {
                this.uploadStudentsViaSis({ canAddInactiveStudents: res })
              })
          }

          this.toastrService.success(message)
          this.initStudentList()
        },
        (err) => this.errorHandlerService.errorLog(err)
      )
  }

  private moveStudents() {
    if (this.isSelectedAll) {
      return this.moveStudent.emit([])
    }

    const academicNos = this.dataSource?.reduce((academicIds, { academicNo, isSelected }) => {
      if (isSelected) {
        academicIds.push(academicNo)
      }

      return academicIds
    }, [])

    if (!academicNos?.length) {
      return this.toastrService.error(this.translate.instant('externalMarks.pleaseSelectStudent'))
    }
    this.moveStudent.emit(academicNos)
  }

  onClickImportTemplate() {
    if (this.dialog?.openDialogs?.length) {
      this.dialog.closeAll()
    }

    const isCustomGroup = this.externalExam.isCustomGroup
    this.csvService.importedFileName = 'exams.uploadPermittedStudents.csvFilesSelectedZero'
    this.dialog
      .open(ImportCsvDialogComponent, {
        width: '400px',
        data: {
          dialogTitle: this.dialogTitle,
          dialogHint: this.csvService.importedFileName,
          type: 'externalMarks',
          examCourseId: this.externalExam?._id,
          isCustomGroup,
          canAllowToUploadWithoutGroup: true
        },
        panelClass: 'dialog-fix',
        direction: this.settingsService.getOptions().dir,
        disableClose: true
      })
      .afterClosed()
      .subscribe((result) => {
        if (!result) {
          return
        }

        if (result?.type === DIGICLASS) {
          return this.routeService.transitionToImportFromDigiClass({
            id: this.externalExam?._id,
            courseName: this.externalExam?.name,
            name: this.externalExam?.courseName,
            courseReportId: this.externalExam?.courseReportId,
            sessionId: this.externalExamSession?._id,
            testCenterId: this.externalExamSession?.testCenters[0]?._id,
            revisionNumber: this.revisionNumber,
            evaluationNumber: this.evaluationNumber
          })
        }

        this.students = (result?.length ? result[0]?.students : [result])?.map(
          ({ academicNo }) => academicNo
        )
        this.type = result?.length ? result[0]?.type : result?.type
        this.fileName = result?.length ? result[0]?.name : this.externalExam?.name
        this.group =
          this.type === 'BULK' && this.externalExam.isCustomGroup
            ? this.externalExam.groups[0]
            : result?.group
        this.uploadStudents({
          courseGroupId: this.externalExam?._id,
          courseReportId: this.externalExam?.courseReportId,
          sessionId: this.externalExamSession?._id,
          testCenterId: this.externalExamSession?.testCenters[0]?._id,
          revisionNumber: this.revisionNumber,
          evaluationNumber: this.evaluationNumber,
          academicNos: this.students,
          fileName: this.fileName,
          type: this.type,
          group: this.group,
          ...(this.type === 'INDIVIDUAL' && { canAddInactiveStudents: true })
        })
      })
  }

  private initStudentList() {
    this.externalMarkService
      .getStudentsList({
        pagination: this.pagination,
        courseGroupId: this.externalExam?._id,
        courseReportId: this.externalExam?.courseReportId,
        sessionId: this.externalExamSession?._id,
        testCenterId: this.externalExamSession?.testCenters[0]?._id,
        searchText: this.searchText
      })
      .subscribe(
        ({ data: { data, currentPage, totalCount, totalPages } }) => {
          this.dataSource = data
          this.totalStudents = totalCount
          this.dataSource?.forEach((student) => {
            student.isSelected = false
          })
          this.changeStudentsList.emit({ notEmpty: !!data?.length, students: data })
          Object.assign(this.pagination, {
            currentPage,
            totalCount,
            totalPages
          })
        },
        (err) => this.errorHandlerService.errorLog(err)
      )
  }

  private uploadStudents({
    courseGroupId,
    courseReportId,
    sessionId,
    testCenterId,
    revisionNumber,
    evaluationNumber,
    academicNos,
    fileName,
    type,
    group,
    canAddInactiveStudents,
    canSkipInvalidStudents
  }: {
    courseGroupId: string
    courseReportId: string
    sessionId: string
    testCenterId: string
    revisionNumber: number
    evaluationNumber: number
    academicNos: string[]
    fileName: string
    type: string
    group?: IStudentGroup
    canAddInactiveStudents?: boolean
    canSkipInvalidStudents?: boolean
  }) {
    if (!academicNos?.length && type !== SCHEDULED_STUDENT_GROUPING) {
      return this.toastrService.error(
        this.translate.instant('exams.uploadStudentError.importedDataIsEmpty')
      )
    }

    this.externalMarkService
      .uploadStudentsForExternalExams({
        courseGroupId,
        courseReportId,
        sessionId,
        testCenterId,
        revisionNumber,
        evaluationNumber,
        academicNos,
        fileName,
        type,
        group,
        canAddInactiveStudents,
        canSkipInvalidStudents
      })
      .subscribe(
        ({ data, message }) => {
          if (data?.students) {
            const hasInactiveStudents = data?.students?.some((student) => !student?.isActive)

            if (hasInactiveStudents) {
              return this.showInActiveStudentsPopUp({
                students: data?.students.filter((student) => !student?.isActive)
              })
                .afterClosed()
                .subscribe((res) => {
                  if (!res) {
                    academicNos = academicNos.filter((academicNo) => {
                      const student = data?.students.find(
                        (student) => student?.academicNo === academicNo
                      )
                      return student?.isActive
                    })
                  }

                  this.uploadStudents({
                    courseGroupId,
                    courseReportId,
                    sessionId,
                    testCenterId,
                    revisionNumber,
                    evaluationNumber,
                    academicNos,
                    fileName,
                    type,
                    group,
                    canAddInactiveStudents: res
                  })
                })
            }
          }

          this.toastrService.success(message)
          this.initStudentList()
        },
        (err) => {
          if (err?.error?.code === 701) {
            return this.uploadErrorPopup({
              errorList: err?.error?.data,
              currentUploadCount: academicNos?.length
            })
          }

          if (err?.error?.code === 700) {
            this.canAddInactiveStudents = false
            return this.showInActiveStudentsPopUp({
              students: err?.error?.data.inactiveStudents
            })
              .afterClosed()
              .subscribe((res) => {
                this.canAddInactiveStudents = res
                this.uploadStudents({
                  courseGroupId,
                  courseReportId,
                  sessionId,
                  testCenterId,
                  revisionNumber,
                  evaluationNumber,
                  academicNos,
                  fileName,
                  type,
                  group,
                  canAddInactiveStudents: this.canAddInactiveStudents
                })
              })
          }
          this.errorHandlerService.errorLog(err)
        }
      )
  }

  private showInActiveStudentsPopUp({ students }: { students: IUploadStudents[] }) {
    return this.dialog.open(TcAssignErrorPopupComponent, {
      panelClass: 'dialog-fix',
      maxWidth: '40vw',
      maxHeight: '90vh',
      direction: this.settingsService.getOptions().dir,
      disableClose: true,
      data: {
        type: 'inactiveError',
        message: this.translate.instant('exams.uploadStudentError.inactiveStudentMessage'),
        students
      }
    })
  }

  uploadErrorPopup({
    errorList,
    currentUploadCount
  }: {
    errorList: IUploadError
    currentUploadCount: number
  }) {
    const canSkipInvalidStudents = !errorList.students.some(({ isInvalid = false }) => !isInvalid)
    this.dialog
      .open(UploadStudentErrorPopupComponent, {
        data: {
          errorType: 'studentData',
          course: { name: this.externalExam?.name },
          errorList,
          currentUploadCount,
          canSkipInvalidStudents
        },
        panelClass: 'dialog-fix',
        direction: this.settingsService.getOptions().dir
      })
      .afterClosed()
      .subscribe(({ proceed = false }: { proceed?: boolean } = {}) => {
        if (!proceed) {
          return
        }

        this.uploadStudents({
          courseGroupId: this.externalExam?._id,
          courseReportId: this.externalExam?.courseReportId,
          sessionId: this.externalExamSession?._id,
          testCenterId: this.externalExamSession?.testCenters[0]?._id,
          revisionNumber: this.revisionNumber,
          evaluationNumber: this.evaluationNumber,
          academicNos: this.students,
          fileName: this.fileName,
          type: this.type,
          group: this.group,
          canSkipInvalidStudents,
          canAddInactiveStudents: this.canAddInactiveStudents
        })
      })
  }

  getFullName({ name }: { name: IName }) {
    return this.globalService.getFullName(name)
  }

  onCheckAll() {
    this.dataSource.forEach((student) => {
      student.isSelected = this.isSelectedAll
    })
  }

  onClickDelete({ academicNo }: { academicNo?: string }) {
    this.dialog
      .open(ConfirmDialogComponent, {
        data: {
          message: ''
        },
        disableClose: true,
        panelClass: 'dialog-fix',
        direction: this.settingsService.getOptions().dir
      })
      .afterClosed()
      .subscribe((result) => {
        if (!result) {
          return
        }

        this.externalMarkService
          .removeStudentsFromList({
            courseGroupId: this.externalExam?._id,
            courseReportId: this.externalExam?.courseReportId,
            assessmentId: this.externalExam?.assessmentId,
            sessionId: this.externalExamSession?._id,
            testCenterId: this.externalExamSession?.testCenters[0]?._id,
            academicNos: academicNo ? [academicNo] : [],
            overAll: !academicNo
          })
          .subscribe(
            ({ message }) => {
              this.toastrService.success(message)

              if (this.isSelectedAll) {
                this.isSelectedAll = false
              }

              this.initStudentList()
            },
            (err) => this.errorHandlerService.errorLog(err)
          )
      })
  }

  onChangePagination({ type, event }: { type: string; event: number }) {
    this.pagination[type] = event
    if (type === 'perPage') {
      this.pagination.limit = event
      this.pagination.currentPage = 1
      this.pagination.pageNo = 1
    }
    if (type === 'currentPage') {
      this.pagination.pageNo = event
    }

    this.initStudentList()
    this.isSelectedAll = false
  }

  ngOnDestroy(): void {
    this.eventService.dispose('students:add')
  }

  onChangeSelectStudent() {
    this.isSelectedAll = !this.dataSource.some((student) => !student?.isSelected)
  }
}
