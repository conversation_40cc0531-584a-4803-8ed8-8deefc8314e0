<div class="digi-mt-20">
  <div fxLayoutAlign="flex-end center" class="digi-mr-20">
    <digi-search
      class="digi-p-5"
      [filter]="true"
      [text]="'courseInput.searchSubjectTopic' | translate"
      (search)="onSearchCourse({ searchText: $event })"
    ></digi-search>
    <digi-button
      button="primary"
      (click)="onClickCreateSubjectOrTopic({ type: isCourse ? 'topic' : 'subject' })"
    >
      {{ (isCourse ? 'courseInput.createNewTopic' : 'courseInput.createNewSubject') | translate }}
    </digi-button>
  </div>

  <div
    *ngIf="
      subjectTopic?.subjects?.length || subjectTopic?.topics?.length;
      else noSubjectOrTopicAvailable
    "
  >
    <div *ngIf="subjectTopic?.subjects?.length">
      <div
        *ngFor="let subject of subjectTopic.subjects; let i = index"
        class="subject-container digi-mt-10"
      >
        <div class="subject-header digi-subject-topic-header">
          <div fxLayout="row" fxLayoutAlign="start center">
            <mat-icon class="digi-blue digi-cursor" (click)="onClickToggleSubject(i)">
              {{ expandedSubjectIndex === i ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}
            </mat-icon>
            <span class="digi-index-background digi-mr-10 digi-ml-10">{{ i + 1 }}</span>
            <span class="subject-code">{{ subject?.code }} - {{ subject?.name }}&nbsp;&nbsp;</span>
            <span class="digi-black digi-fw-500 digi-fs-12 digi-fs-10">
              ({{
                (subject?.topics?.length ? 'courseInput.topicWithCount' : 'courseInput.noTopics')
                  | translate: { count: subject?.topics?.length }
              }})
            </span>
          </div>
          <div fxLayoutAlign="flex-end center" fxLayoutGap="10px">
            <span
              class="digi-cursor digi-mr-20 digi-fs-14 digi-font-500 digi-blue"
              (click)="onClickCreateSubjectOrTopic({ type: 'topic', subjectId: subject?._id })"
            >
              {{ 'courseInput.addTopic' | translate }}
            </span>
            <div class="digi-circle-icon-container">
              <button mat-button [matMenuTriggerFor]="menu" class="digi-cursor">
                <mat-icon class="digi-light-gray">more_vert</mat-icon>
              </button>
            </div>
            <mat-menu #menu="matMenu">
              <button
                mat-menu-item
                (click)="
                  onClickUpdateSubjectOrTopic({
                    subjectId: subject?._id,
                    type: 'subject',
                    name: subject?.name,
                    code: subject?.code,
                    content: subject?.content
                  })
                "
              >
                {{ 'common.edit' | translate | titlecase }}
              </button>
              <button
                mat-menu-item
                (click)="
                  onClickDeleteSubjectAndTopic({ subjectId: subject?._id, type: subject?.name })
                "
              >
                {{ 'common.delete' | translate }}
              </button>
            </mat-menu>
          </div>
        </div>
        <div *ngIf="expandedSubjectIndex === i">
          <div *ngFor="let topic of subject?.topics; let j = index">
            <div fxLayout="row" fxLayoutAlign="space-between center" class="digi-topic-padding">
              <div fxLayoutAlign="flex-end center" class="digi-pl-35">
                <mat-icon
                  *ngIf="topic?.subTopics?.length; else noSubTopicIcon"
                  class="digi-blue digi-cursor"
                  (click)="onClickToggleTopic(j)"
                >
                  {{ expandedTopicIndex === j ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}
                </mat-icon>
                <ng-template #noSubTopicIcon>
                  <mat-icon class="subdirectory-arrow-right">subdirectory_arrow_right</mat-icon>
                </ng-template>
                <span class="digi-index-background digi-mr-10 digi-ml-10">{{ j + 1 }}</span>
                <span class="topic-code">{{ topic?.code }} - {{ topic?.name }}&nbsp;&nbsp;</span>
                <span
                  *ngIf="topic?.subTopics?.length"
                  class="digi-black digi-fw-500 digi-fs-12 digi-fs-10"
                >
                  ({{ topic?.subTopics?.length | arabicNumber }}
                  {{ 'common.subTopics' | translate }})
                </span>
                <span
                  *ngIf="!topic?.subTopics?.length"
                  class="digi-black digi-fw-500 digi-fs-12 digi-fs-10"
                >
                  ({{ 'courseInput.noSubTopics' | translate }})
                </span>
                <span *ngIf="false" class="content-status">
                  ({{
                    (topic?.content ? 'courseInput.writtenContent' : 'courseInput.noContent')
                      | translate
                  }})
                </span>
              </div>

              <div fxLayoutAlign="flex-end center" fxLayoutGap="10px">
                <span
                  *ngIf="false"
                  class="digi-cursor digi-fs-14 digi-font-500 digi-blue"
                  (click)="
                    onClickUpdateSubjectOrTopic({
                      type: 'content',
                      subjectId: subject?._id,
                      topicId: topic?._id
                    })
                  "
                >
                  {{ 'courseInput.addContent' | translate }}
                </span>
                <span
                  class="digi-cursor digi-fs-14 digi-font-500 digi-blue"
                  (click)="
                    onClickCreateSubjectOrTopic({
                      type: 'subTopic',
                      subjectId: subject?._id,
                      topicId: topic?._id
                    })
                  "
                >
                  + {{ 'common.addSubTopic' | translate }}
                </span>
                <div class="digi-circle-icon-container">
                  <button mat-button [matMenuTriggerFor]="menu" class="digi-cursor">
                    <mat-icon class="digi-light-gray">more_vert</mat-icon>
                  </button>
                </div>
                <mat-menu #menu="matMenu">
                  <button
                    mat-menu-item
                    (click)="
                      onClickUpdateSubjectOrTopic({
                        type: 'topic',
                        subjectId: subject?._id,
                        topicId: topic?._id,
                        name: topic?.name,
                        code: topic?.code
                      })
                    "
                  >
                    {{ 'common.edit' | translate | titlecase }}
                  </button>
                  <button
                    mat-menu-item
                    (click)="
                      onClickDeleteSubjectAndTopic({
                        subjectId: subject?._id,
                        topicId: topic?._id,
                        type: topic?.name
                      })
                    "
                  >
                    {{ 'common.delete' | translate }}
                  </button>
                </mat-menu>
                <mat-icon
                  *ngIf="false"
                  class="digi-blue digi-cursor"
                  (click)="onClickToggleTopic(j)"
                >
                  {{ expandedTopicIndex === j ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}
                </mat-icon>
              </div>
            </div>

            <div
              *ngIf="expandedTopicIndex === j && topic?.content"
              fxLayout="column"
              class="topic-content"
            >
              <div class="top-right-button">
                <button mat-button [matMenuTriggerFor]="menu" class="digi-cursor">
                  <mat-icon class="digi-light-gray">more_vert</mat-icon>
                </button>
              </div>
              <span class="top-left-text">{{ topic?.content }}</span>
              <mat-menu #menu="matMenu">
                <button
                  mat-menu-item
                  (click)="
                    onClickUpdateSubjectOrTopic({
                      type: 'content',
                      subjectId: subject?._id,
                      topicId: topic?._id,
                      content: topic?.content
                    })
                  "
                >
                  {{ 'common.edit' | translate | titlecase }}
                </button>
                <button
                  mat-menu-item
                  (click)="
                    onClickDeleteSubjectAndTopic({
                      subjectId: subject?._id,
                      topicId: topic?._id,
                      isContent: true,
                      type: 'Content'
                    })
                  "
                >
                  {{ 'common.delete' | translate }}
                </button>
              </mat-menu>
            </div>

            <div *ngIf="expandedTopicIndex === j && topic?.subTopics?.length">
              <div *ngFor="let subTopic of topic?.subTopics; let k = index" class="sub-topic-item">
                <div fxLayout="row" fxLayoutAlign="space-between center" class="digi-topic-padding">
                  <div
                    fxLayout="row"
                    fxLayoutAlign="start center"
                    style="padding-left: 80px"
                    class="digi-pl-80"
                  >
                    <mat-icon class="subdirectory-arrow-right">subdirectory_arrow_right</mat-icon>
                    <span class="digi-index-background digi-mr-10">{{ k + 1 }}</span>
                    <span class="sub-topic-code">{{ subTopic?.code }} - {{ subTopic?.name }}</span>
                  </div>

                  <div fxLayout="row" fxLayoutAlign="flex-end center">
                    <div class="digi-circle-icon-container">
                      <button mat-button [matMenuTriggerFor]="menu" class="digi-cursor">
                        <mat-icon class="digi-light-gray">more_vert</mat-icon>
                      </button>
                    </div>
                    <mat-menu #menu="matMenu">
                      <button
                        mat-menu-item
                        (click)="
                          onClickUpdateSubjectOrTopic({
                            subjectId: subject?._id,
                            topicId: topic?._id,
                            subTopicId: subTopic?._id,
                            type: 'subTopic',
                            name: subTopic?.name,
                            code: subTopic?.code,
                            content: subTopic?.content || ''
                          })
                        "
                      >
                        {{ 'common.edit' | translate | titlecase }}
                      </button>
                      <button
                        mat-menu-item
                        (click)="
                          onClickDeleteSubjectAndTopic({
                            subjectId: subject?._id,
                            topicId: topic?._id,
                            subTopicId: subTopic?._id,
                            type: subTopic?.name
                          })
                        "
                      >
                        {{ 'common.delete' | translate }}
                      </button>
                    </mat-menu>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="subjectTopic?.topics?.length">
      <div
        *ngFor="let topic of subjectTopic.topics; let i = index"
        class="subject-container digi-pl-10 digi-pr-10 digi-mt-10 digi-ml-20 digi-mr-20"
      >
        <div class="subject-header digi-subject-topic-header">
          <div fxLayout="row" fxLayoutAlign="start center">
            <mat-icon *ngIf="false" class="digi-blue digi-cursor" (click)="onClickToggleTopic(i)">
              {{ expandedTopicIndex === i ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}
            </mat-icon>
            <span class="digi-index-background digi-mr-10 digi-ml-10">{{ i + 1 }}</span>
            <span class="subject-code">{{ topic?.code }} - {{ topic?.name }}</span>
            <span *ngIf="false" class="content-status">
              ({{
                (topic?.content ? 'courseInput.writtenContent' : 'courseInput.noContent')
                  | translate
              }})
            </span>
          </div>
          <div fxLayoutAlign="flex-end center" fxLayoutGap="10px">
            <span
              *ngIf="false"
              class="digi-cursor digi-fs-14 digi-font-500 digi-blue"
              (click)="onClickUpdateSubjectOrTopic({ type: 'content', topicId: topic?._id })"
            >
              {{ 'courseInput.addContent' | translate }}
            </span>
            <div class="digi-circle-icon-container">
              <button mat-button [matMenuTriggerFor]="menu" class="digi-cursor">
                <mat-icon class="digi-light-gray">more_vert</mat-icon>
              </button>
            </div>
            <mat-menu #menu="matMenu">
              <button
                mat-menu-item
                (click)="
                  onClickUpdateSubjectOrTopic({
                    type: 'topic',
                    topicId: topic?._id,
                    name: topic?.name,
                    code: topic?.code
                  })
                "
              >
                {{ 'common.edit' | translate | titlecase }}
              </button>
              <button
                mat-menu-item
                (click)="
                  onClickDeleteSubjectAndTopic({
                    topicId: topic?._id,
                    type: topic?.name
                  })
                "
              >
                {{ 'common.delete' | translate }}
              </button>
            </mat-menu>
          </div>
        </div>

        <div
          *ngIf="expandedTopicIndex === i && topic?.content"
          fxLayout="column"
          class="topic-content"
        >
          <div class="top-right-button">
            <button mat-button [matMenuTriggerFor]="menu" class="digi-cursor">
              <mat-icon class="digi-light-gray">more_vert</mat-icon>
            </button>
          </div>
          <span class="top-left-text">{{ topic?.content }}</span>
          <mat-menu #menu="matMenu">
            <button
              mat-menu-item
              (click)="
                onClickUpdateSubjectOrTopic({
                  type: 'content',
                  topicId: topic?._id,
                  content: topic?.content
                })
              "
            >
              {{ 'common.edit' | translate | titlecase }}
            </button>
            <button
              mat-menu-item
              (click)="
                onClickDeleteSubjectAndTopic({
                  topicId: topic?._id,
                  isContent: true,
                  type: 'Content'
                })
              "
            >
              {{ 'common.delete' | translate }}
            </button>
          </mat-menu>
        </div>
      </div>
    </div>
  </div>
  <ng-template #noSubjectOrTopicAvailable>
    <div fxLayoutAlign="center center" class="select-topic digi-light-gray digi-fs-18">
      {{ 'common.noDataFound' | translate }}
    </div>
  </ng-template>
</div>
