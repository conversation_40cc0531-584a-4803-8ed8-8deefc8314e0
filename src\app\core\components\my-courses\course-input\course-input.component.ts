import { Component, Input, OnChanges, SimpleChanges } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'

import { cloneDeep, isEqual } from 'lodash'
import { ToastrService } from 'ngx-toastr'

import { IExamDetail } from '@appcore/app/models'
import { ISubjectAndTopic } from '@appcore/app/pages/my-courses/my-courses.interface'
import { MyCoursesService } from '@appcore/app/pages/my-courses/my-courses.service'
import { ErrorHandlerService, SettingsService } from '@appcore/app/services'
import { CourseType } from '@appcore/constants'

import { CreateSubjectAndTopicPopupComponent } from '../create-subject-topic-popup'
import { ConfirmDialogComponent } from '../../confirm-dialogbox'
import { TranslateService } from '@ngx-translate/core'
import { sub } from 'date-fns'

@Component({
  selector: 'digi-course-input',
  templateUrl: './course-input.component.html',
  styleUrls: ['./course-input.component.css']
})
export class CourseInputComponent implements OnChanges {
  @Input()
  course: IExamDetail

  subjectTopic: ISubjectAndTopic
  subjectTopicClone: ISubjectAndTopic
  isCourse: boolean
  expandedSubjectIndex: number | null = 0
  expandedTopicIndex: number | null = 0

  constructor(
    private myCoursesService: MyCoursesService,
    private dialog: MatDialog,
    private toastr: ToastrService,
    private errorHandleService: ErrorHandlerService,
    private translateService: TranslateService,
    private settingsService: SettingsService
  ) {}

  ngOnChanges(): void {
    this.isCourse = this.course?.hierarchy?.course?.type === CourseType.COURSE

    if (this.course?.name) {
      this.getSubjectsAndTopicsForCourse()
    }
  }

  private getSubjectsAndTopicsForCourse() {
    this.myCoursesService.getSubjectsAndTopicsForCourse({ courseName: this.course.name }).subscribe(
      ({ data }) => {
        this.subjectTopic = data
        this.subjectTopicClone = cloneDeep(data)
        this.expandedTopicIndex = this.expandedSubjectIndex = null
      },
      (err) => this.errorHandleService.errorLog(err)
    )
  }

  private onOpenCreateSubjectOrTopicPopup({
    type = 'topic',
    nodeId = 1,
    subjectId,
    topicId,
    subTopicId,
    actionType = 'create',
    name,
    code,
    content
  }: {
    type: string
    nodeId?: number
    subjectId: string
    topicId?: string
    subTopicId?: string
    actionType?: string
    name?: string
    code?: string
    content?: string
  }) {
    this.dialog
      .open(CreateSubjectAndTopicPopupComponent, {
        height: 'auto',
        width: 'auto',
        panelClass: ['digi-padding-none', 'dialog-fix'],
        disableClose: true,
        data: {
          type,
          courseName: this.course.name,
          nodeId,
          ...(subjectId && { subjectId }),
          ...(topicId && { topicId }),
          ...(subTopicId && { subTopicId }),
          actionType,
          name,
          code,
          content
        }
      })
      .afterClosed()
      .subscribe((payload) => {
        if (!payload) {
          return
        }

        this.handlePayloadUpdate(payload, actionType, type)
      })
  }

  private handlePayloadUpdate(payload: any, actionType: string, type: string) {
    if (actionType === 'create') {
      this.addNewDataToStructure(payload, type)
    } else if (actionType === 'update') {
      this.updateExistingDataInStructure(payload, type)

      this.getSubjectsAndTopicsForCourse()

      // Log the payload for debugging purposes
      console.log('Popup closed with payload:', { payload, actionType, type })
    }
  }

  private addNewDataToStructure(payload: any, type: string) {
    if (type === 'subject' && payload.name && payload.code) {
      this.subjectTopic.subjects.push(payload)
    } else if (type === 'topic' && payload.topics) {
      // Find the subject and add topics to it
      const subject = this.subjectTopic?.subjects?.find(
        (subject) => subject?._id === payload.subjectId
      )
      if (subject && payload.topics) {
        subject.topics.push(...payload.topics)
      }
    } else if (type === 'subTopic' && payload.subTopics) {
      // Find the topic and add subtopics to it
      const subject = this.subjectTopic?.subjects?.find(
        (subject) => subject?._id === payload.subjectId
      )
      if (subject) {
        const topic = subject?.topics?.find((topic) => topic?._id === payload.topicId)
        if (topic && payload.subTopics) {
          // Note: This assumes topics have a subTopics property, may need interface updates
          ;(topic as any).subTopics = (topic as any).subTopics || []
          ;(topic as any).subTopics.push(...payload.subTopics)
        }
      }
    }
  }

  private updateExistingDataInStructure(payload: any, type: string) {
    if (type === 'subject') {
      const subject = this.subjectTopic?.subjects?.find(
        (subject) => subject?._id === payload.subjectId
      )
      if (subject) {
        Object.assign(subject, payload)
      }
    } else if (type === 'topic') {
      const subject = this.subjectTopic?.subjects?.find(
        (subject) => subject?._id === payload.subjectId
      )
      if (subject) {
        const topic = subject?.topics?.find((topic) => topic?._id === payload.topicId)
        if (topic) {
          Object.assign(topic, payload)
        }
      }
    } else if (type === 'content') {
      const subject = this.subjectTopic?.subjects?.find(
        (subject) => subject?._id === payload.subjectId
      )
      if (subject) {
        const topic = subject?.topics?.find((topic) => topic?._id === payload.topicId)
        if (topic) {
          ;(topic as any).content = payload.content
        }
      }
    }
  }

  onClickToggleSubject(index: number) {
    this.expandedSubjectIndex = this.expandedSubjectIndex === index ? null : index
  }

  onClickToggleTopic(index: number) {
    this.expandedTopicIndex = this.expandedTopicIndex === index ? null : index
  }

  onClickCreateSubjectOrTopic({
    type,
    subjectId = '',
    topicId = ''
  }: {
    type: string
    subjectId?: string
    topicId?: string
  }) {
    let nodeId = 1

    if (!this.subjectTopic?.subjects?.length && !this.subjectTopic?.topics?.length) {
      nodeId = this.subjectTopic.nodeId
    } else if (type === 'subject') {
      nodeId = this.subjectTopic.subjects[length - 1]?.nodeId || 1
    } else if (type === 'topic') {
      if (this.subjectTopic?.subjects?.length) {
        const subject = this.subjectTopic.subjects.find((subjectEntry) =>
          isEqual(subjectEntry._id, subjectId)
        )
        nodeId =
          subject?.topics?.find((topic) => isEqual(topic._id, topicId))?.nodeId || subject?.nodeId
      } else {
        nodeId =
          this.subjectTopic?.topics?.find((topic) => isEqual(topic._id, topicId))?.nodeId || 1
      }
    } else if (type === 'subTopic') {
      nodeId = this.subjectTopic.topics[length - 1]?.nodeId || 1
    }

    this.onOpenCreateSubjectOrTopicPopup({
      type,
      nodeId,
      subjectId,
      topicId,
      actionType: 'create'
    })
  }

  onClickUpdateSubjectOrTopic({
    type,
    topicId,
    subjectId,
    subTopicId,
    name,
    code,
    content
  }: {
    type: string
    subjectId?: string
    topicId?: string
    subTopicId?: string
    name?: string
    code?: string
    content?: string
  }) {
    this.onOpenCreateSubjectOrTopicPopup({
      type,
      subjectId,
      topicId,
      subTopicId,
      actionType: 'update',
      name,
      code,
      content
    })
  }

  onClickDeleteSubjectAndTopic({
    subjectId,
    topicId,
    subTopicId,
    isContent = false,
    type
  }: {
    subjectId?: string
    topicId?: string
    subTopicId?: string
    isContent?: boolean
    type: string
  }) {
    this.dialog
      .open(ConfirmDialogComponent, {
        data: {
          title: 'assessment.createItem.archive',
          message: `${this.translateService.instant('common.archiveMessage')} <b>${type}</b>?`,
          actionBtn: 'assessment.createItem.archive',
          skipBreak: true
        },
        disableClose: true,
        panelClass: 'dialog-fix',
        direction: this.settingsService.getOptions().dir
      })
      .afterClosed()
      .subscribe((result) => {
        if (!result) {
          return
        }

        this.deleteSubjectAndTopic({ subjectId, topicId, subTopicId, isContent })
      })
  }

  private deleteSubjectAndTopic({
    subjectId,
    topicId,
    subTopicId,
    isContent = false
  }: {
    subjectId?: string
    topicId?: string
    subTopicId?: string
    isContent?: boolean
  }) {
    this.myCoursesService
      .deleteSubjectAndTopic({
        courseName: this.course.name,
        subjectId,
        topicId,
        subTopicId,
        type: subTopicId && topicId ? 'subTopic' : !subTopicId && !topicId ? 'subject' : 'topic',
        isContent
      })
      .subscribe(
        ({ message }) => {
          this.toastr.success(message)
          this.getSubjectsAndTopicsForCourse()
        },
        (err) => this.errorHandleService.errorLog(err)
      )
  }

  onSearchCourse({ searchText }: { searchText: string }) {
    const trimmedText = searchText.trim()

    if (!trimmedText) {
      return (this.subjectTopic = cloneDeep(this.subjectTopicClone))
    }

    if (this.subjectTopic?.subjects?.length) {
      this.subjectTopic.subjects = this.subjectTopicClone.subjects.filter(
        ({ name = '', code = '' }) =>
          name.toLowerCase().includes(trimmedText.toLowerCase()) ||
          code.toLowerCase().includes(trimmedText.toLowerCase())
      )
    }

    if (this.subjectTopic?.topics?.length) {
      this.subjectTopic.topics = this.subjectTopicClone.topics.filter(
        ({ name = '', code = '' }) =>
          name.toLowerCase().includes(trimmedText.toLowerCase()) ||
          code.toLowerCase().includes(trimmedText.toLowerCase())
      )
    }
  }
}
