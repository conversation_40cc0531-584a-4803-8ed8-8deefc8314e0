<div>
  <div fxLayoutAlign="space-between center" class="digi-m-10">
    <div fxLayoutAlign="flex-start center" fxLayoutGap="10px">
      <div>
        <mat-icon class="round-background digi-blue"> feed </mat-icon>
      </div>
      <div class="digi-fs-18 digi-font-500">
        {{
          getLabelForType({ type: data.type, isEdit: data?.actionType === 'update' }) | translate
        }}
      </div>
    </div>
    <div fxLayoutAlign="flex-start center" fxLayoutGap="15px">
      <button
        *ngIf="data.type === 'topic'"
        class="digi-import-excel-button"
        (click)="onClickImportExcel()"
      >
        <mat-icon class="digi-import-icon">file_upload</mat-icon>
        {{ 'common.importExcel' | translate }}
      </button>
      <input
        *ngIf="data.type === 'topic'"
        class="digi-none"
        type="file"
        #fileImportInput
        name="File Upload"
        id="txtFileUpload"
        (change)="onChangeFileUpload({ files: $event?.target?.files })"
        accept=".xlsx,.xls"
      />
      <mat-icon class="digi-cursor" (click)="onClickClose()">close</mat-icon>
    </div>
  </div>
  <div class="line"></div>
  <div *ngIf="data.type !== 'content'; else addContent">
    <div *ngIf="data.type === 'subject' || data.actionType === 'update'; else addTopic">
      <div fxLayout="row" fxLayoutGap="16px" fxLayoutAlign="start stretch" class="digi-mt-20">
        <div fxLayout="column" fxLayoutAlign="flex-start" class="digi-pl-20">
          <div class="digi-light-gray">
            {{
              (data?.type === 'subTopic'
                ? 'courseInput.nameOfTheSubTopic'
                : data?.type === 'topic'
                  ? 'courseInput.nameOfTheTopic'
                  : 'courseInput.nameOfTheSubject'
              ) | translate
            }}
            <span class="digi-font-red">*</span>
          </div>
          <textarea
            matInput
            class="digi-input digi-subject-name-input"
            placeholder="{{ 'common.enterName' | translate }}"
            [(ngModel)]="name"
          ></textarea>
        </div>
        <div fxLayout="column" fxLayoutAlign="flex-start" class="digi-pr-10">
          <div class="digi-light-gray">
            {{ 'settings.basicList.basicModule.shortCode' | translate
            }}<span class="digi-font-red">*</span>
          </div>
          <input
            matInput
            class="digi-input digi-subject-code-input digi-mb-10"
            placeholder="{{ 'common.enterCode' | translate }}"
            type="text"
            [(ngModel)]="code"
          />
        </div>
      </div>
      <div
        fxLayout="column"
        fxLayoutAlign="flex-start"
        class="digi-pl-20 digi-pr-10 digi-mb-20"
        *ngIf="data.type === 'subject'"
      >
        <div class="digi-light-gray">
          {{ 'settings.advanceOutcome.descriptionOptional' | translate }}
        </div>
        <textarea
          matInput
          class="digi-input digi-subject-content-input"
          [placeholder]="'courseInput.writeDescription' | translate"
          [(ngModel)]="content"
        ></textarea>
      </div>
    </div>
    <ng-template #addTopic>
      <div class="digi-topic-container digi-m-20">
        <div fxLayoutAlign="space-around center" class="digi-topic-header">
          <span>
            {{
              (data?.type === 'subTopic'
                ? 'assessment.itemCreate.subTopicName'
                : 'assessment.itemCreate.topicName'
              )
                | translate
                | titlecase
            }}
          </span>
          <span>{{ 'settings.basicList.basicModule.shortCode' | translate | titlecase }}</span>
        </div>
        <div class="digi-scroll-container">
          <div *ngIf="data.type === 'subTopic'; else addTopic" class="digi-p-10 digi-mt-10">
            <div fxLayout="row" fxLayoutAlign="space-between center" class="digi-p-10 digi-mt-10">
              <input
                matInput
                class="digi-input digi-subject-name-input"
                placeholder="{{ 'common.enterName' | translate }}"
                type="text"
                [(ngModel)]="subTopic.name"
              />

              <input
                matInput
                class="digi-input digi-subject-code-input"
                placeholder="{{ 'common.enterCode' | translate }}"
                type="text"
                [(ngModel)]="subTopic.code"
              />
            </div>
          </div>
          <ng-template #addTopic>
            <div *ngFor="let topic of topics; let i = index">
              <div fxLayout="row" fxLayoutAlign="space-between center" class="digi-p-10 digi-mt-10">
                <div class="digi-index-background digi-mr-10">{{ i + 1 }}</div>
                <input
                  matInput
                  class="digi-input digi-subject-name-input"
                  placeholder="{{ 'common.enterName' | translate }}"
                  type="text"
                  [(ngModel)]="topic.name"
                />

                <input
                  matInput
                  class="digi-input digi-subject-code-input"
                  placeholder="{{ 'common.enterCode' | translate }}"
                  type="text"
                  [(ngModel)]="topic.code"
                />

                <mat-icon class="digi-icon-grey digi-cursor" (click)="onClickRemoveTopic(i)">
                  delete
                </mat-icon>
              </div>
            </div>
          </ng-template>
        </div>

        <div
          *ngIf="data.type !== 'subTopic'"
          class="digi-add-topic digi-cursor digi-fs-14 digi-font-500 digi-blue"
          (click)="onClickCreateTopic()"
        >
          {{ 'courseInput.addTopic' | translate }}
        </div>
      </div>
    </ng-template>
    <div class="line"></div>
  </div>
  <ng-template #addContent>
    <div class="digi-creation-editor digi-pt-10 digi-pr-20 digi-pl-20 digi-pb-10">
      <digi-ck-text-editor
        [content]="content"
        [options]="toolbarOptions.basicToolbar"
        [topicContent]="true"
        (editorChange)="onEditorChangeText({ editorText: $event })"
      ></digi-ck-text-editor>
    </div>
    <div class="line digi-mt-0 digi-mb-20"></div>
  </ng-template>
  <div fxLayoutAlign="flex-end center" fxLayoutGap="10px" class="digi-mr-20 digi-mb-10 digi-mt-10">
    <digi-button
      class="digi-cancel-button"
      button="primary"
      [transparent]="true"
      [uppercase]="true"
      (click)="onClickClose()"
    >
      {{ 'common.cancel' | translate }}
    </digi-button>
    <digi-button
      [disabled]="canDisableAddSubjectOrTopic"
      button="primary"
      [uppercase]="true"
      (click)="onClickCreateOrUpdateSubjectTopic()"
    >
      {{ 'common.add' | translate }}
    </digi-button>
  </div>
</div>
