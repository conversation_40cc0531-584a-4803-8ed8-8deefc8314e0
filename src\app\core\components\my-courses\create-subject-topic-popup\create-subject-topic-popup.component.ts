import { Component, ElementRef, Inject, OnInit, ViewChild } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog'

import { ToastrService } from 'ngx-toastr'
import * as ExcelJS from 'exceljs'

import { INameCode } from '../../../../models/report/item-revision-result.interface'
import {
  ICourseInputPayload,
  ISubjectTopicData,
  IUpdateSubjectTopicPayload
} from '../../../../pages/my-courses/my-courses.interface'
import { MyCoursesService } from '../../../../pages/my-courses/my-courses.service'
import { ErrorHandlerService, NameConversionService } from '../../../../services'

import { toolbar } from '../../ck-text-editor/cq-editor-toolbar'
import { AlertComponent } from '../../basic-alert/basic-alert.component'
import { TranslateService } from '@ngx-translate/core'

@Component({
  selector: 'digi-create-subject-topic-popup',
  templateUrl: './create-subject-topic-popup.component.html',
  styleUrls: ['./create-subject-topic-popup.component.css']
})
export class CreateSubjectAndTopicPopupComponent implements OnInit {
  @ViewChild('fileImportInput')
  fileImportInput!: ElementRef<HTMLInputElement>

  name = ''
  code = ''
  content = ''
  topics: INameCode[] = [{ name: '', code: '' }]
  subTopic: INameCode = { name: '', code: '' }
  toolbarOptions = toolbar
  columnHeader = ['name', 'code']

  constructor(
    public dialogRef: MatDialogRef<CreateSubjectAndTopicPopupComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: ISubjectTopicData,
    private myCoursesService: MyCoursesService,
    private nameConversionService: NameConversionService,
    private toastr: ToastrService,
    private errorHandleService: ErrorHandlerService,
    private dialog: MatDialog,
    private translate: TranslateService
  ) {
    console.log(data)
  }

  ngOnInit() {
    if (this.data.actionType === 'update') {
      this.name = this.data.name || ''
      this.code = this.data.code || ''
      this.content = this.data.content || ''
    }
  }

  onClickClose(payload: ICourseInputPayload | IUpdateSubjectTopicPayload | boolean = false) {
    this.dialogRef.close(payload)
  }

  onClickCreateTopic() {
    this.topics.push({ name: '', code: '' })
  }

  onClickRemoveTopic(index: number) {
    if (this.topics.length > 1) {
      this.topics.splice(index, 1)
    }
  }

  get canDisableAddSubjectOrTopic(): boolean {
    if (this.data.type === 'topic') {
      if (this.data.actionType === 'update') {
        return !this.name || !this.code
      }

      return this.topics.some((topic) => !topic.name || !topic.code)
    } else if (this.data.type === 'subject') {
      return !this.name || !this.code
    } else if (this.data.type === 'content') {
      return !this.content
    } else if (this.data.type === 'subTopic') {
      if (this.data.actionType === 'update') {
        return !this?.data?.name || !this?.data?.code
      }

      return !this.subTopic?.name || !this.subTopic?.code
    }

    return true
  }

  getLabelForType({ type, isEdit = false }: { type: string; isEdit?: boolean }): string {
    const labelMap: { [key: string]: string } = {
      topic: 'courseInput.newTopic',
      subject: 'courseInput.newSubject',
      subTopic: 'common.addSubTopic'
    }

    if (isEdit) {
      const labelMap: { [key: string]: string } = {
        topic: 'courseInput.editTopic',
        subject: 'courseInput.editSubject',
        subTopic: 'courseInput.editSubTopic'
      }

      return labelMap[type] || 'common.edit'
    }

    return labelMap[type] || 'courseInput.newContent'
  }

  capitalize({ text }: { text: string }) {
    return this.nameConversionService.convertToPascalCase(text)
  }

  private createSubjectOrTopics() {
    const payload = {
      type: this.data.type,
      courseName: this.data.courseName,
      ...(this.data?.subjectId && { subjectId: this.data.subjectId }),
      ...(this.data?.topicId && { topicId: this.data.topicId })
    }
    if (this.data.type === 'topic' && this.topics.length) {
      const topics = this.topics.map((topic) => {
        return {
          name: this.capitalize({ text: topic?.name || '' }),
          code: topic?.code.toUpperCase() || '',
          nodeId: this.data.nodeId + 1
        }
      })
      Object.assign(payload, { topics })
    } else if (this.data.type === 'subject' && this.name && this.code) {
      Object.assign(payload, {
        name: this.capitalize({ text: this.name }),
        code: this.code.toUpperCase(),
        ...(this.content && { content: this.content }),
        nodeId: this.data.nodeId + 1
      })
    } else if (this.data.type === 'subTopic') {
      const subTopics = [
        {
          name: this.subTopic?.name,
          code: this.subTopic?.code.toUpperCase(),
          nodeId: this.data.nodeId + 1
        }
      ]
      Object.assign(payload, {
        subTopics
      })
    }

    this.myCoursesService.createSubjectOrTopics({ payload }).subscribe(
      ({ message }) => {
        this.toastr.success(message)
        this.onClickClose(payload)
      },
      (err) => this.errorHandleService.errorLog(err)
    )
  }

  private updateSubjectOrTopic() {
    const payload: IUpdateSubjectTopicPayload = {
      type: this.data.type === 'content' ? 'topic' : this.data.type,
      courseName: this.data.courseName,
      topicId: this.data.topicId || '',
      ...(this.data?.subjectId && { subjectId: this.data.subjectId }),
      ...(this.data?.subTopicId && { subTopicId: this.data.subTopicId }),
      ...(this.content && { content: this.content }),
      ...(this.name && { name: this.name }),
      ...(this.code && { code: this.code })
    }

    this.myCoursesService.updateSubjectOrTopic({ payload }).subscribe(
      ({ message }) => {
        this.toastr.success(message)

        this.onClickClose(payload)
      },
      (err) => this.errorHandleService.errorLog(err)
    )
  }

  onClickCreateOrUpdateSubjectTopic() {
    if (this.data.actionType === 'create') {
      return this.createSubjectOrTopics()
    }

    this.updateSubjectOrTopic()
  }

  onEditorChangeText({ editorText }: { editorText: string }) {
    this.content = editorText
  }

  onClickImportExcel() {
    if (this.fileImportInput) {
      this.fileImportInput.nativeElement.click()
    }
  }

  onChangeFileUpload({ files }: { files: FileList }) {
    const [file] = Array.from(files)
    if (!file) {
      return
    }

    const reader = new FileReader()
    const header: string[] = []

    reader.onload = async (event) => {
      try {
        const data = reader.result as ArrayBuffer
        const workbook = new ExcelJS.Workbook()
        await workbook.xlsx.load(data)

        const [worksheet] = workbook.worksheets
        worksheet.getRow(1).eachCell((cell, colNumber) => {
          header[colNumber - 1] = cell.text
        })

        const topics: Record<string, string>[] = []
        worksheet.eachRow({ includeEmpty: false }, (row, rowIndex) => {
          if (rowIndex > 1) {
            const rowData: Record<string, string> = {}
            row.eachCell((cell, colNumber) => {
              rowData[header[colNumber - 1]] = cell.text
            })
            topics.push(rowData)
          }
        })

        // Format and process the imported data
        this.formatImportData({ header, topics })
      } catch (error) {
        this.toastr.error(this.translate.instant(`courseInput.errorReadingExcelFile`))
        this.resetFileInput()
      }
    }

    reader.readAsArrayBuffer(file)
  }

  private formatImportData({
    header,
    topics
  }: {
    header: string[]
    topics: Record<string, string>[]
  }) {
    if (!this.validateHeaderColumn({ headers: header })) {
      this.dialog
        .open(AlertComponent, {
          width: '400px',
          data: { message: this.translate.instant(`courseInput.mismatchedHeaderData`) },
          disableClose: true
        })
        .afterClosed()
        .subscribe(() => {
          this.resetFileInput()
        })
      return
    }

    // Clear existing topics and populate with imported data
    this.topics = topics
      .map((topic) => ({
        name: topic.name?.trim() || '',
        code: topic.code?.trim().toUpperCase() || ''
      }))
      .filter((topic) => topic.name && topic.code) // Filter out empty entries

    if (this.topics.length === 0) {
      this.toastr.warning(this.translate.instant(`courseInput.noValidTopicsFoundInExcelFile`))
      this.topics = [{ name: '', code: '' }] // Reset to default
    } else {
      this.toastr.success(
        this.translate.instant(`courseInput.successfullyImportedTopics`, {
          count: this.topics.length
        })
      )
    }

    this.resetFileInput()
  }

  private validateHeaderColumn({ headers }: { headers: string[] }) {
    return (
      this.columnHeader.length === headers.length &&
      this.columnHeader.every(
        (element, index) => element.trim().toLowerCase() === headers[index].trim().toLowerCase()
      )
    )
  }

  private resetFileInput() {
    if (this.fileImportInput) {
      this.fileImportInput.nativeElement.value = ''
    }
  }
}
