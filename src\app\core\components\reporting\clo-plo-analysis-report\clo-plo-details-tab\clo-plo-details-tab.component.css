.digi-content {
  width: 95%;
  margin: 0 auto;
}

.digi-alert {
  background: #d1f4ff;
  border: 1px solid #6cdcff;
  padding: 6px 16px;
  border-radius: 8px;
}

.digi-set-title {
  font-size: 16px;
  line-height: 19px;
  color: #000000;
}

.digi-alert .material-icons {
  color: #3e95ef;
  font-size: 20px;
}

:host mat-tab-group.digi-exam-tabs::ng-deep > .mat-mdc-tab-header {
  background: transparent !important;
  border-bottom: 0px solid rgba(0, 0, 0, 0.12) !important;
}

:host mat-tab-group.digi-exam-tabs::ng-deep > .mat-mdc-tab-header .mat-mdc-tab {
  color: #3e95ef !important;
  text-transform: uppercase !important;
  opacity: 1;
}

:host mat-tab-group.digi-exam-tabs::ng-deep > .mat-mdc-tab-header .mat-ink-bar {
  display: none;
}

:host mat-tab-group.digi-exam-tabs::ng-deep > .mat-mdc-tab-header .mat-mdc-tab.mdc-tab--active {
  background: white;
  color: rgba(0, 0, 0, 0.87) !important;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
}

:host
  mat-tab-group.digi-exam-tabs::ng-deep
  > .mat-mdc-tab-header
  .mat-mdc-tab.mat-mdc-tab-disabled {
  color: #000000 !important;
  font-weight: 500;
}

.filter-box {
  display: flex;
  flex-direction: column;
}

.filter-name {
  font-size: 12px;
  text-transform: uppercase;
  color: #0064c8;
}

.filter-select {
  background-color: white;
  border: 1px solid #dadada;
  color: #000000;
  box-sizing: border-box;
  border-radius: 16px;
  text-transform: capitalize;
  padding: 5px 8px;
}

.digi-header-container {
  gap: 10px;
}

:host ::ng-deep .mat-mdc-tab .mdc-tab-indicator__content--underline {
  display: none;
}

:host ::ng-deep .mat-mdc-tab .mdc-tab__text-label {
  color: #3e95ef !important;
}

:host ::ng-deep .mat-mdc-tab.mdc-tab--active .mdc-tab__text-label {
  color: #000000de !important;
}

.filter-select {
  width: 150px;
  border: 1px solid #dadada;
  color: #000000;
  background-color: #ffffff;
  box-sizing: border-box;
  border-radius: 16px;
  text-transform: capitalize;
  padding: 5px 8px;
}

.digi-info-icon-container .digi-info-icon {
  --digi-info-icon-size: 20px;
  width: var(--digi-info-icon-size);
  height: var(--digi-info-icon-size);
  line-height: var(--digi-info-icon-size);
  font-size: var(--digi-info-icon-size);
  color: var(--themeColor1);
  text-align: center;
}

:host
  ::ng-deep
  .mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs
  > .mat-mdc-tab-header
  .mat-mdc-tab {
  flex-grow: 0 !important;
}

.digi-student-group-clip-text {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 88px;
  display: inline-block;
}

.digi-clo-title {
  white-space: nowrap;
}

.digi-clo-back-icon {
  color: #2f80ed;
}
