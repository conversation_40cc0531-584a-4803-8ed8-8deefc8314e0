import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild
} from '@angular/core'

import {
  ICloPloParams,
  IConductedOnLocation,
  INameWithCode,
  IPloCurriculum,
  IPloCurriculumDetail,
  IStudentGroupWithCourses
} from '@appcore/app/models'
import { ReportService } from '@appcore/app/pages/reporting'
import { ErrorHandlerService, GlobalService, RouteService } from '@appcore/app/services'
import { PloDetailsComponent } from './plo-details/plo-details.component'
import { CloDetailsComponent } from './clo-details/clo-details.component'
import { UtilService } from '@appcore/app/services/util.service'

@Component({
  selector: 'digi-clo-plo-details-tab',
  templateUrl: './clo-plo-details-tab.component.html',
  styleUrls: ['./clo-plo-details-tab.component.css']
})
export class CloPloDetailsTabsComponent implements OnInit, OnChanges {
  @ViewChild('ploDetails')
  ploDetailsComponent: PloDetailsComponent

  @ViewChild('cloDetails')
  cloDetailsComponent: CloDetailsComponent

  @Input()
  cloPloParams: ICloPloParams

  @Input()
  ploCurriculum: IPloCurriculumDetail

  @Output()
  changeFilterColumn = new EventEmitter<void>()

  @Output()
  changeFilters = new EventEmitter<{
    examTypes: string[]
    examCategories: string[]
    mode?: string
    attemptTypes: string[]
    groups: string[]
  }>()

  selectedType = 'ploAnalysis'
  curriculums: string[] = []
  selectedPloProgram: IPloCurriculum
  ploCurriculumProgram: IPloCurriculumDetail
  assessmentAuthor: string
  examTypes: string[] = []
  selectedExamTypes: string[] = []
  examCategories: Partial<INameWithCode[]> = []
  selectedExamCategories: string[] = []
  conductedLocations: IConductedOnLocation[] = []
  selectedConductedLocations: IConductedOnLocation[] = []
  attemptTypes: string[] = []
  selectedAttemptTypes: string[] = []
  academicYear: string
  hierarchyName: string
  isChooseReport = false
  studentGroups: IStudentGroupWithCourses[] = []
  selectedStudentGroups: IStudentGroupWithCourses[] = []

  constructor(
    private globalService: GlobalService,
    private reportService: ReportService,
    private routeService: RouteService,
    private errorHandler: ErrorHandlerService,
    private utilService: UtilService
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    const { ploCurriculum } = changes

    if (ploCurriculum?.currentValue) {
      this.ploCurriculumProgram = ploCurriculum?.currentValue
      this.assessmentAuthor = this.ploCurriculumProgram?.assessmentAuthors?.shift()

      if (this.ploCurriculumProgram?.ploAnalysis?.programCurriculum?.length) {
        this.selectedPloProgram = this.ploCurriculumProgram?.ploAnalysis?.programCurriculum[0]
        this.ploCurriculumProgram.ploAnalysis.programCurriculum[0].isSelected = true
      } else if (this.selectedType === 'ploAnalysis') {
        this.ploDetailsComponent.refreshPloReport()
      }

      if (this.selectedType === 'cloAnalysis') {
        this.cloDetailsComponent.refreshCloReport()
      }
    }
  }

  ngOnInit(): void {
    this.globalService.setHeader({
      headerName: 'reportingAndAnalytics.currentReportDashboard.reportAnalytical',
      secondHeader: 'reportingAndAnalytics.currentReportDashboard.assessmentResult',
      activeHeader: 'reportingAndAnalytics.currentReportDashboard.allReports'
    })

    this.initRouteParamSubscriber({ isInit: true })
  }

  formatCurriculum({ program }: { program: IPloCurriculum }) {
    return `${program?.program?.code}-${program?.curriculum?.code}:${program?.framework?.name}`
  }

  onClickCurriculum({ program }: { program: IPloCurriculum }) {
    if (program?.isSelected) {
      return
    }

    this.selectedPloProgram = program
    this.ploCurriculumProgram.ploAnalysis.programCurriculum.forEach(
      (curriculum) => (curriculum.isSelected = false)
    )

    this.selectedPloProgram.isSelected = true
  }

  onChangeTab({ index }: { index: number }) {
    this.selectedType = index === 1 ? 'cloAnalysis' : 'ploAnalysis'
  }

  onClickExport() {
    const payload = {
      academicYearStart: this.cloPloParams.startYear,
      academicYearEnd: this.cloPloParams.endYear,
      attemptTypes: this.cloPloParams.attemptTypes,
      examCategories: this.cloPloParams.examCategories,
      examTypes: this.cloPloParams.examTypes,
      groups: this.cloPloParams.groups,
      courses: [
        {
          programId: this.cloPloParams.programId,
          courseName: this.cloPloParams.hierarchyName,
          course: {
            name: this.cloPloParams.courseName,
            code: this.cloPloParams.courseCode
          }
        }
      ],
      ...(this.cloPloParams.mode && { mode: this.cloPloParams.mode })
    }

    this.reportService.exportCloPloReports({
      isDetail: true,
      type: 'single',
      payload
    })
  }

  onClickBack(): void {
    if (this.isChooseReport) {
      this.routeService.transitionToCourseDashboard()
    } else {
      this.routeService.transitionToReportDashboard()
    }
  }

  changeColumnFilter() {
    this.changeFilterColumn.next()
  }

  onChangeFilter({ type, isAll = false }: { type?: string; isAll?: boolean }) {
    if (isAll) {
      switch (type) {
        case 'examType':
          this.selectedExamTypes = this.examTypes
          break

        case 'examCategory':
          this.selectedExamCategories = this.examCategories.map(({ code }) => code)
          break

        case 'conductedLocation':
          this.selectedConductedLocations = this.conductedLocations
          break

        case 'attemptType':
          this.selectedAttemptTypes = this.attemptTypes
          break

        case 'studentGroup':
          this.selectedStudentGroups = this.studentGroups
          break
      }
    }

    const ploCloParams = {
      examTypes:
        this.selectedExamTypes.length === this.examTypes.length ? [] : this.selectedExamTypes,
      examCategories:
        this.selectedExamCategories.length === this.examCategories.length
          ? []
          : this.selectedExamCategories,
      ...(this.selectedConductedLocations?.length === 1 && {
        mode: this.selectedConductedLocations[0].code
      }),
      attemptTypes:
        this.selectedAttemptTypes.length === this.attemptTypes.length
          ? []
          : this.selectedAttemptTypes,
      groups:
        this.selectedStudentGroups?.length === this.studentGroups.length
          ? []
          : this.selectedStudentGroups.map(({ code }) => code)
    }

    this.changeFilters.next(ploCloParams)
  }

  getToolTipText() {
    return this.utilService.getCourseDetails({
      hierarchy: this.ploCurriculumProgram?.hierarchy
    })
  }

  private initRouteParamSubscriber({ isInit = false }: { isInit?: boolean }) {
    this.conductedLocations = this.reportService.conductedLocations

    this.reportService
      .getConsolidateReportFilter({
        courseName: this.cloPloParams.hierarchyName,
        startYear: this.cloPloParams.startYear,
        endYear: this.cloPloParams.endYear,
        ...(!isInit && { groups: this.selectedStudentGroups.map(({ code }) => code) })
      })
      .subscribe(
        ({ data }) => {
          this.examTypes = data?.examTypes
          this.attemptTypes = data?.attemptTypes
          this.examCategories = data?.examCategories

          if (isInit) {
            this.studentGroups = data?.groups
          } else {
            this.onChangeFilter({ type: 'studentGroup' })
          }
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  getSelectedStudentGroups({ groups }: { groups: IStudentGroupWithCourses[] }) {
    return groups?.map((group) => group?.code)?.join(', ') || ''
  }

  getSelectedStudentGroupsInfo({ groups }: { groups: IStudentGroupWithCourses[] }) {
    if (!groups?.length) {
      return ''
    }

    const courses = groups.flatMap((group) => group?.courses ?? [])
    const uniqueCourses = [...new Set(courses.filter(Boolean))]
    return uniqueCourses.length ? uniqueCourses.join(', ') : ''
  }

  onChangeStudentGroups() {
    this.initRouteParamSubscriber({})
  }
}
