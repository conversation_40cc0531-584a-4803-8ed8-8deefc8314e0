// Angular dependencies
import { Component, Inject, OnInit } from '@angular/core'
import {
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators
} from '@angular/forms'
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog'
import { ActivatedRoute } from '@angular/router'

import { TranslateService } from '@ngx-translate/core'
import { ToastrService } from 'ngx-toastr'
import { allowAlphanumericAndDot } from 'src/app/core/pattern'

import { BasicListService } from '@appcore/app/pages/settings/basic-list/basic-list.service'
import {
  AuthService,
  ErrorHandlerService,
  GlobalService,
  LoadingScreenService,
  SettingsService,
  UtilService
} from '@appcore/app/services'
import { IEditNode, INewNode, IProgram } from '@appcore/models/global-settings'
import { IResponse } from '@appcore/models/index'
import { TreeFunctionService } from '@appcore/services/tree-function.service'
import { toolbar } from '@appcore/shared/ck-text-editor/cq-editor-toolbar'
import { ConfirmDialogComponent } from '@appcore/shared/shared-component'
import { TreeService } from '@appShared/services/tree/tree.service'

interface ITreeNode {
  _id?: string
  name: string
  code: string
  type: string
  nodeId: number
  parentKey?: string
  children?: ITreeNode[]
}

@Component({
  selector: 'digi-basic-module',
  templateUrl: './module.component.html',
  styleUrls: ['./module.component.css']
})
export class BasicModuleComponent implements OnInit {
  manage = false
  dataSource: IProgram[] = []
  form: UntypedFormGroup
  programs: IProgram[] = []
  selectedProgram: IProgram
  showManageBtn = false
  isLoading = true
  treeState: 'EXPAND' | 'COLLAPSE' | '' = 'COLLAPSE'
  isInit = true
  newNode: INewNode
  editNode: IEditNode
  deleteId: string

  constructor(
    private errorHandler: ErrorHandlerService,
    public globalservis: GlobalService,
    private treeService: TreeFunctionService,
    public dialog: MatDialog,
    private toaster: ToastrService,
    private ts: TreeService,
    private fb: UntypedFormBuilder,
    private basicListService: BasicListService,
    private activatedRoute: ActivatedRoute,
    private authService: AuthService,
    private translateService: TranslateService,
    private settingsService: SettingsService,
    private loaderService: LoadingScreenService
  ) {
    this.getForm()
    this.setProgram({ type: 'init' })
  }

  ngOnInit() {
    const { moduleName, pageName = '' } = this.activatedRoute.snapshot.data

    // eslint-disable-next-line @typescript-eslint/dot-notation
    const currentUrl = this.activatedRoute.snapshot['_routerState'].url

    const params = {
      tabType: 'programs',
      currentUrl,
      modules: this.authService.currentRoleModule?._role?.modules,
      moduleName,
      pageName
    }
    this.showManageBtn = this.basicListService.checkHasPermissionForActions(params)
  }

  getForm() {
    this.form = this.fb.group({
      name: new UntypedFormControl('', [Validators.required]),
      code: new UntypedFormControl('', [
        Validators.required,
        Validators.pattern(allowAlphanumericAndDot)
      ])
    })
  }

  onClickManage() {
    this.globalservis.manage = !this.globalservis.manage
    this.form.reset()
  }

  private setProgram({ type }: { type: string }) {
    this.treeService.expandedIds = []
    this.basicListService
      .getPrograms()
      .subscribe(
        ({ data }) => {
          this.programs = data

          if (this.programs?.length) {
            this.selectedProgram =
              type === 'add' ? this.programs[this.programs.length - 1] : this.programs[0]
            this.setProgramDetails()
          }
        },
        (err) => this.errorHandler.errorLog(err)
      )
      .add(() => {
        if (!this.programs?.length) {
          this.isLoading = false
        }
      })
  }

  private setProgramDetails() {
    this.resetNode()
    this.dataSource = []
    this.isLoading = true
    this.basicListService
      .getProgramDetails({ programId: this.selectedProgram._id })
      .subscribe(
        ({ data }) => {
          this.dataSource = [data]
          this.treeState = 'COLLAPSE'
          this.isInit = true
        },

        (err) => this.errorHandler.errorLog(err)
      )

      .add(() => {
        this.isLoading = false
      })
  }

  onChangeValue({ program }: { program: IProgram }) {
    this.selectedProgram = program
    this.treeState = 'COLLAPSE'
    this.isInit = true
    this.treeService.expandedIds = []
    this.setProgramDetails()
  }

  addNewTop() {
    if (!this.form.value.name) {
      this.toaster.error(
        this.translateService.instant('settings.basicList.basicModule.itemNameRequired')
      )
      return
    }

    if (!this.form.value.code) {
      this.toaster.error(
        this.translateService.instant('settings.basicList.basicModule.itemCodeRequired')
      )
      return
    }

    if (isNaN(this.treeService.findNodeMaxId(this.dataSource))) {
      this.toaster.error(
        this.translateService.instant('settings.basicList.basicModule.treeNodeIdMissing')
      )
      return
    }

    const codeConvert = this.form.value.code.toUpperCase()

    const params: ITreeNode = {
      name: this.form.value.name.trim(),
      code: codeConvert.toUpperCase(),
      type: 'program',
      nodeId: this.dataSource.length > 0 ? this.treeService.findNodeMaxId(this.dataSource) + 1 : 1,
      children: []
    }

    this.globalservis
      .postFormService(`${this.basicListService.basicListUrl}/programs/addparent`, params)
      .then((res: IResponse) => {
        this.setProgram({ type: 'add' })
        this.form.reset()
        this.toaster.success(
          this.translateService.instant(
            'settings.basicList.basicModule.programsCreatedSuccessfully'
          )
        )
      })
      .catch((err) => {
        this.errorHandler.errorLog(err)
      })
  }

  treeData(treeDataValue) {
    this.loaderService.show()

    setTimeout(() => this.treeDataPopup(treeDataValue))
  }

  private treeDataPopup(treeDataValue) {
    let isNew: boolean
    const currentNode = treeDataValue.currentNode
    const action = treeDataValue.action
    const isTop = treeDataValue.isTop
    const maxNodeId = this.treeService.findNodeMaxId(this.dataSource)
    let parentIds: string[]

    if (currentNode.type === 'course') {
      parentIds = [
        ...this.ts.findParents('code', currentNode.parentKey, this.dataSource),
        currentNode._id
      ]
    } else {
      parentIds = this.ts.findParents('_id', currentNode._id, this.dataSource)
    }

    this.isInit = false
    this.loaderService.hide()
    isNew = action === 'add' || action === 'subTopic'

    if (
      action === 'add' ||
      action === 'edit' ||
      action === 'addEditContent' ||
      action === 'subTopic'
    ) {
      this.dialog
        .open(ModulePopComponent, {
          data: {
            name: isNew ? '' : currentNode.name,
            content: currentNode?.content,
            type: action === 'subTopic' ? 'subTopic' : currentNode.type,
            code: isNew ? '' : currentNode.code,
            action,
            isAddContent: treeDataValue.isAddContent
          },
          disableClose: true,
          panelClass: 'dialog-fix',
          direction: this.settingsService.getOptions().dir
        })
        .afterClosed()
        .subscribe((result) => {
          if (result) {
            if (action === 'add' || action === 'subTopic') {
              if (isNaN(maxNodeId)) {
                this.toaster.error(
                  this.translateService.instant('settings.basicList.basicModule.treeNodeIdMissing')
                )
                return
              }

              let postVal

              if (isTop && action !== 'subTopic') {
                postVal = {
                  name: result.name.trim(),
                  content: result?.content || '',
                  type: result.type,
                  code: result.code.toUpperCase(),
                  nodeId: this.dataSource.length > 0 ? maxNodeId + 1 : 1,
                  children: []
                }
              } else {
                postVal = {
                  name: result.name.trim(),
                  content: result?.content || '',
                  type: result.type,
                  code: result.code.toUpperCase(),
                  nodeId: maxNodeId + 1,
                  parentKey: currentNode.code,
                  children: []
                }
                for (let i = 0; i < parentIds.length; i++) {
                  postVal['l' + (i + 1)] = parentIds[i]
                }
              }

              this.globalservis
                .postFormService(`${this.basicListService.basicListUrl}/programs/addchild`, postVal)
                .then(() => {
                  this.setProgramDetails()
                  this.form.reset()
                  this.toaster.success(this.translateService.instant('common.addedSuccessfully'))
                })
                .catch((err) => {
                  this.errorHandler.errorLog(err)
                })
            }

            if (action === 'edit' || action === 'addEditContent') {
              const params = {
                name: result.name.trim(),
                content: result?.content || '',
                code: result.code.toUpperCase(),
                type: result.type
              }

              for (let i = 0; i < parentIds.length; i++) {
                params['l' + (i + 1)] = parentIds[i]
              }

              this.globalservis
                .putService(`${this.basicListService.basicListUrl}/programs/edit`, params)
                .then((res: IResponse) => {
                  this.setProgramDetails()
                  this.toaster.success(
                    this.translateService.instant(
                      action === 'edit' ? 'common.updatedSuccessfully' : 'common.addedSuccessfully'
                    )
                  )
                })
                .catch((err) => {
                  this.errorHandler.errorLog(err)
                })
            }
          }
        })
    }

    if (action === 'delete') {
      const params = {}
      for (let i = 0; i < parentIds.length; i++) {
        params['l' + (i + 1)] = parentIds[i]
      }

      Object.assign(params, {
        type: currentNode.type,
        code: currentNode.code,
        name: currentNode.name
      })

      if (currentNode.children.length > 0) {
        this.toaster.error(
          this.translateService.instant('settings.basicList.basicModule.deleteChildError')
        )
      } else {
        this.dialog
          .open(ConfirmDialogComponent, {
            data: {
              title: 'assessment.createItem.archive',
              message: `${this.translateService.instant('common.archiveMessage')} ${
                currentNode?.name
              }`,
              actionBtn: 'assessment.createItem.archive',
              skipBreak: true
            },
            disableClose: true,
            panelClass: 'dialog-fix',
            direction: this.settingsService.getOptions().dir
          })
          .afterClosed()
          .subscribe((result) => {
            if (result) {
              this.globalservis
                .deleteService(`${this.basicListService.basicListUrl}/programs/archive`, params)
                .then((res: IResponse) => {
                  currentNode.type === 'program'
                    ? this.setProgram({ type: 'archived' })
                    : this.setProgramDetails()
                  this.toaster.success(
                    this.translateService.instant(
                      `settings.basicList.basicModule.${currentNode.type}ArchivedSuccessfully`
                    )
                  )
                })
                .catch((err) => {
                  this.errorHandler.errorLog(err)
                })
            }
          })
      }
    }
  }

  onRefreshProgram() {
    this.setProgramDetails()
  }

  onClickToggle({ treeState }: { treeState: 'EXPAND' | 'COLLAPSE' | '' }) {
    if (this.treeState === treeState) {
      return
    }

    this.isLoading = true
    this.treeState = treeState

    setTimeout(() => (this.isLoading = false), 2000)
  }

  private resetNode() {
    if (this.newNode) {
      this.newNode.parentId = ''
    }
    if (this.editNode) {
      this.editNode._id = ''
    }
    if (this.deleteId) {
      this.deleteId = ''
    }
  }

  public hasError(controlName: string, errorName: string) {
    return this.form.controls[controlName].hasError(errorName)
  }
}

@Component({
  selector: 'digi-module-pop',
  templateUrl: './module-pop.component.html',
  styleUrls: ['./module.component.css']
})
export class ModulePopComponent implements OnInit {
  form: UntypedFormGroup
  dataSource: {
    name: string
    content: string
    type: string
    code: string
    action: string
    dropDownData?: []
  }
  isAddTopicContent = false
  itemTypes
  toolbarOptions = toolbar
  isDisabled = true

  ngOnInit(): void {
    if (this.data.action === 'add' || this.data.action === 'subTopic') {
      this.getData()
    }
  }

  constructor(
    public globalservis: GlobalService,
    private fb: UntypedFormBuilder,
    public dialogRef: MatDialogRef<ModulePopComponent>,
    @Inject(MAT_DIALOG_DATA) public data,
    private utilService: UtilService
  ) {
    this.isAddTopicContent = data?.isAddContent
    this.dataSource = data
    this.form = this.fb.group({
      name: [this.dataSource.name ? this.dataSource.name : '', [Validators.required]],
      code: [this.dataSource.code ? this.dataSource.code : '', [Validators.required]],
      content: [this.dataSource?.content || ''],
      type: [this.dataSource.type ? this.dataSource.type : '', [Validators.required]]
    })
  }

  getData() {
    const type = this.data?.action === 'subTopic' ? 'subTopic' : this.data.type.toLowerCase()
    this.globalservis
      .getService(this.globalservis.config.program + 'type?parentType=' + type)
      .then((res: IResponse) => {
        this.itemTypes = res.data
      })
      .catch((err) => {
        this.errorLog(err)
      })
  }

  formatItemTypes({ key }: { key: string }) {
    return `settings.basicList.basicModule.${key}`
  }

  errorLog(err) {
    if (err.status === 401) {
      this.globalservis.showError(err.error.message, '')
    }
    if (err.status === 422) {
      this.globalservis.showError(err.error.errors[0].msg, '')
    }
    if (err.status === 404) {
      this.globalservis.showError(err.error.message, '')
    }
    if (err.status === 409) {
      this.globalservis.showError(err.error.message, '')
    }
  }

  onSubmit() {
    if (!this.form.valid) {
      return
    }

    const formValue = this.form.value
    formValue.name = formValue.name.trim().replace(/\b\w/g, (c) => c.toUpperCase())
    formValue.code = formValue.code.toUpperCase()

    this.dialogRef.close(this.form.value)
  }

  onDelete() {
    this.dialogRef.close(true)
  }

  enterEvent(event) {
    this.onSubmit()
    event.preventDefault()
  }

  close() {
    this.dialogRef.close()
  }

  getTranslatedSurveyCode({ code }: { code: string }) {
    if (!code) {
      return
    }

    if (this.data.type === 'itemType') {
      return this.utilService.getTranslateItemType({ itemType: code }).code
    }

    return this.utilService.getTranslatedSurveyCodeLabel({ surveyLabel: code })
  }

  public hasError(controlName: string, errorName: string) {
    return this.form.controls[controlName].hasError(errorName)
  }

  setTopicContent(content: string) {
    if (content) {
      this.isDisabled = false
      return this.form.patchValue({
        content
      })
    }

    this.isDisabled = true
  }
}
