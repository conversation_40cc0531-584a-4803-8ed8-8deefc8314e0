@import url('../../share.css');

.mainCont {
  width: 100%;
  height: 100vh;
  background-color: #f5fbff;
}

.subCont {
  width: 100%;
  max-width: 1200px;
  margin: 0px auto;
  padding: 15px 0px 0px 0px;
}

.treeCont {
  width: 100%;
}

.treeSubCont {
  width: 100%;
  max-width: 300px;
}

.mat-mdc-form-field {
  margin-right: 4px;
}

.titleAndCont {
  background-color: white;
}

.title {
  padding: 15px;
  border-bottom: 1px solid #dddddd;
}

.example-tree ul,
.example-tree li {
  margin-top: 0;
  margin-bottom: 0;
  list-style-type: none;
}

.treeModule {
  overflow: auto;
  padding: 0px 10px 0px 0px;
  box-sizing: border-box;
}

/* mat-icon {
  color: #0000008a;
} */

/* .optionarrow{
  color: #008dff;
}

button mat-icon {
  color: currentColor;
} */

.lefticon {
  color: rgba(0, 0, 0, 0.54);
}

.optionarrow {
  color: #3e95ef;
}

/* .button.secondary:hover {
  color: #0064c8;
} */

mat-nested-tree-node mat-tree-node .mat-mdc-icon-button,
mat-nested-tree-node mat-nested-tree-node .mat-icon-button {
  visibility: visible;
  color: #0000008a;
  cursor: default;
}

/* mat-tree-node > li,
mat-nested-tree-node > li > div {
  border-bottom: 1px solid #0000001f;
} */

.none {
  visibility: hidden;
}

.treeText {
  padding: 5px 0px;
  word-break: break-word;
  white-space: normal;
}

.mat-tree-node,
.nestedTreeWrap {
  min-height: 40px;
}

.mat-tree {
  min-width: 400px;
}

.subcode {
  min-width: 110px;
  max-width: 110px;
  padding: 0 20px;
  box-sizing: border-box;
}

mat-tree-node > li .buttonArea,
mat-nested-tree-node > li > div .buttonArea {
  /* visibility: hidden; */
  min-width: 230px;
  min-height: 38px;
  width: 260px;
}

mat-tree-node > li:hover .buttonArea.visible,
mat-nested-tree-node > li > div:hover .buttonArea.visible {
  visibility: visible;
}

mat-nested-tree-node > example-tree li {
  margin-left: -40px;
}

.digi-button-width-fix {
  width: 200px;
}

/* TODO(mdc-migration): The following rule targets internal classes of progress-bar that may no longer apply for the MDC version. */
:host::ng-deep .mat-progress-bar-buffer {
  background: #e4e8eb;
}

.digi-ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  max-height: 50px;
}

.digi-icon-info-container {
  & .digi-icon-info {
    --mat-icon-size: 20px;
    height: var(--mat-icon-size);
    line-height: var(--mat-icon-size);
    width: var(--mat-icon-size);
    text-align: center;
    font-size: calc(var(--mat-icon-size) * 0.9);
    color: #4d4d4d;
  }
}
