<mat-tree
  [dataSource]="flatTreeDataSource"
  [treeControl]="treeControl"
  class="example-tree mat-tree-position"
  [class.showButtons]="manage"
>
  <!-- Without Children -->
  <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding>
    <li class="mat-tree-node" fxLayout="row">
      <div fxLayout="row" fxLayoutAlign="none none" style="max-width: 300px">
        <button mat-icon-button matTreeNodeToggle [attr.aria-label]="'toggle ' + node.Name">
          <mat-icon class="mat-icon-rtl-mirror lefticon"> subdirectory_arrow_right </mat-icon>
        </button>
        <div fxLayout="column">
          <div class="treeText">{{ node?.name }}</div>
          <digi-ck-text-editor
            *ngIf="node?.type === 'topic' && node?.content"
            class="treeText digi-ellipsis"
            [content]="node?.content"
            [isEditable]="false"
            [options]="toolbarOptions.noToolbar"
          ></digi-ck-text-editor>
          <span
            *ngIf="node?.type === 'topic' && node?.content"
            class="digi-blue digi-cursor digi-pl-10 digi-pt-10 digi-fw-500"
            (click)="onClickViewMore({ content: node?.content, topic: node?.name })"
          >
            {{ 'common.viewMore' | translate }}
          </span>
        </div>
      </div>
      <div class="digi-icon-info-container digi-ml-5">
        <mat-icon
          class="digi-icon-info"
          placement="top"
          tooltipClass="digi-tooltip-multiline-left"
          [tooltip]="getHierarchyType({ type: node?.type }) | translate | titlecase"
        >
          info
        </mat-icon>
      </div>

      <span fxFlex></span>
      <div fxLayout="row" fxLayoutAlign="none center">
        <div class="subcode digi-text-clip" maxWidth="50vw" [tooltip]="node?.code">
          {{ node?.code }}
        </div>
        <div class="buttonArea" fxLayout="row" fxLayoutAlign="none center" [class.visible]="manage">
          <div fxLayout="column">
            <div fxLayout="row">
              <div class="digi-pt-5">
                <mat-icon class="mat-icon-rtl-mirror optionarrow">
                  subdirectory_arrow_right
                </mat-icon>
              </div>
              <digi-button
                *ngIf="node?.type === 'topic'"
                style="margin: 0 0 0 0"
                (click)="openPopUp(node, 'addEditContent', false, true)"
                button="secondary"
                [uppercase]="true"
                [transparent]="true"
                [hover]="true"
              >
                {{
                  !node?.content
                    ? ('settings.basicList.basicModule.addContent' | translate)
                    : ('settings.basicList.basicModule.editContent' | translate)
                }}
              </digi-button>
              <ng-container *digiAccessControl="'actions_addChild^tabs_programs'">
                <digi-button
                  style="margin: 0 0 0 0"
                  *ngIf="!['slo', 'topic', 'subTopic'].includes(node.type)"
                  (click)="openPopUp(node, 'add', false)"
                  button="secondary"
                  [uppercase]="true"
                  [transparent]="true"
                  [hover]="true"
                >
                  {{ 'common.add' | translate }}
                </digi-button>
              </ng-container>
              <ng-container *digiAccessControl="'actions_edit^tabs_programs'">
                <digi-button
                  style="margin: 0px 0px 0px 0px"
                  (click)="openPopUp(node, 'edit', false)"
                  button="secondary"
                  [uppercase]="true"
                  [transparent]="true"
                  [hover]="true"
                >
                  {{ 'common.edit' | translate }}
                </digi-button>
              </ng-container>
              <digi-button
                (click)="openPopUp(node, 'delete', false)"
                button="secondary"
                [uppercase]="true"
                style="margin: 0px 0px 0px 0px"
                [transparent]="true"
                [hover]="true"
                *digiAccessControl="'actions_archive^tabs_programs'"
              >
                {{ 'assessment.createItem.archive' | translate }}
              </digi-button>
            </div>

            <div>
              <digi-button
                *ngIf="node?.type === 'topic'"
                style="margin: 0 0 0 0"
                class="digi-pl-25"
                (click)="openPopUp(node, 'subTopic', true, false, true)"
                button="secondary"
                [uppercase]="true"
                [transparent]="true"
                [hover]="true"
              >
                {{ 'common.add' | translate }}
              </digi-button>
            </div>

            <div *digiAccessControl="'actions_topicUpload^tabs_programs'" fxLayout="row">
              <span style="width: 25px"></span>
              <digi-button
                style="margin: 0"
                *ngIf="checkProgramType({ type: node?.type })"
                button="secondary"
                [uppercase]="true"
                [transparent]="true"
                [hover]="true"
                (click)="onClickUploadTopic({ currentNode: node })"
              >
                {{ 'common.upload' | translate }}
              </digi-button>
              <input
                type="file"
                style="display: none"
                #fileImportInput1
                name="File Upload"
                id="txtFileUpload1"
                (change)="onChangeFileUpload({ files: $event?.target?.files })"
                accept=".xlsx"
              />
            </div>
          </div>
        </div>
      </div>
    </li>
    <div class="lineCont">
      <div class="line"></div>
    </div>
  </mat-tree-node>

  <!-- With Children -->
  <mat-tree-node *matTreeNodeDef="let node; when: hasChild" matTreeNodePadding>
    <li>
      <div class="mat-tree-node" fxLayout="column">
        <div fxLayout="row" fxLayoutAlign="none center" class="nestedTreeWrap">
          <div fxLayout="row" style="max-width: 300px">
            <button
              class="arrow"
              mat-icon-button
              matTreeNodeToggle
              [attr.aria-label]="'toggle ' + node.Name"
            >
              <mat-icon class="mat-icon-rtl-mirror lefticon">
                {{ treeControl.isExpanded(node) ? 'expand_more' : 'chevron_right' }}
              </mat-icon>
            </button>
            <div fxLayout="column">
              <div class="treeText">{{ node?.name }}</div>
              <div
                *ngIf="node?.content"
                class="treeText digi-ellipsis"
                [innerHTML]="node?.content"
              ></div>
              <span
                *ngIf="node?.type === 'topic' && node?.content"
                class="digi-blue digi-cursor digi-pl-10 digi-pt-10 digi-fw-500"
                (click)="onClickViewMore({ content: node?.content, topic: node?.name })"
              >
                {{ 'common.viewMore' | translate }}
              </span>
            </div>
          </div>
          <div class="digi-icon-info-container digi-ml-5">
            <mat-icon
              class="digi-icon-info"
              placement="top"
              tooltipClass="digi-tooltip-multiline-left"
              [tooltip]="getHierarchyType({ type: node?.type }) | translate | titlecase"
            >
              info
            </mat-icon>
          </div>

          <span fxFlex></span>
          <div fxLayout="row" class="digi-txt-center" fxLayoutAlign="none center">
            <div class="subcode digi-text-clip" maxWidth="50vw" [tooltip]="node?.code">
              {{ node?.code }}
            </div>

            <div class="buttonArea" fxLayoutAlign="none center" [class.visible]="manage">
              <div fxLayout="column">
                <div fxLayout="row">
                  <div class="digi-pt-5">
                    <mat-icon class="mat-icon-rtl-mirror optionarrow">
                      subdirectory_arrow_right
                    </mat-icon>
                  </div>

                  <ng-container *digiAccessControl="'actions_addChild^tabs_programs'">
                    <digi-button
                      style="margin: 0 0 0 0"
                      *ngIf="!['slo', 'subTopic', 'topic'].includes(node?.type)"
                      (click)="openPopUp(node, 'add', false)"
                      button="secondary"
                      [uppercase]="true"
                      [transparent]="true"
                      [hover]="true"
                    >
                      {{ 'common.add' | translate }}
                    </digi-button>
                  </ng-container>

                  <digi-button
                    *ngIf="node?.type === 'topic'"
                    style="margin: 0 0 0 0"
                    (click)="openPopUp(node, 'addEditContent', false, true)"
                    button="secondary"
                    [uppercase]="true"
                    [transparent]="true"
                    [hover]="true"
                  >
                    {{
                      !node?.content
                        ? ('settings.basicList.basicModule.addContent' | translate)
                        : ('settings.basicList.basicModule.editContent' | translate)
                    }}
                  </digi-button>

                  <digi-button
                    style="margin: 0px 0px 0px 0px"
                    (click)="openPopUp(node, 'edit', false)"
                    button="secondary"
                    [uppercase]="true"
                    [transparent]="true"
                    [hover]="true"
                    *digiAccessControl="'actions_edit^tabs_programs'"
                  >
                    {{ 'common.edit' | translate }}
                  </digi-button>

                  <digi-button
                    (click)="openPopUp(node, 'delete', false)"
                    button="secondary"
                    [uppercase]="true"
                    style="margin: 0px 0px 0px 0px"
                    [transparent]="true"
                    [hover]="true"
                    *digiAccessControl="'actions_archive^tabs_programs'"
                  >
                    {{ 'assessment.createItem.archive' | translate }}
                  </digi-button>
                </div>

                <div>
                  <digi-button
                    *ngIf="node?.type === 'topic'"
                    style="margin: 0 0 0 0"
                    class="digi-pl-25"
                    (click)="openPopUp(node, 'subTopic', true, false, true)"
                    button="secondary"
                    [uppercase]="true"
                    [transparent]="true"
                    [hover]="true"
                  >
                    {{ 'common.add' | translate }}
                  </digi-button>
                </div>

                <div *digiAccessControl="'actions_topicUpload^tabs_programs'" fxLayout="row">
                  <span style="width: 25px"></span>
                  <digi-button
                    style="margin: 0"
                    *ngIf="checkProgramType({ type: node?.type })"
                    button="secondary"
                    [uppercase]="true"
                    [transparent]="true"
                    [hover]="true"
                    (click)="onClickUploadTopic({ currentNode: node })"
                  >
                    {{ 'common.upload' | translate }}
                  </digi-button>
                  <input
                    type="file"
                    style="display: none"
                    #fileImportInput
                    name="File Upload"
                    id="txtFileUpload"
                    (change)="onChangeFileUpload({ files: $event?.target?.files })"
                    accept=".xlsx"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="lineCont">
          <div class="line"></div>
        </div>
      </div>
      <ul *ngIf="treeControl.isExpanded(node)">
        <ng-container matTreeNodeOutlet></ng-container>
      </ul>
    </li>
    <div class="lineCont">
      <div class="line"></div>
    </div>
  </mat-tree-node>
</mat-tree>
