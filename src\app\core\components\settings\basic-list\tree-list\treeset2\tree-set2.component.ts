import { FlatTreeControl } from '@angular/cdk/tree'
import { HttpClient } from '@angular/common/http'
import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
  ViewChild
} from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree'

import { TranslateService } from '@ngx-translate/core'
import { Observable } from 'rxjs'
import { first } from 'rxjs/operators'
import * as ExcelJS from 'exceljs'

import { TreeService } from '@appcore/app/core/services/tree/tree.service'
import { IItemContent, IMappingTreeFlatNode, IResponse } from '@appcore/app/models'
import { BasicListService } from '@appcore/app/pages/settings/basic-list/basic-list.service'
import { ErrorHandlerService, GlobalService, SettingsService } from '@appcore/app/services'
import { IProgram, ITopic, ITopicUploadPayload } from '@appcore/models/global-settings'
import { TreeFunctionService } from '@appcore/services/tree-function.service'
import { toolbar } from '@appcore/shared/ck-text-editor/cq-editor-toolbar'

import { AlertComponent } from '../../../../basic-alert/basic-alert.component'
import { ViewMorePopupComponent } from '../view-more-popup/view-more-popup.component'

@Component({
  selector: 'digi-tree-basic-model-two',
  templateUrl: './tree-set2.component.html',
  styleUrls: ['./tree-set2.component.css']
})
export class TreeSet2Component implements OnChanges {
  @Input()
  dataSource: IProgram[] = []

  @Input()
  manage: boolean

  @Input()
  treeState: 'COLLAPSE' | 'EXPAND' = 'COLLAPSE'

  @Input()
  isInit = true

  @Input()
  selectedProgram: IProgram

  @Output()
  treeData = new EventEmitter<object>()

  @Output()
  changeTreeState = new EventEmitter<'COLLAPSE' | 'EXPAND' | ''>()

  @Output()
  refreshProgramsData = new EventEmitter<boolean>()

  @ViewChild('fileImportInput1')
  fileImportInput: ElementRef<HTMLInputElement>

  toolbarOptions = toolbar

  constructor(
    public dialog: MatDialog,
    private ts: TreeService,
    private globalService: GlobalService,
    private errorHandler: ErrorHandlerService,
    private httpClient: HttpClient,
    private treeService: TreeFunctionService,
    private basicListService: BasicListService,
    private translateService: TranslateService,
    private settingsService: SettingsService
  ) {}

  transformer = (node: IProgram, level: number) => {
    return {
      _id: node?._id,
      expandable: !!node.children && node.children.length > 0,
      name: node.name,
      content: node?.content,
      code: node?.code,
      parentKey: node?.parentKey,
      type: node?.type,
      children: node?.children,
      level
    }
  }
  treeControl = new FlatTreeControl<IMappingTreeFlatNode>(
    (node) => node.level,
    (node) => node.expandable
  )
  treeFlattener = new MatTreeFlattener(
    this.transformer,
    (node) => node.level,
    (node) => node.expandable,
    (node) => node.children
  )
  flatTreeDataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener)
  columnHeader = ['name', 'code']
  currentNode: IProgram
  postVal: ITopicUploadPayload

  hasChild = (_: number, node) => node?.expandable

  ngOnChanges(changes: SimpleChanges) {
    const { dataSource, treeState } = changes

    if (treeState?.currentValue) {
      if (!this.treeControl) {
        return
      }

      if (treeState?.currentValue === 'EXPAND') {
        this.treeControl?.expandAll()
      } else {
        this.treeControl?.collapseAll()
        this.treeControl?.expand(this.treeControl?.dataNodes?.[0])
      }
    }

    if (dataSource) {
      if (!dataSource.currentValue?.length) {
        return
      }
      this.formTree({ programs: dataSource?.currentValue })
    }
  }

  onClickViewMore({ content, topic }: { content: IItemContent; topic: string }) {
    this.dialog.open(ViewMorePopupComponent, {
      width: '50%',
      minWidth: '500px',
      maxHeight: 'calc(100vh - 50px)',
      data: {
        topic,
        content
      },
      disableClose: true,
      panelClass: ['dialog-padding-top-none', 'dialog-fix'],
      direction: this.settingsService.getOptions().dir
    })
  }

  openPopUp(
    currentNode?,
    action?: string,
    isTop?: boolean,
    isAddContent = false,
    isSubTopic = false
  ) {
    this.treeService.expandedIds = this.getPreviousTreeState()
    this.treeService.parentId = currentNode._id

    if (action === 'edit') {
      // To validate curriculum/course
      currentNode.programId = this.selectedProgram._id
      this.basicListService.validateProgram(currentNode).subscribe(
        () => this.emitOpenPopup({ currentNode, action, isTop, isAddContent, isSubTopic }),
        (error) => this.errorHandler.errorLog(error)
      )
    } else {
      this.emitOpenPopup({ currentNode, action, isTop, isAddContent, isSubTopic })
    }
  }

  private emitOpenPopup({
    currentNode,
    action,
    isTop,
    isAddContent = false,
    isSubTopic = false
  }: {
    currentNode?: IProgram
    action?: string
    isTop?: boolean
    isAddContent?: boolean
    isSubTopic?: boolean
  }) {
    this.treeData.emit({
      currentNode,
      action,
      isTop,
      isAddContent,
      isSubTopic
    })
  }

  formTree({ programs }: { programs: IProgram[] }) {
    if (!programs.length) {
      return
    }

    this.flatTreeDataSource.data = programs
    if (this.treeService.expandedIds.length) {
      return this.setExpandNode()
    }

    if (this.isInit) {
      return this.treeControl.expand(this.treeControl.dataNodes[0])
    }
  }

  onClickUploadTopic({ currentNode }: { currentNode: IProgram }) {
    document.getElementById('txtFileUpload1').click()
    this.currentNode = currentNode
  }

  onChangeFileUpload({ files }: { files: FileList }) {
    const file = files[0]
    const reader = new FileReader()
    const header: string[] = []

    reader.onload = async (event) => {
      try {
        const data = reader.result as ArrayBuffer
        const workbook = new ExcelJS.Workbook()
        await workbook.xlsx.load(data)

        const worksheet = workbook.worksheets[0]
        worksheet.getRow(1).eachCell((cell, colNumber) => {
          header[colNumber - 1] = cell.text
        })

        const topics = []
        worksheet.eachRow({ includeEmpty: false }, (row, rowIndex) => {
          if (rowIndex > 1) {
            const rowData: Record<string, string> = {}
            row.eachCell((cell, colNumber) => {
              rowData[header[colNumber - 1]] = cell.text
            })
            topics.push(rowData)
          }
        })

        // Format and process the imported data
        this.formatImportData({ header, topics })
      } catch (error) {
        console.error('Error reading Excel file:', error)
        this.resetFileInput()
      }
    }

    reader.readAsArrayBuffer(file) // Read the file as ArrayBuffer for ExcelJS
  }

  formatImportData({ header, topics }: { header: string[]; topics: ITopic[] }) {
    if (!this.validateHeaderColumn({ headers: header })) {
      this.dialog
        .open(AlertComponent, {
          width: '400px',
          data: { message: 'Mismatched / Invalid header Data' },
          disableClose: true
        })
        .afterClosed()
        .subscribe(() => {
          this.resetFileInput()
        })

      return
    }

    const payload = this.getPayload({ currentNode: this.currentNode, topics })
    this.uploadTopics({ payload })
  }

  // Utility method to reset the file input
  resetFileInput() {
    if (this.fileImportInput) {
      this.fileImportInput.nativeElement.value = '' // Reset the input
    }
  }

  private validateHeaderColumn({ headers }: { headers: string[] }) {
    return (
      this.columnHeader.length === headers.length &&
      this.columnHeader.every((element, index) => element.trim() === headers[index].trim())
    )
  }

  getPayload({ currentNode, topics }: { currentNode: IProgram; topics: ITopic[] }) {
    this.postVal = {
      programId: '',
      yearId: '',
      curriculumId: '',
      levelId: '',
      topics: []
    }
    let parentIds: string[]
    let parentType: string[]

    if (currentNode.type === 'course') {
      parentIds = [
        ...this.ts.findParents('code', currentNode.parentKey, this.dataSource),
        currentNode._id
      ]
      parentType = [
        ...this.ts.findParentType({
          field: 'type',
          value: currentNode._id,
          data: this.dataSource
        }),
        currentNode.type
      ]
    } else {
      parentIds = this.ts.findParents('_id', currentNode._id, this.dataSource)
      parentType = this.ts.findParentType({
        field: 'type',
        value: currentNode._id,
        data: this.dataSource
      })
    }

    for (let i = 0; i < parentIds.length; i++) {
      this.postVal[`${parentType[i]}Id`] = parentIds[i]
    }
    this.postVal.topics = topics
    this.formatTopic({ topics, currentNode })

    return this.postVal
  }

  private formatTopic({ topics, currentNode }: { topics: ITopic[]; currentNode: IProgram }) {
    if (isNaN(this.treeService.findNodeMaxId(this.dataSource))) {
      return this.globalService.showSuccess('Tree nodeId is missing, cannot add new', '')
    }

    topics.forEach((topic) => {
      topic.nodeId = this.treeService.findNodeMaxId(this.dataSource) + 1
      topic.parentKey = currentNode.code
      topic.name = topic.name.trim()
      topic.code = topic.code.trim().toUpperCase()
    })
  }

  private uploadTopics({ payload }: { payload: ITopicUploadPayload }): void {
    this.treeService.expandedIds = this.getPreviousTreeState()
    this.treeService.parentId = this.currentNode._id
    this.getUploadTopic({ payload }).subscribe(
      (res) => {
        this.globalService.showSuccess(res.message, this.translateService.instant('common.topics'))
        this.refreshProgramsData.emit()
      },
      (err) => this.errorHandler.errorLog(err)
    )
  }

  getUploadTopic({ payload }: { payload: ITopicUploadPayload }): Observable<IResponse> {
    const url = this.globalService.config.upload_Topics
    return this.httpClient.post(url, payload).pipe(first())
  }

  checkProgramType({ type }: { type: string }) {
    return ['course', 'subject'].includes(type)
  }

  private getPreviousTreeState() {
    this.treeService.expandedIds = []
    return this.treeControl?.dataNodes?.reduce((acc, node) => {
      if (this.treeControl.isExpanded(node)) {
        acc.push(node._id)
      }

      return acc
    }, [])
  }

  setExpandNode() {
    this.treeControl?.dataNodes?.forEach((node) => {
      if (this.treeService.expandedIds.includes(node._id)) {
        this.treeControl?.expand(node)
      }

      if (this.treeService?.parentId === node?._id) {
        this.treeControl?.expand(node)
      }
    })
  }

  onClickExpandCollapse() {
    this.treeState.includes('COLLAPSE')
      ? this.changeTreeState.next('EXPAND')
      : this.changeTreeState.next('COLLAPSE')
  }

  getHierarchyType({ type }: { type: string }) {
    const hierarchyTypes: { [key: string]: string } = {
      program: 'Program',
      term: 'Term',
      year: 'Year',
      curriculum: 'Curriculum',
      level: 'Level',
      rotationGroup: 'Rotational Group',
      course: 'Course',
      module: 'Module',
      elective: 'Elective',
      subject: 'Subject',
      topic: 'Topic',
      subTopic: 'Sub Topic',
      slo: 'Session Learning Outcomes'
    }

    return hierarchyTypes[type] || type
  }
}
