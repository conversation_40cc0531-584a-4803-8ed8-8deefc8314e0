import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core'

import { TranslateService } from '@ngx-translate/core'
import { ToastrService } from 'ngx-toastr'

import { LocaleNumberPipe } from '@appcore/app/core/pipes'
import {
  ICourseSelectModuleExams,
  IExamCategory,
  IExamType,
  IExamTypePayload,
  IPagination
} from '@appcore/app/models'
import { GlobalExamSettingsService } from '@appcore/app/pages/settings/global-exam-settings/global-exam-settings.service'
import {
  EXAM_INFRATYPE_AS_BOTH,
  EXAM_INFRATYPE_AS_CENTRALIZED,
  EXAM_INFRATYPE_AS_CUSTOMIZED,
  EXAM_LOCATION_AS_IN,
  EXAM_LOCATION_AS_OUT,
  ROUTES
} from '@appcore/constants'
import {
  ICoureseGroup,
  ICoureseGroupchildren,
  ICourseTypeExam,
  IExamInfraType,
  IExamLocation,
  IHierarchy,
  ISignleExamData
} from '@appcore/models/global-settings'
import {
  ApiService,
  AuthService,
  ErrorHandlerService,
  GlobalService
} from '@appcore/services/index'

@Component({
  selector: 'digi-course-type-list',
  templateUrl: './course-type-list.component.html',
  styleUrls: ['./course-type-list.component.css']
})
export class CourseTypeListComponent implements OnChanges {
  @Input()
  tableData: ICourseTypeExam[]

  @Input()
  examTypes: IExamType[]

  @Input()
  isLoading: boolean

  @Input()
  isNewSchedule = false

  @Input()
  pagination: IPagination = {
    currentPage: 1,
    limit: 5,
    pageNo: 1,
    totalPages: 1
  }

  @Input()
  selectedExamCategory: IExamCategory

  @Output()
  changePagination: EventEmitter<IPagination> = new EventEmitter<IPagination>()

  courses: ICourseTypeExam[] = []
  displayedColumns: string[] = []
  examLocation: IExamLocation = {
    in: EXAM_LOCATION_AS_IN,
    out: EXAM_LOCATION_AS_OUT
  }
  examInfraType: IExamInfraType = {
    centralized: EXAM_INFRATYPE_AS_CENTRALIZED,
    customized: EXAM_INFRATYPE_AS_CUSTOMIZED,
    both: EXAM_INFRATYPE_AS_BOTH
  }
  hasExamTypesChangedBefore = false

  constructor(
    private api: ApiService,
    private globalService: GlobalService,
    public toaster: ToastrService,
    private errorHandleServices: ErrorHandlerService,
    private authService: AuthService,
    private localeNumberPipe: LocaleNumberPipe,
    private translate: TranslateService,
    private globalSettingService: GlobalExamSettingsService
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    const { tableData, examTypes } = changes
    this.courses = []
    if (tableData?.currentValue?.length) {
      this.courses = this.setSelectedExams(tableData?.currentValue)
    }

    if (examTypes?.currentValue && this.hasExamTypesChangedBefore) {
      this.pushHeaderData({ examTypes: examTypes.currentValue })
    } else {
      this.hasExamTypesChangedBefore = true
    }
  }

  private pushHeaderData({ examTypes }: { examTypes: ISignleExamData[] }) {
    if (!examTypes?.length) {
      this.toaster.error(
        this.translate.instant('settings.globalExamSettings.examTypes.examCategoryNotMapped')
      )

      return
    }

    this.displayedColumns = examTypes.map((type) => `${type.name} - ${type.code}`)
  }

  rowExpandable({ row }: { row: ICoureseGroup }) {
    row.expandable = !row.expandable
  }

  formatProctoringType(exam) {
    const code = exam.locationAndProctoringType?.code ?? exam.locationAndProctoringType
    const proctoringType = {
      remote_without_proctor: 'settings.globalExamSettings.examTypes.r-p',
      remote_with_proctor: 'settings.globalExamSettings.examTypes.r+p',
      onsite_with_proctor: 'settings.globalExamSettings.examTypes.o+p',
      onsite_without_proctor: 'settings.globalExamSettings.examTypes.o-p'
    }

    return proctoringType[code]
  }

  protected setSelectedExams(courses: ICourseTypeExam[]) {
    courses.forEach((course) => {
      course?.exams.forEach((examType) => {
        let exam
        const examTypeCategoryIds =
          examType.examCategories?.map((category) => {
            return { _id: category._id, code: category?.code }
          }) || []

        examType.selected = course?.examCategories?.some(({ _id, code, examTypes = [] }) => {
          if (
            !examTypeCategoryIds.some(
              ({ _id: categoryId, code: categoryCode }) =>
                categoryId === _id || categoryCode === code
            )
          ) {
            return false
          }

          exam = (examTypes as unknown as IExamTypePayload[]).find(
            ({ _examType }) => _examType === examType._id
          )

          return !!exam
        })

        Object.assign(examType, {
          mode: exam?.mode ?? EXAM_LOCATION_AS_IN,
          infraType: exam?.infraType ?? EXAM_INFRATYPE_AS_BOTH,
          shouldRequestForTCAllocate: exam?.shouldRequestForTCAllocate ?? false
        })
      })
    })

    return courses
  }

  onChangeExamSelect({
    exam,
    course,
    isSelected,
    isModeChange = false
  }: {
    exam: IExamType
    course: ICoureseGroupchildren
    isSelected: boolean
    isModeChange?: boolean
  }) {
    exam.selected = isSelected

    const payload = this.getPayload({
      selectedExam: exam,
      courseGroup: course,
      examCategory: this.selectedExamCategory,
      isModeChange
    })

    this.saveSettings({
      payload,
      exam,
      courseGroup: course,
      examCategoryCode: this.selectedExamCategory?.code
    })
  }

  private getPayload({
    selectedExam,
    courseGroup,
    examCategory,
    isModeChange = false
  }: {
    selectedExam: IExamType
    courseGroup: ICoureseGroupchildren
    examCategory: IExamCategory
    isModeChange?: boolean
  }): ICourseSelectModuleExams {
    const courses: { _id: string }[] = []
    const { hierarchy } = courseGroup
    const { code: examCategoryCoe = '' } = examCategory
    const examCategoryIndex = courseGroup?.examCategories?.findIndex(
      ({ code }) => code === examCategoryCoe
    )

    const examTypes =
      this.getSelectedExamIds({
        selectedExam,
        selectedExamIds: [...(courseGroup?.examCategories[examCategoryIndex]?.examTypes ?? [])]
      }) || []

    courses.push({ _id: hierarchy?.course?._id })

    return {
      course: courses,
      examType: examTypes,
      examCategory: examCategoryCoe,
      isModeChange,
      ...this.getLevels({ hierarchy })
    }
  }

  private getLevels({ hierarchy }: { hierarchy: IHierarchy }) {
    const retVal = {}

    Object.keys(hierarchy).forEach((key, i) => {
      if (key === 'course') {
        return
      }

      if (hierarchy[key]?._id) {
        retVal[`l${i + 1}`] = hierarchy[key]._id
      }
    })

    return retVal
  }

  private getSelectedExamIds({
    selectedExam,
    selectedExamIds
  }: {
    selectedExam: IExamType
    selectedExamIds: string[]
  }): string[] {
    let examIndex = 0
    const exam = (selectedExamIds as unknown as IExamTypePayload[]).find(({ _examType }, index) => {
      examIndex = index

      return _examType === selectedExam?._id
    })

    if (!selectedExam.selected) {
      selectedExamIds.splice(
        (selectedExamIds as unknown as IExamTypePayload[]).findIndex(
          ({ _examType }) => _examType === selectedExam?._id
        ),
        1
      )
    } else {
      exam
        ? ((selectedExamIds as unknown as IExamTypePayload[])[examIndex] = {
            ...(selectedExamIds as unknown as IExamTypePayload[])[examIndex],
            mode: selectedExam?.mode ?? 'IN',
            infraType: selectedExam?.infraType ?? 'CENTRALIZED',
            shouldRequestForTCAllocate: selectedExam?.shouldRequestForTCAllocate ?? false
          })
        : (selectedExamIds as unknown as IExamTypePayload[]).push({
            _examType: selectedExam?._id,
            mode: selectedExam?.mode ?? 'IN',
            infraType: selectedExam?.infraType ?? 'CENTRALIZED',
            shouldRequestForTCAllocate: selectedExam?.shouldRequestForTCAllocate ?? false
          })
    }

    return this.globalService.removeDuplicateArrayObject({
      items: (selectedExamIds as unknown as IExamTypePayload[]).filter(({ mode }) => mode),
      key: '_examType'
    }) as unknown as string[]
  }

  private saveSettings({
    exam,
    payload,
    courseGroup,
    examCategoryCode
  }: {
    exam: IExamType
    payload: ICourseSelectModuleExams
    courseGroup: ICoureseGroupchildren
    examCategoryCode: string
  }) {
    this.globalSettingService.selectCourseSelectModuleExams(payload).subscribe(
      (res) => {
        this.toaster.success(res.message)
        const examCategoryIndex = courseGroup?.examCategories?.findIndex(
          ({ code }) => code === examCategoryCode
        )
        courseGroup.examCategories[examCategoryIndex].examTypes = payload?.examType
      },
      (err) => {
        void this.errorHandleServices.errorLog(err)
        exam.selected = !exam.selected
      }
    )
  }

  checkExamWise(exam, children) {
    exam.forEach((parentExam) => {
      const filterExams = []
      const { _id: parentExamId } = parentExam
      children.forEach((child) => {
        const { exams: childExams } = child
        const ex = childExams.filter((childExam) => parentExamId === childExam._id)
        filterExams.push(ex[0])
      })
      const checkChildExam = filterExams.some((ex) => !ex.selected)
      parentExam.selectAllcourse = !checkChildExam
    })
  }

  onChangePerPage(limit: number) {
    this.pagination = {
      pageNo: 1,
      limit
    }
    this.changePagination.emit(this.pagination)
  }

  onChangePageNo(pageNo: number) {
    this.pagination = {
      limit: this.pagination?.limit,
      pageNo
    }
    this.changePagination.emit(this.pagination)
  }

  getTranslateCourseName({ name }: { name: string }) {
    const [start, end] = name.split('-')
    const courseGroup = name.split('-').slice(2).join('-')

    return `${this.localeNumberPipe.transform(start)}-${this.localeNumberPipe.transform(
      end
    )}-${courseGroup}`
  }

  get canManage(): boolean {
    return this.globalService.manage
  }

  get hasUpdatePermission() {
    return (
      this.canManage &&
      !!this.authService?.currentRoleModule?._role.modules
        .find((module) => module?.key === ROUTES.MODULE_SETTINGS)
        ?.pages.find((page) => page?.key === ROUTES.PAGE_SETTING_GLOBAL_EXAM_SETTINGS)
        ?.tabs.find((tab) => tab.key === ROUTES.TAB_EXAM_SETTINGS_COURSE_TYPES_OF_EXAMS)
        ?.actions.find((action) => action.key === ROUTES.ACTION_UPDATE_COURSE_SELECTED_MODULE_EXAMS)
    )
  }

  onChangeOption({
    type,
    exam,
    course,
    isModeChange = false
  }: {
    type: { [key: string]: string | boolean }
    exam: IExamType
    course: ICoureseGroupchildren
    isModeChange?: boolean
  }) {
    if (this.hasUpdatePermission && exam?.selected) {
      Object.assign(exam, type)
      this.onChangeExamSelect({ exam, course, isSelected: exam?.selected, isModeChange })
    }
  }

  canDisableConfiguration({ exam }: { exam: IExamType }): boolean {
    if (!this.hasUpdatePermission) {
      return true
    }

    if (!exam.selected) {
      return true
    }

    if (exam.mode === this.examLocation.out) {
      return true
    }

    return false
  }
}
