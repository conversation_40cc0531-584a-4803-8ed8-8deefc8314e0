import { IName } from '@appcore/app/core/components/import-csv-dialog/interfaces'

import {
  I<PERSON>ttachment,
  IDomain,
  IFramework,
  IItemRevisionCourseStatus,
  INameWithCode,
  IQuestion,
  ISession,
  IStem
} from '../..'
import { EvaluationStatus } from '../item-revision-result.interface'

export interface IManualEvaluationQuestionDetail {
  questions: IQuestion[]
  count: IManualEvaluationQuestionStatusCount
  status: EvaluationStatus
  title: string
  examType: INameWithCode
  courseName: string
  assessmentTypeName: string
  attemptTypeName: string
  groups: {
    code: string
  }[]
  configuration: IConfiguration
  courseId: string
  date: Date
  session: ISession
  isGeneralUser?: boolean
  courseCode?: string
}

export interface IManualEvaluationQuestions {
  stem: IStem
  status: string
  itemTypeCode: string
  sectionId: string
  itemId: string
  itemCount: IManualEvaluationItemCount
}

export interface IManualEvaluationFrameWork {
  _id: string
  name?: string
  code: string
  no?: number
}

export interface IManualEvaluationDomain {
  _id: string
  no: string
  name: string
}

export interface IManualEvaluationStemClo {
  framework: IFramework
  domain: IDomain
  _id: string
  name: string
  desc: string
}

export interface IManualEvaluationLeadIn {
  attachments: IAttachment[]
}

export interface IManualEvaluationItemCount {
  total: number
  evaluatedCount: number
  evaluatedStatus: string
}

export interface IManualEvaluationQuestionStatusCount {
  all: number
  eqCount: number
  saqCount: number
  csaqCount: number
  mitCount: number
  notStartedCount: number
  evaluatingCount: number
  completedCount: number
  allCount?: number
  totalPages?: number
  currentPage?: number
}

export interface IItem {
  itemId: string
  questionId: string
  questionNo: number
  itemTypeCode?: string
  parentItemTypeCode?: string
  topicName?: string
}

export interface IManualEvaluationItem {
  itemTypeCode: string
  items: IItem[]
}

export interface IItemAuthor {
  name: IName
  employeeId?: string
  gender: string
  _id: string
  roleName: string
  labelName?: string
  isAlreadyAssigned?: boolean
}

export interface IItemReviewer {
  name: IName | string
  employeeId?: string
  gender: string
  _id?: string
  roleName?: string
  labelName?: string
  userId?: string
  level?: number
  status?: string
  stageNo?: number
  isAlreadyAssigned?: boolean
}

export interface IItemReviewerPayload {
  items: IReviewItems[]
}

export interface IReviewItems {
  itemId: string
  reviewers: IItemReviewer[]
}

export interface IItemData {
  itemTypeCode: string
  questionId: string
  itemId: string
  questionNo: number
}

export interface IStudentData {
  academicNo: string
  gender: string
  group?: {
    code?: string
    name?: string
  }
  name: IName
  isChecked?: boolean
  studentId: string
  items?: IItemData
  evaluators?: INameWithCode[]
}

export interface IImportedStudents {
  academicNo: string
  gender: string
  group: {
    code: string
    _id?: string
  }
  groupCode?: string
  name: IName
  isSelected?: boolean
}

export interface IAssignEvaluatorByStudent {
  students: IStudentData[]
  count: {
    male: number
    female: number
    total: number
    assigned: number
    unassigned: number
  }
  totalPages: number
  totalAssigned: number
  totalUnAssigned: number
  currentPage: number
  groups: string[]
  deliveryTypes: string[]
}

export interface IStudentField {
  canShowID: boolean
  canShowName: boolean
  canShowGender: boolean
  canShowGroup: boolean
}

export interface IConfiguration {
  evaluation: string
  isCommonEvaluator?: boolean
  student: IStudentField
  isReadyForEvaluation?: boolean
}

export interface IStudentEvaluatorData {
  manualEvaluationItems: IManualEvaluationItem[]
  assessmentId: string
  evaluationNumber: number
  courseId: string
}

export interface IStudentTab {
  type: string
  count: number
}

export interface IStudentByEvaluator {
  students: IStudentData[]
  totalCount: number
  currentPage: number
  totalPages: number
  totalMaleCount: number
  totalFemaleCount: number
}

export interface ITextValue {
  key: string
  value: string
  name?: string
}

export interface IHideStudentField {
  key: string
  isSelected: boolean
  value: string
}

export interface IStudentLog {
  reportCourseId: string
  itemId: string
  questionId: string
  createdBy: {
    name: IName
    _id: string
  }
  student: {
    _id: string
    academicNo: string
    group: { code: string }
    gender: string
    name: IName
  }
  mark: number
}

export interface IStudentLogData {
  students: IStudentLog[]
  totalPages: number
  totalCount: number
  currentPage: number
}

export interface IAssessmentDashboardCount {
  allCount: number
  requestedByExamSchedular: number
  createdByHimSelfCount: number
  requested: number
  selfCreated: number
  all: number
  completed: number
  pending: number
  requestCompleted?: number
  requestPending?: number
  requestedTcs?: number
}
