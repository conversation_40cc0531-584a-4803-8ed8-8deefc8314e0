import { IStudentGroup } from '../exam-readiness'

export interface IStudents {
  totalCount: number
  totalPages: number
  currentPage: number
  count: ICount
  data: IStudentData
}

export interface IStudentData {
  id: string
  studentId: string
  academicNo: string
  identity: IIdentity
  name: IName
  dob: string
  gender: string
  program: string
  enrolledYear: string
  emailSentL: boolean
  verification: IVerification
  email: IEmail
  mobile: string
  passportNumber?: number
  address: IAddress
  docs: IDocs
  facial: string[]
  status: string
  biometric?: IBiometric
  isUnitCodeRequired?: boolean
  isZipCodeRequired?: boolean
  isDocumentsRequired?: boolean
  isAddressRequired?: boolean
  testCenterId?: string
  testCenterName?: string
  groupCode?: string
  group?: IStudentGroup
  isSelected?: boolean
  isActive?: boolean
}
export interface IBiometric {
  left: []
  right: []
}
export interface ICount {
  allCount: number
  validCount: number
  pendingCount: number
  pendingAllCount: number
  pendingDataCount: number
  pendingBioCount: number
}
export interface IIdentity {
  type: string
  no: string
}
export interface IName {
  first: string
  middle: string
  last: string
}
export interface IVerification {
  email: boolean
  mobile: boolean
  data: string
  employeeId: boolean
  face: boolean
  finger: boolean
  academicNo: boolean
  identityNo: boolean
  firstName: boolean
  middleName: boolean
  lastName: boolean
  gender: boolean
  role: boolean
  dob: boolean
  program: boolean
  enrolledYear: boolean
  userVerify: boolean
  signUpVerify: boolean
}
export interface IEmail {
  personal: string
  organization: string
}
export interface IDocs {
  nationalId: string
  collegeId: string
}
export interface IAddress {
  building: string
  city: string
  region: string
  district: string
  zipCode: string
  unit: string
}

export interface INotifiedStudent extends IName {
  academicNo: string
}
