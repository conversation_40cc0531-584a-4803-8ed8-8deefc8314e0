import { Component, Inject, OnInit } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog'

import { TranslateService } from '@ngx-translate/core'
import { ToastrService } from 'ngx-toastr'

import { IConfirmPopupData } from '@appcore/app/core/components/confirm-dialogbox/confirm-popup.interface'
import { STUDENT_OWNED_DEVICE } from '@appcore/app/core/constants'
import { IExternalExamTimeFormat, IName, ITextValue } from '@appcore/app/models'
import {
  IPublishAssessmentItemTypeCount,
  IPublishIndependentAssessmentDetails
} from '@appcore/app/models/assessment/create-assessment/create-assessment.interface'
import { ErrorHandlerService, GlobalService, SettingsService } from '@appcore/app/services'

import { IndependentAssessmentService } from '../../independent-assessment.service'
import { ConfirmPopupComponent } from '../confirm-popup/confirm-popup.component'

@Component({
  selector: 'digi-assessment-publish-popup',
  templateUrl: './assessment-publish-popup.component.html',
  styleUrls: ['./assessment-publish-popup.component.css']
})
export class AssessmentPublishPopupComponent implements OnInit {
  authMethods: ITextValue[] = [
    {
      key: 'FACIAL',
      name: 'settings.onsiteConductingExams.studentAuthentication.facialAuthentication'
    },
    { key: 'PASSWORD', name: 'settings.onsiteConductingExams.studentAuthentication.userPassword' },
    { key: 'BOTH', name: 'independentAssessmentAuthoring.facialAndPassword' }
  ]

  constructor(
    private dialogRef: MatDialogRef<AssessmentPublishPopupComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: { assessmentDetails: IPublishIndependentAssessmentDetails },
    private independentAssessmentService: IndependentAssessmentService,
    public globalService: GlobalService,
    private toaster: ToastrService,
    private errorHandler: ErrorHandlerService,
    private settingService: SettingsService,
    private dialog: MatDialog,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.formatTestCenter()
  }

  onClickClose({ res = false }: { res?: boolean }) {
    this.dialogRef.close(res)
  }

  get formattedTime() {
    const { hour, minute, format } = this.data.assessmentDetails?.session?.actualStart || {}
    const adjustedHours = format === 'PM' ? (hour % 12) + 12 : hour % 12

    const date = new Date()
    date.setHours(adjustedHours, minute + this.data.assessmentDetails?.totalTime)

    const formattedHours = (date.getHours() % 12 || 12)?.toString()?.padStart(2, '0')
    const formattedMinutes = date.getMinutes().toString().padStart(2, '0')
    const meridiem = date.getHours() < 12 ? 'AM' : 'PM'
    const examStartTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')} ${format}`
    const examEndTime = `${formattedHours}:${formattedMinutes} ${meridiem}`

    return `${examStartTime} to ${examEndTime}`
  }

  formatSessionTime({ actualStart }: { actualStart: IExternalExamTimeFormat }) {
    const hour = String(actualStart?.hour).padStart(2, '0')
    const minute = String(actualStart?.minute).padStart(2, '0')

    return `${hour}:${minute} ${actualStart?.format}`
  }

  formatItemCount({ items }: { items: IPublishAssessmentItemTypeCount[] }) {
    if (items?.length) {
      return items.map((item) => `(${item.code}: ${item.count})`).join(', ')
    }

    return '-'
  }

  formatProctoringType({ locationAndProctoringType }: { locationAndProctoringType: string }) {
    const proctoringTypeMap = {
      remote_without_proctor: 'settings.globalExamSettings.examTypes.noTestCenter',
      remote_with_proctor: 'settings.globalExamSettings.examTypes.virtualTestCenter',
      onsite_with_proctor: 'settings.globalExamSettings.examTypes.customTestCenter',
      onsite_without_proctor: 'settings.globalExamSettings.examTypes.o-p'
    }

    if (locationAndProctoringType === 'onsite_with_proctor') {
      return this.data.assessmentDetails?.examDeviceType === STUDENT_OWNED_DEVICE
        ? proctoringTypeMap[locationAndProctoringType]
        : 'settings.globalExamSettings.examTypes.centralizedTestCenter'
    }

    return proctoringTypeMap[locationAndProctoringType]
  }

  getAuthMethod({ studentAuthenticationMethod }: { studentAuthenticationMethod: string }) {
    return this.authMethods.find((assessment) => assessment?.key === studentAuthenticationMethod)
      ?.name
  }

  private formatTestCenter() {
    const { testCenters, locationAndProctoringType } = this.data.assessmentDetails || {}

    if (locationAndProctoringType === 'remote_without_proctor') {
      this.data.assessmentDetails.concatTestCentersName = this.translateService.instant(
        'common.virtualTestCenter'
      )
      this.data.assessmentDetails.showMore = true

      return
    }

    if (!testCenters?.length) {
      this.data.assessmentDetails.concatTestCentersName = '-'
      this.data.assessmentDetails.showMore = true

      return
    }

    const formattedNames = testCenters
      .map((testCenter) => {
        if (testCenter.gender?.toLowerCase() === 'mixed') {
          return `${testCenter.name} (${this.translateService.instant('common.mixed')})`
        }

        const gender = ['male', 'm'].includes(testCenter.gender.toLowerCase()) ? 'M' : 'F'
        return `${testCenter.name} (${gender})`
      })
      .join(',')

    this.data.assessmentDetails.concatTestCentersName = `${formattedNames}`
    this.data.assessmentDetails.showMore = true

    if (this.data.assessmentDetails.concatTestCentersName.length > 50) {
      this.data.assessmentDetails.isSplit = true
      this.data.assessmentDetails.shortName = formattedNames.substring(0, 50)
    }
  }

  onClickShowMore({ event }: { event: MouseEvent }) {
    this.data.assessmentDetails.showMore = !this.data.assessmentDetails.showMore
    event.stopPropagation()
  }

  getExamAdminName({ name }: { name: IName }) {
    if (!name) {
      return
    }

    return this.globalService.getFullName(name)
  }

  onClickPublishAssessment({ canStartExam }: { canStartExam?: boolean }) {
    this.independentAssessmentService.publishIndependentAssessment({ canStartExam }).subscribe(
      ({ message }) => {
        this.toaster.success(message)
        this.onClickClose({ res: true })
      },
      (err) => {
        if (err?.error?.code === 702) {
          this.openConfirmPopup({ message: err?.error?.message, isForcePublish: true })
            .afterClosed()
            .subscribe((res) => {
              if (!res) {
                return
              }

              this.onClickPublishAssessment({ canStartExam: true })
            })

          return
        }

        if (err?.status === 701) {
          const message = this.translateService.instant(
            'independentAssessmentAuthoring.publishConfirmation'
          )
          this.openConfirmPopup({ message })
            .afterClosed()
            .subscribe((res) => {
              if (res) {
                this.independentAssessmentService
                  .publishIndependentAssessment({ canStartExam, canForceToPublish: true })
                  .subscribe(
                    ({ message }) => {
                      this.toaster.success(message)
                      this.onClickClose({ res: true })
                    },
                    (error) => {
                      this.errorHandler.errorLog(error)
                    }
                  )
              }
            })

          return
        }

        this.errorHandler.errorLog(err)
      }
    )
  }

  private openConfirmPopup({
    message,
    isForcePublish
  }: {
    message: string
    isForcePublish?: boolean
  }) {
    const data = {
      isError: true,
      title: 'independentAssessmentAuthoring.confirmTile',
      message,
      additionalInfo: 'independentAssessmentAuthoring.clickToProceed',
      primaryButton: 'independentAssessmentAuthoring.iUnderstand'
    }
    return this.dialog.open(ConfirmPopupComponent, {
      data,
      width: isForcePublish ? '510px' : '525px',
      height: 'auto',
      panelClass: ['digi-padding-none', 'dialog-fix'],
      disableClose: true,
      direction: this.settingService.getOptions().dir
    })
  }
}
