.digi-creation-details {
  padding: 10px;
  background-color: #f9fafb;
  border-bottom: 1px solid #0064c8;
}

.digi-auto-save {
  background-color: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1px 8px;
  color: #6b7280;
  font-size: 12px;
}

:host::ng-deep .digi-stepper .digi-stepper-container {
  margin: 3px 0px;
  justify-content: flex-start !important;
}

:host::ng-deep .digi-stepper .digi-line.digi-hidden {
  display: none;
}

:host::ng-deep .digi-stepper .digi-count {
  --stepper-count-size: 32px;
  border: 1px solid #e5e7eb;
  color: #9fa0a3;
  width: var(--stepper-count-size);
  min-width: var(--stepper-count-size);
  height: var(--stepper-count-size);
  line-height: var(--stepper-count-size);
  display: inline-block;
  border-radius: 50%;
  text-align: center;
  background-color: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: center;
}

:host::ng-deep .digi-stepper .digi-count mat-icon {
  padding: 3px 0px;
}

.digi-reviewer-refresh {
  background-color: #f4f5fa;
  max-width: calc(100% - 110px);
}

:host::ng-deep digi-button .primary {
  padding: 8px 20px;
}

:host::ng-deep .digi-stepper .digi-step-group {
  font-size: 14px;
  font-weight: 400;
  color: #6b7280;
}

:host::ng-deep .digi-stepper .digi-line {
  min-width: 5px;
  height: 2px;
  background: #e5e7eb;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
}

:host::ng-deep .digi-stepper .digi-step-group.digi-done .digi-line {
  background: #0064c8;
  border-left: 5px solid #0064c8;
  border-right: 5px solid #0064c8;
}

:host::ng-deep .digi-stepper .digi-step-group.active {
  color: #3f95ef;
  font-size: 14px;
  font-weight: 500;
}

:host::ng-deep .digi-stepper .digi-step-group.digi-done {
  color: #4b5563;
  font-size: 14px;
  font-weight: 500;
}

:host::ng-deep .digi-stepper .digi-step-group.active .digi-count {
  background-color: #ffffff;
  border: 1px solid #0064c8;
  color: #3f95ef;
  box-shadow: 0px 0px 1px 4px rgba(33, 150, 243, 0.21);
}

:host::ng-deep .digi-stepper .digi-step-group.active.digi-done .digi-count {
  color: #ffff;
  background: #0064c8;
  border: 1px solid #224796;
}

:host ::ng-deep .digi-step-item {
  padding: 0 15px;
  margin: 0px 5px;
}

:host ::ng-deep .digi-step-item {
  padding: 0 0px;
  margin: 0px 5px;
}

:host::ng-deep .digi-stepper .digi-step-group .digi-count mat-icon {
  color: inherit;
}

:host::ng-deep .digi-stepper .digi-step-group.active .digi-count mat-icon {
  color: #3f95ef;
}

:host::ng-deep .digi-stepper .digi-step-group.active.digi-done .digi-count mat-icon {
  color: #ffffff;
}

.digi-action-icon {
  cursor: pointer;
  border-radius: 16px;
  padding: 5px;
  color: #6b7280;
}

.digi-action-icon:hover {
  background-color: #f3f4f6;
}

:host mat-tab-group.digi-tabs {
  box-sizing: border-box;
}

:host mat-tab-group.digi-tabs::ng-deep > .mat-mdc-tab-header {
  border-bottom: 0px solid rgba(0, 0, 0, 0.12) !important;
  padding: 0 10px;
  position: sticky;
  top: 59;
  z-index: 2;
  background: #fff !important;
}

:host mat-tab-group.digi-tabs::ng-deep > .mat-mdc-tab-header .mat-mdc-tab {
  color: #6b7280 !important;
  opacity: 1;
  min-width: auto;
  font-size: 16px;
}

:host mat-tab-group.digi-tabs::ng-deep > .mat-mdc-tab-header .mat-ink-bar {
  background: #008dff;
}

:host mat-tab-group.digi-tabs::ng-deep > .mat-mdc-tab-header .mat-mdc-tab.mdc-tab--active {
  color: #147afc !important;
  visibility: hidden;
}

:host mat-tab-group.digi-tabs::ng-deep > .mat-mdc-tab-body-wrapper {
  background: #f4f5fa !important;
}

:host ::ng-deep .digi-tabs > mat-tab-header .mat-mdc-tab.mat-mdc-tab-disabled {
  flex: 1;
}

:host ::ng-deep .digi-tabs > mat-tab-header .mat-tab-label.mat-tab-disabled:nth-child(1) {
  justify-content: flex-start;
  margin-right: auto;
}

:host ::ng-deep .digi-tabs > mat-tab-header .mat-tab-label.mat-tab-disabled:nth-last-child(1) {
  justify-content: flex-end;
  margin-left: auto;
}

.tab-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.tab-left {
  display: flex;
  justify-content: center;
  flex: 1;
}

.tab-right {
  display: flex;
  justify-content: flex-end;
}

.digi-input-field {
  width: 68px;
  border: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.37);
  box-sizing: border-box;
  padding: 8px;
  background: white;
  line-height: 125%;
  text-align: center;
  letter-spacing: 0.0125em;
  color: #374151;
}

.digi-input-field::-webkit-inner-spin-button,
.digi-input-field::-webkit-outer-spin-button {
  opacity: 1;
}

.digi-floating-sections-container {
  background-color: #fff;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
}

.floating-buttons {
  position: fixed;
  right: 35px;
  z-index: 1000;
  transition: top 0.3s ease-in-out;
}

::ng-deep [dir='rtl'] digi-independent-assessment-creation .floating-buttons {
  right: auto !important;
  left: 35px !important;
}

.digi-border-red {
  border: 1px solid #ff0000;
  border-radius: 8px;
}

.digi-hierarchy-font {
  color: #6b7280;
}

.digi-back-button {
  border: 1px solid #d1d5db;
  border-radius: 5px;
  color: black;
}

:host::ng-deep digi-button .button {
  padding: 6px 25px !important;
  height: 38px;
}

:host::ng-deep .digi-stepper .digi-stepper-container {
  justify-content: center !important;
}

.digi-scroll-button {
  position: fixed !important;
  bottom: 20px !important;
  right: 3rem !important;
  border: none !important;
  z-index: 999 !important;
  background-color: none !important;
}

::ng-deep [dir='rtl'] digi-independent-assessment-creation .digi-scroll-button {
  right: auto !important;
  left: 3rem !important;
}

.digi-exam-configuration {
  min-width: calc(100vw - 700px);
}

.digi-assessment-header-sticky {
  position: sticky;
  top: 0px;
  z-index: 2;
  background: white;
  min-height: var(--steps-min-height);
}

.digi-sticky-action-toolbar {
  position: sticky;
  top: 109px;
  z-index: 2;
  background: #f4f5fa;
  padding: 5px 0;
}

.step-navigation {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 8px 14px;
  border-radius: 30px;
  border: 1px solid #e5e7eb;
  font-size: 14px;
  font-weight: 500;
}

.step-navigation-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  color: #6c757d;
}

.step-navigation-item.selected {
  background-color: #0064c8;
  color: #fff;
  border-radius: 20px;
  border: 1px solid #0064c8;
}

.step-navigation-item mat-icon {
  font-size: 18px;
  margin-right: 5px;
  display: flex;
  align-items: center;
}

:host ::ng-deep .digi-tabs > mat-tab-header .mat-mdc-tab .mdc-tab-indicator__content--underline {
  border-color: #008dff !important;
}

:host ::ng-deep .digi-tabs > mat-tab-header .mat-mdc-tab .mdc-tab__text-label {
  color: #6b7280 !important;
}

:host ::ng-deep .digi-tabs > mat-tab-header .mat-mdc-tab.mdc-tab--active .mdc-tab__text-label {
  color: #147afc !important;
}

:host ::ng-deep .digi-tabs > mat-tab-header .mat-mdc-tab.mat-mdc-tab-disabled {
  flex: 1 !important;
  justify-content: center;
  padding: 0;
  opacity: 1;
  pointer-events: auto;
}

:host ::ng-deep .digi-tabs > mat-tab-header .mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content {
  pointer-events: auto;
}

:host
  ::ng-deep
  digi-independent-assessment-creation-card.digi-first-creation-card
  > .scrollable-container {
  max-height: calc(100vh - 380px);
  overflow-y: auto;
  /* Enables vertical scrolling */
  padding: 10px;
}

.digi-creation-card {
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
  color: #6b7280;
  box-shadow: 0px 1px 3px 0px rgba(17, 24, 39, 0.2);
}

.digi-move-icon {
  margin-right: 60px;
}

.digi-move-icon-color {
  color: #0064c8;
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
}

.digi-withdraw {
  color: #eb5757;
}

.digi-move-item {
  margin: 3px 5px 0px 5px;
}

.digi-mandatory {
  color: #eb5757;
}

.digi-sent-to-AA {
  color: #219653;
}

.digi-check {
  font-size: 22px;
  height: 22px;
  width: 22px;
}

.digi-review-note {
  color: #4b5563;
}

.digi-border-bottom {
  border-bottom: 1px solid #3e95ef;
}

.digi-icon-size {
  height: 24px;
  width: 24px;
}

.digi-review-completed {
  color: #15803d;
}

:host mat-tab-group.digi-reviewer-tabs::ng-deep > .mat-mdc-tab-header {
  display: none;
}

:host::ng-deep digi-comment-section .digi-comment-container {
  min-width: 500px;
  max-width: 600px;
  height: auto;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  background-color: #ffffff;
  position: fixed !important;
  bottom: 20px !important;
  right: 1rem !important;
  z-index: 99999 !important;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
}

:host::ng-deep .mat-icon-button {
  width: 30px;
  height: 30px;
}

.digi-warning-color {
  color: #ffcc00;
}

.digi-navigation-section-container {
  position: fixed;
  left: 20px;
  z-index: 1000;
  transition: top 0.3s ease-in-out;
  top: 230px;
  background-color: #fff;
  padding: 8px;
  margin-top: 37px;
  border-radius: 8px;
  overflow: auto;
  border: 1px solid #d1d5db;
  max-height: calc(100% - 320px);
}

::ng-deep [dir='rtl'] digi-independent-assessment-creation .digi-navigation-section-container {
  left: auto !important;
  right: 20px !important;
}

.digi-navigation-container {
  background-color: #374151;
  color: #ffffff;
  border-radius: 0 5px 5px 0;
  padding: 5px 0;
  width: 100%;
  min-width: 50px;
  user-select: none;
}

.digi-sections-container {
  background-color: #ffffff;
  border-radius: 0 5px 5px 0;
  box-sizing: border-box;
  padding: 5px;
}

.digi-sections-wrapper {
  overflow: auto;
  transition: all 0.5s ease;
}

.digi-section-item {
  border: 1px solid #e5e7eb;
  color: #6b7280;
  max-width: 30px;
  max-height: 30px;
  min-width: 30px;
  min-height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100%;
  font-size: 0.95rem;

  &.active {
    border: 1px solid #0064c8;
    color: #0064c8;
  }

  &.error {
    border: 1px solid #ff0000;
  }
}

.digi-mandatory {
  color: #eb5757;
}

.digi-sent-to-AA {
  color: #219653;
}

.digi-check {
  font-size: 22px;
  height: 22px;
  width: 22px;
}

.digi-refresh-icon {
  color: #0064c8;
}

.digi-mandatory {
  color: #eb5757;
}

.digi-sent-to-AA {
  color: #219653;
}

.digi-check {
  font-size: 22px;
  height: 22px;
  width: 22px;
}

::ng-deep digi-button.digi-next-button .button {
  padding: 4px 12px !important;
  height: 38px;
  border: 1px solid #0064c8;
}

::ng-deep digi-button.digi-arrow-back-gray .button {
  padding: 4px 12px !important;
  height: 38px;
  border: 1px solid #0064c8 !important;
}

.digi-assign-student {
  padding-top: 10px;
}

.digi-course-list {
  width: 700px;
  overflow: hidden;
}

:host ::ng-deep .digi-course-list .digi-text-clip {
  --text-width: calc(100% - 40px);
  display: block !important;
}

.digi-container-height {
  height: 35px !important;
}

.digi-icon-setting-container {
  --size: 30px;
  width: var(--size);
  height: var(--size);
  text-align: center;

  & .digi-icon-setting {
    font-size: calc(var(--size) * 0.8);
    color: #0064c8;
  }
}

.digi-map-container {
  color: #0064c8;
}

.digi-icon-map {
  --map-icon-size: 24px;
  color: currentColor;
  font-size: calc(var(--map-icon-size) * 1);
  line-height: var(--map-icon-size);
  width: var(--map-icon-size);
  height: var(--map-icon-size);
}

::ng-deep .mat-tooltip {
  background-color: #000;
  color: #fff;
  font-size: 14px;
  padding: 6px 10px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

::ng-deep .mat-tooltip::after {
  content: '';
  position: absolute;
  border-style: solid;
  border-width: 6px;
  border-color: transparent transparent black transparent;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.item-type-count-wrapper {
  max-width: 220px;
  width: 100%;
  white-space: normal;
  overflow-wrap: break-word;
  word-break: break-word;
}

::ng-deep .custom-tooltip {
  color: #fff;
  font-size: 13px;
  border-radius: 4px;
  white-space: pre-line;
}

.item-type-inline div {
  display: inline-block;
  margin-right: 10px;
}

.item-type-tooltip {
  cursor: pointer;
  display: inline-block;
}

.item-type-tooltip {
  background-color: #3e95ef;
  color: #fff;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: normal;
  cursor: pointer;
}

::ng-deep .hide-tab {
  visibility: hidden !important;
}

::ng-deep .question-bank-custom-tooltip {
  color: #fff;
  font-size: 13px;
  border-radius: 4px;
  white-space: pre-line;
  position: relative;
  top: 112px;
  right: 14px;
}

.digi-max-width {
  max-width: 600px !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.digi-assessment-header-content {
  gap: 10px;
}

.digi-select-all-label {
  white-space: nowrap;
}

.digi-create-container {
  --steps-min-height: 65px;
}

.digi-marks-duration-container {
  position: sticky;
  top: var(--steps-min-height);
  z-index: 2;
  padding: 5px 15px !important;
  min-height: 44px;
}

.digi-questions-container {
  background-color: #f4f5fa !important;
}

.digi-center-content {
  max-width: calc(100% - 110px);
}

.digi-text-clip {
  --text-width: 500px;
}

@media screen and (max-width: 1223px) {
  .digi-create-container {
    --steps-min-height: 115px;
  }
}

.digi-gray {
  color: #6b7280;
}

.digi-reschedule-container {
  margin: 0 auto;
  width: 600px;
  height: 200px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  border-bottom: 5px solid #0064c8;
  background: #ffffff;
}

.digi-content {
  margin: auto;
}

:host ::ng-deep .menu-trigger.mat-mdc-icon-button.mat-mdc-button-base {
  --mdc-icon-button-state-layer-size: 40px;
  padding: 8px;
}
