<div *ngIf="!isFromQuestionBank">
  <section *ngIf="isPublished && !isItemAuthor && !isItemReviewer" class="digi-creation-details">
    <div fxLayoutAlign="flex-start center">
      <span class="digi-font-red">*</span>
      <div *ngIf="isPublished && !isCompleted && !isExamOngoing" class="digi-font-red">
        {{ 'independentAssessmentAuthoring.examIsPublished' | translate }}
      </div>
      <div *ngIf="isPublished && isExamOngoing" class="digi-font-red">
        {{ 'independentAssessmentAuthoring.examInProgress' | translate }}
      </div>
      <div *ngIf="isCompleted && !isExamOngoing" class="digi-font-red">
        {{ 'independentAssessmentAuthoring.completedAssessmentToEdit' | translate }}
      </div>
    </div>
  </section>

  <section *ngIf="!canSendToAssessmentAuthor" class="digi-creation-details">
    <div fxLayoutAlign="flex-start center">
      <span class="digi-font-red">*</span>
      <div class="digi-font-red">
        {{ 'independentAssessmentAuthoring.updateNotAllowed' | translate }} {{ authorName }}
      </div>
    </div>
  </section>

  <section *ngIf="independentAssessmentDetails" class="digi-create-container" id="create">
    <div class="digi-assessment-header-sticky">
      <div [ngClass]="{ 'digi-border-bottom': isItemReviewer || isItemAuthor }">
        <div
          fxLayout="row wrap"
          fxLayoutAlign="space-between center"
          class="digi-p-10 digi-assessment-header-content"
        >
          <div fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="15px">
            <mat-icon class="digi-blue digi-cursor mat-icon-rtl-mirror" (click)="onClickBack()">
              arrow_back
            </mat-icon>
            <div
              *ngIf="isItemReviewer || isItemAuthor"
              fxLayout="column wrap"
              fxLayoutAlign="start none"
              fxLayoutGap="3px"
            >
              <div
                class="digi-fs-16 digi-fw-500 digi-max-width"
                [tooltip]="independentAssessmentDetails?.hierarchy?.course?.name"
                [tooltipClass]="'digi-course-tooltip'"
              >
                {{ independentAssessmentDetails?.hierarchy?.course?.code }} -
                {{ independentAssessmentDetails?.hierarchy?.course?.name }}
              </div>
              <div
                class="digi-hierarchy-font digi-text-clip"
                maxWidth="50vw"
                contentType="template"
                [tooltip]="hierarchyContent"
              >
                <ng-container *ngTemplateOutlet="hierarchyContent"></ng-container>
                <ng-template #hierarchyContent>
                  <span>
                    {{ independentAssessmentDetails?.hierarchy?.program?.name }} -
                    {{ independentAssessmentDetails?.hierarchy?.program?.code | uppercase }} /
                  </span>
                  <span>{{ independentAssessmentDetails?.hierarchy?.term?.name }} / </span>
                  <span>{{ independentAssessmentDetails?.hierarchy?.curriculum?.name }} / </span>
                  <span>{{ independentAssessmentDetails?.hierarchy?.year?.name }} / </span>
                  <span>{{ independentAssessmentDetails?.hierarchy?.level?.name }}</span>
                  <span *ngIf="independentAssessmentDetails?.hierarchy?.rotationGroup?.name">
                    / {{ independentAssessmentDetails?.hierarchy?.rotationGroup?.name }}
                  </span>
                </ng-template>
              </div>
            </div>
            <digi-stepper
              *ngIf="!isItemAuthor && !isItemReviewer"
              class="digi-stepper digi-flex-1 digi-text-capitalize"
              [steps]="steps"
              [stepIcons]="['edit_note', 'tune', 'account_box', 'today', 'manage_accounts']"
              [multiSelected]="multiSelected"
              [isClickable]="true"
              [isShowDone]="true"
              (selectedStep)="onSelectedStep({ step: $event })"
            ></digi-stepper>
          </div>

          <div fxLayout="row wrap" fxLayoutAlign="space-between center" class="digi-ml-auto">
            <div *ngIf="!isItemReviewer" fxLayout="row wrap" fxLayoutAlign="space-between center">
              <div fxLayoutAlign="flex-end center" fxLayoutGap="10px">
                <ng-container *ngIf="false">
                  <mat-icon class="digi-action-icon">undo</mat-icon>
                  <mat-icon class="digi-action-icon">redo</mat-icon>
                  <mat-icon class="digi-action-icon">add_photo_alternate</mat-icon>
                  <mat-icon class="digi-action-icon">visibility</mat-icon>
                  <mat-icon class="digi-action-icon">more_vert</mat-icon>
                </ng-container>
              </div>
              <div
                fxLayoutAlign="center center"
                class="digi-icon-setting-container digi-mr-10 digi-cursor"
                (click)="onClickSettingIcon()"
              >
                <mat-icon class="digi-icon-setting">settings</mat-icon>
              </div>
              <digi-button
                *ngIf="selectedIndex && !isItemAuthor"
                class="digi-m-0 digi-mr-10 digi-arrow-back-gray mat-icon-rtl-mirror"
                button="secondary"
                [transparent]="true"
                (click)="onClickStepperBack()"
              >
                <mat-icon class="digi-blue"> arrow_back </mat-icon>
              </digi-button>
              <digi-button
                *ngIf="selectedIndex !== 4 && !isItemAuthor"
                class="digi-m-0 digi-mr-10 digi-next-button mat-icon-rtl-mirror"
                (click)="onClickNext({})"
              >
                <mat-icon class="digi-blue"> arrow_forward </mat-icon>
              </digi-button>
              <digi-button
                *ngIf="!isItemAuthor"
                button="primary"
                class="digi-m-0 digi-mr-10"
                [disabled]="isPublished"
                (click)="!isPublished && onClickNext({ canPublish: true })"
              >
                <span class="digi-fs-16">
                  {{ 'common.publish' | translate | titlecase }}
                </span>
              </digi-button>
              <div
                *ngIf="isItemAuthor"
                fxLayout="row"
                fxLayoutAlign="end center"
                fxLayoutGap="15px"
              >
                <div *ngIf="canSendToAssessmentAuthor">
                  <digi-button
                    *digiAccessControl="'actions_send-to-assessment-author'"
                    button="primary"
                    class="digi-m-0 digi-next-button"
                    (click)="onClickSendToAssessmentAuthor()"
                  >
                    <span class="digi-fs-16">
                      {{
                        'assessment.itemRequest.sendToAssessmentAuthor'
                          | translate: { authorName: getRoleLabel({ role: 'assessmentAuthor' }) }
                          | titlecase
                      }}
                    </span>
                  </digi-button>
                </div>
                <div
                  *ngIf="!canSendToAssessmentAuthor"
                  fxLayout="row"
                  fxLayoutAlign="flex-start center"
                  fxLayoutGap="5px"
                  class="digi-sent-to-AA"
                >
                  <mat-icon class="digi-check">check_circle</mat-icon>
                  <span class="digi-fs-16 digi-fw-500">
                    {{
                      'assessment.itemRequest.sentToAssessmentAuthor'
                        | translate: { authorName: getRoleLabel({ role: 'assessmentAuthor' }) }
                        | titlecase
                    }}
                  </span>
                </div>
                <div
                  *ngIf="
                    hasReviewersAssigned &&
                    !independentAssessmentDetails?.canIASentToReviewer &&
                    canSendToAssessmentAuthor
                  "
                  fxLayout="row"
                  fxLayoutAlign="flex-start center"
                  fxLayoutGap="5px"
                  class="digi-sent-to-AA"
                >
                  <mat-icon class="digi-check">check_circle</mat-icon>
                  <span class="digi-fs-16 digi-fw-500">
                    {{
                      'independentAssessmentAuthoring.sentToReviewer'
                        | translate: { reviewersRoleName: getRoleLabel({ role: 'reviewer' }) }
                    }}
                  </span>
                </div>
                <div
                  *ngIf="
                    hasReviewersAssigned &&
                    independentAssessmentDetails?.canIASentToReviewer &&
                    canSendToAssessmentAuthor
                  "
                >
                  <digi-button
                    *digiAccessControl="'actions_send-to-reviewer'"
                    button="primary"
                    class="digi-m-0 digi-next-button"
                    (click)="onClickIASendToReviewers()"
                  >
                    <span class="digi-fs-16">
                      {{
                        'independentAssessmentAuthoring.sendToReviewer'
                          | translate: { reviewersRoleName: getRoleLabel({ role: 'reviewer' }) }
                          | titlecase
                      }}
                    </span>
                  </digi-button>
                </div>
              </div>

              <mat-icon
                *ngIf="!isItemAuthor && !isItemReviewer"
                class="digi-cursor digi-hierarchy-font"
                [matMenuTriggerFor]="menu"
              >
                more_vert
              </mat-icon>

              <mat-menu #menu="matMenu">
                <button
                  mat-menu-item
                  [disabled]="
                    !isPublished || isCompleted || independentAssessmentDetails?.isExamAdmin
                  "
                  (click)="onClickUnPublish()"
                >
                  {{ 'common.unPublish' | translate | titlecase }}
                </button>
                <button
                  mat-menu-item
                  [disabled]="independentAssessmentDetails?.status !== 'COMPLETED'"
                  (click)="onClickGenerateReport()"
                >
                  {{ 'independentAssessmentAuthoring.regenerateReport' | translate | titlecase }}
                </button>
                <button
                  *digiAccessControl="'actions_print'"
                  mat-menu-item
                  (click)="onClickPrintAssessment()"
                >
                  {{ 'independentAssessmentAuthoring.printAssessment' | translate | titlecase }}
                </button>
                <button
                  mat-menu-item
                  [disabled]="isPublished || isCompleted"
                  (click)="onClickImport({})"
                >
                  {{ 'independentAssessmentAuthoring.importFromExcel' | translate }}
                </button>
              </mat-menu>
            </div>
            <div
              *ngIf="isItemReviewer"
              fxLayout="row wrap"
              fxLayoutAlign="space-between center"
              fxLayoutGap="15px"
            >
              <div class="digi-review-note">
                <span class="digi-mandatory">*</span>
                <span class="digi-note digi-fw-500"> {{ 'common.note' | translate }}:</span>
                <span [ngClass]="{ 'digi-mandatory': isReviewCompleted }">
                  {{
                    (isReviewCompleted
                      ? 'independentAssessmentAuthoring.reviewCompletedNote'
                      : 'independentAssessmentAuthoring.mustAddComment'
                    ) | translate
                  }}
                </span>
              </div>
              <digi-button
                *ngIf="!isReviewCompleted"
                button="primary"
                class="digi-m-0"
                [disabled]="isPublished"
                (click)="onClickCompleteReview()"
              >
                <span class="digi-fs-16">
                  {{ 'independentAssessmentAuthoring.completeReview' | translate }}
                </span>
              </digi-button>

              <div
                *ngIf="isReviewCompleted"
                fxLayout="row"
                fxLayoutAlign="space-between center"
                fxLayoutGap="5px"
                class="digi-review-completed"
              >
                <mat-icon class="digi-icon-size">check_circle</mat-icon>
                <span class="digi-fw-500 digi-fs-16">
                  {{ 'independentAssessmentAuthoring.reviewCompleted' | translate }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <section
      *ngIf="isItemReviewer && independentAssessmentDetails?.items?.length"
      fxLayout="row"
      fxLayoutAlign="flex-end center"
      fxLayoutGap="20px"
      class="digi-reviewer-refresh digi-p-5 digi-center-content"
    >
      <div class="digi-fs-16">
        {{ 'assessment.createItem.totalItems' | translate | titlecase }}:
        <span class="digi-fw-500">
          {{ getItemTypesWithCount({ items: selectedSection?.items })?.total | arabicNumber }}
        </span>
      </div>
      <div
        fxLayout="row"
        fxLayoutAlign="space-between center"
        fxLayoutGap="5px"
        class="digi-refresh-icon digi-cursor digi-blue"
        (click)="onClickRefresh()"
      >
        <mat-icon>refresh</mat-icon>
        <span class="digi-fw-500">{{ 'common.refresh' | translate }}</span>
      </div>
    </section>

    <ng-container [ngSwitch]="selectedIndex">
      <div *ngSwitchCase="0">
        <!-- marks and duration container -->
        <div
          *ngIf="!isItemReviewer"
          fxLayout="row"
          class="digi-marks-duration-container digi-bg-white"
        >
          <div
            *ngIf="!isItemAuthor"
            fxLayoutAlign="flex-start center"
            fxLayoutGap="10px"
            fxFlex
            [ngClass]="{ hidden: !hasShowItemsPermission }"
          >
            <div fxLayoutAlign="flex-start center" fxLayoutGap="5px" class="digi-pr-10">
              <mat-icon
                [tooltip]="tooltipContent"
                contentType="template"
                tooltipPosition="below"
                class="digi-blue"
              >
                info
              </mat-icon>
              <ng-template #tooltipContent>
                <div fxLayout="column" fxLayoutGap="5px">
                  <span>
                    {{ 'independentAssessmentAuthoring.splitMarksOn' | translate }}
                  </span>
                  <span>
                    {{ 'independentAssessmentAuthoring.splitMarksOff' | translate }}
                  </span>
                </div>
              </ng-template>
              <span class="digi-list-topic-color">
                {{ 'independentAssessmentAuthoring.splitMarks' | translate }}
              </span>
            </div>

            <div>
              <mat-slide-toggle
                [disabled]="isPublished"
                [(ngModel)]="isEqualMarks"
                (change)="onClickToggleEqualMarks()"
              ></mat-slide-toggle>
            </div>
          </div>

          <div
            *ngIf="!isItemAuthor && !isItemReviewer"
            fxLayoutAlign="flex-end center"
            fxLayoutGap="10px"
          >
            <div fxLayoutAlign="flex-start center" fxLayoutGap="5px" class="digi-pr-10">
              <span class="digi-list-topic-color">
                {{ 'reportingAndAnalytics.reportTables.marks' | translate }}:
              </span>
              <span
                *ngIf="!isEqualMarks"
                [ngClass]="customMarks.customMarksClass"
                [tooltip]="'independentAssessmentAuthoring.sumOfCustomMarks' | translate"
              >
                ({{ customMarks?.totalMarks | arabicNumber }})
              </span>
              <span class="digi-font-red">*</span>
              <input
                matInput
                digiNumbersOnly
                digiNumberMaxLimit
                class="digi-input-field digi-focus digi-list-topic-color"
                type="number"
                [min]="0"
                [max]="999"
                [maxLimit]="999"
                [decimalPlaces]="0"
                [disabled]="isPublished"
                [ngClass]="{ 'digi-border-red': isTotalTotalMarksInvalid }"
                [(ngModel)]="independentAssessmentDetails.settings.totalMarks"
                (input)="onValueChange({ isMarks: true })"
                (keydown)="stopArrowKeyPropagation({ event: $event })"
              />
            </div>

            <div fxLayoutAlign="flex-start center" fxLayoutGap="5px" class="digi-pr-10">
              <span class="digi-list-topic-color">
                {{ 'independentAssessmentAuthoring.durationMins' | translate }}:
              </span>
              <span class="digi-font-red">*</span>
              <mat-icon
                *ngIf="sectionValidation.isTimeInValid"
                class="digi-warning-color"
                [tooltip]="'independentAssessmentAuthoring.sectionTimeInValid' | translate"
              >
                info
              </mat-icon>
              <input
                matInput
                digiNumbersOnly
                digiNumberMaxLimit
                class="digi-input-field digi-focus digi-list-topic-color"
                type="number"
                [min]="0"
                [max]="999"
                [maxLimit]="999"
                [decimalPlaces]="0"
                [blurEvent]="false"
                [disabled]="isPublished || independentAssessmentDetails?.isScheduleRequest"
                [ngClass]="{ 'digi-border-red': isTotalDurationInvalid }"
                [(ngModel)]="independentAssessmentDetails.settings.totalDuration"
                (input)="onValueChange({ isMarks: false })"
                (keydown)="stopArrowKeyPropagation({ event: $event })"
              />
            </div>

            <div fxLayoutAlign="flex-start center" fxLayoutGap="5px" class="digi-pr-10">
              <span class="digi-list-topic-color">
                {{ 'common.items' | translate | titlecase }}:
              </span>
              <span class="digi-fw-500 digi-list-topic-color">
                {{ getTotalItems() | arabicNumber }}
              </span>
            </div>
          </div>

          <div
            *ngIf="isItemAuthor"
            fxLayout="row"
            fxLayoutGap="10px"
            class="author-tab-label digi-ml-auto"
          >
            <div fxLayoutAlign="end center" fxLayoutGap="3px">
              <div class="digi-hierarchy-font digi-fs-16">
                {{ 'common.requestedItems' | translate | titlecase }}
              </div>
              <span>:</span>
              <div class="digi-fw-500 digi-fs-16">
                {{ itemsCount | arabicNumber: 2 }}
              </div>
            </div>

            <div
              fxLayout="row"
              fxLayoutAlign="space-between center"
              fxLayoutGap="5px"
              class="digi-refresh-icon digi-cursor"
              (click)="onClickRefresh()"
            >
              <mat-icon>refresh</mat-icon>
              <span class="digi-fw-500">{{ 'common.refresh' | translate }}</span>
            </div>
          </div>
        </div>

        <!-- questions container -->
        <ng-container *ngIf="hasShowItemsPermission">
          <div fxLayout="column" fxLayoutAlign="center center" class="digi-questions-container">
            <div fxLayoutAlign="space-between none" fxLayoutGap="4px" class="digi-full-width">
              <!-- Section navigation container -->
              <div fxFlex="100px">
                <div
                  #sectionsSelect
                  *ngIf="independentAssessmentDetails?.sections?.length > 1"
                  fxLayout="column"
                  fxLayoutGap="10px"
                  id="sections-select"
                  class="digi-navigation-section-container digi-sections-wrapper"
                  [ngStyle]="{ top: sectionNavigateContainer + 'px' }"
                >
                  <div fxLayout="column" fxLayoutGap="5px" class="digi-sections-container">
                    <div
                      *ngFor="let section of independentAssessmentDetails?.sections; index as i"
                      matRipple
                      class="digi-section-item digi-cursor"
                      contentType="template"
                      placement="right"
                      [ngClass]="{
                        active: activeSectionIndex === i,
                        error: section?.isMarksNotValid
                      }"
                      [tooltip]="showSectionDetail"
                      (click)="onClickSection({ section: section, index: i })"
                    >
                      {{ 'common.s' | translate | uppercase }}{{ i + 1 | arabicNumber }}

                      <ng-template #showSectionDetail>
                        <div fxFlexLayout="column">
                          <div>
                            {{ 'common.marks' | translate }}:
                            {{ section?.marks | arabicNumber: 2 }}
                          </div>
                          <div *ngIf="section?.duration">
                            {{ 'independentAssessmentAuthoring.duration' | translate }}:
                            {{ section?.duration | arabicNumber: 2 }}
                            {{ 'common.mins' | translate }}
                          </div>
                        </div>
                      </ng-template>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Item creation container -->
              <div fxLayout="column" fxLayoutGap="5px" fxFlex="calc(100% - 210px)">
                <div
                  *ngIf="!isItemReviewer"
                  fxLayout="row"
                  fxLayoutAlign="flex-start center"
                  fxLayoutGap="10px"
                  class="digi-sticky-action-toolbar"
                >
                  <div *ngIf="!isItemReviewer && !isItemAuthor">
                    <mat-checkbox
                      class="digi-ml-15"
                      [(ngModel)]="isSelectAllItem"
                      [indeterminate]="isIntermediateCheckBox"
                      (change)="onChangeSelectAllItem()"
                    >
                      <span class="digi-select-all-label">
                        {{ 'userManagement.common.selectAll' | translate }}
                      </span>
                    </mat-checkbox>
                  </div>
                  <div
                    *ngIf="independentAssessmentDetails?.sections?.length === 1"
                    fxLayout="row"
                    fxLayoutAlign="start center"
                    class="item-type-count-wrapper"
                  >
                    <div
                      fxLayout="row"
                      [innerHTML]="itemTypeCountDisplay.visible | arabicNumber"
                      class="item-type-inline"
                    ></div>

                    <ng-container *ngIf="itemTypeCountDisplay.hiddenCount > 0">
                      <div
                        class="item-type-tooltip digi-ml-5"
                        [tooltip]="itemTypeCountDisplay.hidden"
                        container="body"
                        placement="top"
                        triggers="hover"
                      >
                        <div>+{{ itemTypeCountDisplay.hiddenCount | arabicNumber }}</div>
                      </div>
                    </ng-container>
                    <span
                      id="itemsCount"
                      hidden
                      [innerHTML]="
                        getItemTypesWithCount({
                          items: selectedSection?.items || [],
                          totalCount: false,
                          isHiddenUI: true
                        })?.hiddenUI
                      "
                    ></span>
                  </div>

                  <div
                    *ngIf="!isItemAuthor && !isItemReviewer"
                    fxLayout="row"
                    fxLayoutAlign="end center"
                    fxFlex
                    class="digi-ml-auto digi-container-height"
                  >
                    <!--<div fxLayoutAlign="center center" fxLayoutGap="5px" class="step-navigation">
              <ng-container>
                <div
                  class="step-navigation-item"
                  [class.selected]="isEqualMarks"
                  [ngClass]="{ isDisabled: isPublished }"
                  (click)="!isEqualMarks && onChangeToggleEqualMarks()"
                >
                  <span>{{ 'independentAssessmentAuthoring.equalMarks' | translate }}</span>
                </div>
                <mat-icon class="arrow"> swap_horiz</mat-icon>
                <div
                  class="step-navigation-item"
                  [class.selected]="!isEqualMarks"
                  [ngClass]="{ isDisabled: isPublished }"
                  (click)="isEqualMarks && onClickToggleEqualMarks()"
                >
                  <span>{{ 'independentAssessmentAuthoring.customMarks' | translate }}</span>
                </div>
              </ng-container>
            </div>-->

                    <div
                      fxLayout="row"
                      fxLayoutAlign="end center"
                      fxLayoutGap="10px"
                      class="digi-sticky-action-toolbar"
                    >
                      <div
                        *ngIf="
                          !isSelectAllItem &&
                          isItemSelected &&
                          independentAssessmentDetails.sections.length > 1 &&
                          !isPublished
                        "
                        fxLayout="row"
                        class="digi-move-icon-color digi-cursor"
                        (click)="onClickMoveItemsToSection()"
                      >
                        <mat-icon>drive_file_move</mat-icon>
                        <span class="digi-font-wgt-500 digi-move-item">
                          {{ 'assessment.createItem.moveTo' | translate }}
                        </span>
                      </div>
                      <div
                        *ngIf="selectedItemsForWithdraw?.length && isItemExists && !isPublished"
                        fxLayout="row"
                        fxLayoutAlign="center center"
                        fxLayoutGap="5px"
                        class="digi-withdraw digi-cursor"
                        [matMenuTriggerFor]="withdrawMenu"
                      >
                        <mat-icon>do_disturb_alt</mat-icon>
                        <span class="digi-font-wgt-500 digi-m-5">
                          {{ 'common.withdrawRequest' | translate }}
                        </span>
                        <mat-icon class="digi-cursor digi-icon-size">
                          keyboard_arrow_down
                        </mat-icon>
                      </div>
                      <mat-menu #withdrawMenu="matMenu">
                        <button
                          mat-menu-item
                          *ngFor="let item of assignItems"
                          [disabled]="item.disabled"
                          (click)="onClickWithDrawItem({ type: item?.value })"
                        >
                          <span>{{ getRoleName({ role: item?.text }) }}</span>
                        </button>
                      </mat-menu>

                      <ng-container *ngIf="isItemExists && isItemsSelected">
                        <div
                          fxLayout="row"
                          fxLayoutAlign="center center"
                          fxLayoutGap="3px"
                          class="digi-map-container digi-cursor"
                          (click)="onClickMappingIcon()"
                        >
                          <mat-icon class="digi-icon-map">account_tree</mat-icon>
                          <span class="digi-label-map digi-fw-500">
                            {{ 'common.map' | translate }}
                          </span>
                        </div>
                      </ng-container>

                      <div
                        *ngIf="isItemExists && !isPublished && isItemsSelected"
                        fxLayout="row"
                        fxLayoutAlign="center center"
                        fxLayoutGap="3px"
                        class="digi-cursor digi-blue digi-word-nowrap"
                        (click)="onClickAssignTo({ menuTrigger: menuTrigger })"
                      >
                        <mat-icon>share</mat-icon>
                        <span class="digi-font-wgt-500">
                          {{ 'independentAssessmentAuthoring.assignTo' | translate }}
                        </span>
                        <button
                          mat-icon-button
                          aria-label="Open menu"
                          class="menu-trigger"
                          [matMenuTriggerFor]="menu"
                          #menuTrigger="matMenuTrigger"
                        >
                          <mat-icon class="digi-cursor digi-icon-size">
                            keyboard_arrow_down
                          </mat-icon>
                        </button>
                        <mat-menu #menu="matMenu">
                          <button
                            mat-menu-item
                            *ngFor="let item of assignItems"
                            class="digi-no-background"
                            [disabled]="item.disabled"
                            (click)="
                              onClickMenu({
                                type: item?.text,
                                event: $event,
                                isQuestionBank: isFromQuestionBank
                              })
                            "
                          >
                            <span>{{ getRoleName({ role: item?.text }) | translate }}</span>
                          </button>
                        </mat-menu>
                      </div>
                    </div>
                  </div>
                </div>
                <digi-creation-section-card
                  class="digi-first-creation-card"
                  *ngIf="independentAssessmentDetails?.sections?.length > 1; else withoutSection"
                  [itemTypes]="itemTypes"
                  [independentAssessmentDetails]="independentAssessmentDetails"
                  [sectionValidation]="sectionValidation"
                  [selectedSection]="selectedSection"
                  [activeSectionIndex]="activeSectionIndex"
                  [mappingValues]="mappingValues"
                  [sectionNavigationActions]="sectionNavigationActions"
                  (navigateSection)="onNavigateSection({ sectionIndex: $event })"
                  (changeItemSelect)="onChangeItemSelect()"
                ></digi-creation-section-card>

                <ng-template #withoutSection>
                  <digi-independent-assessment-creation-card
                    class="digi-first-creation-card"
                    [independentAssessmentDetails]="independentAssessmentDetails"
                    [itemTypes]="itemTypes"
                    [isItemAuthor]="isItemAuthor"
                    [isItemReviewer]="isItemReviewer"
                    [isAssessmentSentToAA]="!canSendToAssessmentAuthor"
                    [section]="selectedSection"
                    [mappingValues]="mappingValues"
                    [canUseViewPort]="true"
                    [isReviewCompleted]="isReviewCompleted"
                    (changeItemSelect)="onChangeItemSelect()"
                  ></digi-independent-assessment-creation-card>
                </ng-template>
              </div>

              <!-- Actions container -->
              <div fxFlex="100px" #sectionsSelect>
                <div class="floating-buttons">
                  <digi-creation-floating-buttons
                    *ngIf="!isPublished && !isItemAuthor && !isItemReviewer"
                    [ngStyle]="{ top: sectionNavigateContainer + 'px' }"
                    [isSection]="independentAssessmentDetails?.sections?.length > 1"
                    [canGenerateItemByHeba]="independentAssessmentDetails?.canGenerateItemByHeba"
                    (clickActionButton)="onClickActionButton({ action: $event })"
                    (clickRefresh)="onClickRefresh()"
                  ></digi-creation-floating-buttons>
                </div>
                <digi-window-scrollup-button
                  *ngIf="selectedIndex === 0"
                  class="digi-scroll-button"
                  (clickScrollToTop)="onClickScrollToTop()"
                ></digi-window-scrollup-button>
              </div>
            </div>
          </div>
        </ng-container>

        <ng-container *ngIf="!hasShowItemsPermission">
          <div fxLayoutAlign="space-between center" class="digi-reschedule-container digi-mt-50">
            <div class="digi-fs-14 digi-gray digi-content">
              <div>
                <span class="digi-mandatory digi-fw-500">*</span>
                {{
                  'independentAssessmentAuthoring.youDontHavePermissionToCreateViewItems'
                    | translate
                }}
              </div>
            </div>
          </div>
        </ng-container>
      </div>

      <div
        *ngSwitchCase="1"
        fxLayoutAlign="center none"
        class="digi-mt-10 digi-background-color-grey"
      >
        <digi-independent-assessment-exam-configuration
          [courseGroupId]="independentAssessmentDetails?._courseGroup"
          (changeConfig)="selectedIndex = 2"
          (changeClickedStep)="selectedIndex = $event"
          (changeDeviceType)="selectedDeviceType = $event"
          (changeExamConfig)="isConfigChanged = $event"
          [isScheduleRequest]="independentAssessmentDetails?.isScheduleRequest"
        ></digi-independent-assessment-exam-configuration>
      </div>

      <div
        *ngSwitchCase="2"
        fxLayoutAlign="center center"
        class="digi-background-color-grey digi-assign-student"
      >
        <digi-independent-assign-students
          [hierarchy]="independentAssessmentDetails?.hierarchy"
          [academicYear]="independentAssessmentDetails?.academicYear"
          [courseGroupId]="independentAssessmentDetails?._courseGroup"
          [course]="independentAssessmentDetails?.hierarchy?.course"
          [hierarchyCode]="independentAssessmentDetails?.courseName"
          [groups]="independentAssessmentDetails?.groups"
          [malePlanedCount]="independentAssessmentDetails?.maleStudents"
          [femalePlanedCount]="independentAssessmentDetails?.femaleStudents"
          [isExamRescheduled]="isExamRescheduled"
          [isScheduleRequest]="independentAssessmentDetails?.isScheduleRequest"
          [canValidateFileName]="true"
          (changeAssignStudent)="onChangeAssignStudent()"
        ></digi-independent-assign-students>
      </div>

      <div *ngSwitchCase="3" class="digi-background-color-grey digi-p-15">
        <div *ngIf="isExamRescheduled" class="digi-assign-student">
          <digi-reschedule-exam-note-component></digi-reschedule-exam-note-component>
        </div>
        <div *ngIf="!isExamRescheduled" class="digi-creation-card">
          <div fxLayout="row" fxLayoutAlign="space-between center">
            <div fxLayout="row" fxLayoutAlign=" center" *ngIf="canShowChooseMethod">
              <div class="digi-mr-15">
                {{ 'independentAssessmentAuthoring.chooseMethod' | translate }}
              </div>
              <mat-radio-group
                [disabled]="
                  isPublished || disableTcOptions || independentAssessmentDetails?.raisedByScheduler
                "
                [(ngModel)]="selectedDeviceType"
                (ngModelChange)="
                  onChangeChooseMethod({ selectedTestCenter: $event, isManualChange: true })
                "
              >
                <mat-radio-button
                  *ngFor="let testCenter of testCenterOptions"
                  [checked]="testCenter.type === selectedDeviceType"
                  [value]="testCenter.type"
                  class="digi-mr-10"
                >
                  {{ testCenter.label | translate }}
                </mat-radio-button>
              </mat-radio-group>
            </div>

            <div fxLayout="row" fxLayoutAlign="space-between center">
              <div fxLayoutAlign="center center" class="digi-mt-5 digi-mr-10 digi-text-grey">
                <span class="digi-fw-500 digi-mr-5">
                  {{ 'exams.assignDateTimeHeader.examDuration' | translate }}:
                </span>

                <span class="digi-mr-5">
                  {{ independentAssessmentDetails.settings.totalDuration | arabicNumber }}
                  <b>{{ 'common.mins' | translate }}</b>
                </span>
              </div>
              <div fxLayoutAlign="center center" class="digi-mt-5 digi-text-grey">
                <span class="digi-fw-500 digi-mr-5">
                  {{ 'exams.assignDateTimeHeader.plannedStudents' | translate }}:
                </span>

                <span class="digi-mr-5">
                  {{ 'common.male' | translate }}:
                  <b>{{ maleStudents | arabicNumber }}</b>
                </span>
                <span class="digi-mr-5">
                  {{ 'common.female' | translate }}:
                  <b>{{ femaleStudents | arabicNumber }}</b>
                </span>
              </div>
            </div>
          </div>

          <!-- Old assign date time picker -->
          <digi-assign-date-time-picker
            *ngIf="!isNewUi && canShowAssignDatePicker"
            [isIndependentAssessment]="true"
            [isVirtualLoader]="false"
            [infraType]="infraType"
            [headerDetails]="headerDetails"
            [examDuration]="independentAssessmentDetails.settings.totalDuration"
            [courseGroupId]="independentAssessmentDetails._courseGroup"
            (emitSetTestCenterView)="onEmitSetTestCenterView()"
            (changeSessionTime)="onChangeRescheduleExam({ type: $event })"
          ></digi-assign-date-time-picker>

          <!-- New assign date time picker -->
          <digi-select-exam-start-date
            *ngIf="isNewUi && canShowAssignDatePicker"
            [examDetails]="headerDetails"
            [isIndependentAssessment]="true"
            [courseGroupId]="independentAssessmentDetails._courseGroup"
            [examDuration]="independentAssessmentDetails.settings.totalDuration"
            [isSchedulRequest]="independentAssessmentDetails?.isScheduleRequest"
            (emitSetTestCenterView)="onEmitSetTestCenterView()"
            (changeSessionTime)="onChangeRescheduleExam({ type: $event })"
            (changeTestCenterRequested)="onChangeTestCenterRequest()"
          ></digi-select-exam-start-date>

          <digi-schedule-exam-component
            *ngIf="canShowTestCenterView"
            [courseGroupId]="independentAssessmentDetails._courseGroup"
            [assessmentId]="independentAssessmentDetails._id"
            [courseName]="independentAssessmentDetails.courseName"
            [totalDuration]="independentAssessmentDetails.settings.totalDuration"
            [examDeviceType]="selectedDeviceType"
            [infraType]="infraType"
            [canShowAllTestCenter]="canShowTestCenters"
            [examType]="independentAssessmentDetails?.examType"
            [isScheduleRequest]="independentAssessmentDetails?.isScheduleRequest"
            (changeScheduleExam)="selectedIndex = 4"
            (changeRescheduleExam)="onChangeRescheduleExam({ type: $event })"
          ></digi-schedule-exam-component>
        </div>
      </div>

      <div *ngSwitchCase="4" class="digi-background-color-grey digi-p-15">
        <div *ngIf="isExamRescheduled" class="digi-assign-student">
          <digi-reschedule-exam-note-component></digi-reschedule-exam-note-component>
        </div>
        <digi-assign-exam-admin
          *ngIf="!isExamRescheduled"
          [courseGroupId]="independentAssessmentDetails?._courseGroup"
          [isExamRescheduled]="isExamRescheduled"
          [isNewUi]="isNewUi"
        ></digi-assign-exam-admin>
      </div>
    </ng-container>
  </section>

  <digi-comment-section
    *ngIf="canShowComment"
    class="digi-comment-container"
    [item]="selectedItem"
    [isItemAuthor]="isItemAuthor"
    [isItemReviewer]="isItemReviewer"
    [isAdminReply]="isAdminReply"
    [isReviewCompleted]="isReviewCompleted"
    (changeComment)="onChangeComment({ data: $event })"
  ></digi-comment-section>
</div>

<div
  *ngIf="isFromQuestionBank"
  fxLayout="column"
  fxLayoutAlign="center center"
  id="create"
  class="digi-mt-10"
>
  <div fxLayout="row" fxLayoutAlign="space-between center" fxFlex class="digi-full-width">
    <mat-icon class="digi-ml-30 digi-blue digi-cursor mat-icon-rtl-mirror" (click)="onClickBack()">
      arrow_back
    </mat-icon>
    <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="10px" class="digi-mr-30">
      <div *ngIf="isCreatingQuestionBankItemTab" fxLayoutAlign="start center" fxLayoutGap="10px">
        <div class="digi-text-grey">{{ 'common.course' | translate | titlecase }}:</div>
        <div class="digi-width-full">
          <digi-dropdown
            class="digi-course-list"
            placeholder="common.selectCourse"
            panelClass="digi-overflow-option-container digi-dropdown digi-assessment-item-author-dropdown"
            panelWidth="700px"
            format="subItems"
            [items]="courses"
            [selectedItem]="selectedCourse"
            [nestedKeysLabel]="['hierarchy', 'course', 'code']"
            [nestedKeysLabelTwo]="['hierarchy', 'course', 'name']"
            [searchKeysLabel]="searchKeysLabel"
            [subLabel]="['hierarchy', 'program', 'name']"
            [subLabelTwo]="['hierarchy', 'term', 'code']"
            [subLabelThree]="['hierarchy', 'curriculum', 'name']"
            [subLabelFour]="['hierarchy', 'year', 'name']"
            [subLabelFive]="['hierarchy', 'level', 'name']"
            [subLabelSix]="['hierarchy', 'rotationGroup', 'code']"
            [nestedToolTip]="['name']"
            [showTooltip]="true"
            [canChangeAllOptionFontWeight]="true"
            [isIndependentAssessment]="true"
            [isVirtualScroll]="false"
            [disabled]="isCourseNavigation"
            (valueChange)="onChangeCourse({ course: $event?.value })"
          ></digi-dropdown>
        </div>
      </div>

      <div *ngIf="isItemReviewer" fxLayoutAlign="end center" class="digi-full-width">
        <digi-button
          button="primary"
          class="digi-mr-30"
          [disabled]="isReviewCompleted"
          (click)="onClickCompleteReview()"
        >
          <span class="digi-fs-16">
            {{ 'independentAssessmentAuthoring.completeReview' | translate }}
          </span>
        </digi-button>
      </div>

      <div
        *ngIf="isCreatingQuestionBankItemTab && getSelectedItems?.length"
        fxLayout="row"
        fxLayoutAlign="center center"
        fxLayoutGap="3px"
        class="digi-cursor digi-blue"
        (click)="onClickSendToReviewer($event)"
      >
        <mat-icon>share</mat-icon>
        <span class="digi-font-wgt-500">
          {{
            'independentAssessmentAuthoring.sendToReviewer'
              | translate: { reviewersRoleName: getRoleLabel({ role: 'reviewer' }) }
          }}
        </span>
      </div>
    </div>
  </div>

  <div fxLayoutAlign="space-between none" fxLayoutGap="4px" class="digi-mt-8 digi-full-width">
    <!-- Section navigation container -->
    <div fxFlex="100px">
      <div
        #sectionsSelect
        *ngIf="independentAssessmentDetails?.sections?.length > 1"
        fxLayout="column"
        fxLayoutGap="10px"
        id="sections-select"
        class="digi-navigation-section-container digi-sections-wrapper"
        [ngStyle]="{ top: sectionNavigateContainer + 'px' }"
      >
        <div fxLayout="column" fxLayoutGap="5px" class="digi-sections-container">
          <div
            *ngFor="let section of independentAssessmentDetails?.sections; index as i"
            matRipple
            class="digi-section-item digi-cursor"
            contentType="template"
            placement="right"
            [ngClass]="{ active: activeSectionIndex === i }"
            [tooltip]="showSectionDetail"
            (click)="onClickSection({ section: section, index: i })"
          >
            {{ 'common.s' | translate | uppercase }}{{ i + 1 }}

            <ng-template #showSectionDetail>
              <div fxFlexLayout="column">
                <div>{{ 'common.marks' | translate }}: {{ section?.marks | arabicNumber: 2 }}</div>
                <div *ngIf="section?.duration">
                  {{ 'independentAssessmentAuthoring.duration' | translate }}:
                  {{ section?.duration | arabicNumber: 2 }} {{ 'common.mins' | translate }}
                </div>
              </div>
            </ng-template>
          </div>
        </div>
      </div>
    </div>

    <!-- Item creation container -->
    <div fxLayout="column" fxLayoutGap="10px" fxFlex="calc(100% - 210px)">
      <div fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="10px">
        <div *ngIf="!isItemReviewer" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="10px">
          <mat-checkbox
            class="digi-ml-15"
            [(ngModel)]="isSelectAllItem"
            [indeterminate]="isIntermediateCheckBox"
            (change)="onChangeSelectAllItem()"
          >
            <span class="digi-select-all-label">
              {{ 'userManagement.common.selectAll' | translate }}
            </span>
          </mat-checkbox>
          <div fxLayout="row" fxLayoutAlign="start center" class="item-type-count-wrapper">
            <div
              fxLayout="row"
              [innerHTML]="itemTypeCountDisplay.visible | arabicNumber"
              class="item-type-inline"
            ></div>

            <ng-container *ngIf="itemTypeCountDisplay.hiddenCount > 0">
              <div
                class="item-type-tooltip digi-ml-5"
                [tooltip]="itemTypeCountDisplay.hidden"
                container="body"
                placement="top"
                triggers="hover"
              >
                <div>+{{ itemTypeCountDisplay.hiddenCount | arabicNumber }}</div>
              </div>
            </ng-container>
            <span
              id="itemsCount"
              hidden
              [innerHTML]="
                getItemTypesWithCount({
                  items: selectedSection?.items || [],
                  totalCount: false,
                  isHiddenUI: true
                })?.hiddenUI
              "
            ></span>
          </div>
        </div>
        <span fxFlex></span>
        <div fxLayoutAlign="flex-start center" fxLayoutGap="5px" class="digi-pr-10">
          <span class="digi-list-topic-color digi-fw-500">
            {{ 'common.items' | translate | titlecase }}:
          </span>
          <span class="digi-fw-500 digi-list-topic-color">
            {{ getTotalItems() | arabicNumber }}
          </span>
        </div>
        <div
          fxLayout="row"
          fxLayoutAlign="space-between center"
          fxLayoutGap="5px"
          class="digi-refresh-icon digi-cursor digi-blue"
          (click)="onClickRefresh()"
        >
          <mat-icon>refresh</mat-icon>
          <span class="digi-fw-500">{{ 'common.refresh' | translate }}</span>
        </div>
      </div>

      <digi-creation-section-card
        class="digi-first-creation-card"
        *ngIf="independentAssessmentDetails?.sections?.length > 1; else withoutSection"
        [itemTypes]="itemTypes"
        [independentAssessmentDetails]="independentAssessmentDetails"
        [sectionValidation]="sectionValidation"
        [selectedSection]="selectedSection"
        [activeSectionIndex]="activeSectionIndex"
        [mappingValues]="mappingValues"
        [sectionNavigationActions]="sectionNavigationActions"
        (navigateSection)="onNavigateSection($event)"
        (changeItemSelect)="onChangeItemSelect()"
      ></digi-creation-section-card>

      <ng-template #withoutSection>
        <digi-independent-assessment-creation-card
          class="digi-first-creation-card"
          [independentAssessmentDetails]="independentAssessmentDetails"
          [itemTypes]="itemTypes"
          [isItemAuthor]="isItemAuthor"
          [isItemReviewer]="isItemReviewer"
          [isAssessmentSentToAA]="!canSendToAssessmentAuthor"
          [section]="selectedSection"
          [mappingValues]="mappingValues"
          [canUseViewPort]="true"
          [isReviewCompleted]="isReviewCompleted"
          [isFromQuestionBank]="isFromQuestionBank"
          (changeItemSelect)="onChangeItemSelect()"
        ></digi-independent-assessment-creation-card>
      </ng-template>
    </div>

    <!-- Actions container -->
    <div fxFlex="100px" #sectionsSelect>
      <div class="floating-buttons">
        <digi-creation-floating-buttons
          *ngIf="!isPublished && !isItemAuthor && !isItemReviewer"
          class="floating-buttons"
          [ngStyle]="{ top: sectionNavigateContainer + 'px' }"
          [isFromQuestionBank]="true"
          [isSection]="independentAssessmentDetails?.sections?.length > 1"
          [isCreatingQuestionBankItemTab]="isCreatingQuestionBankItemTab"
          (clickActionButton)="onClickActionButton({ action: $event })"
          (clickRefresh)="onClickRefresh()"
        ></digi-creation-floating-buttons>
      </div>

      <digi-window-scrollup-button
        *ngIf="selectedIndex === 0"
        class="digi-scroll-button"
        (clickScrollToTop)="onClickScrollToTop()"
      ></digi-window-scrollup-button>
    </div>
  </div>

  <div *ngIf="canShowComment">
    <digi-comment-section
      class="digi-comment-container"
      [item]="selectedItem"
      [isItemReviewer]="isItemReviewer"
      [isAdminReply]="isAdminReply"
      [isReviewCompleted]="isReviewCompleted"
      (changeComment)="onChangeComment({ data: $event })"
    ></digi-comment-section>
  </div>
</div>

<digi-custom-slider [sliderId]="settingSliderId" (closeBackPanel)="onClickSettingIcon()">
  <div class="digi-p-10 digi-bg-white">
    <div fxLayout="row" fxLayoutAlign="none center" class="digi-setting-slider-header">
      <div class="digi-setting-header-title" fxFlex>
        {{ 'common.settings' | translate | titlecase }}
      </div>
      <div class="digi-setting-header-close digi-cursor digi-p-10" (click)="onClickSettingIcon()">
        <mat-icon>close</mat-icon>
      </div>
    </div>
    <div *ngIf="showSetting" class="digi-custom-slider-content-body">
      <digi-creation-settings
        [settings]="independentAssessmentDetails?.settings"
        [isItemAuthor]="isItemAuthor"
      ></digi-creation-settings>
    </div>
  </div>
</digi-custom-slider>

<digi-custom-slider [sliderId]="mappingSliderId" (closeBackPanel)="onClickMappingIcon()">
  <digi-item-mapping-slider
    #itemMappingSlider
    [selectedSection]="selectedSection"
    [getSelectedItemIds]="getSelectedItemIds"
    [mappingValues]="mappingValues"
    [assessmentId]="independentAssessmentDetails?._id"
    [selectedCourse]="selectedCourse"
    [independentAssessmentDetails]="independentAssessmentDetails"
    (refreshAssessmentDetails)="refreshAssessmentDetails()"
  ></digi-item-mapping-slider>
</digi-custom-slider>

<digi-custom-slider
  *ngIf="false"
  [sliderId]="'digi-auto-generate'"
  (closeBackPanel)="onClickAutoGenerateSlider()"
>
  <digi-auto-generate
    [item]="selectedItem"
    [assessmentId]="assessmentId"
    (closeBackPanel)="onClickAutoGenerateSlider()"
  ></digi-auto-generate>
</digi-custom-slider>

<digi-custom-slider [sliderId]="'importSlider'">
  <digi-excel-questions-import
    *ngIf="showImportSlider"
    (closeBackPanel)="onClickImport({ items: $event })"
  ></digi-excel-questions-import>
</digi-custom-slider>
