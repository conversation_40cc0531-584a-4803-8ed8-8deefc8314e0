/* tslint:disable:member-ordering */
import { AfterViewInit, Component, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { MatMenuTrigger } from '@angular/material/menu'
import { ActivatedRoute, Router } from '@angular/router'

import { TranslateService } from '@ngx-translate/core'
import { ToastrService } from 'ngx-toastr'
import { Subject, Subscription } from 'rxjs'
import { debounceTime, take } from 'rxjs/operators'

import {
  EItemType,
  EPermissionType,
  itemReviewType,
  ONSITE_WITH_PROCTOR,
  REMOTE_WITHOUT_PROCTOR,
  REQUESTED,
  ROUTES,
  SCHEDULED,
  STUDENT_OWNED_DEVICE,
  TEST_CENTER_OWNED_DEVICE
} from '@appcore/app/core/constants'
import {
  IExamCenterSingleGroup,
  IExamType,
  IGroup,
  ITestCenterOption
} from '@appcore/app/models/exam-center-management'
import { IConfirmPopupData } from '@appcore/app/models/exam-readiness/exam-readiness-on-going.interface'
import {
  AuthService,
  cloneDeep,
  ErrorHandlerService,
  EventsService,
  FacultyService,
  FilterService,
  GlobalService,
  RouteService,
  SettingsService,
  UtilService
} from '@appcore/app/services'

import { IndependentAssessmentService } from '../..'
import {
  IAcademicYear,
  IAssessmentItem,
  IAssignedItemStatus,
  IExamConfig,
  IExamDetail,
  IIndependentAssessmentDetails,
  IIndependentSection,
  IItemType,
  IMappingValues,
  IPublishIndependentAssessmentDetails,
  IQuestion,
  IScheduleSettings,
  ISectionValidation,
  ITextValue,
  IUpdateComment
} from '../../../../models'
import { AssessmentService } from '../../assessment.service'
import { ItemValidationService } from '../../item-validation.service'
import { AssessmentPublishPopupComponent } from '../assessment-publish-popup/assessment-publish-popup.component'
import { AssignExamAdminComponent } from '../assign-exam-admin/assign-exam-admin.component'
import { AssignItemAuthorPopupComponent } from '../assign-item-author'
import { ConfirmPopupComponent } from '../confirm-popup/confirm-popup.component'
import { ExtractAssessmentPopupComponent } from '../extract-assessment-popup/extract-assessment-popup.component'
import { IndependentAssessmentExamConfigurationComponent } from '../independent-assessment-exam-configuration/independent-assessment-exam-configuration.component'
import { IndependentAssignStudentComponent } from '../independent-assign-students/independent-assign-students.component'
import { MoveItemsIntoSectionPopupComponent } from '../move-item-popup'
import { ScheduleExamComponent } from '../schedule-exam'
import { PrintAssessmentDialogComponent } from '../print-assessment-dialog/print-assessment-dialog.component'
import { ItemMappingSliderComponent } from '@appcore/app/core/components/item-mapping-slider/item-mapping-slider.component'
import { ArabicNumberPipe } from '@appcore/app/core/pipes/arabic-number.pipe'
import { AutoGenerateItemPopupComponent } from '@appcore/app/core/components/assessment/create-item-mcq/digi-auto-genarate/auto-generate-item-popup.component'
import { IExtractedQuestion } from '@appcore/app/models/global-settings'

type ItemTypeCountDisplay = {
  visible: string
  hidden: string
  hiddenCount: number
  total: number
  hiddenUI: string
}

@Component({
  selector: 'digi-independent-assessment-creation',
  templateUrl: './independent-assessment-creation.component.html',
  styleUrls: ['./independent-assessment-creation.component.css']
})
export class IndependentAssessmentCreationComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild(IndependentAssessmentExamConfigurationComponent)
  independentAssessmentExamConfigurationComponent!: IndependentAssessmentExamConfigurationComponent

  @ViewChild(IndependentAssignStudentComponent)
  independentAssignStudentComponent!: IndependentAssignStudentComponent

  @ViewChild(ScheduleExamComponent)
  scheduleExamComponent!: ScheduleExamComponent

  @ViewChild(AssignExamAdminComponent)
  assignExamAdminComponent!: AssignExamAdminComponent

  @ViewChild('menuTrigger')
  menuTrigger!: MatMenuTrigger

  @ViewChild(ItemMappingSliderComponent)
  itemMappingSlider!: ItemMappingSliderComponent

  selectedSectionId: string
  steps: string[] = [
    'independentAssessmentAuthoring.itemCreation',
    'independentAssessmentAuthoring.examConfiguration',
    'independentAssessmentAuthoring.studentDetailsIndependentAssessment',
    'independentAssessmentAuthoring.scheduledDetails',
    'independentAssessmentAuthoring.proctorDetails'
  ]
  selectedTab = 1
  buttonPositionTop = 55
  assessmentId = ''
  programName = ''
  itemTypes: IItemType[] = []
  independentAssessmentDetails: IIndependentAssessmentDetails
  independentAssessmentDetailsClone: IIndependentAssessmentDetails
  onClickIndependentAssessmentSubscription: Subscription
  debounceInput$ = new Subject<boolean>()
  isTotalTotalMarksInvalid = false
  isTotalDurationInvalid = false
  sectionValidation: ISectionValidation = {
    isTimeInValid: false,
    isMarksInValid: false,
    isZeroDuration: false
  }
  selectedIndex = 0
  multipleQuestionItemTypes = ['MQ', 'CQ', 'EMQ', 'CSAQ', 'MIT']
  updateItemsSubscription: Subscription
  courseId = ''
  courseName = ''
  selectedSection: IIndependentSection
  activeSectionIndex = 0
  mappingValues: IMappingValues = {
    selectedClos: [],
    selectedTaxonomies: [],
    cloMapping: null,
    taxonomies: [],
    subjectTopics: null,
    selectedSubjectTopic: null
  }
  isItemAuthor = false
  isItemReviewer = false
  isExamRescheduled = false
  canSendToAssessmentAuthor = true
  isReviewCompleted = false
  authorName = ''
  groupId: string
  examCategory = ''
  headerDetails: Partial<IExamCenterSingleGroup> = {
    examType: {
      locationAndProctoringType: ONSITE_WITH_PROCTOR
    } as IExamType,
    group: {
      maleStudents: 0,
      femaleStudents: 0
    } as IGroup
  }
  testCenterOptions: ITestCenterOption[] = [
    {
      label: 'independentAssessmentAuthoring.centralizedInfrastructure',
      type: TEST_CENTER_OWNED_DEVICE
    },
    {
      label: 'independentAssessmentAuthoring.UsingCustomTestCenter',
      type: STUDENT_OWNED_DEVICE
    }
  ]
  selectedDeviceType = TEST_CENTER_OWNED_DEVICE
  studentOwnedDevice = STUDENT_OWNED_DEVICE
  assessmentDetails: IPublishIndependentAssessmentDetails
  assignItems: ITextValue[] = [
    { text: 'itemAuthor', disabled: false, value: 'ITEM_AUTHOR' },
    { text: 'medicalEducationist', disabled: false, value: 'REVIEWER' }
  ]
  readonly assessmentStatus = itemReviewType
  selectedItem: IQuestion
  canShowComment = false
  isAdminReply = false
  paramsIndex: number
  scrollOffset = 0
  sectionNavigateContainer = 0
  isAllQuestionsValidated = false
  isConfigChanged = false
  isSelectAllItem = false
  isIntermediateCheckBox = false
  itemsCount = 0

  private onScrollMainContainerSubscription: Subscription
  isFromQuestionBank: boolean
  isCourseNavigation = false
  courses: IExamDetail[] = []
  selectedAcademicYear: IAcademicYear
  currentAcademicYear: IAcademicYear
  selectedCourse: IExamDetail
  selectedCourseClone: IExamDetail
  isAllCourse: boolean
  searchKeysLabel = [
    ['hierarchy', 'program', 'name'],
    ['hierarchy', 'program', 'code'],
    ['hierarchy', 'term', 'name'],
    ['hierarchy', 'term', 'code'],
    ['hierarchy', 'curriculum', 'name'],
    ['hierarchy', 'curriculum', 'code'],
    ['hierarchy', 'year', 'name'],
    ['hierarchy', 'year', 'code'],
    ['hierarchy', 'level', 'name'],
    ['hierarchy', 'level', 'code'],
    ['hierarchy', 'rotationGroup', 'name'],
    ['hierarchy', 'rotationGroup', 'code'],
    ['hierarchy', 'course', 'code'],
    ['hierarchy', 'course', 'name']
  ]
  isCreatingQuestionBankItemTab = false
  questionBankChipType: string
  marksClone: number
  isNewUi = false
  disableTcOptions = true
  infraType: string
  showSetting = false
  attachmentStatusSubscription: Subscription
  showImportSlider = false
  hasShowItemsPermission = false

  readonly settingSliderId = 'setting-slider'
  readonly mappingSliderId = 'mapping-slider'

  itemTypeCountDisplay: ItemTypeCountDisplay = {
    visible: '',
    hidden: '',
    hiddenCount: 0,
    total: 0,
    hiddenUI: ''
  }

  constructor(
    private filterService: FilterService,
    private routeService: RouteService,
    private activatedRoute: ActivatedRoute,
    private independentAssessmentService: IndependentAssessmentService,
    private errorHandler: ErrorHandlerService,
    private toastrService: ToastrService,
    private translateService: TranslateService,
    private assessmentService: AssessmentService,
    private itemValidationService: ItemValidationService,
    private globalService: GlobalService,
    public dialog: MatDialog,
    private settingsService: SettingsService,
    private utilService: UtilService,
    private router: Router,
    private eventService: EventsService,
    private route: ActivatedRoute,
    private facultyService: FacultyService,
    private arabicNumberPipe: ArabicNumberPipe,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.hasShowItemsPermission = this.authService.checkHasPermission({
      permissionType: EPermissionType.ACTION,
      moduleName: ROUTES.MODULE_ASSESSMENT,
      pageName: ROUTES.PAGE_INDEPENDENT_ASSESSMENT_CREATION,
      actionName: ROUTES.ACTION_SHOW_ITEMS
    })

    this.updateSectionNavigateContainer()
    this.onScrollMainContainerSubscription = this.eventService
      .onScrollMainContainer()
      .subscribe((type) => {
        if (type !== 'last') {
          this.updateSectionNavigateContainer(type)
        }
      })

    this.activatedRoute.data.subscribe((data) => {
      this.isItemReviewer = data?.isItemReviewer
      this.isItemAuthor = data.isItemAuthor
    })

    this.activatedRoute.queryParamMap.subscribe((params) => {
      if (params.has('selectedSectionId')) {
        this.selectedSectionId = params.get('selectedSectionId')
      }
    })

    this.activatedRoute.data.subscribe((data) => (this.isItemAuthor = data.isItemAuthor))

    if (this.isItemAuthor) {
      this.selectedTab = 0
    }

    this.debounceInput$.pipe(debounceTime(500)).subscribe((isMarks) => {
      this.updateIndependentAssessmentMarksAndDuration({ isMarks })
    })

    this.updateItemsSubscription = this.independentAssessmentService.updateItemsCount.subscribe(
      (canScrollSection = true) => {
        this.calculateTotalItems()
        this.independentAssessmentService.distributeMarks({
          assessment: this.independentAssessmentDetails as IIndependentAssessmentDetails
        })
        this.refreshAssessment()

        if (canScrollSection) {
          this.scrollSectionsTo({ direction: 'end', id: 'section-select' })
        }
      }
    )

    this.onClickIndependentAssessmentSubscription =
      this.independentAssessmentService?.clickIndependentAssessmentAction
        ?.pipe(debounceTime(100))
        .subscribe(
          ({
            action,
            itemId,
            sectionId,
            itemTypeCode,
            questionId,
            questionIndex,
            isAdminReply,
            type
          }) => {
            this.onClickActionButton({
              action,
              itemId,
              sectionId,
              itemTypeCode,
              questionId,
              questionIndex,
              isAdminReply,
              type
            })
          }
        )

    this.activatedRoute.queryParams.subscribe(
      ({
        programName,
        courseId,
        selectedIndex,
        isFromQuestionBank = false,
        isCreatingQuestionBankItemTab = false,
        questionBankChipType = 'All',
        isCourseNavigation = false
      }) => {
        this.programName = programName
        this.courseId = courseId
        this.isFromQuestionBank = isFromQuestionBank === 'true'
        this.questionBankChipType = questionBankChipType
        this.isCreatingQuestionBankItemTab = isCreatingQuestionBankItemTab === 'true'
        this.isCourseNavigation = isCourseNavigation === 'true'

        if (selectedIndex) {
          this.paramsIndex = Number(selectedIndex)
        }
      }
    )

    this.activatedRoute.paramMap.subscribe((params) => {
      this.assessmentId = params.get('assessmentId')
      this.independentAssessmentService.assessmentId = this.assessmentId

      if (this.assessmentId) {
        this.getAssessmentDetails()
      }
    })

    this.getItemTypes()

    if (this.isFromQuestionBank) {
      this.getCurrentAcademicYear()
    }

    this.attachmentStatusSubscription = this.eventService.emitAttachmentStatus$.subscribe(
      (data) => {
        const sections = this.independentAssessmentDetails?.sections?.find(
          (section) => section?._id === data.sectionId
        )
        const items = sections?.items?.find((item) => item?._id === data.itemId)
        let attachment = null

        if (data?.questionId) {
          const question = items?.stem?.questions?.find(
            (question) => question?._id === data.questionId
          )
          attachment = question?.attachments?.find(
            (attachment) => attachment?._id === data.attachmentId
          )
        } else {
          attachment = items?.stem?.question?.attachments?.find(
            (attachment) => attachment?._id === data.attachmentId
          )
        }

        attachment.webmStatus = data.status

        if (data?.webmLink) {
          attachment.link = data.webmLink
        }

        this.refreshAssessment()
      }
    )
  }

  ngAfterViewInit() {
    this.updateSectionNavigateContainer()
  }

  private getCurrentAcademicYear() {
    this.filterService.getCurrentAcademicYear().subscribe(
      ({ data }) => {
        this.currentAcademicYear = data
        this.selectedAcademicYear = this.currentAcademicYear
        this.getAllCourses()
      },
      (err) => this.errorHandler.errorLog(err)
    )
  }

  private getAllCourses() {
    this.facultyService.getAllCourses().subscribe(
      ({ data }) => {
        this.courses = data

        if (this.courseId || this.courseName) {
          this.selectedCourse = this.courses?.find(
            (course) => course?._id === this.courseId || course?.name === this.courseName
          )

          this.selectedCourseClone = cloneDeep(this.selectedCourse)

          if (this.isFromQuestionBank) {
            this.globalService.setHeader({
              headerName: 'common.assessment',
              secondHeader: 'independentAssessmentAuthoring.assessmentCreation',
              activeHeader: this.getCourseCodeAndName(),
              toolTipText: this.getToolTipText(),
              canShowTooltip: true
            })
          }
        }
      },
      (err) => this.errorHandler.errorLog(err)
    )
  }

  private updateSectionNavigateContainer(scrollType?: string) {
    const screenWidth = window.innerWidth
    const hasSingleSection = this.independentAssessmentDetails?.sections?.length === 1

    if (scrollType === 'top') {
      if (screenWidth < 1210) {
        this.sectionNavigateContainer = hasSingleSection ? 210 : 285
      } else {
        this.sectionNavigateContainer = hasSingleSection ? 112 : 210
      }
    } else if (scrollType === 'down') {
      this.sectionNavigateContainer = 180
    } else {
      if (screenWidth < 1210) {
        this.sectionNavigateContainer = hasSingleSection ? 210 : 285
      } else {
        this.sectionNavigateContainer = hasSingleSection ? 112 : 210
      }
    }
  }

  onChangeTestCenterRequest() {
    this.independentAssessmentDetails.isScheduleRequest = true
  }

  onChangeRescheduleExam({ type }: { type: boolean }) {
    this.independentAssessmentService.canShowTestCenters = type
    this.independentAssessmentService.sessionDetails =
      this.independentAssessmentService.allTestCenterDetails =
      this.independentAssessmentService.examTimeDetails =
      this.independentAssessmentService.testCenterDetails =
        undefined
    this.getScheduleSettings({ canValidate: false, deviceType: this.selectedDeviceType })
  }

  ngOnDestroy(): void {
    this.onClickIndependentAssessmentSubscription.unsubscribe()
    this.updateItemsSubscription.unsubscribe()
    this.debounceInput$.unsubscribe()
    this.independentAssessmentService.resetSelectedValues({})
    this.independentAssessmentService.selectedExamConfig = undefined
    this.attachmentStatusSubscription?.unsubscribe()
  }

  private getSubjectTopics() {
    this.independentAssessmentService
      .getSubjectTopics({ courseHierarchyId: this.independentAssessmentDetails?.courseHierarchyId })
      .subscribe(({ data }) => {
        Object.assign(this.mappingValues, { subjectTopics: data })
      })
  }

  private getTaxonomy() {
    this.independentAssessmentService.getTaxonomy({}).subscribe(({ data }) => {
      Object.assign(this.mappingValues, { taxonomies: data })
      this.mappingValues.taxonomies[0].isExpanded = true
      this.mappingValues = { ...this.mappingValues }
    })
  }

  private getCloMapping() {
    const { program, curriculum, course } = this.independentAssessmentService?.assessmentHierarchy

    this.independentAssessmentService
      .getCloMapping({
        programId: program?._id,
        curriculumId: curriculum?._id,
        curriculumCode: curriculum?.code,
        curriculumName: curriculum?.name,
        courseCode: course?.code,
        assessmentId: this.assessmentId
      })
      .subscribe(({ data }) => {
        Object.assign(this.mappingValues, { cloMapping: data })
      })
  }

  private getAssessmentDetails({ isItemAttached = false }: { isItemAttached?: boolean } = {}) {
    const type = this.isItemAuthor ? 'itemAuthor' : this.isItemReviewer ? 'reviewer' : ''
    this.independentAssessmentService
      .getIndependentAssessmentDetails({
        assessmentId: this.assessmentId,
        type,
        isFromQuestionBank: this.isFromQuestionBank,
        questionBankChipType: this.questionBankChipType
      })
      .subscribe(
        ({ data }) => {
          if (!this.courseId) {
            this.courseId = data?.courseHierarchyId
            this.selectedCourse = this.courses?.find((course) => course?._id === this.courseId)
          }
          this.isNewUi = data?.testCenterScheduleVersion === 'v2'
          this.independentAssessmentService.canUseWiris = data?.canUseWiris
          data?.sections?.forEach((section, i) => {
            section.isActive = i === 0
            section?.items?.forEach((item, itemIndex) => (item.isActive = itemIndex === 0))
          })
          this.independentAssessmentDetails = data
          this.independentAssessmentDetails?.sections?.forEach((section) => {
            section.isEqualMarks = section?.isEqualMarks ?? true
          })

          this.independentAssessmentService.createdBy =
            this.independentAssessmentDetails?.createdBy?.authorName
          this.isSelectAllItem = false
          this.isIntermediateCheckBox = false
          this.updateSectionNavigateContainer()
          this.globalService.setHeader({
            headerName: 'common.assessment',
            secondHeader: this.isItemReviewer
              ? 'assessment.title.reviewAssessment'
              : 'independentAssessmentAuthoring.assessmentCreation',
            activeHeader: this.getCourseCodeAndName(),
            toolTipText: this.getToolTipText(),
            canShowTooltip: true
          })

          if (this.independentAssessmentDetails?.hierarchy?.course?._id) {
            this.courseName = this.independentAssessmentDetails?.courseName
          }

          if (
            this.independentAssessmentDetails?.raisedByScheduler &&
            this.independentAssessmentDetails?.isNotScheduled
          ) {
            this.isExamRescheduled = true
          }

          if (this.isItemAuthor) {
            this.canSendToAssessmentAuthor =
              this.independentAssessmentDetails?.canSentToAssessmentAuthor
            this.authorName =
              this.globalService.getFullName(
                this.independentAssessmentDetails?.createdBy?.authorName
              ) ?? this.independentAssessmentDetails?.assessmentAuthorLabel
            this.independentAssessmentDetails.items.forEach((item) => {
              item.requestType = item?.requestType || 'NEW'
            })
            this.createSection({ items: this.independentAssessmentDetails?.items })
          }

          if (this.isItemReviewer) {
            this.isReviewCompleted = this.independentAssessmentDetails?.isReviewCompleted
            this.setItemReviewStatus()
            this.independentAssessmentDetails?.items?.forEach((item) => {
              item.requestType = item?.requestType || 'NEW'
            })
            this.createSection({ items: this.independentAssessmentDetails?.items })
          }
          this.selectedDeviceType = this.independentAssessmentDetails?.settings?.examDeviceType
          this.independentAssessmentService.examDeviceType = this.selectedDeviceType

          const sectionList = this.independentAssessmentDetails?.sections || []
          const selectedIndex = sectionList.findIndex(
            (section) => section?._id === this.selectedSectionId
          )

          this.activeSectionIndex = selectedIndex !== -1 ? selectedIndex : 0
          this.selectedSection = sectionList[this.activeSectionIndex]
          this.independentAssessmentService.selectedSection = this.selectedSection

          this.setDefaultItemSelect({ isItemAttached })

          this.independentAssessmentService.assessmentHierarchy = data?.hierarchy
          this.independentAssessmentService.courseName = data?.courseName
          this.independentAssessmentService.isPublished = ['PUBLISHED', 'COMPLETED'].includes(
            data?.status
          )
          this.independentAssessmentService.isCompleted = data?.status === 'COMPLETED'
          this.independentAssessmentService.isEqualMarks = data?.settings?.isEqualMarks
          this.independentAssessmentService.isExamOngoing = data?.examStatus === 'ON_GOING'

          if (this.independentAssessmentDetails?.sections?.length === 1) {
            this.selectedSection.isEqualMarks = true
          }

          if (data?.settings?.isEqualMarks) {
            this.independentAssessmentService.distributeMarks({ assessment: data })
          }

          this.independentAssessmentService.raisedByScheduler = data?.raisedByScheduler

          if (this.independentAssessmentDetails?.sections?.length) {
            if (this.independentAssessmentDetails?.sections?.length === 1) {
              this.independentAssessmentService.withoutSectionId =
                this.independentAssessmentDetails?.sections[0]?._id
              this.independentAssessmentDetails.sections[0].isActive = true
              this.updateItemTypeCount()
            }
          }

          if (this.paramsIndex !== undefined) {
            this.selectedIndex = Number(this.paramsIndex)

            if (this.paramsIndex === 3) {
              this.getScheduleSettings({
                canValidate: false,
                deviceType: this.independentAssessmentService.examDeviceType
              })
            }
          }

          this.independentAssessmentDetailsClone = cloneDeep(this.independentAssessmentDetails)
          this.setLatestTab()
          this.getSubjectTopics()
          this.getCloMapping()
          this.getTaxonomy()
          this.marksClone = cloneDeep(this.independentAssessmentDetails?.settings?.totalMarks)

          if (isItemAttached) {
            this.independentAssessmentService.updateAssessmentDetail()
          }
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  private getCourseCodeAndName() {
    if (
      !this.independentAssessmentDetails?.hierarchy?.course &&
      !this.selectedCourse?.hierarchy?.course
    ) {
      return 'independentAssessmentAuthoring.noCourseName'
    }

    const course =
      this.independentAssessmentDetails?.hierarchy?.course ||
      this.selectedCourseClone?.hierarchy?.course
    const { code, name } = course || {}

    return `${code} - ${name}`
  }

  private getToolTipText() {
    if (!this.independentAssessmentDetails?.hierarchy && !this.selectedCourse?.hierarchy) {
      return ''
    }

    const hierarchy = this.independentAssessmentDetails?.hierarchy || this.selectedCourse?.hierarchy

    return this.utilService.getCourseDetails({ hierarchy })
  }

  private createSection({ items }: { items: IQuestion[] }) {
    Object.assign(this.independentAssessmentDetails, {
      sections: [{ items }]
    })
  }

  private setItemReviewStatus() {
    this.independentAssessmentDetails.sections?.forEach(({ items }) => {
      items?.forEach((item) => {
        if (item.reviewType === this.assessmentStatus.reviewed) {
          item.isReviewed = item.reviewType === this.assessmentStatus.reviewed
        }

        if (item.reviewType === this.assessmentStatus.CorrectionRequired) {
          item.isCorrectionRequired = item.reviewType === this.assessmentStatus.CorrectionRequired
        }
      })
    })
  }

  onClickToggleEqualMarks() {
    if (this.isEqualMarks) {
      const data = {
        isError: false,
        title: 'independentAssessmentAuthoring.confirmReset',
        message: 'independentAssessmentAuthoring.customMarkReset',
        additionalInfo: 'independentAssessmentAuthoring.resetAndDistributeMsg',
        primaryButton: 'independentAssessmentAuthoring.resetAndDistribute',
        secondaryButton: 'common.noGoBack',
        icon: 'add_alert_bell'
      }

      this.openConfirmPopup({ data })
        .afterClosed()
        .subscribe((res) => {
          if (!res) {
            return (this.isEqualMarks = !this.isEqualMarks)
          }

          this.independentAssessmentService.distributeMarks({
            assessment: this.independentAssessmentDetails as IIndependentAssessmentDetails
          })
          this.updateMarkType()
          this.refreshAssessment()
        })
    } else {
      this.resetMarks()
      this.updateMarkType()
      this.refreshAssessment()
    }
  }

  private resetMarks() {
    this.independentAssessmentDetails?.sections?.forEach((section) => {
      section?.items.forEach((item) => {
        if (this.multipleQuestionItemTypes.includes(item?.itemType?.code)) {
          item?.stem?.questions.forEach((question) => {
            question.marks = 0
          })
        } else {
          item.stem.question.marks = 0
        }
      })
    })
  }

  private openConfirmPopup({
    data,
    isItemsWithDraw = false,
    isCompleteReview = false,
    isPendingCorrection = false,
    isExamRescheduled = false,
    isSendToAAPopup = false,
    isMultipleReviewers = false
  }: {
    data: Partial<IConfirmPopupData>
    isItemsWithDraw?: boolean
    isCompleteReview?: boolean
    isPendingCorrection?: boolean
    isExamRescheduled?: boolean
    isSendToAAPopup?: boolean
    isMultipleReviewers?: boolean
  }) {
    return this.dialog.open(ConfirmPopupComponent, {
      data,
      panelClass: ['digi-padding-none', 'dialog-fix'],
      disableClose: true,
      direction: this.settingsService.getOptions().dir,
      ...(isItemsWithDraw && { minWidth: '540px', height: '220px' }),
      ...(isCompleteReview && { width: '450px' }),
      ...(isPendingCorrection && { width: '510px' }),
      ...(isExamRescheduled && { width: '500px', height: '220px' }),
      ...(isSendToAAPopup && { width: '520px', height: '250px' }),
      ...(isMultipleReviewers && { width: '450px' })
    })
  }

  private updateMarkType() {
    this.independentAssessmentService.updateMarkType().subscribe(
      () => {
        this.independentAssessmentService.isItemMarksInvalid = false
        this.independentAssessmentService.hasZeroMarks = false
        this.independentAssessmentService.updateAssessmentDetail()
      },
      (err) => this.errorHandler.errorLog(err)
    )
  }

  private setLatestTab(): void {
    const {
      isStudentAssigned,
      isDateAdded,
      isTimeAdded,
      maleStudents,
      femaleStudents,
      isStudentUploaded,
      isGroupAdded,
      isExamConfigured,
      locationAndProctoringType
    } = this.independentAssessmentDetails

    if (
      (locationAndProctoringType === 'remote_without_proctor' || isStudentAssigned) &&
      isDateAdded &&
      isTimeAdded
    ) {
      this.independentAssessmentService.latestTab = 4
    } else if ((maleStudents || femaleStudents) && (isStudentUploaded || isGroupAdded)) {
      this.independentAssessmentService.latestTab = 3
    } else if (isExamConfigured) {
      this.independentAssessmentService.latestTab = 2
    }
  }

  private getItemTypes() {
    this.assessmentService.getItemTypes().subscribe(
      ({ data }) => {
        this.itemTypes = data
        const order = ['MCQ', 'TF', 'SAQ', 'EQ', 'CSAQ', 'CQ', 'MQ', 'EMQ', 'MIT', 'MCQ-MRA', 'HQ']
        this.itemTypes = this.itemTypes
          .filter((itemType) =>
            ['MCQ', 'TF', 'SAQ', 'CQ', 'EQ', 'MIT', 'CSAQ', 'MQ', 'EMQ', 'MCQ-MRA', 'HQ'].includes(
              itemType.code
            )
          )
          .sort((a, b) => order.indexOf(a.code) - order.indexOf(b.code))
      },
      (err) => this.errorHandler.errorLog(err)
    )
  }

  onClickBack() {
    if (this.isFromQuestionBank) {
      return this.routeService.transitionToQuestionBank({
        selectedTab: this.isCourseNavigation ? 1 : 2,
        canShowTopicOnly: this.isCourseNavigation,
        courseId: this.isCourseNavigation ? this.courseId : '',
        isFromQuestionBank: this.isFromQuestionBank,
        questionBankChipType: this.questionBankChipType,
        isCreatingQuestionBankItemTab: this.isCreatingQuestionBankItemTab,
        isCourseNavigation: this.isCourseNavigation
      })
    }

    this.independentAssessmentService.resetSelectedValues({})
    this.routeService.transitionToIndependentAssessmentDashboard({
      courseId: this.courseId,
      isItemAuthor: this.isItemAuthor,
      isItemReviewer: this.isItemReviewer
    })
  }

  onClickStepperBack() {
    if (this.selectedIndex > 0) {
      const stepperIndex = this.selectedIndex - 1
      if (stepperIndex === 3) {
        return this.getScheduleSettings({
          canValidate: false,
          stepperIndex,
          deviceType: this.selectedDeviceType
        })
      }

      this.selectedIndex--

      if (this.selectedIndex === 0) {
        this.setSelectedIndexInQuery()
      }
    }
  }

  onClickActionButton({
    action,
    itemId,
    sectionId,
    itemTypeCode,
    questionId,
    questionIndex,
    isAdminReply = false,
    type
  }: {
    action: string
    itemId?: string
    sectionId?: string
    itemTypeCode?: string
    questionId?: string
    questionIndex?: number
    isAdminReply?: boolean
    type?: string
  }) {
    switch (action) {
      case 'addItem':
        if (!this.independentAssessmentDetails?.hierarchy?.course?._id && !this.courseId) {
          this.toastrService.error(
            this.translateService.instant('assessment.itemCreate.courseRequiredForAddItem')
          )

          return
        }

        this.onClickAddItem()
        break

      case 'uploadItems':
        this.onClickExtractFullAssessment({ type, itemId })
        break

      case 'addSection':
        this.onClickAddSection()
        break

      case 'deleteItem':
        this.onClickDeleteItem({ itemId, sectionId, questionId, questionIndex, itemTypeCode })
        break

      case 'changeItemType':
        this.onChangeItemType({ itemId, sectionId, itemTypeCode })
        break

      case 'clickCard':
        this.onClickCard({ itemId, sectionId })
        break

      case 'withdrawItem':
        this.onClickWithDrawItem({ selectedItemId: itemId, type })
        break

      case 'assignItemAuthor':
        this.onClickAssignItemAuthor({ selectedItemId: itemId })
        break

      case 'comment':
        this.onClickShowComment({ itemId, isAdminReply })
        break

      case 'closeComment':
        this.canShowComment = false
        this.independentAssessmentService.updateAssessmentDetail()
        break

      case 'cloneOldItem':
        this.onClickCloneOldItem({ itemId, sectionId })
        break

      case 'generateByHeba':
        this.onClickAutoGenerate()
        break
    }
  }

  onClickImport({ items }: { items?: IExtractedQuestion[] }) {
    this.showImportSlider = !this.showImportSlider
    this.utilService.customSliderToggle({ id: 'importSlider' })

    if (!this.showImportSlider && items?.length) {
      this.dialog
        .open(MoveItemsIntoSectionPopupComponent, {
          width: '350px',
          height: 'auto',
          panelClass: ['digi-padding-none', 'dialog-fix'],
          disableClose: true,
          direction: this.settingsService.getOptions().dir,
          data: {
            sections: this.independentAssessmentDetails.sections,
            multipleQuestionItemTypes: this.multipleQuestionItemTypes,
            isItemImport: true,
            importedItems: items
          }
        })
        .afterClosed()
        .subscribe((res) => {
          if (!res) {
            return
          }

          this.importGeneratedQuestions({ items, sectionId: res })
        })
    }
  }

  private importGeneratedQuestions({
    items,
    sectionId
  }: {
    items?: IExtractedQuestion[]
    sectionId: string
  }) {
    this.independentAssessmentService
      .importGeneratedQuestions({
        query: {
          assessmentId: this.assessmentId,
          sectionId
        },
        payload: {
          items,
          hierarchy: this.independentAssessmentDetails?.hierarchy
        },
        isImportFromExcel: true
      })
      .subscribe(
        ({ message }) => {
          this.toastrService.success(message)
          this.getAssessmentDetails({})

          const index = this.independentAssessmentDetails?.sections?.findIndex(({ _id }) => {
            return sectionId === _id
          })

          this.onClickSection({
            section: this.independentAssessmentDetails?.sections[index],
            index
          })
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  onClickAutoGenerate() {
    this.dialog
      .open(AutoGenerateItemPopupComponent, {
        panelClass: ['dialog-padding-top-bottom-none', 'digi-white-bg', 'dialog-fix'],
        maxWidth: '100%',
        width: '90%',
        maxHeight: '90vh',
        disableClose: true,
        direction: this.settingsService.getOptions().dir,
        data: {
          ...{
            mappingValues: this.mappingValues,
            hierarchy: this.independentAssessmentDetails?.hierarchy,
            type: 'independentAssessment',
            assessmentId: this.assessmentId,
            sectionId: this.selectedSection?._id
          }
        }
      })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          return
        }

        this.getAssessmentDetails({})
      })
  }

  private onClickCloneOldItem({ itemId, sectionId }: { itemId: string; sectionId?: string }) {
    this.independentAssessmentService.updateAssessmentDetail()
    this.independentAssessmentService
      .cloneOldItem({
        itemId,
        sectionId: this.selectedSection._id || sectionId,
        courseId: this.courseId
      })
      .subscribe(
        ({ data, message }) => {
          this.toastrService.success(message)
          const itemIndex = this.selectedSection?.items?.findIndex(({ _id }) => _id === itemId)
          let item = data?.item

          if (itemIndex !== -1) {
            const previousItem = this.selectedSection.items[itemIndex]?.stageNo
            Object.assign(this.selectedSection.items[itemIndex], {
              ...data?.item,
              requestType: 'NEW',
              stageNo: Number(previousItem ?? 0),
              isAttachedItem: false,
              canEdit: true,
              requestStatus: 'On Going',
              isDiscarded: false
            })
          }

          if (this.multipleQuestionItemTypes.includes(item.itemType.code)) {
            item?.stem?.questions?.forEach((question, i) => {
              question.marks = this.selectedSection?.items[itemIndex]?.stem?.questions[i]?.marks
            })
          } else {
            item.stem.question.marks =
              this.selectedSection?.items[itemIndex]?.stem?.question?.marks || 0
          }

          Object.assign(item, {
            duration: this.selectedSection?.items[itemIndex]?.duration || 0,
            settings: this.selectedSection?.items[itemIndex]?.settings
          })

          if (this.independentAssessmentDetails?.settings?.isEqualMarks) {
            this.independentAssessmentService.distributeMarks({
              assessment: this.independentAssessmentDetails as IIndependentAssessmentDetails
            })
          }

          this.independentAssessmentService.updateAssessmentDetail()
          this.refreshAssessment()
          this.onClickCard({
            itemId: this.selectedSection?.items[itemIndex]._id,
            sectionId: this.selectedSection?._id
          })
          this.independentAssessmentService.canScrollToActiveItem = true
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  private getSelectedItem({ itemId }: { itemId: string }) {
    const selectedSection = this.independentAssessmentDetails?.sections?.find((section) =>
      section?.items?.some((item) => item?._id === itemId)
    )

    if (!selectedSection) {
      return
    }

    const itemIndex = selectedSection?.items?.findIndex((item) => item?._id === itemId)
    const selectedItem = selectedSection?.items[itemIndex]

    return { selectedItem, itemIndex }
  }

  private onClickShowComment({ itemId, isAdminReply }: { itemId: string; isAdminReply?: boolean }) {
    if (!this.independentAssessmentDetails?.sections[0]?.items[0]?._id) {
      this.createSection({ items: this.independentAssessmentDetails?.items })
    }

    const selectedItem = this.getSelectedItem({ itemId })?.selectedItem
    const selectedItemIndex = this.getSelectedItem({ itemId })?.itemIndex
    this.selectedItem = {
      ...selectedItem,
      itemNo: selectedItemIndex + 1
    }
    this.isAdminReply = isAdminReply
    this.canShowComment = true
  }

  onClickWithDrawItem({ selectedItemId, type }: { selectedItemId?: string; type?: string }) {
    if (selectedItemId) {
      return this.withDrawItemRequest({ selectedItemId, type })
    }

    if (!this.checkAllSelectedItemsAreAssigned({ type })) {
      return
    }

    const data = {
      title: 'assessment.stepWizard.confirmation',
      message: 'independentAssessmentAuthoring.withdrawRequestConfirmation',
      additionalInfo: 'independentAssessmentAuthoring.areYouSureWantToWithdraw',
      primaryButton: 'independentAssessmentAuthoring.yesWithdraw'
    }

    this.openConfirmPopup({ data, isItemsWithDraw: true })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          return
        }

        this.withDrawItemRequest({ selectedItemId, type })
      })
  }

  private withDrawItemRequest({
    selectedItemId,
    type
  }: {
    selectedItemId?: string
    type?: string
  }) {
    const payload = {
      itemsIds: selectedItemId
        ? [selectedItemId]
        : this.selectedItemsForWithdraw.map(({ _id }) => _id),
      type: type ?? 'REVIEWER'
    }

    this.independentAssessmentService
      .withDrawItemRequest({ assessmentId: this.assessmentId, payload })
      .subscribe(
        ({ message }) => {
          this.toastrService.success(message)

          if (selectedItemId) {
            this.independentAssessmentDetails?.sections?.forEach((section) => {
              section?.items?.find((item) => {
                if (item?._id === selectedItemId) {
                  item.isSelected = true
                  item.stageNo = 0
                  item.canEdit = true
                  item.isAssigned = false
                }
              })
            })

            return
          }

          this.setWithdrawnItemsStatus()
          this.refreshAssessment()
          this.onClickRefresh()
          this.isIntermediateCheckBox = this.isSelectAllItem = false
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  private setWithdrawnItemsStatus() {
    this.independentAssessmentDetails?.sections?.forEach((section) => {
      section?.items?.forEach((item) => {
        this.getSelectedItemIds?.forEach((itemId) => {
          if (itemId === item?._id) {
            Object.assign(item, {
              stageNo: 0,
              canEdit: true,
              isAssigned: false,
              reviewers: [],
              isSelected: false,
              isInvalid: false
            })
          }
        })
      })
    })
  }

  private onClickCard({
    itemId,
    sectionId,
    currentSection
  }: {
    itemId: string
    sectionId: string
    currentSection?: IIndependentSection
  }) {
    if (currentSection) {
      this.selectedSection = currentSection
      this.independentAssessmentService.selectedSection = this.selectedSection
    }

    this.selectedSection.isActive = false
    this.selectedSection.items.forEach((sectionItem) => {
      sectionItem.isActive = this.selectedSection._id === sectionId && sectionItem._id === itemId
      this.independentAssessmentDetails?.sections?.forEach((section) => {
        if (!section) {
          return
        }

        section.isActive = false
        section?.items?.forEach((item) => {
          item.isActive = section._id === sectionId && item._id === itemId

          if (item.isActive) {
            this.selectedSection.isActive = true
          }
          if (!item.isActive) {
            item?.stem?.questions?.forEach((question) => {
              question.isActive = false
            })
          }

          if (item?.isActive) {
            section.isActive = true
          }
        })
      })
    })
  }

  private onChangeItemType({
    itemId,
    sectionId,
    itemTypeCode
  }: {
    itemId: string
    sectionId: string
    itemTypeCode: string
  }) {
    this.independentAssessmentService
      .updateIndependentAssessmentItemType({
        itemId,
        sectionId,
        itemTypeCode,
        assessmentId: this.assessmentId,
        isItemAuthor: this.isItemAuthor
      })
      .subscribe(
        ({ data }) => {
          const currentSection = this.isItemAuthor
            ? this.independentAssessmentDetails?.sections[0]
            : this.independentAssessmentDetails?.sections?.find(
                (section) => section?._id === sectionId
              )
          Object.assign(data?.item, { requestType: 'NEW', canEdit: true })
          const itemIndex = currentSection?.items?.findIndex((item) => item._id === itemId)
          const reviewers = currentSection?.items[itemIndex]?.reviewers
          Object.assign(data?.item, { reviewers })
          currentSection.items[itemIndex] = {
            ...data?.item,
            settings: currentSection?.items[itemIndex]?.settings
          }

          this.independentAssessmentDetails.canIASentToReviewer = data?.item?.canIASentToReviewer
          this.onClickCard({ itemId, sectionId, currentSection })
          this.independentAssessmentService.distributeMarks({
            assessment: this.independentAssessmentDetails as IIndependentAssessmentDetails
          })
          this.calculateTotalItems()
          this.refreshAssessment()
          this.onChangeItemSelect()
          this.updateItemTypeCount()
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  private calculateTotalItems() {
    this.independentAssessmentDetails.settings.totalNoOfItems =
      this.independentAssessmentDetails.sections.reduce((acc, section) => {
        return (
          acc +
          section.items.reduce((itemAcc, item) => {
            if (this.multipleQuestionItemTypes.includes(item.itemType.code)) {
              return itemAcc + item.stem.questions.length
            } else {
              return itemAcc + 1
            }
          }, 0)
        )
      }, 0)
  }

  stopArrowKeyPropagation({ event }: { event: KeyboardEvent }): void {
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
      event.stopPropagation()
    }
  }

  onClickDeleteItem({
    itemId,
    sectionId,
    questionId,
    questionIndex,
    itemTypeCode
  }: {
    itemId: string
    sectionId: string
    questionId: string
    questionIndex?: number
    itemTypeCode?: string
  }) {
    if (questionId) {
      this.deleteQuestion({ itemId, questionId, questionIndex, itemTypeCode })

      return
    }

    const section = this.independentAssessmentDetails?.sections?.find(
      (currentSection) => currentSection?._id === sectionId
    )
    if (section?.items) {
      section.items.forEach((item) => {
        item.isActive = item?._id === itemId
      })
    }

    if (section?.items?.length === 1) {
      this.toastrService.error(
        this.translateService.instant('independentAssessmentAuthoring.cannotDeleteLastItem')
      )

      return
    }

    this.independentAssessmentService
      .deleteIndependentAssessmentItem({
        itemId,
        sectionId,
        assessmentId: this.assessmentId
      })
      .subscribe(
        () => {
          let selectedItemIndex = section?.items?.findIndex((item) => item?._id === itemId)
          section?.items?.splice(selectedItemIndex, 1)

          if (section?.items?.length > 0) {
            const nextItemIndex =
              selectedItemIndex < section?.items?.length ? selectedItemIndex : selectedItemIndex - 1
            section.items[nextItemIndex].isActive = true
          }

          this.independentAssessmentService.canScrollToActiveItem = true

          this.selectedSection = section
          this.independentAssessmentService.selectedSection = this.selectedSection
          this.calculateTotalItems()
          this.updateSectionNavigateContainer()

          this.toastrService.success(this.translateService.instant('common.deletedSuccessfully'))
          this.independentAssessmentService.distributeMarks({
            assessment: this.independentAssessmentDetails as IIndependentAssessmentDetails
          })
          this.refreshAssessment()
          this.onChangeItemSelect()
          this.independentAssessmentService.updateAssessmentDetail()
          this.updateItemTypeCount()
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  private refreshAssessment() {
    this.independentAssessmentDetails = cloneDeep(this.independentAssessmentDetails)

    const selectedSectionIndex = this.independentAssessmentDetails.sections.findIndex(
      (section) => section._id === this.selectedSection._id
    )

    if (selectedSectionIndex !== -1) {
      this.selectedSection = cloneDeep(
        this.independentAssessmentDetails.sections[selectedSectionIndex]
      )
      this.independentAssessmentService.selectedSection = this.selectedSection
    }
  }

  private deleteQuestion({
    itemId,
    questionId,
    questionIndex,
    itemTypeCode
  }: {
    itemId: string
    questionId: string
    questionIndex: number
    itemTypeCode: string
  }) {
    const sectionId = this.independentAssessmentDetails?.sections?.find((section) =>
      section?.items?.some((item) => item._id === itemId)
    )?._id

    this.independentAssessmentService
      .deleteSubItem({
        itemId,
        questionId,
        assessmentId: this.assessmentId,
        sectionId
      })
      .subscribe(
        () => {
          const sectionIndex =
            this.independentAssessmentDetails?.sections?.findIndex(
              (section) => section?._id === sectionId
            ) || 0

          const itemIndex = this.independentAssessmentDetails?.sections[
            sectionIndex
          ]?.items?.findIndex((item) => item._id === itemId)

          this.independentAssessmentDetails?.sections[sectionIndex]?.items[
            itemIndex
          ]?.stem?.questions?.splice(questionIndex, 1)

          if (itemTypeCode === EItemType?.MQ) {
            this.independentAssessmentDetails?.sections[sectionIndex]?.items[
              itemIndex
            ]?.options?.splice(questionIndex, 1)
          }

          this.independentAssessmentService.canScrollToActiveItem = true
          this.selectedSection = this.independentAssessmentDetails.sections[sectionIndex]
          this.independentAssessmentService.selectedSection = this.selectedSection
          this.calculateTotalItems()
          this.independentAssessmentService.distributeMarks({
            assessment: this.independentAssessmentDetails
          })
          this.toastrService.success(this.translateService.instant('common.deletedSuccessfully'))
          this.scrollSectionsTo({ id: 'sections-select' })
          this.refreshAssessment()
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  private scrollSectionsTo({
    direction = 'start',
    id
  }: {
    direction?: ScrollLogicalPosition
    id: string
  }) {
    const createElement = document.getElementById(id)
    if (createElement) {
      createElement.scrollIntoView({ behavior: 'smooth', block: direction })
    }
  }

  private onClickAddItem() {
    const hierarchyClone = cloneDeep(
      this.isFromQuestionBank
        ? this.selectedCourse?.hierarchy
        : this.independentAssessmentDetails?.hierarchy
    )
    const keys = ['program', 'term', 'year', 'curriculum', 'level']
    keys.forEach((key) => {
      if (hierarchyClone[key]?.type) {
        delete hierarchyClone[key].type
      }
    })
    const payload = { hierarchy: hierarchyClone }
    const sectionIndex = this.independentAssessmentDetails?.sections?.findIndex(
      (section) => this.selectedSection?._id === section?._id
    )
    const sectionId = this.selectedSection._id
    const activeItemType = this.selectedSection?.items?.find((item) => item.isActive)

    this.independentAssessmentService
      .addIndependentAssessmentItem({
        assessmentId: this.assessmentId,
        sectionId,
        itemTypeCode: activeItemType?.itemType?.code || EItemType.MCQ,
        payload
      })
      .subscribe(
        ({ data: { item } }) => {
          Object.assign(item, { isActive: false, requestType: 'NEW' })
          this.independentAssessmentDetails.sections[sectionIndex].items.push({
            ...item,
            canEdit: true
          })
          this.selectedSection = this.independentAssessmentDetails.sections[sectionIndex]
          this.independentAssessmentService.selectedSection = this.selectedSection

          this.onClickCard({ itemId: item._id, sectionId })
          this.toastrService.success(this.translateService.instant('common.addedSuccessfully'))
          this.independentAssessmentService.distributeMarks({
            assessment: this.independentAssessmentDetails
          })
          this.calculateTotalItems()
          this.refreshAssessment()
          this.onChangeItemSelect()
          this.independentAssessmentService.canScrollToActiveItem = true
          this.updateItemTypeCount()
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  private onClickAddSection() {
    this.independentAssessmentService
      .addIndependentAssessmentSection({ assessmentId: this.assessmentId })
      .subscribe(
        ({ data }) => {
          Object.assign(data?.item, { requestType: 'NEW' })

          this.independentAssessmentDetails.sections.push({
            _id: data.sectionId,
            items: [{ ...data?.item, canEdit: true }],
            duration: 0,
            marks: 0,
            settings: {
              answeringSequenceOfItem: 'RESTRICTED_SEQUENCE'
            },
            totalItems: 0,
            isEqualMarks: true
          })
          this.independentAssessmentDetails.settings.totalNoOfSections += 1
          this.onClickCard({ itemId: data?.item?._id, sectionId: data?.sectionId })
          this.independentAssessmentService.distributeMarks({
            assessment: this.independentAssessmentDetails as IIndependentAssessmentDetails
          })
          this.calculateTotalItems()
          this.activeSectionIndex = this.independentAssessmentDetails.sections?.length - 1
          this.selectedSection = {
            canScrollToTop: true,
            ...this.independentAssessmentDetails.sections[this.activeSectionIndex]
          }
          this.independentAssessmentService.selectedSection = this.selectedSection
          this.setDefaultItemSelect()
          this.refreshAssessment()
          this.updateSectionNavigateContainer()
          this.scrollSectionsTo({ direction: 'end', id: 'sections-select' })
          this.setQueryParam()
          this.onChangeItemSelect()
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  onValueChange({ isMarks }: { isMarks: boolean }) {
    if (isMarks && this.marksClone === this.independentAssessmentDetails?.settings?.totalMarks) {
      return
    }

    if (this.selectedTab === 1) {
      this.independentAssessmentDetails.settings = {
        ...this.independentAssessmentDetails.settings,
        totalMarks: this.independentAssessmentDetails.settings.totalMarks,
        totalDuration: this.independentAssessmentDetails.settings.totalDuration
      }
    }

    this.independentAssessmentService.canScrollToActiveItem = true
    this.debounceInput$.next(isMarks)
    if (!isMarks) {
      const { isSectionTimeValid } =
        this.independentAssessmentService.checkIfSectionsMarksAndDurationMatchesSettings({
          independentAssessmentDetails: this.independentAssessmentDetails,
          sectionValidation: this.sectionValidation
        })

      if (!isSectionTimeValid) {
        this.sectionValidation.isTimeInValid = true
      }
    }
  }

  private updateIndependentAssessmentMarksAndDuration({
    canRescheduleExam,
    isMarks
  }: { canRescheduleExam?: boolean; isMarks?: boolean } = {}) {
    this.independentAssessmentService
      .updateIndependentAssessmentMarksAndDuration({
        assessmentId: this.assessmentId,
        marks: this.independentAssessmentDetails?.settings?.totalMarks || 0,
        duration: this.independentAssessmentDetails?.settings?.totalDuration || 0,
        canRescheduleExam
      })
      .subscribe(
        () => {
          if (isMarks) {
            this.marksClone = cloneDeep(this.independentAssessmentDetails?.settings?.totalMarks)
          }

          if (this.independentAssessmentDetails?.settings?.totalMarks) {
            this.isTotalTotalMarksInvalid = false
          }

          if (this.independentAssessmentDetails?.settings?.totalDuration) {
            this.isTotalDurationInvalid = false
          }

          if (
            this.independentAssessmentDetails?.settings?.totalMarks !==
              this.independentAssessmentDetailsClone?.settings?.totalMarks ||
            isMarks
          ) {
            this.independentAssessmentService.distributeMarks({
              assessment: this.independentAssessmentDetails as IIndependentAssessmentDetails
            })
          }
          this.selectedSection = this.independentAssessmentDetails.sections[this.activeSectionIndex]
          this.independentAssessmentService.selectedSection = this.selectedSection
          this.independentAssessmentService.updateAssessmentDetail()
        },
        (err) => {
          if (err.status === 702) {
            return this.dialog
              .open(ConfirmPopupComponent, {
                data: {
                  isError: true,
                  title: 'learningOutcomeReports.warning',
                  message: err.error.message
                },
                width: '505px',
                height: '233px',
                panelClass: ['digi-padding-none', 'dialog-fix'],
                disableClose: true,
                direction: this.settingsService.getOptions().dir
              })
              .afterClosed()
              .subscribe((res) => {
                if (res) {
                  this.updateIndependentAssessmentMarksAndDuration({ canRescheduleExam: true })
                  this.independentAssessmentService.resetScheduleData.next(true)
                  this.independentAssessmentService.sessionDetails =
                    this.independentAssessmentService.allTestCenterDetails =
                    this.independentAssessmentService.examTimeDetails =
                      undefined
                  this.independentAssessmentService.canShowTestCenters = false
                }
              })
          }

          if (err.status === 701) {
            this.independentAssessmentDetails.settings.totalDuration =
              err?.error?.data?.plannedDuration
          }

          this.errorHandler.errorLog(err)
        }
      )
  }

  private fetchAssessmentDetails() {
    return this.independentAssessmentService
      .getAssessmentDetails({
        courseGroupId: this.independentAssessmentDetails._courseGroup
      })
      .subscribe(
        ({ data }) => {
          this.assessmentDetails = data
          const { isExamConfigured, isProctorAssigned, isScheduled, isStudentUploaded } =
            this.assessmentDetails?.config

          Object.assign(this.independentAssessmentService.assessmentValidation, {
            isExamConfigured,
            isStudentUploaded,
            isScheduled,
            isProctorAssigned
          })

          if (isExamConfigured && isProctorAssigned && isScheduled && isStudentUploaded) {
            if (!this.selectedIndex) {
              this.updateAssessmentMarks({ onlySave: true })
            }

            this.openPublishPopup()
          } else {
            this.toastrService.error(
              this.translateService.instant(
                'independentAssessmentAuthoring.completeAllStepsToPublish'
              )
            )
          }
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  get hasCorrectionRequiredItem() {
    return this.independentAssessmentDetails?.sections?.some((section) =>
      section?.items?.some((item) => item?.requestStatus === 'Correction Required')
    )
  }

  get isAnyItemWithItemAuthor() {
    return this.independentAssessmentDetails?.sections?.some((section) =>
      section?.items?.some(({ stageNo }) => [10, 11].includes(stageNo))
    )
  }

  private navigateToDashboard() {
    this.routeService.transitionToIndependentAssessmentDashboard({
      courseId: this.courseId
    })
  }

  onClickNext({ canPublish = false }: { canPublish?: boolean }) {
    if (!canPublish) {
      if (this.selectedIndex === 0) {
        if (this.isPublished) {
          this.isAllQuestionsValidated = true
        }

        const { settings, ...details } = this.independentAssessmentDetails || {}
        const { settings: cloneSettings, ...detailsClone } =
          this.independentAssessmentDetailsClone || {}

        if (
          (!this.isPublished && JSON.stringify(details) !== JSON.stringify(detailsClone)) ||
          !this.isAllQuestionsValidated
        ) {
          this.validateItemCreation({ canFreeFlow: true })
        } else {
          this.validateItemCreation({ onlyValidate: true, canFreeFlow: true })
        }

        return
      }

      if (this.selectedIndex === 1) {
        if (this.isPublished) {
          return (this.selectedIndex = 2)
        }

        this.independentAssessmentExamConfigurationComponent.validateSelectedFilter({})

        return
      }

      if (this.selectedIndex === 2) {
        if (this.isPublished || this.independentAssessmentDetails?.isScheduleRequest) {
          return (this.selectedIndex = 3)
        }

        this.independentAssignStudentComponent.validateAssignStudents()
        return
      }

      if (this.selectedIndex === 3) {
        if (this.isPublished || this.isExamRescheduled) {
          return (this.selectedIndex = 4)
        }

        if (this.canShowAssignDatePicker) {
          const isCentralizedTC = this.selectedDeviceType === TEST_CENTER_OWNED_DEVICE

          if (isCentralizedTC && !this.independentAssessmentService.canShowTestCenters) {
            return this.toastrService.error(
              this.translateService.instant(
                'independentAssessmentAuthoring.pleaseScheduleAndContinue'
              )
            )
          }
        }

        this.scheduleExamComponent.validateScheduleExam()

        return
      }
    }

    if (this.isExamRescheduled) {
      const data = {
        isError: true,
        title: 'independentAssessmentAuthoring.assessmentCancelled',
        message: 'independentAssessmentAuthoring.assessmentCancelledNote',
        additionalInfo: 'independentAssessmentAuthoring.contactScheduler',
        primaryButton: 'common.goToDashboard'
      }
      return this.openConfirmPopup({ data, isExamRescheduled: this.isExamRescheduled })
        .afterClosed()
        .subscribe((res) => {
          if (!res) {
            return
          }

          this.navigateToDashboard()
        })
    }

    if (this.selectedIndex === 4 || canPublish) {
      if (!canPublish && !this.assignExamAdminComponent.validateAssignExamAdminAndProctor()) {
        return
      }

      if (canPublish && !this.validateItemCreation({ onlyValidate: true, stepperIndex: 0 })) {
        return (this.selectedIndex = 0)
      }

      if (this.hasCorrectionRequiredItem) {
        const data = {
          isError: true,
          title: 'independentAssessmentAuthoring.pendingCorrection',
          message: 'independentAssessmentAuthoring.pendingCorrectionNote'
        }
        return this.openConfirmPopup({ data, isPendingCorrection: true })
          .afterClosed()
          .subscribe((res) => {
            if (!res) {
              return
            }

            if (this.selectedIndex === 1 && this.isConfigChanged) {
              this.independentAssessmentExamConfigurationComponent.validateSelectedFilter({
                canHideToast: true
              })

              this.independentAssessmentService?.clickPublishAction.pipe(take(1)).subscribe(() => {
                this.fetchAssessmentDetails()
              })
            } else {
              this.fetchAssessmentDetails()
            }

            return
          })
      }

      if (this.isAnyItemWithItemAuthor) {
        return this.toastrService.error(
          this.translateService.instant('independentAssessmentAuthoring.itemAuthorNote', {
            itemAuthor: this.utilService.getRoleName({ role: 'itemAuthor' })
          })
        )
      }

      if (this.selectedIndex === 1 && this.isConfigChanged) {
        this.independentAssessmentExamConfigurationComponent.validateSelectedFilter({
          canHideToast: true
        })

        this.independentAssessmentService?.clickPublishAction.pipe(take(1)).subscribe(() => {
          this.fetchAssessmentDetails()
        })
      } else {
        this.fetchAssessmentDetails()
      }
    }
  }

  private openPublishPopup() {
    this.dialog
      .open(AssessmentPublishPopupComponent, {
        data: { assessmentDetails: this.assessmentDetails },
        maxHeight: '98vh',
        width: '100%',
        maxWidth: '90%',
        disableClose: true,
        direction: this.settingsService.getOptions().dir,
        panelClass: ['dialog-padding-top-bottom-none', 'dialog-fix']
      })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          return
        }

        this.independentAssessmentService.isPublished = true

        this.getAssessmentDetails()
      })
  }

  getScheduleSettings({
    canValidate,
    stepperIndex,
    deviceType,
    isManualChange
  }: {
    canValidate: boolean
    deviceType: string
    stepperIndex?: number
    isManualChange?: boolean
  }) {
    this.independentAssessmentService
      .getScheduleSettings({
        assessmentId: this.assessmentId
      })
      .subscribe(
        ({ data }) => {
          this.infraType = data?.infraType
          this.independentAssessmentService.scheduleSettingDetails = data
          this.validateSessionDateAndTime({
            canValidate,
            stepperIndex,
            deviceType,
            isManualChange
          })
        },
        (err) => {
          this.errorHandler.errorLog(err)
        }
      )
  }

  private validateSessionDateAndTime({
    canValidate,
    stepperIndex,
    deviceType,
    isManualChange
  }: {
    canValidate: boolean
    deviceType: string
    stepperIndex?: number
    isManualChange?: boolean
  }) {
    if (!this.infraType && this.independentAssessmentService.scheduleSettingDetails) {
      this.infraType = this.independentAssessmentService.scheduleSettingDetails.infraType
    }

    this.disableTcOptions =
      !this.canChangeSchedule ||
      !this.independentAssessmentService.scheduleSettingDetails.canChangeInfraType

    const setIndex = () => {
      if (stepperIndex !== undefined) {
        this.selectedIndex = stepperIndex
      }
    }

    if (!isManualChange) {
      if (this.infraType !== 'BOTH') {
        this.selectedDeviceType = deviceType =
          this.infraType === 'CENTRALIZED' ? TEST_CENTER_OWNED_DEVICE : STUDENT_OWNED_DEVICE
      }
    }

    if (this.isPublished) {
      return setIndex()
    }

    this.independentAssessmentService.examDeviceType = this.selectedDeviceType
    const isCentralizedTC = deviceType === TEST_CENTER_OWNED_DEVICE
    const setCanShowTCView = () => {
      this.independentAssessmentService.canShowTestCenters =
        deviceType === STUDENT_OWNED_DEVICE ||
        (isCentralizedTC &&
          [SCHEDULED, REQUESTED].includes(this.independentAssessmentService.sessionDetails.status))
    }

    const validate = () => {
      if (isCentralizedTC && !this.independentAssessmentService.canShowTestCenters) {
        return this.toastrService.error(
          this.translateService.instant('independentAssessmentAuthoring.pleaseScheduleAndContinue')
        )
      }

      this.scheduleExamComponent.validateScheduleExam()
      return
    }
    this.independentAssessmentService
      .getSessionTimeAndDate({ courseGroupId: this.independentAssessmentDetails._courseGroup })
      .subscribe(
        ({ data }) => {
          this.independentAssessmentService.sessionDetails = data
          Object.assign(this.independentAssessmentService.plannedStudents, {
            femaleStudents: data?.femaleStudents,
            maleStudents: data?.maleStudents
          })

          if (isCentralizedTC) {
            this.setHeaderDetails()
          }

          if (canValidate) {
            validate()
          }

          setIndex()
          setCanShowTCView()
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  onClickSendToAssessmentAuthor() {
    const { notReviewedItemCount, inReviewItemsCount, correctionRequiredItemCount } =
      this.independentAssessmentDetails?.items.reduce(
        (counts, item) => {
          if (
            item.stageNo !== 29 &&
            item.reviewers?.length &&
            !item.reviewers?.some(({ stageNo }) => ![0, 1].includes(stageNo))
          ) {
            counts.notReviewedItemCount += 1
          }
          if (
            item.stageNo !== 29 &&
            item.reviewers?.length &&
            item.reviewers?.some(({ stageNo }) => stageNo === 2)
          ) {
            counts.inReviewItemsCount += 1
          }

          if (item.stageNo === 34) {
            counts.correctionRequiredItemCount += 1
          }

          return counts
        },
        {
          notReviewedItemCount: 0,
          inReviewItemsCount: 0,
          reviewedItemsCount: 0,
          correctionRequiredItemCount: 0
        }
      )

    const errorMessage = 'independentAssessmentAuthoring.areYouSureWantToSendIA'
    const data = {
      title: 'assessment.stepWizard.confirmation',
      message: this.translateService.instant(errorMessage, { author: this.authorName })
    }

    const infoList = []
    if (inReviewItemsCount) {
      infoList.push(
        this.translateService.instant('independentAssessmentAuthoring.inReviewItemsStatus', {
          count: inReviewItemsCount
        })
      )
    }

    if (notReviewedItemCount) {
      infoList.push(
        this.translateService.instant('independentAssessmentAuthoring.needToReviewItemsStatus', {
          count: notReviewedItemCount
        })
      )
    }

    if (correctionRequiredItemCount) {
      infoList.push(
        this.translateService.instant(
          'independentAssessmentAuthoring.needToCorrectRequiredItemsStatus',
          {
            count: correctionRequiredItemCount
          }
        )
      )
    }

    if (infoList.length) {
      Object.assign(data, {
        infoList,
        notes: this.translateService.instant(
          'independentAssessmentAuthoring.sendBackAAConfirmationInfo'
        )
      })
    }

    this.openConfirmPopup({
      data,
      isSendToAAPopup: notReviewedItemCount > 0 || inReviewItemsCount > 0
    })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          return
        }
        this.sendToAssessmentAuthor()
      })
  }

  onClickIASendToReviewers() {
    const errorMessage = 'independentAssessmentAuthoring.areYouSureWantToSendIA'
    const data = {
      title: 'assessment.stepWizard.confirmation',
      message: this.translateService.instant(errorMessage, {
        author: `${this.independentAssessmentDetails?.reviewerLabel || 'Reviewer'}`
      })
    }

    this.openConfirmPopup({ data, isMultipleReviewers: true })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          return
        }

        this.sendItemAuthorToReviewers()
      })
  }

  private sendToAssessmentAuthor() {
    this.independentAssessmentService
      .sendToAssessmentAuthor({ assessmentId: this.assessmentId })
      .subscribe(
        ({ message }) => {
          this.toastrService.success(message)

          this.canSendToAssessmentAuthor = false
          this.onClickRefresh()
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  private sendItemAuthorToReviewers() {
    this.independentAssessmentService
      .sendItemAuthorToReviewers({ assessmentId: this.assessmentId })
      .subscribe(
        ({ message }) => {
          this.toastrService.success(message)
          this.independentAssessmentDetails.canIASentToReviewer = false
          this.onClickRefresh()
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  private checkAllSelectedItemsAreAssigned({ type }: { type?: string }): boolean {
    if (!this.getSelectedItems?.length) {
      this.toastrService.error(
        this.translateService.instant('independentAssessmentAuthoring.selectItemsToWithdraw')
      )

      return false
    } else {
      const isAllUnAssignedItems = !this.getSelectedItems.some((selectedItem) =>
        type === 'REVIEWER'
          ? selectedItem?.reviewers && selectedItem.reviewers.length
          : selectedItem.itemAuthor?._id
      )

      if (isAllUnAssignedItems) {
        this.toastrService.error(
          this.translateService.instant(
            type === 'REVIEWER'
              ? 'independentAssessmentAuthoring.assignItemsToWithdrawReviewer'
              : 'independentAssessmentAuthoring.assignItemsToWithdrawItemAuthor'
          )
        )

        return false
      }

      return true
    }
  }

  private validateItemCreation({
    stepperIndex = 1,
    onlyValidate,
    canFreeFlow = false
  }: {
    stepperIndex?: number
    onlyValidate?: boolean
    canFreeFlow?: boolean
  }) {
    if (!this.independentAssessmentDetails?.settings?.totalDuration) {
      this.toastrService.warning(
        this.translateService.instant('independentAssessmentAuthoring.addTotalDuration')
      )
      this.isTotalDurationInvalid = true

      return
    }

    if (!canFreeFlow) {
      if (!this.independentAssessmentDetails?.settings?.totalMarks) {
        this.toastrService.warning(
          this.translateService.instant('independentAssessmentAuthoring.specifyTotalMarks')
        )
        this.isTotalTotalMarksInvalid = true

        return
      }

      if (this.hasAnyItemZeroMarks) {
        this.toastrService.warning(
          this.translateService.instant('independentAssessmentAuthoring.itemShouldNotHaveZeroMarks')
        )
        this.independentAssessmentService.hasZeroMarks = true

        return
      }

      const { isAllItemsValid, errorMessage, sectionNo, itemNo, itemTypeCode, subItemNo } =
        this.checkIsItemsValid({})

      if (!isAllItemsValid) {
        if (errorMessage) {
          this.toastrService.warning(
            this.translateService.instant(errorMessage, {
              sectionNo,
              itemNo,
              itemTypeCode,
              subItemNo
            })
          )
        }

        return
      }

      const { isTimeValid, isMarksNotValid } = this.checkIfItemsTimeAndMarksMatchesSection()

      if (!isTimeValid) {
        this.toastrService.warning(
          this.translateService.instant('independentAssessmentAuthoring.itemTimeInvalid')
        )

        return
      }

      if (isMarksNotValid) {
        if (this.isEqualMarks) {
          let sectionNo = 0

          sectionNo = this.independentAssessmentDetails?.sections?.findIndex((section) => {
            return section?.isMarksNotValid
          })
          const selectedSection = this.independentAssessmentDetails?.sections[sectionNo]

          this.toastrService.warning(
            this.translateService.instant('independentAssessmentAuthoring.itemMarksInvalid', {
              sectionNo: sectionNo + 1
            })
          )
          this.onClickSection({ section: selectedSection as IIndependentSection, index: sectionNo })
        } else {
          this.toastrService.warning(
            this.translateService.instant('independentAssessmentAuthoring.itemTotalMarksMismatch')
          )
        }

        this.independentAssessmentService.isItemMarksInvalid = true

        return
      }

      const { isSectionTimeValid, isSectionMarksValid } =
        this.independentAssessmentService.checkIfSectionsMarksAndDurationMatchesSettings({
          independentAssessmentDetails: this.independentAssessmentDetails,
          sectionValidation: this.sectionValidation
        })

      if (!isSectionTimeValid) {
        this.toastrService.warning(
          this.translateService.instant('independentAssessmentAuthoring.sectionTimeInValid')
        )
        this.sectionValidation.isTimeInValid = true

        return
      }

      if (!isSectionMarksValid) {
        this.toastrService.warning(
          this.translateService.instant('independentAssessmentAuthoring.sectionMarksInValid')
        )

        if (this.isEqualMarks) {
          this.sectionValidation.isMarksInValid = true
        } else {
          this.sectionValidation.isMarksInValid = true
          this.independentAssessmentService.isItemMarksInvalid = true
        }

        return
      }

      this.updateAssessmentMarks({ onlySave: true })

      return true
    }

    if (onlyValidate) {
      if (stepperIndex === 3) {
        this.getScheduleSettings({
          canValidate: false,
          stepperIndex,
          deviceType: this.selectedDeviceType
        })
      } else {
        this.selectedIndex = stepperIndex
      }

      return true
    }

    this.selectedIndex = stepperIndex
  }

  private updateAssessmentMarks({
    stepperIndex,
    onlySave
  }: {
    stepperIndex?: number
    onlySave?: boolean
  }) {
    const sections = this.independentAssessmentDetails?.sections.map((section) => ({
      _id: section?._id,
      marks: section?.marks,
      duration: section?.duration,
      answeringSequence: 'FREE_SEQUENCE',
      items: section?.items?.map((item) => ({
        _id: item._id,
        itemTypeCode: item?.itemType?.code,
        marks: this.multipleQuestionItemTypes.includes(item?.itemType?.code)
          ? item?.stem?.questions?.reduce((acc, q) => acc + q.marks, 0)
          : item?.stem?.question?.marks,
        duration: item?.duration,
        canShuffleChoices: item?.settings?.canShuffleChoices,
        restrictNavigation: item?.settings?.restrictNavigation,
        mustAnswer: item?.settings?.mustAnswer,
        ...(this.multipleQuestionItemTypes.includes(item?.itemType?.code) && {
          questions: item.stem.questions.map((question) => ({
            _id: question._id,
            marks: question.marks
          }))
        })
      }))
    }))

    this.independentAssessmentService
      .updateAssessmentMarks({
        sections,
        totalDuration: this.independentAssessmentDetails?.settings?.totalDuration
      })
      .subscribe(
        ({ message }) => {
          this.isAllQuestionsValidated = true
          this.independentAssessmentDetailsClone = cloneDeep(this.independentAssessmentDetails)

          if (onlySave) {
            return
          }

          this.toastrService.success(message)
          if (stepperIndex === 3) {
            this.getScheduleSettings({
              canValidate: false,
              stepperIndex,
              deviceType: this.selectedDeviceType
            })
          } else {
            this.selectedIndex = stepperIndex || 1
          }
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  private checkIfItemsTimeAndMarksMatchesSection(): {
    isTimeValid: boolean
    isMarksNotValid: boolean
  } {
    const isTimeValid = true
    let isMarksNotValid = true
    this.independentAssessmentService.isItemMarksInvalid = false

    this.independentAssessmentDetails?.sections?.forEach((section) => {
      let { totalSectionMarks, totalSectionDuration } = section?.items?.reduce(
        (acc, item) => {
          acc.totalSectionMarks += this.multipleQuestionItemTypes.includes(item?.itemType?.code)
            ? item.stem.questions.reduce((sum, question) => sum + (question?.marks ?? 0), 0)
            : (item?.stem?.question?.marks ?? 0)
          acc.totalSectionDuration += item?.duration ?? 0

          return acc
        },
        { totalSectionMarks: 0, totalSectionDuration: 0 }
      )

      totalSectionMarks = Number(totalSectionMarks.toFixed(2))

      // if (totalSectionDuration !== section.duration) isTimeValid = false

      if (this.isEqualMarks && section?.isEqualMarks) {
        const isTotalSectionMarksInInt = section?.marks % 1 === 0

        if (isTotalSectionMarksInInt) {
          section.isMarksNotValid =
            Math.floor(totalSectionMarks) !== section.marks &&
            Math.ceil(totalSectionMarks) !== section.marks
        } else {
          section.isMarksNotValid =
            this.utilService.truncateDecimalPlaces({ value: totalSectionMarks }) !==
            this.utilService.truncateDecimalPlaces({ value: section.marks })

          if (section.isMarksNotValid) {
            section.isMarksNotValid =
              this.utilService.truncateDecimalPlaces({
                value: this.utilService.roundToTwoDecimalPlaces({ value: totalSectionMarks })
              }) !== this.utilService.truncateDecimalPlaces({ value: section.marks })
          }
        }
      } else {
        if (this.independentAssessmentDetails?.sections?.length === 1) {
          section.marks = totalSectionMarks
        }

        section.isMarksNotValid = totalSectionMarks !== section.marks
      }
    })

    isMarksNotValid = this.independentAssessmentDetails?.sections?.some(
      (section) => section.isMarksNotValid
    )

    return { isTimeValid, isMarksNotValid }
  }

  private checkIsItemsValid({
    validateSelectedItems = false,
    isAlreadySentToReview = false,
    type
  }: {
    validateSelectedItems?: boolean
    isAlreadySentToReview?: boolean
    type?: string
  }) {
    let isAllItemsValid = true
    let errorMessage = ''
    let sectionNo = 0
    let itemNo = 0
    let itemTypeCode = ''
    let subItemNo = 0

    this.independentAssessmentDetails?.sections?.forEach((section, sectionIndex) => {
      section?.items?.forEach((item, index) => {
        let isItemValid = true
        let isSubItemValid = true

        if ((!validateSelectedItems || item?.isSelected) && !isAlreadySentToReview) {
          // check stem
          if (
            !item.stem?.question?.text &&
            !item.stem?.question?.importAttachments?.length &&
            !['MQ', 'EMQ'].includes(item?.itemType?.code)
          ) {
            isItemValid = false
            isAllItemsValid = false

            if (!errorMessage) {
              errorMessage =
                this.independentAssessmentDetails?.sections?.length > 1
                  ? 'independentAssessmentAuthoring.stemEmpty'
                  : 'independentAssessmentAuthoring.withOutSectionStemEmpty'
              sectionNo = sectionIndex + 1
              itemNo = index + 1
              itemTypeCode = item?.itemType?.code
            }
          }

          // check if item type is HQ and if answer key is there
          if (item?.itemType?.code === 'HQ') {
            if (!item?.stem?.question?.markerAttachments?.length) {
              isItemValid = false
              isAllItemsValid = false

              if (!errorMessage) {
                errorMessage =
                  this.independentAssessmentDetails?.sections?.length > 1
                    ? 'independentAssessmentAuthoring.hotSpotAreaShouldBeDrawn'
                    : 'independentAssessmentAuthoring.withoutSectionhotSpotAreaShouldBeDrawn'
                sectionNo = sectionIndex + 1
                itemNo = index + 1
                itemTypeCode = item?.itemType?.code
              }
            }
          }

          // check options and answer
          if (item?.itemType?.code === EItemType.MCQ) {
            const validOptionsAndAnswers = this.checkOptionsAndAnswer({ item })

            if (!validOptionsAndAnswers) {
              isItemValid = false
              isSubItemValid = false
              isAllItemsValid = false

              if (!errorMessage) {
                errorMessage =
                  this.independentAssessmentDetails?.sections?.length > 1
                    ? 'independentAssessmentAuthoring.addChoicesOrAnswer'
                    : 'independentAssessmentAuthoring.withOutAddChoicesOrAnswer'
                sectionNo = sectionIndex + 1
                itemNo = index + 1
                itemTypeCode = item?.itemType?.code
              }
            } else {
              item?.stem?.questions?.forEach((question) => {
                let subItemType = ''

                if (item?.itemType?.code === 'MIT') {
                  subItemType = question?.itemType?.code
                }

                const isQuestionValid = this.itemValidationService.isItemValid(
                  question as unknown as IAssessmentItem,
                  true,
                  subItemType
                )

                question.isInvalid = !isQuestionValid

                if (!errorMessage && !isQuestionValid) {
                  isItemValid = false
                  isAllItemsValid = false
                  errorMessage =
                    this.independentAssessmentDetails?.sections?.length > 1
                      ? 'independentAssessmentAuthoring.addChoicesOrAnswer'
                      : 'independentAssessmentAuthoring.withOutAddChoicesOrAnswer'
                  sectionNo = sectionIndex + 1
                  itemNo = index + 1
                  itemTypeCode = item?.itemType?.code
                }
              })
            }
          }

          // check TF answer
          if (item?.itemType?.code === EItemType.TF) {
            if (!item?.answer) {
              isItemValid = false
              isAllItemsValid = false

              if (!errorMessage) {
                errorMessage =
                  this.independentAssessmentDetails?.sections?.length > 1
                    ? 'independentAssessmentAuthoring.markAnswerKey'
                    : 'independentAssessmentAuthoring.withOutMarkAnswerKey'
                sectionNo = sectionIndex + 1
                itemNo = index + 1
                itemTypeCode = item?.itemType?.code
              }
            }
          }

          // check subItem
          if (
            (this.multipleQuestionItemTypes.includes(item?.itemType?.code) &&
              item?.itemType?.code !== EItemType.MIT) ||
            item?.itemType?.code === EItemType.EQ
          ) {
            item?.stem?.questions.forEach((question, i) => {
              if (!question?.text) {
                isItemValid = false
                isSubItemValid = false
                isAllItemsValid = false

                if (!errorMessage) {
                  errorMessage =
                    item?.itemType?.code === 'EQ'
                      ? 'independentAssessmentAuthoring.promptEmpty'
                      : 'independentAssessmentAuthoring.subItemStemEmpty'
                  sectionNo = sectionIndex + 1
                  itemNo = index + 1
                  itemTypeCode = item?.itemType?.code
                  subItemNo = i + 1
                }
              }

              if (item?.itemType?.code === 'CQ') {
                const validOptionsAndAnswers = this.checkOptionsAndAnswer({ subItem: question })
                if (!validOptionsAndAnswers) {
                  isItemValid = false
                  isSubItemValid = false
                  isAllItemsValid = false

                  if (!errorMessage) {
                    errorMessage =
                      this.independentAssessmentDetails?.sections?.length > 1
                        ? 'independentAssessmentAuthoring.subItemAddChoicesOrAnswer'
                        : 'independentAssessmentAuthoring.withOutSubItemAddChoicesOrAnswer'
                    sectionNo = sectionIndex + 1
                    itemNo = index + 1
                    itemTypeCode = item?.itemType?.code
                    subItemNo = i + 1
                  }
                }
              }

              if (!isSubItemValid) {
                question.isInvalid = true
              } else {
                delete question.isInvalid
              }
            })
          }

          if (item?.itemType?.code === EItemType.MRA) {
            const showError = ({ key }: { key: string }) => {
              if (!errorMessage) {
                errorMessage = key
                sectionNo = sectionIndex + 1
                itemNo = index + 1
                itemTypeCode = item?.itemType?.code
              }

              isItemValid = false
              isAllItemsValid = false
            }

            if (!this.checkOptionsAndAnswer({ item })) {
              showError({
                key:
                  this.independentAssessmentDetails?.sections?.length > 1
                    ? 'independentAssessmentAuthoring.addChoicesOrAnswer'
                    : 'independentAssessmentAuthoring.withOutAddChoicesOrAnswer'
              })
            }

            const correctOptionCount = item?.options?.reduce(
              (count, option) => count + (option.correct ? 1 : 0),
              0
            )

            if (item?.metaData?.totalAnswersKeys !== correctOptionCount) {
              showError({
                key:
                  this.independentAssessmentDetails?.sections?.length > 1
                    ? 'independentAssessmentAuthoring.bothAnswerKeyAndCount'
                    : 'independentAssessmentAuthoring.withOutBothAnswerKeyAndCount'
              })
            }

            if (item?.metaData?.isWeightageApplied) {
              const weightagePercentage = item?.options?.reduce(
                (sum, option) => sum + (Number(option?.weightage) || 0),
                0
              )

              if (weightagePercentage !== 100) {
                showError({ key: 'independentAssessmentAuthoring.weightageShouldBe100Percentage' })
              }

              if (item?.options?.some((option) => option?.weightage && !option.correct)) {
                showError({
                  key: 'independentAssessmentAuthoring.weightageShouldOnlyAddedForCorrectAnswers'
                })
              }
            }
          }

          if (item?.itemType?.code === EItemType.MIT && item?.stem?.questions?.length) {
            item.stem.questions.forEach((question, i) => {
              isSubItemValid = true

              if (!question?.text && !question?.importAttachments?.length) {
                if (!errorMessage) {
                  errorMessage =
                    this.independentAssessmentDetails?.sections?.length > 1
                      ? 'independentAssessmentAuthoring.subItemStemEmpty'
                      : 'independentAssessmentAuthoring.withoutSectionSubItemStemEmpty'
                  sectionNo = sectionIndex + 1
                  itemNo = index + 1
                  itemTypeCode = item?.itemType?.code
                  subItemNo = i + 1
                }

                isItemValid = false
                isAllItemsValid = false
                isSubItemValid = false
              }

              if (question?.itemType?.code === EItemType.MCQ) {
                if (!question?.options?.some((option) => option.correct)) {
                  if (!errorMessage) {
                    errorMessage =
                      this.independentAssessmentDetails?.sections?.length > 1
                        ? 'independentAssessmentAuthoring.subItemAddChoicesOrAnswer'
                        : 'independentAssessmentAuthoring.withOutSubItemAddChoicesOrAnswer'
                    sectionNo = sectionIndex + 1
                    itemNo = index + 1
                    itemTypeCode = item?.itemType?.code
                    subItemNo = i + 1
                  }

                  isItemValid = false
                  isAllItemsValid = false
                  isSubItemValid = false
                }

                if (
                  question?.options?.some((option) => !option.text && !option?.attachments?.length)
                ) {
                  if (!errorMessage) {
                    errorMessage =
                      this.independentAssessmentDetails?.sections?.length > 1
                        ? 'independentAssessmentAuthoring.subItemOptionText'
                        : 'independentAssessmentAuthoring.withoutSubItemOptionText'
                    sectionNo = sectionIndex + 1
                    itemNo = index + 1
                    itemTypeCode = item?.itemType?.code
                    subItemNo = i + 1
                  }

                  isItemValid = false
                  isAllItemsValid = false
                  isSubItemValid = false
                }
              } else if (
                question?.itemType?.code === EItemType.TF &&
                typeof question?.answer?.trueOrFalse !== 'boolean'
              ) {
                if (!errorMessage) {
                  errorMessage =
                    this.independentAssessmentDetails?.sections?.length > 1
                      ? 'independentAssessmentAuthoring.markAnswerKeyForMIT'
                      : 'independentAssessmentAuthoring.withOutMarkAnswerKeyForMIT'
                  sectionNo = sectionIndex + 1
                  itemNo = index + 1
                  itemTypeCode = item?.itemType?.code
                  subItemNo = i + 1
                }

                isItemValid = false
                isAllItemsValid = false
                isSubItemValid = false
              }

              if (!isSubItemValid) {
                question.isInvalid = true
              } else {
                delete question.isInvalid
              }
            })
          }

          if (
            item?.itemType?.code === EItemType.EMQ &&
            !errorMessage &&
            item?.stem?.questions?.length
          ) {
            if (!item.stem.leadIn.text) {
              errorMessage =
                this.independentAssessmentDetails?.sections?.length > 1
                  ? 'independentAssessmentAuthoring.withSectionItemStemLeadInEmpty'
                  : 'independentAssessmentAuthoring.itemStemLeadInEmpty'

              sectionNo = sectionIndex + 1
              itemNo = index + 1
              itemTypeCode = item?.itemType?.code
            }

            if (!item.stem.theme) {
              errorMessage =
                this.independentAssessmentDetails?.sections?.length > 1
                  ? 'independentAssessmentAuthoring.withSectionItemThemEmpty'
                  : 'independentAssessmentAuthoring.itemThemEmpty'
              sectionNo = sectionIndex + 1
              itemNo = index + 1
              itemTypeCode = item?.itemType?.code
            }

            item.stem.questions.forEach((question, i) => {
              if (!question?.optionId && !errorMessage) {
                errorMessage =
                  this.independentAssessmentDetails?.sections?.length > 1
                    ? 'independentAssessmentAuthoring.subItemAddChoicesOrAnswer'
                    : 'independentAssessmentAuthoring.withOutSubItemAddChoicesOrAnswer'
                sectionNo = sectionIndex + 1
                itemNo = index + 1
                itemTypeCode = item?.itemType?.code
                subItemNo = i + 1
              }

              if (!item?.options.length && !errorMessage) {
                errorMessage =
                  this.independentAssessmentDetails?.sections?.length > 1
                    ? 'independentAssessmentAuthoring.subItemAddChoicesOrAnswer'
                    : 'independentAssessmentAuthoring.withOutSubItemAddChoicesOrAnswer'
                sectionNo = sectionIndex + 1
                itemNo = index + 1
                itemTypeCode = item?.itemType?.code
                subItemNo = i + 1
              }

              if (!question?.text && !errorMessage) {
                errorMessage =
                  this.independentAssessmentDetails?.sections?.length > 1
                    ? 'independentAssessmentAuthoring.subItemStemEmpty'
                    : 'independentAssessmentAuthoring.promptEmpty'
                sectionNo = sectionIndex + 1
                itemNo = index + 1
                itemTypeCode = item?.itemType?.code
                subItemNo = i + 1
              }
            })
          }
        } else if (
          item?.isSelected &&
          isAlreadySentToReview &&
          ![0, 29, 52].includes(item.stageNo) &&
          !(item.stageNo === 12 && (type === 'reviewer' || !item?.itemAuthor?._id))
        ) {
          isItemValid = false
          isAllItemsValid = false
          errorMessage = this.isFromQuestionBank
            ? 'independentAssessmentAuthoring.itemAlreadySentToReviewers'
            : 'independentAssessmentAuthoring.itemAlreadySentToItemAuthorOrReviewers'
        }

        if (!isItemValid) {
          item.isInvalid = true
        } else {
          delete item.isInvalid
        }
      })
    })

    return { isAllItemsValid, errorMessage, sectionNo, itemNo, itemTypeCode, subItemNo }
  }

  private checkOptionsAndAnswer({ item, subItem }: { item?: IQuestion; subItem?: IQuestion }) {
    const options = subItem ? subItem?.options : item?.options

    return (
      options?.every((option) => option?.text || option?.attachments?.length) &&
      (options?.some((option) => option?.correct) ||
        item?.answer?.trueOrFalse !== undefined ||
        subItem?.optionId)
    )
  }

  onClickUnPublish() {
    const data = {
      isError: true,
      icon: 'warning_amber',
      title: 'independentAssessmentAuthoring.unPublishAssessmentConfirmation',
      message: 'independentAssessmentAuthoring.unPublishNote',
      additionalInfo: 'independentAssessmentAuthoring.areYouSureToUnPublish',
      primaryButton: 'common.unPublish'
    }

    this.openConfirmPopup({ data })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          return
        }

        this.independentAssessmentService
          .unPublishIndependentAssessment({ assessmentId: this.assessmentId })
          .subscribe(
            ({ message }) => {
              this.toastrService.success(message)
              this.independentAssessmentService.isPublished = false

              this.getAssessmentDetails()
              this.independentAssessmentService?.clickUnPublishAction.next(true)
              if (this.selectedIndex === 3) {
                this.getScheduleSettings({
                  canValidate: false,
                  deviceType: this.independentAssessmentService.examDeviceType
                })
              }
            },
            (err) => this.errorHandler.errorLog(err)
          )
      })
  }

  onClickGenerateReport() {
    this.independentAssessmentService
      .reGenerateReport({
        assessmentId: this.assessmentId,
        academicYearStart: this.independentAssessmentDetails?.academicYear?.start,
        academicYearEnd: this.independentAssessmentDetails?.academicYear?.end,
        attemptType: this.independentAssessmentDetails?.attemptType?.name,
        termCode: this.independentAssessmentDetails?.hierarchy?.term?.code,
        programCode: this.independentAssessmentDetails?.hierarchy?.program?.code,
        courseHierarchyCode: this.independentAssessmentDetails?.courseName,
        examType: this.independentAssessmentDetails?.examType?.code,
        examCategory: this.independentAssessmentDetails?.examType?.examCategory?.code,
        mode: this.independentAssessmentDetails?.examType?.examCategory?.mode
      })
      .subscribe(
        ({ message }) => {
          this.toastrService.success(message)
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  onClickPrintAssessment() {
    this.independentAssessmentService.getExamConfigs().subscribe(
      ({ data }) => {
        this.dialog.open(PrintAssessmentDialogComponent, {
          data: {
            assessmentDetails: this.independentAssessmentDetails,
            examConfigs: data
          },
          maxHeight: '98vh',
          width: '100%',
          maxWidth: '90%',
          disableClose: true,
          direction: this.settingsService.getOptions().dir,
          panelClass: ['dialog-padding-top-bottom-none', 'dialog-fix']
        })
      },
      (err) => this.errorHandler.errorLog(err)
    )
  }

  private setSelectedIndexInQuery() {
    void this.router.navigate([], {
      queryParams: { selectedIndex: 0 },
      queryParamsHandling: 'merge'
    })
  }

  onSelectedStep({ step }: { step: number }) {
    const clickedStep = step - 1

    if (this.isPublished || this.isCompleted) {
      if (clickedStep === 0) {
        this.setSelectedIndexInQuery()
      }

      return (this.selectedIndex = clickedStep)
    }

    if (this.selectedIndex === clickedStep) {
      return
    }

    if (clickedStep === this.selectedIndex + 1) {
      return this.onClickNext({})
    }

    if (
      this.independentAssessmentDetails?.isScheduleRequest ||
      clickedStep <= this.independentAssessmentService.latestTab
    ) {
      if (this.selectedIndex === 0) {
        const { settings, ...details } = this.independentAssessmentDetails || {}
        const { settings: cloneSettings, ...detailsClone } =
          this.independentAssessmentDetailsClone || {}

        if (!this.isPublished && JSON.stringify(details) !== JSON.stringify(detailsClone)) {
          this.validateItemCreation({ stepperIndex: clickedStep, canFreeFlow: true })
        } else {
          this.validateItemCreation({
            onlyValidate: true,
            stepperIndex: clickedStep,
            canFreeFlow: true
          })
        }

        return
      }

      if (this.selectedIndex === 1) {
        this.setSelectedIndexInQuery()
        return this.independentAssessmentExamConfigurationComponent.validateSelectedFilter({
          clickedStep
        })
      }

      if (clickedStep === 3) {
        return this.getScheduleSettings({
          canValidate: false,
          stepperIndex: clickedStep,
          deviceType: this.selectedDeviceType
        })
      }

      if (clickedStep === 0) {
        this.setSelectedIndexInQuery()
      }

      return (this.selectedIndex = clickedStep)
    } else {
      if (
        this.independentAssessmentService.latestTab === 2 &&
        clickedStep === 3 &&
        (this.independentAssessmentService?.examConfigs?.isStudentAssigned ||
          this.independentAssessmentService?.examConfigs?.isGroupAssigned)
      ) {
        return (this.selectedIndex = clickedStep)
      }

      this.toastrService.warning(
        this.translateService.instant('independentAssessmentAuthoring.pleaseCompletePreviousSteps')
      )
    }
  }

  onClickExtractFullAssessment({ type = '', itemId }: { type?: string; itemId?: string } = {}) {
    if (type === 'itemAuthor') {
      this.createSection({ items: this.independentAssessmentDetails?.items })
    }

    this.dialog
      .open(ExtractAssessmentPopupComponent, {
        data: {
          ...(type && { type, itemId }),
          courseName: this.independentAssessmentDetails.courseName,
          sections: this.independentAssessmentDetails.sections
        },
        width: '100%',
        maxWidth: '95%',
        panelClass: ['digi-padding-none', 'dialog-fix'],
        height: '95vh',
        disableClose: true,
        direction: this.settingsService.getOptions().dir
      })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          return
        }

        this.getAssessmentDetails({ isItemAttached: true })
      })
  }

  onClickScrollToTop() {
    this.scrollSectionsTo({ id: 'create' })
  }

  onClickSection({ section, index }: { section: IIndependentSection; index: number }) {
    if (this.independentAssessmentService.isTextUpdating) {
      setTimeout(() => this.updateSection({ section, index }), 400)
    } else {
      this.updateSection({ section, index })
    }
  }

  private updateSection({ section, index }: { section: IIndependentSection; index: number }) {
    this.selectedSection = section
    this.independentAssessmentService.selectedSection = this.selectedSection
    this.activeSectionIndex = index
    this.setDefaultItemSelect()
    Object.assign(this.selectedSection, { canScrollToTop: true })
    this.setQueryParam()
    this.updateItemTypeCount()
    setTimeout(() => this.onClickScrollToTop(), 300)
  }

  private setDefaultItemSelect({ isItemAttached = false }: { isItemAttached?: boolean } = {}) {
    const items = this.selectedSection?.items || []
    items?.forEach((item, i) => {
      item.isActive = isItemAttached ? i === items?.length - 1 : i === 0

      item?.stem?.questions?.forEach((question, j) => (question.isActive = j === 0))
    })

    if (isItemAttached) {
      this.independentAssessmentService.canScrollToActiveItem = true
    }
  }

  private validateSelectedItems({
    content,
    skipValidation = false
  }: {
    content: string
    skipValidation?: boolean
  }): boolean {
    const sections = this.independentAssessmentDetails?.sections || []

    if (!sections?.some((section) => section?.items?.some((item) => item?.isSelected))) {
      if (!skipValidation) {
        this.toastrService.warning(this.translateService.instant(content))
      }

      return false
    }

    return true
  }

  private moveItemsIntoSection({ sectionId }: { sectionId: string }) {
    const selectedItems = this.independentAssessmentDetails.sections.flatMap((section) =>
      section.items.filter((item) => item.isSelected)
    )
    this.independentAssessmentDetails.sections.forEach((section) => {
      section.items = section.items.filter((item) => !item.isSelected)
    })
    this.independentAssessmentDetails.sections
      .find((section) => section._id === sectionId)
      ?.items.push(...selectedItems)

    this.independentAssessmentDetails.sections.forEach(({ items }) => {
      items.forEach((item) => {
        item.isSelected = false
      })
    })

    this.independentAssessmentDetails.sections = this.independentAssessmentDetails.sections.filter(
      ({ items = [] }) => items.length
    )

    if (this.independentAssessmentService.isEqualMarks) {
      this.independentAssessmentService.distributeMarks({
        assessment: this.independentAssessmentDetails
      })
    }

    const index = this.independentAssessmentDetails.sections?.findIndex(({ _id }) => {
      return sectionId === _id
    })

    this.onClickSection({ section: this.independentAssessmentDetails?.sections[index], index })
    this.independentAssessmentService.updateAssessmentDetail()
    this.updateItemTypeCount()
  }

  onClickMoveItemsToSection() {
    if (
      !this.validateSelectedItems({
        content: 'independentAssessmentAuthoring.pleaseSelectItemToMoveSection'
      })
    ) {
      return
    }

    this.dialog
      .open(MoveItemsIntoSectionPopupComponent, {
        width: '350px',
        height: 'auto',
        panelClass: ['digi-padding-none', 'dialog-fix'],
        disableClose: true,
        direction: this.settingsService.getOptions().dir,
        data: {
          sections: this.independentAssessmentDetails.sections,
          isEqualMarks: this.independentAssessmentService.isEqualMarks,
          multipleQuestionItemTypes: this.multipleQuestionItemTypes
        }
      })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          return
        }

        this.moveItemsIntoSection({ sectionId: res })
      })
  }

  onClickAssignItemAuthor({ selectedItemId }: { selectedItemId?: string }) {
    if (
      !this.validateSelectedItems({
        content: 'independentAssessmentAuthoring.pleaseSelectItemForAssignItemAuthor',
        skipValidation: !!selectedItemId
      }) &&
      !selectedItemId
    ) {
      return
    }

    if (this.checkHasOldItems()?.length) {
      return this.openConfirmationPopup({ type: 'itemAuthor' })
        .afterClosed()
        .subscribe((res) => {
          if (!res) {
            return
          }

          if (this.hasNoNewItems) {
            return
          }

          return this.assignItemsToItemAuthor()
        })
    }

    this.assignItemsToItemAuthor()
  }

  private assignItemsToItemAuthor() {
    this.openAssignPopup({ assignType: 'itemAuthor' })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          return
        }

        if (res?.isCloneSuccessfully) {
          return this.getAssessmentDetails()
        }

        return this.setItemStatus({ res })
      })
  }

  private setItemStatus({ res }: { res: IAssignedItemStatus }) {
    this.isSelectAllItem = false
    this.independentAssessmentService.updateAssessmentDetail()

    if (res?.items?.length) {
      this.independentAssessmentDetails?.sections?.forEach((section) => {
        section?.items?.forEach((item) => {
          res?.items?.forEach((resItem) => {
            if (item?._id === resItem?.itemId) {
              Object.assign(item, {
                stageNo: resItem?.stageNo,
                itemAuthor: res?.itemAuthor,
                reviewers: res?.reviewers,
                canEdit: resItem?.canEdit,
                isSelected: false,
                requestStatus: resItem?.status
              })
            }
          })
        })
      })
    }

    this.independentAssessmentService.updateAssessmentDetail()
  }

  onClickAssignReviewer({ isQuestionBank }: { isQuestionBank?: boolean }) {
    const { isAllItemsValid, errorMessage, sectionNo, itemNo, itemTypeCode, subItemNo } =
      this.checkIsItemsValid({ validateSelectedItems: true })

    if (!isAllItemsValid) {
      if (errorMessage) {
        this.toastrService.warning(
          this.translateService.instant(errorMessage, {
            sectionNo,
            itemNo,
            itemTypeCode,
            subItemNo
          })
        )
      }

      return
    }

    if (this.checkHasOldItems()?.length) {
      return this.openConfirmationPopup()
        .afterClosed()
        .subscribe((res) => {
          if (!res) {
            return
          }

          if (this.hasNoNewItems) {
            return
          }

          return this.assignItemsToReviewer()
        })
    }

    this.assignItemsToReviewer({ isQuestionBank })
  }

  get hasNoNewItems() {
    return !this.getSelectedItems.some((item) => item?.requestType === 'NEW')
  }

  private assignItemsToReviewer({ isQuestionBank }: { isQuestionBank?: boolean } = {}) {
    this.openAssignPopup({ assignType: 'reviewer', isQuestionBank })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          return
        }

        if (res?.isCloneSuccessfully) {
          return this.getAssessmentDetails()
        }

        this.setAssignedItemsStatus({ selectedItems: res?.assignedItems })
      })
  }

  private openConfirmationPopup({ type }: { type?: string } = {}) {
    const message = this.translateService.instant(
      type === 'itemAuthor'
        ? 'independentAssessmentAuthoring.oldItemIANote'
        : 'independentAssessmentAuthoring.mapItemsNote',
      type === 'itemAuthor'
        ? { oldItemCount: this.checkHasOldItems()?.length }
        : { cannotMapItemCount: this.checkHasCannotMapItems() }
    )

    return this.dialog.open(ConfirmPopupComponent, {
      data: {
        title: 'common.note',
        message,
        canHideSecondaryButton: true,
        primaryButton: 'confirmDialogBox.okay!'
      },
      width: '465px',
      panelClass: ['digi-padding-none', 'dialog-fix', 'digi-assessment-note-popup'],
      disableClose: true,
      direction: this.settingsService.getOptions().dir
    })
  }

  private checkHasOldItems() {
    return this.getSelectedItems.filter((item) => item?.requestType === 'OLD')
  }

  private checkHasCannotMapItems() {
    return this.getSelectedItems
      .filter((item) => !item?.canEdit || item.isAttachedItem || item?.requestType === 'OLD')
      .reduce((acc, item) => {
        if (this.multipleQuestionItemTypes.includes(item?.itemType?.code)) {
          return acc + (item?.stem?.questions?.length || 1)
        }

        return acc + 1
      }, 0)
  }

  private setAssignedItemsStatus({ selectedItems }: { selectedItems: Partial<IQuestion[]> }) {
    this.independentAssessmentDetails?.sections?.forEach((section) => {
      section?.items?.forEach((item) => {
        selectedItems?.forEach((selectedItem) => {
          if (selectedItem?.itemId === item?._id) {
            const { stageNo, status, canEdit, reviewers } = selectedItem

            Object.assign(item, {
              stageNo,
              requestStatus: status,
              canEdit,
              reviewers,
              isAssigned: true,
              isSelected: false,
              isInvalid: false
            })

            return
          }

          item.isInvalid = false
        })
      })
    })

    this.refreshAssessment()
    this.isSelectAllItem = this.isIntermediateCheckBox = false
  }

  private openAssignPopup({
    assignType,
    isQuestionBank
  }: {
    assignType: string
    isQuestionBank?: boolean
  }) {
    const selectedItemIds =
      this.independentAssessmentDetails.sections?.flatMap((section) =>
        section?.items
          ?.filter((item) => item?.isSelected && item?.requestType !== 'OLD')
          .map((item) => item._id)
      ) || []

    return this.dialog.open(AssignItemAuthorPopupComponent, {
      width: '520px',
      height: 'auto',
      panelClass: ['digi-padding-none', 'dialog-fix'],
      disableClose: true,
      data: {
        assignType,
        itemIds: selectedItemIds,
        courseHierarchyCode: this.isFromQuestionBank
          ? this.selectedCourse?.name
          : this.independentAssessmentDetails.courseName,
        sections: this.independentAssessmentDetails.sections,
        isQuestionBank
      }
    })
  }

  private setHeaderDetails() {
    const sessionDetails = this.independentAssessmentService.sessionDetails
    if (sessionDetails) {
      const { examType = {}, maleStudents, femaleStudents } = sessionDetails
      this.headerDetails = {
        examType: {
          _id: examType._id,
          locationAndProctoringType: examType.locationAndProctoringType
        },
        examCategory: examType.examCategory?.code,
        group: {
          maleStudents,
          femaleStudents,
          _id: this.independentAssessmentDetails._courseGroup
        },
        settings: { duration: this.independentAssessmentDetails?.settings?.totalDuration }
      }
    }
  }

  onChangeChooseMethod({
    selectedTestCenter,
    canRescheduleExam,
    isManualChange
  }: {
    selectedTestCenter: string
    canRescheduleExam?: boolean
    isManualChange?: boolean
  }): void {
    this.independentAssessmentService
      .onChangeDeviceType({
        courseGroupId: this.independentAssessmentDetails._courseGroup,
        examDeviceType: selectedTestCenter,
        canRescheduleExam
      })
      .subscribe(
        () => {
          this.independentAssessmentService.sessionDetails =
            this.independentAssessmentService.allTestCenterDetails =
            this.independentAssessmentService.examTimeDetails =
              undefined

          this.router.navigate([], {
            queryParams: { examTimeDetails: null },
            queryParamsHandling: 'merge'
          })
          this.getScheduleSettings({
            canValidate: false,
            deviceType: selectedTestCenter,
            isManualChange
          })

          if (this.scheduleExamComponent) {
            this.scheduleExamComponent.resetData()
          }
        },
        (err) => {
          this.selectedDeviceType =
            selectedTestCenter === TEST_CENTER_OWNED_DEVICE
              ? STUDENT_OWNED_DEVICE
              : TEST_CENTER_OWNED_DEVICE
          if (err.status === 701) {
            return this.dialog
              .open(ConfirmPopupComponent, {
                data: {
                  isError: true,
                  title: 'learningOutcomeReports.warning',
                  message: err.error.message
                },
                width: '505px',
                height: '233px',
                panelClass: ['digi-padding-none', 'dialog-fix'],
                disableClose: true,
                direction: this.settingsService.getOptions().dir
              })
              .afterClosed()
              .subscribe((res) => {
                if (res) {
                  this.selectedDeviceType = selectedTestCenter
                  this.onChangeChooseMethod({
                    selectedTestCenter,
                    canRescheduleExam: true,
                    isManualChange: true
                  })
                }
              })
          } else {
            this.errorHandler.errorLog(err)
          }
        }
      )
  }

  private validateAssessment() {
    if (!this.validateItemCreation({ onlyValidate: true, stepperIndex: this.selectedIndex })) {
      return false
    }

    const validations = [
      {
        condition: !this.independentAssessmentService.assessmentValidation.isExamConfigured,
        message: 'independentAssessmentAuthoring.pleaseCompleteAllRequiredFieldsInExamConfiguration'
      },
      {
        condition: !this.independentAssessmentService.assessmentValidation.isStudentUploaded,
        message: 'independentAssessmentAuthoring.pleaseUploadStudentsInStudentDetails'
      },
      {
        condition: !this.independentAssessmentService.assessmentValidation.isScheduled,
        message: 'independentAssessmentAuthoring.pleaseCompleteScheduledDetails'
      },
      {
        condition: !this.independentAssessmentService.assessmentValidation.isProctorAssigned,
        message: 'independentAssessmentAuthoring.pleaseAssignProctorInProctorDetails'
      }
    ]

    for (const { condition, message } of validations) {
      if (condition) {
        this.toastrService.warning(this.translateService.instant(message))
        return false
      }
    }

    return true
  }

  onNavigateSection({ sectionIndex }: { sectionIndex: number }) {
    this.onClickSection({
      section: this.independentAssessmentDetails?.sections[sectionIndex],
      index: sectionIndex
    })
    this.updateSectionNavigateContainer()
  }

  private calculateTotalMarks(): number {
    return (
      this.independentAssessmentDetails?.sections?.reduce((sectionTotal, section) => {
        const sectionMarks = section.items?.reduce((itemTotal, item) => {
          if (
            this.multipleQuestionItemTypes.includes(item?.itemType?.code) &&
            item?.stem?.questions?.length
          ) {
            return (
              itemTotal +
              item.stem.questions.reduce((qTotal, question) => qTotal + (question.marks || 0), 0)
            )
          } else {
            return itemTotal + item.stem.question.marks
          }
        }, 0)

        return sectionTotal + (sectionMarks || 0)
      }, 0) || 0
    )
  }

  private setQueryParam() {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { selectedSectionId: this.selectedSection?._id },
      queryParamsHandling: 'merge'
    })
  }

  get hasAnyItemZeroMarks(): boolean {
    this.independentAssessmentService.hasZeroMarks = false

    return this.independentAssessmentDetails?.sections?.some((section) =>
      section.items.some((item) => {
        if (this.multipleQuestionItemTypes.includes(item.itemType.code)) {
          return item.stem.questions.some((question) => !question.marks)
        }

        return !item.stem.question.marks
      })
    )
  }

  get multiSelected() {
    return Array.from({ length: this.selectedIndex + 1 }, (_, i) => i + 1) || [1]
  }

  get isPublished() {
    return this.independentAssessmentService.isPublished
  }

  get isExamOngoing() {
    return this.independentAssessmentService.isExamOngoing
  }

  get isCompleted() {
    return this.independentAssessmentService.isCompleted
  }

  get isEqualMarks() {
    return this.independentAssessmentService.isEqualMarks
  }

  set isEqualMarks(value: boolean) {
    this.independentAssessmentService.isEqualMarks = value
  }

  get isItemExists() {
    return this.independentAssessmentDetails?.sections?.some(({ items = [] }) => items.length)
  }

  get canShowWithDraw() {
    return this.independentAssessmentDetails?.sections?.some(({ items = [] }) =>
      items.some(({ stageNo }) => [1, 2].includes(stageNo))
    )
  }

  get isItemsSelected() {
    return this.independentAssessmentDetails?.sections?.some(({ items = [] }) =>
      items.some(({ isSelected }) => isSelected)
    )
  }

  get canShowTestCenters() {
    return (
      this.independentAssessmentService.canShowTestCenters || this.isPublished || this.isCompleted
    )
  }

  get canShowAssignDatePicker() {
    return (
      this.independentAssessmentService.examDeviceType === TEST_CENTER_OWNED_DEVICE &&
      !this.canShowTestCenters
    )
  }

  get canShowTestCenterView() {
    return (
      this.independentAssessmentService.examDeviceType !== TEST_CENTER_OWNED_DEVICE ||
      this.canShowTestCenters
    )
  }

  get canShowChooseMethod() {
    const proctoringType =
      this.independentAssessmentService.sessionDetails?.locationAndProctoringType ??
      this.independentAssessmentDetails.locationAndProctoringType

    return proctoringType !== REMOTE_WITHOUT_PROCTOR
  }

  onClickRefresh() {
    this.getAssessmentDetails()
  }

  get sectionNavigationActions() {
    return {
      hasPrevious: !!this.independentAssessmentDetails?.sections[this.activeSectionIndex - 1],
      hasNext: !!this.independentAssessmentDetails?.sections[this.activeSectionIndex + 1]
    }
  }

  get maleStudents() {
    return (
      this.independentAssessmentService?.sessionDetails?.maleStudents ??
      this.independentAssessmentService?.plannedStudents?.maleStudents ??
      0
    )
  }

  get femaleStudents() {
    return (
      this.independentAssessmentService?.sessionDetails?.femaleStudents ??
      this.independentAssessmentService?.plannedStudents?.femaleStudents ??
      0
    )
  }

  get customMarks() {
    const totalMarks = this.utilService.truncateDecimalPlaces({ value: this.calculateTotalMarks() })
    const settingsMarks = this.utilService.truncateDecimalPlaces({
      value: this.independentAssessmentDetails?.settings?.totalMarks
    })

    const customMarksClass =
      totalMarks > settingsMarks
        ? 'digi-font-red'
        : totalMarks === settingsMarks
          ? 'digi-color-green'
          : ''
    const roundedTotalMarks = this.utilService.truncateDecimalPlaces({ value: totalMarks })

    return { totalMarks: roundedTotalMarks, customMarksClass }
  }

  get isItemSelected(): boolean {
    return this.selectedSection?.items?.some((item) => item.isSelected)
  }

  private validateItems({ type }: { type?: string }): boolean {
    const { isAllItemsValid, errorMessage } = this.checkIsItemsValid({
      validateSelectedItems: true,
      isAlreadySentToReview: true,
      type
    })

    if (!isAllItemsValid) {
      this.toastrService.warning(this.translateService.instant(errorMessage))

      return false
    }
    return true
  }

  onClickAssignTo({ menuTrigger }: { menuTrigger: MatMenuTrigger }): void {
    menuTrigger.openMenu()
  }

  onClickMenu({
    type,
    isQuestionBank,
    event
  }: {
    type: string
    isQuestionBank?: boolean
    event: MouseEvent
  }) {
    if (!type) {
      return
    }

    if (type === 'itemAuthor') {
      if (!this.validateItems({ type: 'itemAuthor' })) {
        event.stopPropagation()
        return
      }

      return this.onClickAssignItemAuthor({})
    }

    if (!this.validateItems({ type: 'reviewer' })) {
      event.stopPropagation()
      return
    }

    this.onClickAssignReviewer({ isQuestionBank })
  }

  onClickCompleteReview() {
    if (this.independentAssessmentDetails?.isWithDrawn) {
      return this.toastrService.error(
        this.translateService.instant('independentAssessmentAuthoring.withdrawnItems')
      )
    }

    const data = {
      title: 'assessment.stepWizard.confirmation',
      message: 'independentAssessmentAuthoring.areYouSureWantToCompleteReview'
    }

    this.openConfirmPopup({ data, isCompleteReview: true })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          return
        }

        this.completeReview()
      })
  }

  private completeReview() {
    this.independentAssessmentService
      .completeReview({ assessmentId: this.independentAssessmentService?.assessmentId })
      .subscribe(
        ({ message }) => {
          this.toastrService.success(message)
          this.isReviewCompleted = true
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  private hasUnReviewedItems(): boolean {
    let hasUnReviewedItem = false

    this.independentAssessmentDetails?.sections?.forEach((section) => {
      section?.items?.forEach((item) => {
        if (!['Correction Required', 'Reviewed'].includes(item?.reviewType)) {
          item.isInvalid = true
          hasUnReviewedItem = true
        } else {
          delete item.isInvalid
        }
      })
    })

    return hasUnReviewedItem
  }

  onChangeComment({ data }: { data: IUpdateComment }) {
    this.independentAssessmentDetails?.sections?.forEach((section) =>
      section?.items?.forEach((item) => {
        if (item?._id === data?.itemId) {
          Object.assign(item, {
            comment: data?.comment,
            commentsCount: data?.commentsCount,
            reviewType: data?.reviewType
          })
        }
      })
    )
    this.independentAssessmentService.updateAssessmentDetail()
  }

  onClickSendToReviewer(event: MouseEvent): void {
    if (!this.selectedCourse) {
      this.toastrService.error(
        this.translateService.instant('assessment.sentReview.courseRequiredForReviewer')
      )

      return
    }

    this.onClickMenu({ type: 'reviewer', isQuestionBank: true, event })
  }

  get getSelectedItemIds() {
    return this.independentAssessmentDetails?.sections?.flatMap((section) =>
      section?.items?.filter((item) => item?.isSelected)?.map((item) => item?._id)
    )
  }

  get getSelectedItems() {
    return (
      this.independentAssessmentDetails?.sections?.flatMap(
        (section) => section?.items?.filter((item) => item?.isSelected) || []
      ) || []
    )
  }

  get selectedItemsForWithdraw() {
    return (
      this.independentAssessmentDetails?.sections?.flatMap(
        (section) =>
          section?.items?.filter(
            (item) => item?.isSelected && (item?.reviewers?.length || item?.itemAuthor?._id)
          ) || []
      ) || []
    )
  }

  onEmitSetTestCenterView() {
    this.setCanShowTCView()
  }

  private setCanShowTCView() {
    this.independentAssessmentService.examDeviceType = this.selectedDeviceType
    const isCentralizedTC = this.selectedDeviceType === TEST_CENTER_OWNED_DEVICE
    this.independentAssessmentService.canShowTestCenters =
      this.selectedDeviceType === STUDENT_OWNED_DEVICE ||
      (isCentralizedTC && this.independentAssessmentService.sessionDetails.status === SCHEDULED)
  }

  updateItemTypeCount(): void {
    const result = this.getItemTypesWithCount({
      items: this.selectedSection?.items || [],
      totalCount: false
    })

    if (typeof result === 'object') {
      this.itemTypeCountDisplay = result
    }
  }

  getItemTypesWithCount({
    items,
    totalCount = false,
    isHiddenUI = false
  }: {
    items: IQuestion[]
    totalCount?: boolean
    isHiddenUI?: boolean
  }) {
    if (!items?.length) {
      return { visible: '', hidden: '', hiddenCount: 0, total: 0, hiddenUI: '' }
    }

    let hiddenUI = ''

    const countItemTypes = (acc: Record<string, number>, code: string) => {
      acc[code] = (acc[code] || 0) + 1
      return acc
    }

    const itemTypeCount = items?.reduce(
      (acc, item) => {
        const code = item?.itemType?.code
        if (this.multipleQuestionItemTypes.includes(code)) {
          item?.stem?.questions?.forEach(() => countItemTypes(acc, code))
        } else {
          countItemTypes(acc, code)
        }
        return acc
      },
      {} as Record<string, number>
    )

    const entries = Object.entries(itemTypeCount)
    const total = entries.reduce((sum, [, count]) => sum + count, 0)

    if (this.isItemAuthor || totalCount) {
      this.itemsCount = total

      if (totalCount) {
        return this.itemsCount
      }
    }

    if (isHiddenUI) {
      const itemTypeCodeUI = Object.keys(itemTypeCount)
      hiddenUI = ''

      itemTypeCodeUI.forEach((codeUI) => {
        hiddenUI += `
      <div fxLayout="row">
        <span>${codeUI}:</span>
        <span class="digi-mr-5"><span>${itemTypeCount[codeUI]}</span></span>
      </div>
    `
      })

      return {
        visible: '',
        hidden: '',
        hiddenCount: 0,
        total: 0,
        hiddenUI
      }
    }

    const visibleEntries = entries.slice(0, 3)
    const hiddenEntries = entries.slice(3)

    const visible = visibleEntries
      .map(([key, value]) => {
        const itemType = this.utilService.getTranslateItemType({ itemType: key })
        const itemValue = this.arabicNumberPipe.transform(value)

        return `<div fxLayout="row"><span>${itemType?.code}:</span> <span class="digi-mr-5"><span>${itemValue}</span></span></div>`
      })
      .join(' ')

    const hidden = hiddenEntries
      .map(([key, value]) => {
        const itemType = this.utilService.getTranslateItemType({ itemType: key })
        const itemValue = this.arabicNumberPipe.transform(value)

        return `<div fxLayout="row"><span>${itemType?.code}:</span> <span class="digi-mr-5"><span>${itemValue}</span></span></div>`
      })
      .join(' ')

    return {
      visible,
      hidden,
      hiddenCount: hiddenEntries.length,
      total,
      hiddenUI
    }
  }

  getTotalItems() {
    return this.independentAssessmentDetails?.sections?.reduce((acc, section) => {
      return acc + Number(this.getItemTypesWithCount({ items: section?.items, totalCount: true }))
    }, 0)
  }

  onChangeSelectAllItem(): void {
    this.independentAssessmentDetails.sections.forEach((section) => {
      section.items.forEach((item) => (item.isSelected = this.isSelectAllItem))
    })

    this.refreshAssessment()
  }

  onChangeItemSelect() {
    const sections = this.independentAssessmentDetails.sections

    const hasAnySelected = sections.some((section) => section.items.some((item) => item.isSelected))

    const areAllSelected = !sections.some((section) =>
      section.items.some((item) => !item.isSelected)
    )

    this.isSelectAllItem = areAllSelected

    this.isIntermediateCheckBox = !areAllSelected && hasAnySelected
  }

  get hasReviewersAssigned() {
    return this.independentAssessmentDetails.items?.some(({ reviewers = [] }) => reviewers.length)
  }

  onChangeCourse({ course }: { course: IExamDetail }) {
    if (!this.selectedCourse) {
      return this.setSelectedCourse({ course })
    }

    const data = {
      isError: true,
      title: 'common.alertMessage',
      message: 'independentAssessmentAuthoring.switchCourseConfirmation',
      additionalInfo: 'independentAssessmentAuthoring.wouldYouLikeToUpdate',
      icon: 'warning_amber'
    }

    this.openConfirmPopup({ data })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          return (this.selectedCourse = { ...this.selectedCourseClone })
        }

        this.setSelectedCourse({ course })
      })
  }

  private setSelectedCourse({ course }: { course: IExamDetail }) {
    this.selectedCourse = course
    this.selectedCourseClone = cloneDeep(this.selectedCourse)

    void this.router.navigate([], {
      queryParams: course ? { courseId: (course as IExamDetail)?._id } : {},
      queryParamsHandling: 'merge'
    })

    this.independentAssessmentDetails.courseName = this.selectedCourse?.name

    this.assessmentService
      .assignCourseForQuestionBank({
        assessmentId: this.assessmentId,
        payload: {
          hierarchy: this.selectedCourse?.hierarchy,
          courseId: this.selectedCourse?._id,
          courseName: this.independentAssessmentDetails.courseName
        }
      })
      .subscribe(() => {
        this.getAssessmentDetails()
      })
  }

  onChangeAssignStudent() {
    this.getScheduleSettings({
      canValidate: false,
      stepperIndex: 3,
      deviceType: this.selectedDeviceType
    })
  }

  getRoleName({ role }: { role: string }) {
    if (!role) {
      return ''
    }

    return this.utilService.getRoleName({ role })
  }

  getRoleLabel({ role }: { role: string }) {
    if (!role) {
      return ''
    }

    if (role === 'assessmentAuthor') {
      return this.independentAssessmentDetails?.assessmentAuthorLabel ?? 'Assessment Author'
    }

    if (role === 'reviewer') {
      return this.independentAssessmentDetails?.reviewerLabel ?? 'Reviewer'
    }

    return ''
  }

  get isFromAssessmentCreation() {
    return this.route.routeConfig?.path === 'independent/:assessmentId'
  }

  get canChangeSchedule() {
    if (this.isFromAssessmentCreation && this.independentAssessmentDetails?.isScheduleRequest) {
      return false
    }

    if (this.isPublished) {
      return false
    }

    return true
  }

  onClickSettingIcon() {
    this.utilService.customSliderToggle({
      id: this.settingSliderId,
      customWidth: window.innerWidth > 1000 ? '950px' : '90%'
    })
    this.showSetting = !this.showSetting
  }

  onClickMappingIcon() {
    if (this.checkHasCannotMapItems()) {
      return this.openConfirmationPopup({ type: 'mapping' })
        .afterClosed()
        .subscribe((res) => {
          if (!res) {
            return
          }

          if (this.getSelectedItems.length === this.checkHasCannotMapItems()) {
            return
          }

          return this.itemMappingSlider.mappingSliderToggle()
        })
    }

    this.itemMappingSlider.mappingSliderToggle()
  }

  onClickAutoGenerateSlider() {
    this.utilService.customSliderToggle({ id: 'digi-auto-generate', customWidth: '70%' })
  }

  refreshAssessmentDetails() {
    this.getAssessmentDetails()
  }
}
