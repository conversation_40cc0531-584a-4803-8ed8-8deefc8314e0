import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'

import { TranslateService } from '@ngx-translate/core'
import { ToastrService } from 'ngx-toastr'

import {
  IAssessmentName,
  IExamCategory,
  IExamConfig,
  IExamConfigPayload,
  IImpactLevel,
  IIndependentAssessmentType,
  IIndependentExamCategory,
  IIndependentExamType,
  ITextValue
} from '@appcore/app/models'
import { IConfirmPopupData } from '@appcore/app/models/exam-readiness/exam-readiness-on-going.interface'
import { IExamType } from '@appcore/app/models/scheduler/exam-details.interface'
import { cloneDeep, ErrorHandlerService, SettingsService } from '@appcore/app/services'
import {
  ONSITE_WITH_PROCTOR,
  REMOTE_WITH_PROCTOR,
  REMOTE_WITHOUT_PROCTOR,
  STUDENT_OWNED_DEVICE
} from '@appcore/constants/index'

import { IndependentAssessmentService } from '../../independent-assessment.service'
import { AddNewExamAttemptTypePopupComponent } from '../add-new-types-popup/add-new-type-popup.component'
import { ConfirmPopupComponent } from '../confirm-popup/confirm-popup.component'

@Component({
  selector: 'digi-independent-assessment-exam-configuration',
  templateUrl: './independent-assessment-exam-configuration.component.html',
  styleUrls: ['./independent-assessment-exam-configuration.component.css']
})
export class IndependentAssessmentExamConfigurationComponent implements OnInit {
  @Input()
  courseGroupId: string

  @Input()
  isScheduleRequest = false

  @Output()
  changeConfig: EventEmitter<boolean> = new EventEmitter()

  @Output()
  changeClickedStep: EventEmitter<number> = new EventEmitter()

  @Output()
  changeDeviceType: EventEmitter<string> = new EventEmitter()

  @Output()
  changeExamConfig: EventEmitter<boolean> = new EventEmitter()

  isActive = false
  selectedSod = 'CAMERA'
  assessmentTypes: IIndependentAssessmentType[]
  selectedAssessmentType: IIndependentAssessmentType
  selectedExamType: IIndependentExamType
  examCategories: IIndependentExamCategory[]
  selectedExamCategory: IIndependentExamCategory
  attemptTypes: IAssessmentName[]
  existingAttemptTypes: IAssessmentName[]
  filterExistingAttemptTypes: IAssessmentName[]
  newAttemptTypes: IAssessmentName[]
  filterNewAttemptTypes: IAssessmentName[]
  examTypes: IIndependentExamType[]
  existingExamTypes: IIndependentExamType[]
  filterExistingExamTypes: IIndependentExamType[]
  newExamTypes: IIndependentExamType[]
  filterNewExamTypes: IIndependentExamType[]
  selectedAttemptType: IAssessmentName
  browsers: ITextValue[] = [
    // { name: 'common.digiBrowser', key: 'FULL_SCREEN_MODE' },
    { name: 'common.normalBrowser', key: 'BROWSER' },
    { name: 'common.safeExamBrowser', key: 'SEB' }
  ]
  selectedBrowser: ITextValue
  proctoringTypes: IImpactLevel[] = [
    {
      name: 'settings.globalExamSettings.examTypes.onsiteWithProctor',
      code: 'settings.globalExamSettings.examTypes.o+p',
      text: 'independentAssessmentAuthoring.onsiteWithProctorTooltip',
      value: ONSITE_WITH_PROCTOR
    }
    // {
    //   name: 'settings.globalExamSettings.examTypes.remoteWithProctor',
    //   code: 'settings.globalExamSettings.examTypes.r+p',
    //   description: 'Virtual Test Center',
    //   text: 'independentAssessmentAuthoring.remoteWithProctorTooltip',
    //   value: REMOTE_WITH_PROCTOR,
    // },
    // {
    //   name: 'settings.globalExamSettings.examTypes.remoteWithOutProctor',
    //   code: 'settings.globalExamSettings.examTypes.r-p',
    //   description: 'No Test Center',
    //   text: 'independentAssessmentAuthoring.remoteWithoutProctorTooltip',
    //   value: REMOTE_WITHOUT_PROCTOR
    // },
    // {
    //   name: 'settings.globalExamSettings.examTypes.remoteWithProctor',
    //   code: 'settings.globalExamSettings.examTypes.r+p',
    //   description: 'Virtual Test Center',
    //   text: 'independentAssessmentAuthoring.remoteWithProctorTooltip',
    //   value: REMOTE_WITH_PROCTOR
    // }
  ]
  selectedProctoringType: IImpactLevel = {
    name: '',
    code: '',
    description: '',
    text: '',
    value: ''
  }
  authMethods: ITextValue[] = [
    {
      key: 'FACIAL',
      name: 'settings.onsiteConductingExams.studentAuthentication.facialAuthentication'
    },
    { key: 'PASSWORD', name: 'settings.onsiteConductingExams.studentAuthentication.userPassword' },
    { key: 'BOTH', name: 'independentAssessmentAuthoring.facialAndPassword' }
  ]
  studentAuthMethodsByGender: ITextValue[] = [
    { name: 'common.none', key: 'NONE' },
    { name: 'common.male', key: 'M' },
    { name: 'common.female', key: 'F' }
  ]
  selectedAuthMethodByGender = 'NONE'
  selectedAuthMethod = ''
  selectedFilter: IExamConfigPayload = {
    systemRequirement: '',
    studentAuthenticationMethod: '',
    canSkipStudentAuthMethodByGender: '',
    browsers: [],
    assessmentTypeId: '',
    examTypeId: '',
    locationAndProctoringType: '',
    examCategoryCode: '',
    attemptType: {
      _id: '',
      name: ''
    }
  }
  hasCustomAttemptType = false
  hasCustomExamType = false
  previousProctoringType: IImpactLevel | null = null
  examConfigs: IExamConfig
  searchText = ''
  allowToAddNewTypes = false
  isFromAssessmentCreation = false

  constructor(
    private dialog: MatDialog,
    private settingService: SettingsService,
    private independentAssessmentService: IndependentAssessmentService,
    private toastrService: ToastrService,
    private translateService: TranslateService,
    private errorHandler: ErrorHandlerService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.isFromAssessmentCreation = this.route.routeConfig?.path === 'independent/:assessmentId'

    void this.router.navigate([], {
      queryParams: { selectedIndex: this.isScheduleRequest ? 0 : 1 },
      queryParamsHandling: 'merge'
    })

    if (this.independentAssessmentService.latestTab < 1) {
      this.independentAssessmentService.latestTab = 1
    }

    this.getExamConfigs()

    this.independentAssessmentService.isConfigChanged = false
  }

  private getExamConfigs() {
    this.independentAssessmentService
      .getExamConfigs({ isScheduleRequest: this.isScheduleRequest })
      .subscribe(
        ({ data }) => {
          this.examConfigs = this.independentAssessmentService.examConfigs = data
          this.setExamConfigValues()

          if (this.isScheduleRequest) {
            if (
              this.examConfigs.isTcAdded ||
              this.examConfigs.isGroupAssigned ||
              this.examConfigs.isStudentAssigned
            ) {
              this.independentAssessmentService.latestTab = 2
            }
          }
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  private setExamConfigValues() {
    const {
      assessmentType,
      examType,
      attemptType,
      browsers,
      canSkipStudentAuthMethodByGender,
      studentAuthenticationMethod,
      systemRequirement
    } = this.examConfigs
    const selectedConfig = this.independentAssessmentService?.selectedExamConfig

    if (selectedConfig) {
      if (selectedConfig?.isGroupAssigned) {
        this.examConfigs.isGroupAssigned = selectedConfig?.isGroupAssigned
      }

      if (selectedConfig?.isTcAdded) {
        this.examConfigs.isTcAdded = selectedConfig?.isTcAdded
      }

      if (selectedConfig?.isStudentAssigned) {
        this.examConfigs.isStudentAssigned = selectedConfig?.isStudentAssigned
      }
    }

    if (this.independentAssessmentService.assessmentTypes) {
      this.assessmentTypes = cloneDeep(this.independentAssessmentService.assessmentTypes)

      if (selectedConfig || assessmentType) {
        const selectedAssessmentTypeId = selectedConfig?.assessmentTypeId || assessmentType?._id
        const selectedExamCategoryCode =
          selectedConfig?.examCategoryCode || examType?.examCategory?.code
        const selectedExamTypeId = selectedConfig?.examTypeId || examType?._id

        if (selectedAssessmentTypeId) {
          this.selectedAssessmentType = this.assessmentTypes.find(
            (type) => type._id === selectedAssessmentTypeId
          )
        } else {
          this.setDefaultAssessmentType()
        }

        if (selectedExamCategoryCode) {
          this.selectedExamCategory = this.selectedAssessmentType?.examCategories.find(
            (category) => category?.code === selectedExamCategoryCode
          )
        }

        this.setExamTypes()

        if (selectedExamTypeId) {
          this.selectedExamType = this.selectedExamCategory?.examTypes.find(
            (type) => type._id === selectedExamTypeId
          )
        }
      } else {
        this.setDefaultAssessmentType()
      }
    } else {
      this.getAssessmentTypes()
    }

    if (this.independentAssessmentService.attemptTypes) {
      this.attemptTypes =
        this.existingAttemptTypes =
        this.filterExistingAttemptTypes =
          cloneDeep(this.independentAssessmentService.attemptTypes)
      this.newAttemptTypes = this.filterNewAttemptTypes = this.getNewAttemptTypes()
      this.hasCustomAttemptType = this.existingAttemptTypes.some((type) => type.isCustom)
      this.existingAttemptTypes = this.filterExistingAttemptTypes = this.getExistingAttemptTypes()

      if (selectedConfig || attemptType) {
        const selectedAttemptTypeId = selectedConfig?.attemptType?._id || attemptType?._id

        this.selectedAttemptType = this.attemptTypes.find(
          (type) => type._id === selectedAttemptTypeId
        )
      } else if (!this.selectedAttemptType && this.attemptTypes?.length) {
        this.selectedAttemptType =
          this.attemptTypes?.find((attempt) => attempt?.name?.toLowerCase() === 'regular') ||
          this.attemptTypes[0]
      }
    } else {
      this.getAttemptTypes()
    }

    const locationAndProctoringType = (selectedConfig?.locationAndProctoringType ||
      examType?.locationAndProctoringType) as string
    const browserKey = selectedConfig?.browsers?.[0] || browsers[0]

    this.selectedBrowser =
      this.browsers.find((browser) => browser.key === browserKey) || this.browsers[1]
    this.selectedAuthMethodByGender =
      selectedConfig?.canSkipStudentAuthMethodByGender || canSkipStudentAuthMethodByGender
    this.selectedAuthMethod =
      selectedConfig?.studentAuthenticationMethod || studentAuthenticationMethod
    this.selectedSod = selectedConfig?.systemRequirement || systemRequirement || 'CAMERA'
    this.selectedProctoringType = this.proctoringTypes.find(
      (type) => type.value === locationAndProctoringType?.toLowerCase()
    )

    if (this.independentAssessmentService.selectedExamConfig) {
      this.selectedFilter = cloneDeep(this.independentAssessmentService.selectedExamConfig)
    }
  }

  private getAssessmentTypes() {
    this.independentAssessmentService.getIndependentAssessmentAssessmentTypes().subscribe(
      ({ data }) => {
        this.assessmentTypes = data
        this.setDefaultAssessmentType()

        this.independentAssessmentService.assessmentTypes = cloneDeep(this.assessmentTypes)

        if (this.examConfigs?.assessmentType) {
          this.selectedAssessmentType = this.assessmentTypes.find(
            (type) => type._id === this.examConfigs.assessmentType._id
          )

          this.selectedExamCategory = this.selectedAssessmentType?.examCategories.find(
            (category) => category?.code === this.examConfigs?.examType?.examCategory?.code
          )
          this.setExamTypes()

          this.selectedExamType = this.selectedExamCategory?.examTypes.find(
            (type) => type._id === this.examConfigs?.examType?._id
          )
        }
      },
      (err) => this.errorHandler.errorLog(err)
    )
  }

  private setDefaultAssessmentType() {
    this.selectedAssessmentType =
      this.assessmentTypes?.find((assessment) => assessment?.name?.toLowerCase() === 'summative') ||
      this.assessmentTypes[0]

    this.selectedExamCategory =
      this.selectedAssessmentType?.examCategories?.find(
        (category) => category?.name?.toLowerCase() === 'theory'
      ) || this.selectedAssessmentType?.examCategories[0]

    this.examTypes = this.selectedExamCategory?.examTypes
    this.selectedExamType = this.examTypes[0]
  }

  private setExamTypes() {
    this.hasCustomExamType = this.selectedExamCategory?.examTypes.some((type) => type.isCustom)
    this.examTypes = this.selectedExamCategory?.examTypes
    this.newExamTypes = this.filterNewExamTypes = this.selectedExamCategory?.examTypes.filter(
      (type) => type.isCustom
    )
    this.existingExamTypes = this.filterExistingExamTypes =
      this.selectedExamCategory?.examTypes.filter((type) => !type.isCustom)
  }

  private setExamTypeAndExamCategory() {
    this.examConfigs.assessmentType = this.selectedAssessmentType
    this.examCategories = this.selectedAssessmentType?.examCategories
    this.selectedExamCategory = undefined
    this.selectedExamType = undefined
    this.updateSelectedFilter({})
  }

  private getAttemptTypes() {
    this.existingAttemptTypes = []
    this.independentAssessmentService.getIndependentAssessmentAttemptTypes().subscribe(
      ({ data }) => {
        this.attemptTypes = this.existingAttemptTypes = data
        this.selectedAttemptType =
          this.attemptTypes?.find((attempt) => attempt?.name?.toLowerCase() === 'regular') ||
          this.attemptTypes[0]
        this.independentAssessmentService.attemptTypes = cloneDeep(this.existingAttemptTypes)
        this.hasCustomAttemptType = this.existingAttemptTypes.some((type) => type.isCustom)
        this.newAttemptTypes = this.filterNewAttemptTypes = this.getNewAttemptTypes()
        this.existingAttemptTypes = this.filterExistingAttemptTypes = this.getExistingAttemptTypes()

        if (this.examConfigs?.attemptType) {
          this.selectedAttemptType = this.attemptTypes.find(
            (type) => type._id === this.examConfigs.attemptType._id
          )
        }
      },
      (err) => this.errorHandler.errorLog(err)
    )
  }

  onClickAddNewType({ type }: { type: string }) {
    this.dialog
      .open(AddNewExamAttemptTypePopupComponent, {
        data: { type },
        panelClass: ['digi-padding-none', 'dialog-fix'],
        disableClose: true,
        direction: this.settingService.getOptions().dir
      })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          return
        }

        if (type === 'Attempt') {
          this.createNewAttemptType({ name: res?.name })
        }

        if (type === 'Exam') {
          this.createNewExamType({ name: res?.name, code: res?.code })
        }
      })
  }

  onClickEditNewType({
    attemptType,
    examType,
    event
  }: {
    attemptType?: IAssessmentName
    examType?: IIndependentExamType
    event: MouseEvent
  }) {
    event.stopPropagation()
    let nameCode = { name: '', code: '' }

    if (attemptType) {
      nameCode = { name: attemptType.name, code: '' }
    }

    if (examType) {
      nameCode = { name: examType.name, code: examType.code }
    }

    this.dialog
      .open(AddNewExamAttemptTypePopupComponent, {
        data: { type: attemptType ? 'Attempt' : 'Exam', nameCode },
        panelClass: ['digi-padding-none', 'dialog-fix'],
        disableClose: true,
        direction: this.settingService.getOptions().dir
      })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          return
        }

        if (attemptType) {
          this.updateAttemptType({ name: res?.name, id: attemptType._id })
        }

        if (examType) {
          this.updateExamType({ name: res?.name, code: res?.code, id: examType._id })
        }
      })
  }

  private updateAttemptType({ name, id }: { name: string; id: string }) {
    this.independentAssessmentService.updateNewAttemptType({ name, id }).subscribe(
      ({ message }) => {
        this.toastrService.success(message)
        this.getAttemptTypes()
      },
      (err) => this.errorHandler.errorLog(err)
    )
  }

  private updateExamType({ name, code, id }: { name: string; code: string; id: string }) {
    const payload = {
      name,
      code,
      type: this.selectedAssessmentType?.name,
      examCategories: [this.selectedExamCategory]
    }

    this.independentAssessmentService.updateNewExamType({ payload, id }).subscribe(
      ({ message }) => {
        this.toastrService.success(message)
        this.getAssessmentTypes()
      },
      (err) => this.errorHandler.errorLog(err)
    )
  }

  private createNewExamType({ name, code }: { name: string; code: string }) {
    const selectedExamCategory = cloneDeep(this.selectedExamCategory)

    if (selectedExamCategory && selectedExamCategory.examTypes) {
      delete selectedExamCategory.examTypes
    }

    const payload = {
      name,
      code,
      type: this.selectedAssessmentType?.name,
      examCategories: [selectedExamCategory]
    }

    this.independentAssessmentService.createNewExamType({ payload }).subscribe(
      ({ message }) => {
        this.toastrService.success(message)
        this.getAssessmentTypes()
      },
      (err) => this.errorHandler.errorLog(err)
    )
  }

  private createNewAttemptType({ name }: { name: string }) {
    this.independentAssessmentService.createNewAttemptType({ name }).subscribe(
      ({ message }) => {
        this.toastrService.success(message)
        this.getAttemptTypes()
      },
      (err) => this.errorHandler.errorLog(err)
    )
  }

  onChangeBrowserType() {
    this.updateSelectedFilter({})
  }

  private openConfirmPopup({ data }: { data: IConfirmPopupData }) {
    return this.dialog.open(ConfirmPopupComponent, {
      data,
      width: '505px',
      height: '233px',
      panelClass: ['digi-padding-none', 'dialog-fix'],
      disableClose: true,
      direction: this.settingService.getOptions().dir
    })
  }

  onChangeFilter({
    type,
    isSod = false,
    isExamPreference = false
  }: {
    type?: string
    isSod?: boolean
    isExamPreference?: boolean
  }) {
    this.independentAssessmentService.isConfigChanged = true
    this.changeExamConfig.emit(true)
    if (type === 'proctor' && this.selectedProctoringType?.value === REMOTE_WITHOUT_PROCTOR) {
      this.selectedAuthMethod = 'PASSWORD'
    }

    if (type === 'authMethod') {
      if (['FACIAL', 'BOTH'].includes(this.selectedAuthMethod) && this.selectedSod === 'NONE') {
        this.selectedSod = 'CAMERA'
      }

      if (this.selectedAuthMethod === 'PASSWORD') {
        this.selectedSod = 'NONE'
      }
    }

    if (type === 'proctor' && this.independentAssessmentService?.sessionDetails) {
      this.independentAssessmentService.sessionDetails.locationAndProctoringType =
        this.selectedProctoringType.value
    }

    if (
      (type === 'proctor' && this.selectedProctoringType?.value === REMOTE_WITHOUT_PROCTOR) ||
      (isExamPreference &&
        (this.examConfigs?.isStudentAssigned || this.examConfigs?.isGroupAssigned))
    ) {
      const data = {
        isError: true,
        title: this.translateService.instant(
          type === 'proctor'
            ? 'independentAssessmentAuthoring.proctorChangeTitle'
            : 'independentAssessmentAuthoring.confirmTile'
        ),
        message: this.translateService.instant(
          type === 'proctor'
            ? 'independentAssessmentAuthoring.proctorChangeMessage'
            : 'independentAssessmentAuthoring.confirmMessage'
        ),
        additionalInfo: this.translateService.instant(
          'independentAssessmentAuthoring.confirmAdditionalInfo'
        ),
        primaryButton: this.translateService.instant(
          type === 'proctor'
            ? 'independentAssessmentAuthoring.proctorChangePrimaryButton'
            : 'independentAssessmentAuthoring.confirmChangePrimaryButton'
        )
      }

      this.openConfirmPopup({ data })
        .afterClosed()
        .subscribe((res) => {
          if (!res) {
            if (
              type === 'proctor' &&
              this.selectedProctoringType?.value === REMOTE_WITHOUT_PROCTOR
            ) {
              this.examConfigs.examType.locationAndProctoringType = this.proctoringTypes[0]?.value
              this.selectedProctoringType = this.proctoringTypes.find((proctor) => {
                return proctor?.value === this.examConfigs?.examType?.locationAndProctoringType
              })

              this.independentAssessmentService.sessionDetails.locationAndProctoringType =
                this.selectedFilter.locationAndProctoringType = this.selectedProctoringType?.value
            }

            if (isExamPreference) {
              switch (type) {
                case 'assessmentType':
                  this.selectedAssessmentType = this.assessmentTypes?.find((assessment) => {
                    return assessment?._id === this.examConfigs[type]?._id
                  })
                  this.resetStudentDetails()
                  break

                case 'examCategory':
                  this.selectedExamCategory = this.selectedAssessmentType?.examCategories.find(
                    (examCategory) => {
                      return examCategory?._id === this.examConfigs?.examType[type]?._id
                    }
                  )
                  this.resetStudentDetails()
                  break

                case 'examType':
                  this.selectedExamType = this.selectedExamCategory?.examTypes?.find((examType) => {
                    return examType?._id === this.examConfigs[type]?._id
                  })
                  this.resetStudentDetails()
                  break

                case 'attemptType':
                  this.selectedAttemptType = this.attemptTypes.find((attempt) => {
                    return attempt?._id === this.examConfigs[type]?._id
                  })
                  this.resetStudentDetails()
                  break
              }
            }
            this.updateProctorConfiguration({ type, isExamPreference })

            return
          }
          this.independentAssessmentService.latestTab = 1

          if (type === 'proctor') {
            this.independentAssessmentService.assignedGroups = undefined
            this.independentAssessmentService.testCenterDetails.testCenters = []
            this.independentAssessmentService.selectedExamConfig.isTcAdded =
              this.examConfigs.isTcAdded = false
            this.independentAssessmentService.assignExamInvigilatorValues = undefined

            return
          }

          this.examConfigs.isGroupAssigned = this.examConfigs.isStudentAssigned = false

          if (this.independentAssessmentService?.assignedGroups) {
            this.independentAssessmentService.assignedGroups = undefined
          }

          if (this.independentAssessmentService?.assignExamInvigilatorValues?.testCenters) {
            this.independentAssessmentService.assignExamInvigilatorValues.testCenters = undefined
          }

          if (this.independentAssessmentService?.testCenterDetails) {
            this.independentAssessmentService.testCenterDetails = undefined
          }

          if (this.independentAssessmentService?.sessionDetails) {
            this.independentAssessmentService.sessionDetails = undefined
          }

          if (this.independentAssessmentService?.allTestCenterDetails) {
            this.independentAssessmentService.allTestCenterDetails = undefined
          }

          if (this.independentAssessmentService?.examTimeDetails) {
            this.independentAssessmentService.examTimeDetails = undefined
          }
        })

      return
    } else {
      if (type === 'proctor') {
        this.examConfigs.examType.locationAndProctoringType = this.selectedProctoringType?.value
      }

      switch (type) {
        case 'assessmentType':
          this.examConfigs.assessmentType = this.selectedAssessmentType
          this.resetStudentDetails()
          this.independentAssessmentService.hasSameExamConfig = false
          break

        case 'examCategory':
          this.examConfigs.examType.examCategory = this
            .selectedExamCategory as unknown as IExamCategory
          this.resetStudentDetails()
          this.independentAssessmentService.hasSameExamConfig = false
          break

        case 'examType':
          this.examConfigs.examType = this.selectedExamType as unknown as IExamType
          this.resetStudentDetails()
          this.independentAssessmentService.hasSameExamConfig = false
          break

        case 'attemptType':
          this.examConfigs.attemptType = this.selectedAttemptType
          this.resetStudentDetails()
          this.independentAssessmentService.hasSameExamConfig = false
          break
      }
    }

    if (isSod) {
      this.selectedSod = type
    }

    if (type === 'assessmentType') {
      this.setExamTypeAndExamCategory()
    }

    if (type === 'examCategory') {
      this.selectedExamType = undefined
      this.setExamTypes()
    }

    this.updateSelectedFilter({})
  }

  private resetStudentDetails() {
    if (this.independentAssessmentService.institutionCalenders) {
      this.independentAssessmentService.institutionCalenders = []
    }

    if (this.independentAssessmentService.institutionGroup) {
      this.independentAssessmentService.institutionGroup = undefined
    }

    if (this.independentAssessmentService.assignedGroups) {
      this.independentAssessmentService.assignedGroups = undefined
    }
  }

  private updateProctorConfiguration({
    type,
    isExamPreference
  }: {
    type: string
    isExamPreference: boolean
  }): void {
    if (type === 'proctor' && this.selectedProctoringType?.value === REMOTE_WITHOUT_PROCTOR) {
      this.examConfigs.examType.locationAndProctoringType = this.proctoringTypes[0]?.value
      this.selectedProctoringType = this.proctoringTypes.find((proctor) => {
        return proctor?.value === this.examConfigs?.examType?.locationAndProctoringType
      })

      this.independentAssessmentService.sessionDetails.locationAndProctoringType =
        this.selectedFilter.locationAndProctoringType = this.selectedProctoringType?.value
    }

    if (isExamPreference) {
      switch (type) {
        case 'assessmentType':
          this.selectedAssessmentType = this.assessmentTypes?.find((assessment) => {
            return assessment?._id === this.examConfigs[type]?._id
          })
          break

        case 'examCategory':
          this.selectedExamCategory = this.selectedAssessmentType?.examCategories.find(
            (examCategory) => {
              return examCategory?._id === this.examConfigs?.examType[type]?._id
            }
          )
          break

        case 'examType':
          this.selectedExamType = this.selectedExamCategory?.examTypes?.find((examType) => {
            return examType?._id === this.examConfigs[type]?._id
          })
          break

        case 'attemptType':
          this.selectedAttemptType = this.existingAttemptTypes.find((attempt) => {
            return attempt?._id === this.examConfigs[type]?._id
          })
          break
      }
    }
  }

  updateSelectedFilter({ isUpdate }: { isUpdate?: boolean }) {
    this.selectedFilter = {
      systemRequirement: this.selectedSod,
      studentAuthenticationMethod: this.selectedAuthMethod,
      canSkipStudentAuthMethodByGender: this.selectedAuthMethodByGender,
      browsers: this.selectedBrowser ? [this.selectedBrowser.key] : [],
      assessmentTypeId: this.selectedAssessmentType?._id,
      examTypeId: this.selectedExamType?._id,
      locationAndProctoringType: this.selectedProctoringType?.value,
      examCategoryCode: this.selectedExamCategory?.code,
      attemptType: this.selectedAttemptType && {
        _id: this.selectedAttemptType?._id,
        name: this.selectedAttemptType?.name
      }
    }

    this.independentAssessmentService.selectedExamConfig = cloneDeep(this.selectedFilter)

    if (isUpdate) {
      this.independentAssessmentService.resetSelectedValues({ page: 'examConfiguration' })
    }
  }

  private resetSelectedFilters() {
    this.selectedFilter = {
      systemRequirement: '',
      studentAuthenticationMethod: '',
      canSkipStudentAuthMethodByGender: '',
      browsers: [],
      assessmentTypeId: '',
      examTypeId: '',
      locationAndProctoringType: '',
      examCategoryCode: '',
      attemptType: {
        _id: '',
        name: ''
      }
    }
  }

  private isAllFieldsSelected() {
    return Object.entries(this.selectedFilter).every(([key, value]) => {
      if (key === 'browsers') {
        return Array.isArray(value) && value.length
      }

      return !!value
    })
  }

  validateSelectedFilter({
    onlyValidate = false,
    canHideToast = false,
    clickedStep
  }: {
    onlyValidate?: boolean
    canHideToast?: boolean
    clickedStep?: number
  }): boolean {
    if (
      !this.independentAssessmentService.isConfigChanged &&
      this.isAllFieldsSelected() &&
      clickedStep === undefined
    ) {
      this.independentAssessmentService.assessmentValidation.isExamConfigured = true
      this.changeConfig.emit(true)

      return true
    }

    this.updateSelectedFilter({ isUpdate: true })

    if (!this.selectedFilter.assessmentTypeId) {
      this.toastrService.warning(
        this.translateService.instant('independentAssessmentAuthoring.assessmentTypeIsMissing')
      )
      this.resetSelectedFilters()

      return false
    }

    if (!this.selectedFilter.examCategoryCode) {
      this.toastrService.warning(
        this.translateService.instant('independentAssessmentAuthoring.examCategoryIsMissing')
      )
      this.resetSelectedFilters()

      return false
    }

    if (!this.selectedFilter.examTypeId) {
      this.toastrService.warning(
        this.translateService.instant('independentAssessmentAuthoring.examTypeIsMissing')
      )
      this.resetSelectedFilters()

      return false
    }

    if (!this.selectedFilter.attemptType) {
      this.toastrService.warning(
        this.translateService.instant('independentAssessmentAuthoring.attemptTypeIsMissing')
      )
      this.resetSelectedFilters()

      return false
    }

    if (!this.selectedFilter.locationAndProctoringType) {
      this.toastrService.warning(
        this.translateService.instant(
          'independentAssessmentAuthoring.locationAndProctoringTypeIsMissing'
        )
      )
      this.resetSelectedFilters()

      return false
    }

    if (!this.selectedFilter.systemRequirement) {
      this.toastrService.warning(
        this.translateService.instant('independentAssessmentAuthoring.systemRequirementIsMissing')
      )
      this.resetSelectedFilters()

      return false
    }

    if (!this.selectedFilter.studentAuthenticationMethod) {
      this.toastrService.warning(
        this.translateService.instant(
          'independentAssessmentAuthoring.studentAuthenticationMethodIsMissing'
        )
      )
      this.resetSelectedFilters()

      return false
    }

    if (!this.selectedFilter.browsers.length) {
      this.toastrService.warning(
        this.translateService.instant('independentAssessmentAuthoring.browsersSelectionIsMissing')
      )
      this.resetSelectedFilters()

      return false
    }

    if (!onlyValidate) {
      this.updateIndependentAssessmentExamConfiguration({
        canRescheduleExam: true,
        canHideToast,
        clickedStep
      })
    }

    this.independentAssessmentService.assessmentValidation.isExamConfigured = true

    return false
  }

  private updateIndependentAssessmentExamConfiguration({
    canRescheduleExam,
    canHideToast = false,
    clickedStep
  }: { canRescheduleExam?: boolean; canHideToast?: boolean; clickedStep?: number } = {}) {
    this.independentAssessmentService
      .updateIndependentAssessmentExamConfiguration({
        courseGroupId: this.courseGroupId,
        ...(this.isScheduleRequest && { isScheduleRequest: true }),
        payload: { ...this.selectedFilter, canRescheduleExam }
      })
      .subscribe(
        ({ message }) => {
          if (!canHideToast) {
            this.toastrService.success(message)
          }
          if (this.selectedFilter.locationAndProctoringType === REMOTE_WITHOUT_PROCTOR) {
            this.independentAssessmentService.examDeviceType = STUDENT_OWNED_DEVICE
            this.changeDeviceType.emit(STUDENT_OWNED_DEVICE)
          }
          if (!canHideToast) {
            if (clickedStep !== undefined) {
              this.changeClickedStep.emit(clickedStep)
            } else {
              this.changeConfig.emit(true)
            }
          }

          this.independentAssessmentService?.clickPublishAction.next(true)
        },
        (err) => {
          if (err.status === 701) {
            return this.dialog
              .open(ConfirmPopupComponent, {
                data: {
                  isError: true,
                  title: 'learningOutcomeReports.warning',
                  message: err.error.message
                },
                width: '505px',
                height: '233px',
                panelClass: ['digi-padding-none', 'dialog-fix'],
                disableClose: true,
                direction: this.settingService.getOptions().dir
              })
              .afterClosed()
              .subscribe((res) => {
                if (res) {
                  this.updateIndependentAssessmentExamConfiguration({ canRescheduleExam: true })
                  this.independentAssessmentService.resetScheduleData.next(true)
                  this.independentAssessmentService.sessionDetails =
                    this.independentAssessmentService.allTestCenterDetails =
                    this.independentAssessmentService.examTimeDetails =
                      undefined
                  this.independentAssessmentService.canShowTestCenters = false
                }
              })

            this.errorHandler.errorLog(err)
          }

          this.errorHandler.errorLog(err)
          this.independentAssessmentService.hasSameExamConfig = true
        }
      )
  }

  private getNewAttemptTypes() {
    return this.existingAttemptTypes?.filter((type) => type.isCustom)
  }

  private getExistingAttemptTypes() {
    return this.existingAttemptTypes?.filter((type) => !type.isCustom)
  }

  onClickDeleteNewType({ id, type }: { id: string; type: string }) {
    event.stopPropagation()

    if (type === 'attempt') {
      this.deleteAttemptType({ id })
    }

    if (type === 'exam') {
      this.deleteExamType({ id })
    }
  }

  private deleteExamType({ id }: { id: string }) {
    this.independentAssessmentService.deleteExamType({ id }).subscribe(
      ({ message }) => {
        this.toastrService.success(message)
        this.setExamTypeAndExamCategory()
        this.getAssessmentTypes()
      },
      (err) => this.errorHandler.errorLog(err)
    )
  }

  private deleteAttemptType({ id }: { id: string }) {
    this.independentAssessmentService.deleteAttemptType({ id }).subscribe(
      ({ message }) => {
        this.toastrService.success(message)
        this.getAttemptTypes()
      },
      (err) => this.errorHandler.errorLog(err)
    )
  }

  compareFn(examType: IIndependentExamType, selectedExamType: IIndependentExamType) {
    return examType?._id === selectedExamType?._id
  }

  get isPublished() {
    return this.independentAssessmentService.isPublished
  }

  get isRaisedByScheduler() {
    return this.independentAssessmentService.raisedByScheduler
  }

  onSearchExamTypeDropdown() {
    const searchText = this.searchText?.toLowerCase()

    this.filterExistingExamTypes = this.existingExamTypes?.filter((type) =>
      searchText ? type?.name?.toLowerCase().includes(searchText) : !type.isCustom
    )

    this.filterNewExamTypes = this.newExamTypes?.filter((type) =>
      searchText ? type?.name?.toLowerCase().includes(searchText) : type.isCustom
    )
  }

  onSearchAttemptTypeDropdown() {
    const searchText = this.searchText?.toLowerCase()

    this.filterExistingAttemptTypes = this.existingAttemptTypes?.filter((type) =>
      searchText ? type?.name?.toLowerCase().includes(searchText) : !type.isCustom
    )

    this.filterNewAttemptTypes = this.newAttemptTypes?.filter((type) =>
      searchText ? type?.name?.toLowerCase().includes(searchText) : type.isCustom
    )
  }

  onOpenedDropdown({ isOpen }: { isOpen: boolean }): void {
    if (!isOpen) {
      this.searchText = ''
    }
  }

  get canEditConductMode(): boolean {
    if (this.route.routeConfig?.path === 'schedule/creation' && this.isScheduleRequest) {
      return true
    }

    return this.canEditConfiguration
  }

  get canEditConfiguration(): boolean {
    if (this.isFromAssessmentCreation && this.isScheduleRequest) {
      return false
    }

    if (this.isRaisedByScheduler) {
      return false
    }

    return !this.isPublished
  }
}
