import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild
} from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'

import { TranslateService } from '@ngx-translate/core'
import { ToastrService } from 'ngx-toastr'
import { take } from 'rxjs/operators'
import { Subject } from 'rxjs'
import { debounceTime, distinctUntilChanged } from 'rxjs/operators'

import { TcAssignErrorPopupComponent } from '@appcore/app/core/components/exams/table/exammgt/dashboard/unscheduled-course-group-table/tc-assign-error-popup'
import { UploadStudentErrorPopupComponent } from '@appcore/app/core/components/exams/table/exammgt/dashboard/unscheduled-course-group-table/upload-student-error-popup'
import { ImportCsvDialogComponent } from '@appcore/app/core/components/shared-component'
import {
  IAssignStudent,
  IEvaluatorsFullName,
  IExamConfigPayload,
  IHeirarchy,
  IName,
  INameWithCode,
  IScheduledGroups,
  IStudentGroup,
  IUnAssignCount,
  IUnscheduledGroup
} from '@appcore/app/models'
import {
  IInstitutionCalendar,
  IUploadError
} from '@appcore/app/models/exam-readiness/exam-readiness-on-going.interface'
import { IAcadamicYear } from '@appcore/app/models/global-settings'
import { IIndependentStudentCount } from '@appcore/app/models/response/response.interface'
import { ProfileSettingsService } from '@appcore/app/pages/settings/profile-settings/profile-settings.service'
import {
  cloneDeep,
  CsvService,
  ErrorHandlerService,
  GlobalService,
  RouteService,
  SettingsService,
  UtilService
} from '@appcore/app/services'

import { IUploadStudents } from '../../../../models/exam-readiness/exam-readiness-on-going.interface'
import { IndependentAssessmentService } from '../../independent-assessment.service'
import { ConfirmPopupComponent } from '../confirm-popup/confirm-popup.component'
import { UploadStudentListComponent } from '@appcore/app/core/components/exams/table/exammgt/dashboard/unscheduled-course-group-table/upload-student-list/upload-student-list.component'
import { environment } from 'src/environments/environment'
import { ExamScheduleService } from '@appcore/app/pages/exams/exam-schedule.service'
import { PlannedCountExistComponent } from '@appcore/app/core/components/exams/table/exammgt/dashboard/unscheduled-course-group-table/planned-count-exist-popup/planned-count-exist-popup.component'

@Component({
  selector: 'digi-independent-assign-students',
  templateUrl: './independent-assign-students.component.html',
  styleUrls: ['./independent-assign-students.component.css']
})
export class IndependentAssignStudentComponent implements OnInit, OnChanges {
  @ViewChild(UploadStudentListComponent)
  uploadStudentListComponent!: UploadStudentListComponent

  @Input()
  hierarchy: IHeirarchy

  @Input()
  academicYear: IAcadamicYear

  @Input()
  courseGroupId: string

  @Input()
  course: INameWithCode

  @Input()
  courseId: string

  @Input()
  publishedAuthorDetails: IEvaluatorsFullName

  @Input()
  hierarchyCode: string

  @Input()
  canValidateFileName = false

  @Output()
  changeAssignStudent: EventEmitter<boolean> = new EventEmitter()

  @Input()
  groups: IStudentGroup[] = []

  @Input()
  malePlanedCount = 0

  @Input()
  femalePlanedCount = 0

  @Input()
  isExamRescheduled = false

  @Input()
  isScheduleRequest = false

  @Input()
  isExamSchedule = false

  displayedColumns: string[] = ['plannedStudents', 'count', 'uploadedStudents']
  academicYearId = ''
  institutionCalenders: IInstitutionCalendar[]
  selectedInstitutionCalender: IInstitutionCalendar
  canUseExternalGroup = false
  unscheduledGroups: IStudentGroup[]
  selectedUnscheduledGroups: IStudentGroup[] = []
  selectedUnscheduledGroupsClone: IUnscheduledGroup[]
  institutionGroup: IScheduledGroups
  assignedGroups: IAssignStudent
  importedStudentsCount = 0
  importedMaleStudentsCount = 0
  importedFemaleStudentsCount = 0
  deliveryTypeWithGroups: IStudentGroup[] = []
  selectedDeliveryType: IStudentGroup
  isLoading = false
  plannedStudentsCount: IUnAssignCount
  updateStudentCount = new Subject<void>()
  isStudentUploaded = false
  canEnableSis = environment.canEnableSis
  assessmentAuthors: <AUTHORS>
  allAssessmentAuthors: <AUTHORS>

  selectedAssessmentAuthors: <AUTHORS>
  bufferCount = {
    male: 0,
    female: 0
  }

  constructor(
    private activatedRoute: ActivatedRoute,
    private independentAssessmentService: IndependentAssessmentService,
    private errorHandler: ErrorHandlerService,
    private profileSettingsService: ProfileSettingsService,
    private toastrService: ToastrService,
    private dialog: MatDialog,
    private settingsService: SettingsService,
    private routeService: RouteService,
    private utilService: UtilService,
    private translateService: TranslateService,
    private csvService: CsvService,
    private router: Router,
    private globalService: GlobalService,
    private errorHandlerService: ErrorHandlerService,
    private route: ActivatedRoute,
    private examScheduleService: ExamScheduleService
  ) {}

  ngOnInit(): void {
    this.updateStudentCount
      .pipe(debounceTime(1000), distinctUntilChanged())
      .subscribe(() => this.assignStudentGroups({ isInit: true }))

    void this.router.navigate([], {
      queryParams: { selectedIndex: this.isScheduleRequest ? 1 : 2 },
      queryParamsHandling: 'merge'
    })

    this.activatedRoute.queryParams.subscribe(({ academicYearId }) => {
      this.academicYearId = academicYearId
    })
    this.setDefaultValues()

    this.independentAssessmentService?.clickUnPublishAction.pipe(take(1)).subscribe(() => {
      this.canUseExternalGroup = cloneDeep(this.independentAssessmentService.canUseExternalGroup)

      this.setDefaultValues()
    })

    if (this.independentAssessmentService.latestTab < 2) {
      this.independentAssessmentService.latestTab = this.isScheduleRequest ? 1 : 2
    }

    if (this.isExamSchedule && !this.publishedAuthorDetails?._id) {
      this.getAssessmentAuthors()
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    const { groups, publishedAuthorDetails } = changes

    if (publishedAuthorDetails?.currentValue) {
      this.selectedAssessmentAuthors = [publishedAuthorDetails?.currentValue]
    }

    if (groups?.currentValue?.length) {
      if (!this.canChangeAssignStudent) {
        this.groups = groups.currentValue?.filter(({ isCustomGroup = false }) => !isCustomGroup)
      }
    }
  }

  private getAssessmentAuthors() {
    const payload = {
      examCourseId: this.courseGroupId
    }

    this.examScheduleService.getAllAssessmentAuthors({ payload, type: 'schedule' }).subscribe(
      ({ data }) => {
        this.assessmentAuthors = data
        this.allAssessmentAuthors = [...data]

        if (this.examScheduleService.selectedAssessmentAuthors?.length) {
          this.selectedAssessmentAuthors = this.examScheduleService.selectedAssessmentAuthors
        }
      },
      (err) => {
        this.errorHandler.errorLog(err)
      }
    )
  }

  compareAssessmentAuthorFn(
    assessmentAuthor: IEvaluatorsFullName,
    selectedAssessmentAuthor: IEvaluatorsFullName
  ) {
    return selectedAssessmentAuthor?._id === assessmentAuthor?._id
  }

  get isFromAssessmentCreation() {
    return this.route.routeConfig?.path === 'independent/:assessmentId'
  }

  private async fetchExternalGroup(): Promise<void> {
    if (this.independentAssessmentService.canUseExternalGroup === undefined) {
      await this.getCanUseExternalGroup()
    }
    this.canUseExternalGroup = cloneDeep(this.independentAssessmentService.canUseExternalGroup)
  }

  private async setDefaultValues() {
    await this.fetchExternalGroup()
    await this.getAssignedGroup()

    if (this.examScheduleService.bufferCount) {
      this.bufferCount = this.examScheduleService.bufferCount
    }

    if (!this.canUseExternalGroup) {
      this.getStudentGroups({})
    } else {
      this.setInstitutionDetails()
    }
  }

  onClickUploadSis() {
    this.independentAssessmentService
      .getSisStudent({
        courseId: this.courseGroupId,
        type: this.isExamSchedule ? 'schedule' : 'independent'
      })
      .subscribe(
        ({ message }) => {
          this.toastrService.success(message)
          this.getAssignedGroup()
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  private addCustomGroupToGroups(): void {
    const customGroup = this.assignedGroups.groups?.find((group) => group?.isCustomGroup)

    if (customGroup && !this.unscheduledGroups?.some((group) => group?._id === customGroup._id)) {
      if (!this.unscheduledGroups?.length) {
        this.unscheduledGroups = []
      }
      this.unscheduledGroups.push(customGroup)
      this.selectedUnscheduledGroups.push(customGroup)
      this.selectedUnscheduledGroupsClone = cloneDeep(this.selectedUnscheduledGroups)
    }
  }

  private setInstitutionDetails() {
    this.getInstitutionCalenders({})
  }

  onSelectInstitutionCalender({ institute }: { institute: IInstitutionCalendar }) {
    this.independentAssessmentService.latestTab = this.isScheduleRequest ? 1 : 2
    this.selectedInstitutionCalender = institute
    this.assignedGroups.institutionCalendar = cloneDeep(this.selectedInstitutionCalender)
    this.independentAssessmentService.assignedGroups.institutionCalendar = cloneDeep(
      this.selectedInstitutionCalender
    )
    this.getStudentGroups({})
  }

  getAssignedGroup(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.independentAssessmentService
        .getAssignStudents({
          courseGroupId: this.courseGroupId,
          isScheduleRequest: this.isScheduleRequest,
          type: this.isExamSchedule ? 'schedule' : 'independent'
        })
        .subscribe(
          ({ data }) => {
            if (data?.maleStudents < 0) {
              Object.assign(data, { maleStudents: 0 })
            }
            if (data?.femaleStudents < 0) {
              Object.assign(data, { femaleStudents: 0 })
            }
            this.assignedGroups = data

            this.independentAssessmentService.assignedGroups = cloneDeep(this.assignedGroups)
            this.getImportStudentsCount()
            resolve()
            if (this.unscheduledGroups?.length) {
              this.selectedUnscheduledGroups = this.unscheduledGroups.filter((group) =>
                this.assignedGroups?.groups.some((assignedGroup) => assignedGroup._id === group._id)
              )
              this.selectedUnscheduledGroupsClone = cloneDeep(this.selectedUnscheduledGroups)
            }
            const { maleStudents, femaleStudents } = this.assignedGroups

            this.malePlanedCount = maleStudents
            this.femalePlanedCount = femaleStudents

            if (this.independentAssessmentService?.sessionDetails) {
              Object.assign(this.independentAssessmentService?.sessionDetails, {
                maleStudents,
                femaleStudents
              })
            }

            if (this.independentAssessmentService?.plannedStudents) {
              Object.assign(this.independentAssessmentService?.plannedStudents, {
                maleStudents,
                femaleStudents
              })
            }

            this.updateStudentList()
          },
          (err) => {
            this.errorHandler.errorLog(err)
            reject(err)
          }
        )
    })
  }

  private getImportStudentsCount() {
    this.importedStudentsCount =
      this.assignedGroups?.importedStudentsCount?.male +
      this.assignedGroups?.importedStudentsCount?.female

    this.importedMaleStudentsCount = this.assignedGroups?.importedStudentsCount?.male
    this.importedFemaleStudentsCount = this.assignedGroups?.importedStudentsCount?.female
  }

  private getCanUseExternalGroup(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.profileSettingsService.getCanUseExternalGroup().subscribe(
        ({ data }) => {
          this.canUseExternalGroup = this.independentAssessmentService.canUseExternalGroup =
            data?.canUseExternalGroup
          resolve()
        },
        (err) => {
          this.errorHandler.errorLog(err)
          reject(err)
        }
      )
    })
  }

  onClickRefreshInstitutionCalender() {
    this.getInstitutionCalenders({ isRefreshCalender: true })
  }

  private getInstitutionCalenders({ isRefreshCalender }: { isRefreshCalender?: boolean }) {
    this.independentAssessmentService
      .getInstitutionCalenders({
        academicYearId: this.academicYearId,
        isRefreshCalender,
        type: this.isExamSchedule ? 'schedule' : 'independent'
      })
      .subscribe(
        ({ data }) => {
          this.institutionCalenders = this.independentAssessmentService.institutionCalenders = data

          if (this.assignedGroups?.institutionCalendar) {
            this.selectedInstitutionCalender = this.institutionCalenders.find(
              (calender) => calender._id === this.assignedGroups?.institutionCalendar?._id
            )
            this.getStudentGroups({})
          }

          if (isRefreshCalender && this.selectedUnscheduledGroups?.length) {
            this.selectedUnscheduledGroups = this.assignedGroups.groups = []
            this.selectedUnscheduledGroupsClone = []
            this.onClickAssignGroups({})
          }
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  onClickRefreshStudentGroups({ type }: { type?: string }) {
    if (type === 'group') {
      this.selectedUnscheduledGroups = this.assignedGroups.groups = []
      this.onClickAssignGroups({})
    }

    this.getStudentGroups({ type })
  }

  private getStudentGroups({ type }: { type?: string }) {
    this.isLoading = true
    const payload = {
      ...(this.canUseExternalGroup && {
        institutionCalendarId: this.selectedInstitutionCalender?._id
      }),
      examCourseId: this.courseGroupId,
      isScheduleRequest: this.isScheduleRequest
    }

    if (type === 'delivery') {
      Object.assign(payload, {
        isRefreshGroupsList: true
      })
    }

    this.independentAssessmentService
      .getStudentGroups({ payload, type: this.isExamSchedule ? 'schedule' : 'independent' })
      .subscribe(
        ({ data }) => {
          if (this.canUseExternalGroup) {
            this.deliveryTypeWithGroups = data?.unscheduledGroups
            this.plannedStudentsCount = data?.plannedStudents
          }
          this.institutionGroup = this.independentAssessmentService.institutionGroup = data

          if (!this.canUseExternalGroup) {
            this.unscheduledGroups = this.institutionGroup?.unscheduledGroups
          } else {
            const filteredGroups = this.assignedGroups?.groups?.filter(
              ({ isCustomGroup }) => !isCustomGroup
            )

            if (filteredGroups?.length) {
              this.selectedDeliveryType = this.deliveryTypeWithGroups.find(
                (deliveryTypeWithGroup) =>
                  deliveryTypeWithGroup.deliveryType === filteredGroups[0]?.deliveryType?.name
              )

              this.unscheduledGroups = this.deliveryTypeWithGroups.find(
                (deliveryTypeWithGroup) =>
                  deliveryTypeWithGroup.deliveryType === this.selectedDeliveryType?.deliveryType
              )?.groups
            }
          }

          if (!this.unscheduledGroups?.length && this.canChangeAssignStudent) {
            this.assignPlannedStudentWithoutGroup({ isInit: true })
          }

          if (this.assignedGroups?.groups?.length && this.unscheduledGroups?.length) {
            this.selectedUnscheduledGroups = this.unscheduledGroups.filter((group) =>
              this.assignedGroups?.groups.some((assignedGroup) => assignedGroup._id === group._id)
            )
            this.selectedUnscheduledGroupsClone = cloneDeep(this.selectedUnscheduledGroups)
          }

          this.updateStudentList()
        },
        (err) => {
          this.deliveryTypeWithGroups = []
          this.selectedUnscheduledGroups = []
          this.selectedUnscheduledGroupsClone = []
          this.groups = []
          this.unscheduledGroups = []

          this.selectedDeliveryType = undefined

          if (err?.error?.code === 704 && this.canChangeAssignStudent) {
            return this.setCustomGroup()
          }

          this.errorHandler.errorLog(err)
        }
      )
      .add(() => (this.isLoading = false))
  }

  private setCustomGroup() {
    const payload = {
      institutionCalendar: this.selectedInstitutionCalender,
      isCustomGroup: true,
      maleCount: this.assignedGroups?.maleStudents,
      femaleCount: this.assignedGroups?.femaleStudents
    }

    this.independentAssessmentService
      .assignStudentGroups({
        courseGroupId: this.courseGroupId,
        payload,
        isScheduleRequest: this.isScheduleRequest,
        type: this.isExamSchedule ? 'schedule' : 'independent'
      })
      .subscribe({
        next: () => {
          this.updateStudentList()
          this.getAssignedGroup()
        },
        error: (err) => {
          this.updateStudentList()
          if (err?.error?.code === 702) {
            return this.openConfirmationPopup({ message: err?.error?.message })
          }

          this.errorHandler.errorLog(err)
        }
      })
  }

  get plannedStudents(): { male: number; female: number } {
    return (
      this.selectedUnscheduledGroups?.reduce(
        (acc, group) => {
          acc.male += group?.count?.male || 0
          acc.female += group?.count?.female || 0

          return acc
        },
        { male: 0, female: 0 }
      ) || { male: 0, female: 0 }
    )
  }

  private showInActiveStudentsPopUp({ students }: { students: Partial<IUploadStudents[]> }) {
    return this.dialog.open(TcAssignErrorPopupComponent, {
      panelClass: 'dialog-fix',
      maxWidth: '40vw',
      maxHeight: '90vh',
      direction: this.settingsService.getOptions().dir,
      disableClose: true,
      data: {
        type: 'inactiveError',
        message: this.translateService.instant('exams.uploadStudentError.inactiveStudentMessage'),
        students
      }
    })
  }

  onClickAssignGroups({ canRescheduleExam }: { canRescheduleExam?: boolean }) {
    this.independentAssessmentService.latestTab = this.isScheduleRequest ? 1 : 2

    if (
      JSON.stringify(this.selectedUnscheduledGroups) ===
      JSON.stringify(this.selectedUnscheduledGroupsClone)
    ) {
      return
    }
    this.uploadStudentListComponent.updateStudentsList()
    let maleCount = this.studentsCount?.male
    let femaleCount = this.studentsCount?.female

    const removedGroups = this.selectedUnscheduledGroupsClone?.filter(
      (cloneGroup) => !this.selectedUnscheduledGroups?.some((group) => group._id === cloneGroup._id)
    )

    const addedGroups = this.selectedUnscheduledGroups?.filter(
      (group) =>
        !this.selectedUnscheduledGroupsClone?.some((cloneGroup) => cloneGroup._id === group._id)
    )

    removedGroups?.forEach((group) => {
      const importedGroup = this.independentAssessmentService.importedGroups?.find(
        (importedGroup) => importedGroup._id === group._id
      )

      if (importedGroup) {
        group.count.male = importedGroup.maleCount || 0
        group.count.female = importedGroup.femaleCount || 0
      }

      maleCount -= group.count?.male || 0
      femaleCount -= group.count?.female || 0
    })

    addedGroups?.forEach((group) => {
      const duplicateStudents = this.examScheduleService.studentList?.filter((student) =>
        group.students?.some((groupStudent) => groupStudent.academicNo === student.academicNo)
      )
      let maleDuplicates = 0
      let femaleDuplicates = 0

      duplicateStudents.forEach((student) => {
        if (student.gender?.toLowerCase()?.startsWith('m')) {
          maleDuplicates++
        } else if (student.gender?.toLowerCase()?.startsWith('f')) {
          femaleDuplicates++
        }
      })

      maleCount += group.count?.male || 0
      femaleCount += group.count?.female || 0

      if (duplicateStudents?.length) {
        maleCount -= maleDuplicates
        femaleCount -= femaleDuplicates
      }
    })

    if (maleCount < 0) {
      maleCount = 0
    }

    if (femaleCount < 0) {
      femaleCount = 0
    }

    if (!this.independentAssessmentService.selectedExamConfig) {
      this.independentAssessmentService.selectedExamConfig = {} as IExamConfigPayload
    }

    this.independentAssessmentService.selectedExamConfig.isGroupAssigned = true

    const payload = {
      studentGroupId: this.institutionGroup?.studentGroupId,
      ...(this.canUseExternalGroup && { institutionCalendar: this.selectedInstitutionCalender }),
      isCustomGroup: false,
      maleCount,
      femaleCount,
      groups: this.selectedUnscheduledGroups,
      ...(canRescheduleExam && { canRescheduleExam })
    }

    this.independentAssessmentService
      .assignStudentGroups({
        courseGroupId: this.courseGroupId,
        payload,
        isScheduleRequest: this.isScheduleRequest,
        type: this.isExamSchedule ? 'schedule' : 'independent'
      })
      .subscribe(
        ({ message, data: { inactiveStudents } }) => {
          this.toastrService.success(message)

          if (inactiveStudents?.length) {
            this.showInActiveStudentsPopUp({ students: inactiveStudents })
              .afterClosed()
              .subscribe((res) => {
                if (!res) {
                  return this.setAssignGroups()
                }

                const academicNos = inactiveStudents.map(({ academicNo }) => academicNo)
                this.removeInActiveStudents({ academicNos })
              })
          }

          this.setAssignGroups()
          this.independentAssessmentService.testCenterDetails = undefined
          this.updateStudentList()
        },
        (err) => {
          if (err?.error?.code === 702) {
            this.independentAssessmentService.canShowTestCenters = false
            return this.openConfirmationPopup({ message: err?.error?.message })
          }

          this.errorHandler.errorLog(err)
          this.selectedUnscheduledGroups = []
          this.selectedUnscheduledGroupsClone = []
        }
      )
  }

  private setAssignGroups() {
    this.independentAssessmentService.sessionDetails = undefined
    this.selectedUnscheduledGroupsClone = this.independentAssessmentService.assignedGroups.groups =
      cloneDeep(this.selectedUnscheduledGroups)

    this.getAssignedGroup()
  }

  private removeInActiveStudents({ academicNos }: { academicNos: string[] }) {
    this.independentAssessmentService
      .removeStudent({
        courseGroupId: this.courseGroupId,
        academicNos,
        type: this.isExamSchedule ? 'schedule' : 'independent'
      })
      .subscribe(
        ({ message }) => {
          this.globalService.showSuccess(message)
          this.setAssignGroups()
        },
        (err) => this.errorHandlerService.errorLog(err)
      )
  }

  private openConfirmationPopup({ message }: { message: string }) {
    this.dialog
      .open(ConfirmPopupComponent, {
        data: { isError: true, message, title: 'learningOutcomeReports.warning' },
        panelClass: ['digi-padding-none', 'dialog-fix'],
        disableClose: true,
        direction: this.settingsService.getOptions().dir
      })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          const groups = this.unscheduledGroups.filter((group) =>
            this.independentAssessmentService.assignedGroups.groups.some(
              (assignedGroup) => assignedGroup._id === group._id
            )
          )
          this.selectedUnscheduledGroups = cloneDeep(groups)
          this.selectedUnscheduledGroupsClone = cloneDeep(this.selectedUnscheduledGroups)

          return
        }

        this.onClickAssignGroups({ canRescheduleExam: true })
      })
  }

  compareFn(selectedGroup: IStudentGroup, group: IStudentGroup): boolean {
    return selectedGroup?._id === group?._id
  }

  get uploadStudentData() {
    return {
      isIndependentAssessment: true,
      examCourseId: this.courseGroupId,
      course: this.hierarchy?.course,
      ...(!this.canChangeAssignStudent && { isPublished: true }),
      canUseExternalGroup: this.canUseExternalGroup,
      ...(this.isExamSchedule && { isExamSchedule: true })
    }
  }

  onClickEditStudents({ isCustom, gender }: { isCustom?: boolean; gender?: 'M' | 'F' }) {
    this.dialog
      .open(UploadStudentListComponent, {
        width: 'auto',
        data: {
          isIndependentAssessment: true,
          isExamSchedule: this.isExamSchedule,
          examCourseId: this.courseGroupId,
          course: this.hierarchy?.course,
          ...(isCustom && { withoutGroup: !this.isPublished }),
          ...(gender && { gender }),
          ...(!this.canChangeAssignStudent && { isPublished: true }),
          canUseExternalGroup: this.canUseExternalGroup
        },
        panelClass: 'dialog-fix',
        direction: this.settingsService.getOptions().dir,
        disableClose: true
      })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          return
        }

        if (isCustom) {
          this.setStudentCount({ count: res })
          this.independentAssessmentService.testCenterDetails = undefined
          this.independentAssessmentService.sessionDetails = undefined
          this.independentAssessmentService.assignedGroups = cloneDeep(this.assignedGroups)
          return
        }

        this.getAssignedGroup()
      })
  }

  onClickGoToStudentGrouping() {
    const data = {
      ...(!this.isScheduleRequest && { isIAA: true }),
      ...(this.isScheduleRequest && { isScheduleRequest: true, examCourseId: this.courseGroupId }),
      academicYearId: this.academicYearId,
      assessmentId: this.independentAssessmentService.assessmentId,
      courseCode: this.hierarchy?.course?.code,
      programCode: this.hierarchy?.program?.code,
      curriculumCode: this.hierarchy?.curriculum?.code,
      termCode: this.hierarchy?.term?.code,
      yearCode: this.hierarchy?.year?.code,
      levelCode: this.hierarchy?.level?.code,
      ...(this.hierarchy?.rotationGroup?.code && {
        rotationalGroup: this.hierarchy?.rotationGroup?.code
      }),
      ...(this.selectedInstitutionCalender && {
        institutionCalendarId: this.selectedInstitutionCalender?._id
      }),
      courseId: this.courseId
    }

    this.routeService.transitionToStudentGrouping({ data })
  }

  onClickDownloadTemplate() {
    const academicNo = this.translateService.instant('exams.uploadPermittedStudents.academicNo')
    const studentColumn = [academicNo]
    const templateToExcel: string[][] = [studentColumn, []]
    this.utilService.exportTemplateAsExcel({
      templateToExcel,
      fileName: this.hierarchyCode || this.hierarchy?.course?.name,
      sheetName: this.hierarchy?.course?.code
    })
  }

  validateAssignStudents() {
    if (this.isExamSchedule) {
      this.validateExamScheduleStudents()

      return
    }

    if (this.independentAssessmentService.raisedByScheduler) {
      this.assignPlannedStudentWithoutGroup({})
      this.independentAssessmentService.assessmentValidation.isStudentUploaded = true

      return
    }

    if (
      this.unscheduledGroups?.length &&
      !this.selectedUnscheduledGroups?.length &&
      !this.independentAssessmentService.assignedGroups?.groups?.some(
        (group) => group.isCustomGroup
      ) &&
      !this.isStudentUploaded
    ) {
      this.toastrService.warning(
        this.translateService.instant('independentAssessmentAuthoring.selectStudentGroup')
      )

      return
    }

    if (this.unscheduledGroups?.length && this.selectedUnscheduledGroups?.length) {
      const studentCount =
        this.assignedGroups?.maleStudents + this.assignedGroups?.femaleStudents ||
        this.assignedGroups?.importedStudentsCount?.male +
          this.assignedGroups?.importedStudentsCount?.female

      if (!studentCount) {
        this.toastrService.warning(
          this.translateService.instant('independentAssessmentAuthoring.noStudentCount')
        )

        return
      }

      this.independentAssessmentService.assessmentValidation.isStudentUploaded = true

      return this.changeAssignStudent.emit(true)
    }

    const studentCount =
      this.assignedGroups?.maleStudents + this.assignedGroups?.femaleStudents ||
      this.assignedGroups?.importedStudentsCount?.male +
        this.assignedGroups?.importedStudentsCount?.female

    if (!studentCount) {
      this.toastrService.warning(
        this.translateService.instant('independentAssessmentAuthoring.noStudentCount')
      )

      return
    }

    if (!this.importedStudentsCount) {
      this.toastrService.warning(
        this.translateService.instant('independentAssessmentAuthoring.selectStudentGroup')
      )

      return
    }

    this.independentAssessmentService.assessmentValidation.isStudentUploaded = true

    this.assignPlannedStudentWithoutGroup({})
  }

  onChangeStudentCount() {
    this.updateStudentCount.next()
  }

  private assignPlannedStudentWithoutGroup({ isInit }: { isInit?: boolean }) {
    if (
      JSON.stringify(this.assignedGroups) ===
        JSON.stringify(this.independentAssessmentService.assignedGroups) &&
      !isInit
    ) {
      this.changeAssignStudent.emit(true)

      return
    }

    if (this.independentAssessmentService.raisedByScheduler) {
      this.changeAssignStudent.emit(true)

      return
    }

    if (!isInit) {
      this.independentAssessmentService.assignedGroups = cloneDeep(this.assignedGroups)
      this.independentAssessmentService.testCenterDetails = undefined
    }

    if (!this.selectedInstitutionCalender && !this.isExamSchedule) {
      this.changeAssignStudent.emit(true)

      return
    }

    this.assignStudentGroups({ isInit })
  }

  private assignStudentGroups({ isInit }: { isInit?: boolean }) {
    if (!this.canChangeAssignStudent) {
      return
    }

    const payload = {
      institutionCalendar: this.selectedInstitutionCalender,
      isCustomGroup: true,
      maleCount: this.assignedGroups?.maleStudents,
      femaleCount: this.assignedGroups?.femaleStudents
    }
    this.independentAssessmentService
      .assignStudentGroups({
        courseGroupId: this.courseGroupId,
        payload,
        isScheduleRequest: this.isScheduleRequest,
        type: this.isExamSchedule ? 'schedule' : 'independent'
      })
      .subscribe(
        () => {
          if (!isInit) {
            this.changeAssignStudent.emit(true)
          }
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  onClickUploadStudents() {
    this.csvService.importedFileName = 'exams.studentGrouping.addStudent'

    this.dialog
      .open(ImportCsvDialogComponent, {
        minWidth: '430px',
        data: {
          dialogTitle: 'exams.uploadPermittedStudents.selectStudentPermitted',
          dialogHint: this.csvService.importedFileName,
          type: this.isExamSchedule ? 'scheduleAssessment' : 'independentAssessment',
          examCourseId: this.courseGroupId,
          isStudentGroup: true,
          canAllowToUploadWithoutGroup: true
        },
        panelClass: 'dialog-fix',
        direction: this.settingsService.getOptions().dir,
        disableClose: true
      })
      .afterClosed()
      .subscribe((result) => {
        if (!result) {
          return
        }

        let isBulkUpload = false
        let students = []

        if (Array.isArray(result)) {
          isBulkUpload = true

          students = result[0]?.students
        } else {
          students = [result]
        }

        if (!students?.length) {
          this.toastrService.error(this.translateService.instant('common.noStudentsFound'))

          return
        }

        let isValidFile = true
        if (isBulkUpload && this.canValidateFileName) {
          isValidFile = this.checkIsValidateFileName({
            fileName: result[0]?.name,
            course: this.hierarchy?.course
          })
        }

        if (isValidFile) {
          this.importStudents({
            students,
            isBulkUpload,
            ...(!isBulkUpload && { canAddInactiveStudents: true })
          })
        }
      })
  }

  private openFileMismatchPopup({ course }: { course: INameWithCode }) {
    this.dialog.open(PlannedCountExistComponent, {
      data: {
        errorType: 'filenameMismatch',
        course
      },
      panelClass: 'dialog-fix',
      direction: this.settingsService.getOptions().dir
    })
  }

  private checkIsValidateFileName({
    fileName,
    course
  }: {
    fileName?: string
    course: INameWithCode
  }) {
    const fileNameToValidate = this.hierarchyCode || this.hierarchy?.course?.name
    if (fileName?.replace(' ', '') !== fileNameToValidate?.replace(' ', '')) {
      this.openFileMismatchPopup({ course })
      return false
    }

    return true
  }

  private importStudents({
    students,
    canAddInactiveStudents,
    canSkipInvalidStudents,
    studentsCount,
    isBulkUpload,
    canSkipCheckInactiveStudents
  }: {
    students: IStudentGroup[]
    canSkipInvalidStudents?: boolean
    canAddInactiveStudents?: boolean
    studentsCount?: number
    isBulkUpload?: boolean
    canSkipCheckInactiveStudents?: boolean
  }) {
    const academicNumbers = students?.map(({ academicNo }) => ({ academicNo }))
    let studentsPayload

    if (isBulkUpload) {
      studentsPayload = academicNumbers
    } else {
      const [student] = students
      studentsPayload = [
        {
          academicNo: student?.academicNo,
          group: {
            _id: student?.group?._id,
            isActive: student?.group?.isActive,
            isDenial: student?.group?.isDenial
          }
        }
      ]
    }

    const payload = {
      students: studentsPayload,
      ...(canSkipCheckInactiveStudents && { canSkipCheckInactiveStudents }),
      ...(canAddInactiveStudents !== undefined && { canAddInactiveStudents }),
      ...(canSkipInvalidStudents && { canSkipInvalidStudents }),
      ...(isBulkUpload && { isBulkUpload: true })
    }

    this.independentAssessmentService
      .importStudents({
        courseGroupId: this.courseGroupId,
        payload,
        type: this.isExamSchedule ? 'schedule' : 'independent'
      })
      .subscribe(
        ({ data: { count, maleStudents, femaleStudents, students }, message }) => {
          if (students?.some((student) => !student?.isActive)) {
            this.showInActiveStudentsPopUp({
              students: students as unknown as Partial<IUploadStudents[]>
            })
              .afterClosed()
              .subscribe((res) => {
                if (!res && !isBulkUpload) {
                  return
                }

                if (!res) {
                  studentsPayload = studentsPayload.filter((payload) => {
                    const student = students.find((s) => s.academicNo === payload.academicNo)

                    return !(student && !student.isActive)
                  })
                }

                this.importStudents({
                  students: studentsPayload as unknown as IStudentGroup[],
                  canAddInactiveStudents: res,
                  canSkipInvalidStudents,
                  studentsCount,
                  isBulkUpload
                })
              })

            return
          }

          this.toastrService.success(message)
          if (!this.independentAssessmentService.selectedExamConfig) {
            this.independentAssessmentService.selectedExamConfig = {} as IExamConfigPayload
          }

          this.independentAssessmentService.selectedExamConfig.isStudentAssigned = true
          this.setStudentCount({ count, maleStudents, femaleStudents })
          this.independentAssessmentService.sessionDetails = undefined
          this.updateStudentList()

          if (!this.assignedGroups?.groups?.some((group) => group.isCustomGroup)) {
            this.isStudentUploaded = true
          }
        },
        (err) => {
          if (err?.error?.code === 701) {
            return this.uploadErrorPopup({
              students,
              errorList: err?.error?.data,
              currentUploadCount: students?.length
            })
          }
          this.errorHandler.errorLog(err)
        }
      )
  }

  setStudentCount({
    count,
    maleStudents,
    femaleStudents
  }: {
    count: IIndependentStudentCount
    maleStudents?: number
    femaleStudents?: number
  }) {
    if (!this.assignedGroups.importedStudentsCount) {
      this.assignedGroups.importedStudentsCount = { male: 0, female: 0 }
    }

    this.assignedGroups.importedStudentsCount.male = count?.male
    this.assignedGroups.importedStudentsCount.female = count?.female
    this.assignedGroups.maleStudents = maleStudents || 0
    this.assignedGroups.femaleStudents = femaleStudents || 0
    this.plannedStudents.male = count?.male
    this.plannedStudents.female = count?.female
    this.importedStudentsCount =
      this.assignedGroups.importedStudentsCount.male +
      this.assignedGroups.importedStudentsCount.female
    this.independentAssessmentService.assignedGroups.importedStudentsCount =
      this.assignedGroups.importedStudentsCount
  }

  onSelectDeliveryType({ deliveryType }: { deliveryType: IStudentGroup }) {
    this.selectedDeliveryType = deliveryType
    this.unscheduledGroups = this.deliveryTypeWithGroups.find(
      (deliveryTypeWithGroup) => deliveryTypeWithGroup.deliveryType === deliveryType.deliveryType
    )?.groups
    this.selectedUnscheduledGroups = []

    if (!this.selectedDeliveryType?.groups?.length) {
      this.setCustomGroup()
    }

    this.onClickAssignGroups({})
  }

  getStudentCount({ students }: { students: IStudentGroup[] }): {
    maleCount: number
    femaleCount: number
  } {
    const maleCount = students?.filter((student) =>
      student?.gender?.toLowerCase().startsWith('m')
    )?.length
    const femaleCount = students?.filter((student) =>
      student?.gender?.toLowerCase().startsWith('f')
    )?.length

    return { maleCount, femaleCount }
  }

  get isPublished() {
    return (
      this.independentAssessmentService.isPublished ||
      this.independentAssessmentService.raisedByScheduler
    )
  }

  uploadErrorPopup({
    students,
    errorList,
    currentUploadCount,
    isExamSchedule
  }: {
    students: IStudentGroup[]
    errorList: IUploadError
    currentUploadCount: number
    isExamSchedule?: boolean
  }) {
    const canSkipInvalidStudents = errorList?.students?.some(({ isInvalid = false }) => isInvalid)
    const canAddInactiveStudents = errorList?.students?.some(({ isInactive = false }) => isInactive)
    let studentsCount = 0

    if (canSkipInvalidStudents) {
      studentsCount = errorList?.students?.filter((student) => student?.isInvalid)?.length
    }

    this.dialog
      .open(UploadStudentErrorPopupComponent, {
        data: {
          isIndependentAssessment: true,
          errorType: 'studentData',
          course: { name: this.hierarchy?.course?.name },
          errorList,
          currentUploadCount,
          canSkipInvalidStudents,
          canAddInactiveStudents
        },
        panelClass: 'dialog-fix',
        direction: this.settingsService.getOptions().dir,
        disableClose: true
      })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          if (canAddInactiveStudents && canSkipInvalidStudents) {
            studentsCount += errorList?.students?.filter((student) => student?.isInactive)?.length

            this.importStudents({
              students,
              canAddInactiveStudents: false,
              canSkipInvalidStudents,
              studentsCount
            })
          }

          return
        }

        this.importStudents({
          students,
          canAddInactiveStudents: res?.canAddInactiveStudents,
          canSkipInvalidStudents,
          studentsCount
        })
      })
  }

  get canShowUploadStudents() {
    if (this.isLoading) {
      return false
    }

    if (this.canUseExternalGroup) {
      if (this.institutionGroup && !this.institutionCalenders?.length) {
        return true
      }

      if (this.selectedInstitutionCalender && !this.deliveryTypeWithGroups?.length) {
        return true
      }

      if (this.selectedDeliveryType && !this.unscheduledGroups?.length) {
        return true
      }

      return false
    }

    return (
      this.institutionGroup &&
      !this.unscheduledGroups?.length &&
      (this.selectedInstitutionCalender || !this.canUseExternalGroup)
    )
  }

  get isRaisedByScheduler() {
    return this.independentAssessmentService.raisedByScheduler
  }

  get canChangeAssignStudent() {
    if (this.isFromAssessmentCreation && this.isScheduleRequest) {
      return false
    }

    if (this.isPublished) {
      return false
    }

    return true
  }

  updateStudentList() {
    if (this.uploadStudentListComponent) {
      this.uploadStudentListComponent.searchText = ''
      this.uploadStudentListComponent.selectedGroupId = ''
      this.uploadStudentListComponent.getIndependentStudents({})
    }
  }

  get studentsCount() {
    if (this.isPublished) {
      return { male: this.malePlanedCount, female: this.femalePlanedCount }
    }

    return {
      male: this.assignedGroups?.maleStudents || 0,
      female: this.assignedGroups?.femaleStudents || 0
    }
  }

  validateExamScheduleStudents() {
    if (
      !this.studentsCount?.male &&
      !this.studentsCount?.female &&
      !this.bufferCount?.male &&
      !this.bufferCount?.female
    ) {
      this.toastrService.error(
        this.translateService.instant('exams.studentGrouping.noStudentsFound')
      )

      return
    }

    if (!this.selectedAssessmentAuthors?.length) {
      this.toastrService.error(
        this.translateService.instant('exams.studentGrouping.noAssessmentAuthorFound')
      )

      return
    }

    if (!this.uploadStudentListComponent.independentStudents?.length) {
      this.updateStudentsDetails()

      return
    }

    this.examScheduleService.verifyExamScheduleStudents({ courseId: this.courseGroupId }).subscribe(
      () => this.updateStudentsDetails(),
      (err) => {
        if (err?.error?.code === 701) {
          return this.uploadErrorPopup({
            students: this.assignedGroups?.groups,
            errorList: err?.error?.data,
            currentUploadCount: this.assignedGroups?.groups?.length,
            isExamSchedule: true
          })
        }

        this.errorHandler.errorLog(err)
      }
    )
  }

  private updateStudentsDetails() {
    this.examScheduleService.selectedAssessmentAuthors = this.selectedAssessmentAuthors
    this.examScheduleService.bufferCount = this.bufferCount
    this.independentAssessmentService.assignedGroups = cloneDeep(this.assignedGroups)
    this.uploadStudentListComponent.updateStudentsList()
    this.changeAssignStudent.emit(true)
  }

  get canShowBufferNote() {
    return this.utilService?.clientConfig?.helpDeskClientCode !== 'upm'
  }

  getFullName(name: IName) {
    return this.globalService.getFullName(name)
  }

  get selectedAssessmentAuthorsName() {
    if (!this.selectedAssessmentAuthors?.length) {
      return ''
    }

    return this.selectedAssessmentAuthors
      ?.map((assessmentAuthor) => {
        return this.getFullName(assessmentAuthor?.name)
      })
      .join(', ')
  }

  onSearchAssessmentAuthor(event: Event) {
    const searchValue = (event.target as HTMLInputElement).value
    if (!searchValue) {
      this.assessmentAuthors = this.allAssessmentAuthors
      return
    }

    this.assessmentAuthors = this.allAssessmentAuthors.filter((assessmentAuthor) =>
      this.getFullName(assessmentAuthor?.name)?.toLowerCase().includes(searchValue.toLowerCase())
    )
  }
}
