import { animate, state, style, transition, trigger } from '@angular/animations'
import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'

import { isEqual } from 'lodash'
import { ToastrService } from 'ngx-toastr'

import { IPagination } from '@appcore/app/models'
import { ITestCenter } from '@appcore/app/models/assessment/create-assessment/create-assessment.interface'
import { cloneDeep, ErrorHandlerService } from '@appcore/app/services'

import { IndependentAssessmentService } from '../../independent-assessment.service'
import { AddTestCenterPopupComponent } from '../add-test-center-popup'
import { GlobalService, IName } from '@appcore/app/services'
import { TEST_CENTER_OWNED_DEVICE } from '@appcore/app/core/constants'

@Component({
  selector: 'digi-test-center-table',
  templateUrl: './test-center-table.component.html',
  styleUrls: ['./test-center-table.component.css'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)'))
    ])
  ]
})
export class TestCenterTableComponent implements OnChanges {
  @Input()
  dataSource: ITestCenter[] = []

  @Input()
  courseGroupId: string

  @Input()
  assessmentId: string

  @Output()
  canRefreshTestCenter: EventEmitter<boolean> = new EventEmitter()

  @Input()
  hideActionButton = false

  displayedColumns: string[] = ['testCenterName', 'roomNo', 'gender', 'capacity', 'action']
  studentColumns: string[] = ['sNo', 'studentName', 'academicId', 'gender', 'studentGroup']
  pagination: IPagination = { currentPage: 1, perPage: 5, totalPages: 1 }

  constructor(
    private independentAssessmentService: IndependentAssessmentService,
    private dialog: MatDialog,
    private toastr: ToastrService,
    private errorHandler: ErrorHandlerService,
    private globalService: GlobalService
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    const { hideActionButton } = changes

    if (hideActionButton?.currentValue !== undefined) {
      if (hideActionButton?.currentValue) {
        const index = this.displayedColumns.indexOf('action')
        if (index !== -1) {
          this.displayedColumns.splice(index, 1)
        }
      } else {
        if (this.displayedColumns.indexOf('action') === -1) {
          this.displayedColumns.push('action')
        }
      }
    }
  }

  private getStudentsByTestCenter({ testCenter }: { testCenter: ITestCenter }) {
    this.independentAssessmentService
      .getStudentsByTestCenter({
        courseGroupId: this.courseGroupId,
        testCenterId: testCenter._id
      })
      .subscribe(({ data }) => {
        data.students.forEach((student) => {
          const matchedGroup = data.groups.find((group) => isEqual(group._id, student.groupId))

          student.groupCode = matchedGroup?.code || '-'
        })
        Object.assign(testCenter, {
          count: data.count,
          students: data.students,
          studentsClone: cloneDeep(data.students)
        })

        this.setPagination({ testCenterId: testCenter._id })

        if (this.independentAssessmentService.examDeviceType === TEST_CENTER_OWNED_DEVICE) {
          this.studentColumns.splice(this.studentColumns.length, 0, 'seat')
        } else {
          const seatIndex = this.studentColumns.indexOf('seat')
          if (seatIndex !== -1) {
            this.studentColumns.splice(seatIndex, 1)
          }
        }
      })
  }

  private deleteTestCenter({ testCenterId }: { testCenterId: string }) {
    this.independentAssessmentService
      .deleteTestCenter({ courseGroupId: this.courseGroupId, testCenterId })
      .subscribe(
        ({ message }) => {
          if (this.independentAssessmentService?.assignExamInvigilatorValues) {
            this.independentAssessmentService.assignExamInvigilatorValues = undefined
          }

          this.toastr.success(message)
          this.canRefreshTestCenter.emit(true)
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  onClickStudent({ testCenter }: { testCenter: ITestCenter }) {
    testCenter.isExpanded = !testCenter.isExpanded
    const hasStudent = testCenter?.count?.male + testCenter?.count?.female > 0

    if (!testCenter?.students?.length && testCenter.isExpanded && hasStudent) {
      this.getStudentsByTestCenter({ testCenter })
    }
  }

  private setPagination({ testCenterId }: { testCenterId: string }) {
    const testCenter = this.dataSource.find((testCenterEntry) =>
      isEqual(testCenterEntry._id, testCenterId)
    )
    if (testCenter) {
      if (!testCenter?.pagination) {
        testCenter.pagination = { currentPage: 1, perPage: 5, totalPages: 1 }
      }

      testCenter.students = cloneDeep(testCenter.studentsClone)

      testCenter.pagination.totalCount = testCenter.students.length
      testCenter.pagination.totalPages = Math.ceil(
        testCenter.pagination.totalCount / testCenter.pagination.perPage
      )

      testCenter.students = testCenter.students.slice(
        (testCenter.pagination.currentPage - 1) * testCenter.pagination.perPage,
        testCenter.pagination.currentPage * testCenter.pagination.perPage
      )
    }
  }

  onChangePagination({
    type,
    event,
    testCenterId
  }: {
    type: string
    event: number
    testCenterId: string
  }) {
    const testCenter = this.dataSource.find((testCenterEntry) =>
      isEqual(testCenterEntry._id, testCenterId)
    )

    testCenter.pagination[type] = event

    if (type === 'perPage') {
      testCenter.pagination.currentPage = 1
    }

    this.setPagination({ testCenterId })
  }

  onClickDeleteTestCenter({ testCenterId }: { testCenterId: string }) {
    this.deleteTestCenter({ testCenterId })
  }

  onClickEditTestCenter({ testCenter }: { testCenter: ITestCenter }) {
    this.dialog
      .open(AddTestCenterPopupComponent, {
        panelClass: 'dialog-fix',
        width: 'auto',
        data: {
          courseGroupId: this.courseGroupId,
          testCenter,
          assessmentId: this.assessmentId,
          isEditClicked: true
        },
        disableClose: true
      })
      .afterClosed()
      .subscribe((res) => {
        if (!res) {
          return
        }

        if (this.independentAssessmentService?.assignExamInvigilatorValues) {
          this.independentAssessmentService.assignExamInvigilatorValues = undefined
        }

        this.canRefreshTestCenter.emit(true)
      })
  }

  get isPublished() {
    return (
      this.independentAssessmentService.isPublished ||
      this.independentAssessmentService.raisedByScheduler
    )
  }

  getFormattedName({ name }: { name: IName }) {
    return this.globalService.getFullName(name as IName)
  }
}
