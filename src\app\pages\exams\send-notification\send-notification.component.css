.digi-container {
  padding: 10px 25px;
  box-sizing: border-box;
  overflow: auto;
}

h4.digi-title {
  font-weight: 500;
  font-family: inherit;
  font-size: 20px;
  margin: 0;
}

.digi-name {
  font-family: <PERSON><PERSON>;
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
  line-height: 24px;
  letter-spacing: 0.0125em;
  color: #000000;
}

.mat-mdc-card {
  padding: 0;
}

.digi-boder-bottom {
  border-bottom: 2px solid #0064c8;
  font-variant: small-caps;
}
.digi-blue {
  font-variant: small-caps;
  color: #0064c8;
}
.digi-bg-header {
  padding: 10px;
}

.digi-message-container {
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
  background-color: #d2ecff;
}

.digi-message-title,
.digi-message-content {
  width: 100%;
  box-sizing: border-box;
}

.digi-label-text,
.digi-title-text {
  font-family: inherit;
  font-weight: 500;
  font-size: 18px;
  text-transform: capitalize;
}

.digi-title-text {
  font-size: 20px;
}

:host ::ng-deep digi-button .message {
  background-color: white !important;
}

.digi-message-title {
  background-color: #d2ecff;
  padding: 15px 15px 0;
}

.digi-message-type-container {
  width: 100%;
}

.digi-message-type {
  padding: 5px;
  margin-right: 5px;
  text-transform: uppercase;
  cursor: pointer;
  user-select: none;
  color: #0064c8;
}

.digi-message-type.active {
  border-bottom: 2px solid #0064c8;
  color: rgba(0, 0, 0, 0.87);
}

/* TODO(mdc-migration): The following rule targets internal classes of card that may no longer apply for the MDC version. */
:host ::ng-deep .mat-card-header-text {
  margin: 0px;
}

.digi-checkbox-container mat-checkbox {
  padding: 0 5px;
}

/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
:host::ng-deep
  .digi-checkbox-container
  .mat-checkbox-indeterminate.mat-accent
  .mat-checkbox-background,
:host::ng-deep .digi-checkbox-container .mat-checkbox-checked.mat-accent .mat-checkbox-background {
  background: #6e6e6f;
}

/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
:host::ng-deep .digi-checkbox-container .mat-checkbox-frame {
  border-color: #6e6e6f;
}

:host::ng-deep .mat-mdc-tab-header {
  background: transparent !important;
}

:host::ng-deep .mat-ink-bar {
  display: none;
}

/* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
:host::ng-deep .mat-mdc-tab {
  color: #0064c8 !important;
  opacity: 1;
  border-radius: 15px 15px 0 0;
  user-select: none;
}

.digi-sub-tab-container {
  background-color: white;
}

/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
:host::ng-deep .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background,
:host::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
  background-color: #6c6c6d;
}

/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
:host::ng-deep .mat-checkbox-frame {
  border-color: #6c6c6d;
}

/* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
:host::ng-deep .mat-mdc-tab.mat-mdc-tab-disabled {
  min-width: 50px;
  color: rgba(0, 0, 0, 0.87) !important;
}

:host ::ng-deep .mat-mdc-tab-group {
  min-height: auto;
}

:host::ng-deep
  .digi-tab-group
  > mat-tab-header
  .mat-mdc-tab
  .mdc-tab-indicator__content--underline {
  border-color: #0064c8 !important;
  --mdc-tab-indicator-active-indicator-height: 3px;
}

:host::ng-deep .digi-tab-group > mat-tab-header .mat-mdc-tab .mdc-tab__text-label {
  color: #000000a6 !important;
  font-size: 1.1rem;
}

:host::ng-deep .digi-tab-group > mat-tab-header .mat-mdc-tab.mat-mdc-tab-disabled {
  background-color: transparent !important;
  opacity: 1;
}

:host::ng-deep
  .digi-tab-group
  > mat-tab-header
  .mat-mdc-tab.mat-mdc-tab-disabled
  .mdc-tab__text-label {
  color: #000000de !important;
}

/* :host::ng-deep .digi-tab-group > mat-tab-header .mat-mdc-tab.mdc-tab--active {
  
} */

:host::ng-deep .digi-tab-group > mat-tab-header .mat-mdc-tab.mdc-tab--active .mdc-tab__text-label {
  color: #0064c8 !important;
}

:host::ng-deep .digi-tab-group > mat-tab-header .mat-mdc-tab {
  flex: none !important;
}

.digi-custom-tabs-button.active {
  font-weight: 500;
}

:host::ng-deep .digi-date-picker .mat-mdc-form-field-subscript-wrapper {
  display: none;
}

.digi-schedule-header {
  gap: 10px;
}

.digi-schedule-message-panel {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  padding: 10px;
  box-sizing: border-box;
  margin: 0 !important;
}

.digi-schedule-message-panel .digi-message-container {
  width: 100%;
  max-width: calc(100vw - 130px);
  height: 100%;
  max-height: calc(100vh - 130px);
  overflow: auto;
  background-color: white;
}

.digi-date-picker-container {
  width: 165px;
}

.digi-date-picker {
  width: 100%;
}

:host ::ng-deep .digi-date-picker-container .mdc-text-field--filled:not(.mdc-text-field--disabled) {
  --mdc-filled-text-field-container-color: white;
}

.digi-exam-type-dropdown {
  width: 350px !important;
}

.digi-message-header {
  position: sticky;
  top: 0;
  z-index: 1001;
  padding: 15px 15px 10px;
  border-bottom: 0.1rem solid #0064c8;
  margin-bottom: 5px;
  box-sizing: border-box;
}

.digi-message-content {
  padding: 0 10px 10px;
  box-sizing: border-box;
}

.digi-web-icon {
  color: #0064c8;
}

.digi-message-header-title {
  font-size: 18px;
  font-weight: 500;
}

.digi-message-header-subtitle {
  font-size: 14px;
  color: #666666;
}

:host::ng-deep .digi-tab-content {
  padding: 10px 0 0 !important;
  border: 1px solid #808080;
}
