<div fxLayout="column" fxLayoutGap="10px" class="digi-container">
  <section fxLayout="row wrap" fxLayoutAlign="space-between center" class="digi-schedule-header">
    <div class="digi-cursor" (click)="onClickBack()">
      <mat-icon class="mat-icon-rtl-mirror">keyboard_backspace</mat-icon>
    </div>
    <h4 class="digi-title">
      {{ 'exams.sendNotification.publishScheduleSendNotification' | translate }}
    </h4>
    <span fxFlex></span>
    <digi-button class="digi-m-0" button="secondary" (click)="showEmailTemplate = true">
      {{ 'exams.sendNotification.viewEmailTemplate' | translate | titlecase }}
    </digi-button>
    <digi-button
      class="digi-m-0"
      button="primary"
      [disabled]="!canPublish()"
      (click)="handlePublish()"
    >
      {{ 'common.publish' | translate | titlecase }}
    </digi-button>
  </section>

  <div class="digi-schedule-message-panel" [ngClass]="{ 'digi-none': !showEmailTemplate }">
    <div class="digi-message-container">
      <div fxLayout="row" fxLayoutAlign="none center" class="digi-message-header digi-bg-white">
        <div fxLayout="row" fxLayoutAlign="none center" fxLayoutGap="10px">
          <span class="digi-web-icon-container">
            <mat-icon class="digi-web-icon">web</mat-icon>
          </span>
          <div fxLayout="column">
            <span class="digi-message-header-title">
              {{ 'exams.sendNotification.allMemberTemplates' | translate | titlecase }}
            </span>
            <span class="digi-message-header-subtitle">
              ({{ 'exams.sendNotification.mailMembers' | translate }})
            </span>
          </div>
        </div>
        <span fxFlex></span>
        <mat-icon class="digi-cursor" (click)="showEmailTemplate = false">close</mat-icon>
      </div>
      <div *ngIf="checkOwnDeviceStatus" class="digi-message-content">
        <mat-tab-group
          class="digi-tab-group"
          [(selectedIndex)]="selectedTab"
          (selectedTabChange)="handleTabChange($event)"
        >
          <mat-tab [label]="'common.students' | translate">
            <div *ngIf="collapseTabs" class="digi-sub-tab-container">
              <digi-notification-tabs
                [isLocationWithoutProctor]="isLocationWithoutProctor"
                [proctoringType]="proctoringType"
                [userType]="userType"
                [isUsingOwnedDevicesForExam]="checkOwnDeviceStatus.isUsingOwnedDevicesForExam"
                [examSchedule]="examSchedule"
                (publishNotification)="publishNotificationContent($event)"
              ></digi-notification-tabs>
            </div>
          </mat-tab>
          <mat-tab
            [disabled]="isLocationWithoutProctor"
            [label]="'exams.sendNotification.proctors' | translate"
          >
            <div *ngIf="collapseTabs" class="digi-sub-tab-container">
              <digi-notification-tabs
                [isLocationWithoutProctor]="isLocationWithoutProctor"
                [proctoringType]="proctoringType"
                [userType]="userType"
                [isUsingOwnedDevicesForExam]="checkOwnDeviceStatus.isUsingOwnedDevicesForExam"
                [examSchedule]="examSchedule"
                (publishNotification)="publishNotificationContent($event)"
              ></digi-notification-tabs>
            </div>
          </mat-tab>
          <mat-tab [label]="'assessment.extractBulkOldQuestions.assessmentAuthors' | translate">
            <div *ngIf="collapseTabs" class="digi-sub-tab-container">
              <digi-notification-tabs
                [isLocationWithoutProctor]="isLocationWithoutProctor"
                [proctoringType]="proctoringType"
                [userType]="userType"
                [isUsingOwnedDevicesForExam]="checkOwnDeviceStatus.isUsingOwnedDevicesForExam"
                [examSchedule]="examSchedule"
                (publishNotification)="publishNotificationContent($event)"
              ></digi-notification-tabs>
            </div>
          </mat-tab>
        </mat-tab-group>
      </div>
    </div>
  </div>

  <div fxLayout="row wrap" fxLayoutAlign="space-between flex-end" fxLayoutGap="10px">
    <div fxFlex>
      <digi-custom-tabs>
        <button
          class="digi-custom-tabs-button"
          [class.active]="tagSelected === 'All'"
          (click)="handleTagSelected({ tagSelected: 'All' })"
        >
          {{ 'common.all' | translate | titlecase }}
        </button>
        <button
          class="digi-custom-tabs-button"
          [class.active]="tagSelected === 'notificationNotSent'"
          (click)="handleTagSelected({ tagSelected: 'notificationNotSent' })"
        >
          {{ 'exams.sendNotification.notificationsNotSent' | translate | titlecase }}
          {{ notificationsNotSentCount | arabicNumber }}
        </button>
      </digi-custom-tabs>
    </div>

    <!-- exam type dropdown -->
    <digi-dropdown
      class="digi-exam-type-dropdown"
      format="scheduleDropdown"
      searchPlaceholder="exams.examsManagement.searchTextExamType"
      panelWidth="500px"
      panelClass="digi-schedule-ongoing-attempt-type-dropdown digi-full-select-panel"
      [compareFn]="compareFn"
      [label]="'exams.examsManagement.pastExam.pastExamActivity.selectExam' | translate | titlecase"
      [items]="onGoingExams"
      [nestedKeysLabel]="['examType', 'name']"
      [nestedKeysLabelTwo]="['examType', 'code']"
      [selectedItem]="selectedExam"
      [showTooltip]="true"
      [isMultiple]="true"
      (valueChange)="selectedExam = $event.value"
      (dropdownClose)="onDropdownClose($event)"
    ></digi-dropdown>

    <!-- date picker -->
    <div class="digi-date-picker-container">
      <mat-form-field class="digi-date-picker">
        <mat-label>
          {{ 'common.chooseDate' | translate }}
        </mat-label>
        <input
          matInput
          [matDatepicker]="notificationDatePicker"
          [min]="minDate"
          autocomplete="off"
          readonly
          [(ngModel)]="notificationDate"
          (dateChange)="onDateChange({})"
        />
        <mat-datepicker-toggle matSuffix [for]="notificationDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #notificationDatePicker></mat-datepicker>
      </mat-form-field>
    </div>

    <div>
      <digi-search
        #searchInput
        [text]="'common.searchByCourse' | translate | titlecase"
        [filter]="true"
        [minChar]="0"
        (search)="onSearch($event)"
      ></digi-search>
    </div>

    <!-- To do later -->
    <!--
    <div fxLayout="row wrap" fxLayoutGap="10px">
      <mat-checkbox disabled [(ngModel)]="notificationModes.email">
        {{ 'exams.sendNotification.sendEmail' | translate }}</mat-checkbox
      >
      <mat-checkbox *ngIf="canEnableSms" disabled [(ngModel)]="notificationModes.sms">
        {{ 'exams.sendNotification.sendSMS' | translate }}
      </mat-checkbox>
    </div> -->
  </div>

  <digi-notification-table
    #notificationTable
    [filterBy]="tagSelected"
    [formattedDate]="formattedDate"
    [exams]="selectedExam"
    (publishNotification)="publishNotificationGroup($event)"
    (updateNotificationsNotSentCount)="notificationsNotSentCount = $event"
  ></digi-notification-table>
</div>
