import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core'
import { MatDialog } from '@angular/material/dialog'
import { MatTabChangeEvent } from '@angular/material/tabs'
import {
  MAT_MOMENT_DATE_ADAPTER_OPTIONS,
  MomentDateAdapter
} from '@angular/material-moment-adapter'
import { Subscription } from 'rxjs'
import { environment } from 'src/environments/environment'

import { DateTimeService } from '@appcore/services/date-time.service'
import {
  ApiService,
  ErrorHandlerService,
  GlobalService,
  LoadingScreenService,
  RouteService,
  SettingsService
} from '@appcore/services/index'

import { SearchComponent } from '@appcore/app/core/components/digi-search/digi-search.component'
import { NotificationPublishPopupComponent } from '@appcore/app/core/components/exams/notification-publish-popup/notification-publish-popup.component'
import { NotificationTableComponent } from '@appcore/app/core/components/exams/notification-table/notification-table.component'
import { LocaleNumberPipe } from '@appcore/app/core/pipes/locale-number.pipe'

import { IBYOD } from '@appcore/app/models/response/response.interface'
import {
  IExam,
  IExamCategory,
  IScheduleNotificationTemplate,
  ISendNotification,
  ISendNotificationMode,
  ISendNotificationRequest
} from '@appcore/models/index'

import { ExamScheduleService } from '../exam-schedule.service'

const MY_FORMATS = {
  parse: {
    dateInput: ''
  },
  display: {
    dateInput: 'DD/MM/YYYY',
    monthYearLabel: 'MMM/YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM/YYYY'
  }
}

@Component({
  selector: 'digi-send-notification',
  templateUrl: './send-notification.component.html',
  styleUrls: ['./send-notification.component.css'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
  ]
})
export class SendNotificationComponent implements OnInit, OnDestroy {
  @ViewChild('notificationTable')
  notificationTable: NotificationTableComponent

  @ViewChild('searchInput')
  searchInput: SearchComponent

  collapseTabs = true
  userType = 'student'
  tagSelected = 'All'
  selectedTab = 1
  notificationNotSend = { current: 0, total: 0 }
  dataSource: ISendNotification[] = []
  subscription: Subscription
  examId: string
  notificationNotSent: ISendNotification[] = []
  notificationFor: string
  isLocationWithoutProctor: boolean
  proctoringType: string
  canEnableSms = environment.canEnableSms
  notificationModes: ISendNotificationMode = {
    sms: this.canEnableSms,
    app: false,
    email: true
  }
  publishNotificationPayload: ISendNotificationRequest = {
    mode: this.notificationModes,
    content: {
      student: {
        sms: '',
        email: '',
        app: ''
      },
      proctor: {
        sms: '',
        email: '',
        app: ''
      },
      assessmentAuthor: {
        sms: '',
        email: '',
        app: ''
      }
    }
  }
  checkOwnDeviceStatus: IBYOD
  examSchedule: IScheduleNotificationTemplate
  examCategory: Partial<IExamCategory>
  minDate = new Date()
  notificationDate: Date
  formattedDate: string
  showEmailTemplate = false
  onGoingExams: IExam[] = []
  selectedExam: Partial<IExam>[] = []
  notificationsNotSentCount = 0

  constructor(
    private globalService: GlobalService,
    private api: ApiService,
    private activeRoute: ActivatedRoute,
    public routeService: RouteService,
    private errorHandler: ErrorHandlerService,
    private dialog: MatDialog,
    private localNumberPipe: LocaleNumberPipe,
    private settingsService: SettingsService,
    private examScheduleService: ExamScheduleService,
    private dateTimeService: DateTimeService,
    private loadingScreenService: LoadingScreenService
  ) {}

  ngOnInit() {
    this.globalService.setHeader({
      headerName: 'common.examManagement',
      activeHeader: 'common.scheduler'
    })

    this.onDateChange({ isInitial: true })

    this.isContentForBYOD()

    this.subscription = this.activeRoute.params.subscribe((params) => (this.examId = params.id))

    this.activeRoute.queryParams.subscribe(
      (params: {
        notification_for: string
        isLocationWithoutProctor: boolean
        proctorType: string
        examCategoryName: string
        examCategoryCode: string
        locationAndProctoringType: string
      }) => {
        this.notificationFor = params?.notification_for
        this.isLocationWithoutProctor = params?.isLocationWithoutProctor
        this.proctoringType = params?.proctorType
        this.examCategory = {
          name: params?.examCategoryName,
          code: params?.examCategoryCode,
          locationAndProctoringType: params?.locationAndProctoringType
        }
        Object.assign(this.publishNotificationPayload, {
          locationAndProctoringType: params?.locationAndProctoringType
        })
      }
    )

    this.refreshData({ isInitial: true })
    this.getScheduleTemplate()
    this.fetchExams()
  }

  ngOnDestroy() {
    this.subscription.unsubscribe()
  }

  private fetchExams() {
    this.loadingScreenService.show()
    this.examScheduleService
      .getScheduleExams({})
      .subscribe({
        next: ({ data }) => {
          this.onGoingExams = this.formatExamsByCategory({ exams: data?.ongoingExams })
        },
        error: (err) => this.errorHandler.errorLog(err)
      })
      .add(() => {
        setTimeout(() => this.loadingScreenService.hide(), 1000)
      })
  }

  compareFn(value1: IExam, value2: IExam) {
    return value1 && value2 && value1?._id === value2?._id
  }

  private refreshData({ isInitial = false }: { isInitial?: boolean }) {
    void this.handleTagSelected({ tagSelected: 'All', isInitial, isRefresh: true })
  }

  handleTabChange(event: MatTabChangeEvent) {
    const {
      index,
      tab: { textLabel }
    } = event

    this.selectedTab = index
    const selectLabel = {
      1: 'student',
      2: 'proctor',
      3: 'assessmentAuthor'
    }
    this.userType = selectLabel[index]
  }

  handleTagSelected({
    tagSelected,
    isInitial = false,
    isRefresh = false
  }: {
    tagSelected: string
    isInitial?: boolean
    isRefresh?: boolean
  }) {
    if (this.tagSelected === tagSelected && !isRefresh) {
      return
    }

    if (!isInitial) {
      this.resetSearch()
    }

    if (!isRefresh) {
      this.tagSelected = tagSelected
    }
    this.notificationTable?.refreshTable()
  }

  publishNotificationGroup(data: string[]) {
    this.publishNotificationPayload.groups = data
  }

  publishNotificationContent(data: ISendNotificationRequest) {
    this.publishNotificationPayload.content = data.content
  }

  handlePublish() {
    const { content, ...notificationDetails } = this.publishNotificationPayload
    const payload = {
      content: {
        students: content.student,
        proctors: content.proctor,
        assessmentAuthors: <AUTHORS>
      },
      ...notificationDetails
    }

    this.dialog
      .open(NotificationPublishPopupComponent, {
        width: '100%',
        maxWidth: '450px',
        data: {
          payload
        },
        panelClass: 'dialog-fix',
        direction: this.settingsService.getOptions().dir
      })
      .afterClosed()
      .subscribe((res) => {
        if (res) {
          this.refreshData({ isInitial: false })
        }
      })
  }

  canPublish() {
    return (
      this.publishNotificationPayload.groups?.length &&
      (this.notificationModes.email || this.notificationModes.sms || this.notificationModes.app)
    )
  }

  private isContentForBYOD() {
    this.api
      .getOwnedDevicesStatusForExamSettings()
      .subscribe(({ data }) => (this.checkOwnDeviceStatus = data))
  }

  private getScheduleTemplate() {
    this.resetTemplate()

    this.examScheduleService.getScheduleTemplate().subscribe(
      ({ data }) => (this.examSchedule = data),
      (err) => {
        this.errorHandler.errorLog(err)
      }
    )
  }

  private resetTemplate() {
    this.globalService.notificationContent = {
      student: {
        email: '',
        sms: '',
        app: ''
      },
      proctor: {
        email: '',
        sms: '',
        app: ''
      },
      assessmentAuthor: {
        email: '',
        sms: '',
        app: ''
      }
    }
  }

  onDateChange({ isInitial = false }: { isInitial?: boolean }) {
    this.notificationDate = this.notificationDate || new Date()
    this.formattedDate = this.dateTimeService.formatDate(
      this.notificationDate.toISOString(),
      'YYYY-MM-DD'
    )

    if (!isInitial) {
      this.notificationTable?.refreshTable()
      this.resetSearch()
    }
  }

  onSearch(searchText: string) {
    this.notificationTable?.filterBySearch(searchText)
  }

  resetSearch() {
    this.searchInput.reset()
    this.notificationTable?.filterBySearch('')
  }

  private formatExamsByCategory({ exams }: { exams: IExam[] }) {
    return exams.reduce((prevExams, exam) => {
      exam?.examType?.examCategories?.forEach((category) => {
        const formattedExam = {
          ...exam,
          examType: {
            ...exam.examType,
            examCategories: [category],
            showCategory: true
          }
        }

        prevExams.push(formattedExam)
      })
      return prevExams
    }, [])
  }

  onDropdownClose(isOpen: boolean) {
    if (!isOpen) {
      this.notificationTable?.refreshTable()
    }
  }

  onClickBack() {
    this.routeService.transitionToOngoing({
      id: this.examId,
      categoryCode: this.examCategory?.code
    })
  }
}
