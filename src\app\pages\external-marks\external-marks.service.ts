import { HttpClient, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'

import { Observable, of } from 'rxjs'
import { first, map } from 'rxjs/operators'
import { environment } from 'src/environments/environment'

import {
  IExternalExamClo,
  IExternalExamCourseSubjectTopic,
  IExternalExamItem,
  IExternalExamMappingTaxonomy,
  IExternalExamStudentUpload,
  IExternalInvigilator,
  IStudentList,
  IUpdateMarkStudents
} from '@appcore/app/core/components/external-marks/external-mark-interface'
import { ROUTES, SCHEDULED_STUDENT_GROUPING } from '@appcore/app/core/constants'
import {
  IAssessmentItem,
  IAuthor,
  ICourseHierarchy,
  ICreateExternalExam,
  IExamTypeLocation,
  IExtenalExamFaculty,
  IExtenalExamFacultyResponse,
  IExternalExam,
  IExternalExamAssessmentInfo,
  IExternalExamAssessmentInfoMapping,
  IExternalExamAuthor,
  IExternalExamSession,
  IExternalExamTimeFormat,
  IExternalMarkStudentGroupPayload,
  IItemType,
  IPagination,
  IResponse,
  IScheduledGroups,
  ISelectedFilter,
  IStudentData,
  IStudentGroup
} from '@appcore/app/models'
import { IPaginationResponse } from '@appcore/app/models/response/response.interface'
import { IUploadStudents } from '@appcore/app/models/exam-readiness/exam-readiness-on-going.interface'

const BASE_URL = `${environment.endPoint}/${ROUTES.MODULE_EXAM}`

@Injectable()
export class ExternalMarkService {
  staffNames: IExtenalExamFaculty[] = []
  canAddQuestionWiseMarks = true

  constructor(private httpClient: HttpClient) {}

  getListOfCourseForExternalExams({
    examCategory,
    examTypeId,
    attemptTypeId,
    academicYearStart,
    academicYearEnd,
    pagination,
    programFilter,
    courseGroupId
  }: {
    examTypeId: string
    examCategory: string
    attemptTypeId: string
    academicYearStart: string
    academicYearEnd: string
    pagination?: IPagination
    programFilter?: ISelectedFilter
    courseGroupId?: string
  }): Observable<IResponse<IPaginationResponse<IExternalExam[]>>> {
    const queryParams = this.getQueryParamsFromObject({
      examCategory,
      examTypeId,
      attemptTypeId,
      academicYearStart,
      academicYearEnd,
      ...pagination,
      ...programFilter,
      courseGroupId
    })

    return this.httpClient
      .get<
        IResponse<IPaginationResponse<IExternalExam[]>>
      >(`${BASE_URL}/external-exams`, queryParams)
      .pipe(first())
  }

  getCoursesForExternalExams({
    examTypeId,
    examCategory,
    pagination,
    programFilter
  }: {
    examTypeId: string
    examCategory: string
    pagination?: IPagination
    programFilter?: ISelectedFilter
  }): Observable<IResponse<IPaginationResponse<ICourseHierarchy[]>>> {
    const queryParams = this.getQueryParamsFromObject({
      examTypeId,
      examCategory,
      ...programFilter,
      ...pagination
    })

    return this.httpClient
      .get<
        IResponse<IPaginationResponse<ICourseHierarchy[]>>
      >(`${BASE_URL}/external-exams/courses`, queryParams)
      .pipe(first())
  }

  createExternalExam({
    payload
  }: {
    payload: ICreateExternalExam
  }): Observable<IResponse<{ courseGroupId: string }>> {
    return this.httpClient
      .post<IResponse<{ courseGroupId: string }>>(`${BASE_URL}/external-exams`, payload)
      .pipe(first())
  }

  createSession({
    courseGroupId
  }: {
    courseGroupId: string
  }): Observable<IResponse<{ sessionId: string; testCenterId: string }>> {
    return this.httpClient
      .post<
        IResponse<{ sessionId: string; testCenterId: string }>
      >(`${BASE_URL}/external-exam-detail/session`, { courseGroupId })
      .pipe(first())
  }

  updateSession({
    courseGroupId,
    sessionId,
    date,
    start,
    end,
    testCenter
  }: {
    courseGroupId: string
    sessionId: string
    date: string
    start: IExternalExamTimeFormat
    end: IExternalExamTimeFormat
    testCenter: { _id: string; name: string; floorName: string; roomNo: string; location: string }
  }): Observable<IResponse> {
    return this.httpClient
      .put<IResponse>(`${BASE_URL}/external-exam-detail/session`, {
        courseGroupId,
        sessionId,
        date,
        start,
        end,
        testCenter
      })
      .pipe(first())
  }

  getSession({
    courseGroupId,
    sessionId
  }: {
    courseGroupId: string
    sessionId?: string
  }): Observable<IResponse<IExternalExamSession>> {
    const queryParams = this.getQueryParamsFromObject({
      courseGroupId,
      ...(sessionId && { sessionId })
    })

    return this.httpClient
      .get<IResponse<IExternalExamSession>>(`${BASE_URL}/external-exam-detail/session`, queryParams)
      .pipe(first())
  }

  removeSession({
    courseGroupId,
    sessionId
  }: {
    courseGroupId: string
    sessionId: string
  }): Observable<IResponse<IExternalExamSession>> {
    const queryParams = this.getQueryParamsFromObject({
      courseGroupId,
      sessionId
    })

    return this.httpClient
      .delete<
        IResponse<IExternalExamSession>
      >(`${BASE_URL}/external-exam-detail/session`, queryParams)
      .pipe(first())
  }

  addExternalExamInvigilator({
    courseGroupId,
    sessionId,
    testCenterId,
    invigilator
  }: {
    courseGroupId: string
    sessionId: string
    testCenterId: string
    invigilator: IExternalInvigilator
  }): Observable<IResponse> {
    return this.httpClient
      .post<IResponse>(`${BASE_URL}/external-exam-detail/test-center-invigilator`, {
        courseGroupId,
        sessionId,
        testCenterId,
        invigilator
      })
      .pipe(first())
  }

  addExternalExamCourseAuthors({
    courseReportId,
    courseGroupId,
    authors
  }: {
    courseReportId: string
    courseGroupId: string
    authors: IAuthor[]
  }): Observable<IResponse> {
    return this.httpClient
      .post<IResponse>(`${BASE_URL}/external-exams/authors`, {
        courseReportId,
        courseGroupId,
        authors
      })
      .pipe(first())
  }

  editExternalExamInvigilator({
    courseGroupId,
    sessionId,
    testCenterId,
    invigilator
  }: {
    courseGroupId: string
    sessionId: string
    testCenterId: string
    invigilator: IExternalInvigilator
  }): Observable<IResponse> {
    return this.httpClient
      .put<IResponse>(`${BASE_URL}/external-exam-detail/test-center-invigilator`, {
        courseGroupId,
        sessionId,
        testCenterId,
        invigilator
      })
      .pipe(first())
  }

  removeExternalExamInvigilator({
    courseGroupId,
    sessionId,
    testCenterId,
    invigilatorId
  }: {
    courseGroupId: string
    sessionId: string
    testCenterId: string
    invigilatorId: string
  }): Observable<IResponse> {
    return this.httpClient
      .request<IResponse>('delete', `${BASE_URL}/external-exam-detail/test-center-invigilator`, {
        body: {
          courseGroupId,
          sessionId,
          testCenterId,
          invigilatorId
        }
      })
      .pipe(first())
  }

  uploadStudentsForExternalExams({
    courseGroupId,
    courseReportId,
    sessionId,
    testCenterId,
    revisionNumber,
    evaluationNumber,
    academicNos,
    fileName,
    type,
    group,
    canAddInactiveStudents,
    canSkipInvalidStudents
  }: {
    courseGroupId: string
    courseReportId: string
    sessionId: string
    testCenterId: string
    revisionNumber: number
    evaluationNumber: number
    academicNos?: string[]
    fileName?: string
    type?: string
    group?: IStudentGroup
    canAddInactiveStudents?: boolean
    canSkipInvalidStudents?: boolean
  }): Observable<IResponse<{ students: IUploadStudents[] }>> {
    const url = `${BASE_URL}/external-exam-detail/${
      type === SCHEDULED_STUDENT_GROUPING ? 'group-' : ''
    }student`
    const students = []
    if (['INDIVIDUAL', 'BULK'].includes(type)) {
      academicNos.forEach((academicNo) => {
        students.push({ academicNo, group })
      })
    }

    return this.httpClient
      .post<IResponse<{ students: IUploadStudents[] }>>(url, {
        courseGroupId,
        courseReportId,
        sessionId,
        testCenterId,
        revisionNumber,
        evaluationNumber,
        ...(![SCHEDULED_STUDENT_GROUPING, 'INDIVIDUAL'].includes(type) && { academicNos }),
        ...(![SCHEDULED_STUDENT_GROUPING, 'BULK', 'INDIVIDUAL'].includes(type) && { fileName }),
        ...(students?.length && { students }),
        ...(canAddInactiveStudents !== undefined && { canAddInactiveStudents }),
        ...(canSkipInvalidStudents && { canSkipInvalidStudents }),
        ...(['BULK', 'SIS'].includes(type) && { type })
      })
      .pipe(first())
  }

  getStudentsList({
    pagination,
    courseGroupId,
    courseReportId,
    sessionId,
    testCenterId,
    searchText = ''
  }: {
    pagination: IPagination
    courseGroupId: string
    courseReportId: string
    sessionId: string
    testCenterId: string
    searchText: string
  }): Observable<IResponse<IPaginationResponse<IStudentList[]>>> {
    const queryParams = this.getQueryParamsFromObject(
      {
        ...pagination,
        courseGroupId,
        courseReportId,
        sessionId,
        testCenterId,
        search: searchText
      },
      false
    )

    return this.httpClient
      .get<
        IResponse<IPaginationResponse<IStudentList[]>>
      >(`${BASE_URL}/external-exam-detail/student`, queryParams)
      .pipe(first())
  }

  removeStudentsFromList({
    courseGroupId,
    courseReportId,
    assessmentId,
    sessionId,
    testCenterId,
    academicNos,
    overAll
  }: {
    courseGroupId: string
    courseReportId: string
    assessmentId: string
    sessionId: string
    testCenterId: string
    academicNos: string[]
    overAll: boolean
  }): Observable<IResponse> {
    return this.httpClient
      .request<IResponse>('delete', `${BASE_URL}/external-exam-detail/student`, {
        body: {
          courseGroupId,
          courseReportId,
          assessmentId,
          sessionId,
          testCenterId,
          academicNos,
          overAll
        }
      })
      .pipe(first())
  }

  getStudentDetails({
    academicNo,
    courseGroupId,
    canUploadWithoutGroup
  }: {
    academicNo: string
    courseGroupId: string
    canUploadWithoutGroup?: boolean
  }): Observable<IResponse<IStudentData>> {
    const queryParams = this.getQueryParamsFromObject({
      academicNo,
      courseGroupId,
      ...(canUploadWithoutGroup && { canThrowStudentNotFound: 'false' })
    })
    return this.httpClient
      .get<IResponse<IStudentData>>(`${BASE_URL}/external-exam-detail/student-details`, queryParams)
      .pipe(first())
  }

  getStudentListForAddMarks({
    courseReportId,
    courseGroupId,
    sessionId,
    testCenterId,
    evaluationNumber,
    isPaginate,
    pagination,
    searchText = ''
  }: {
    courseReportId: string
    courseGroupId: string
    sessionId: string
    testCenterId: string
    evaluationNumber: string
    isPaginate?: boolean
    pagination: IPagination
    searchText: string
  }): Observable<IResponse<IPaginationResponse<IStudentList[]>>> {
    const queryParams = this.getQueryParamsFromObject(
      {
        courseReportId,
        courseGroupId,
        sessionId,
        testCenterId,
        evaluationNumber,
        ...(isPaginate && { isPaginate }),
        ...pagination,
        search: searchText
      },
      false
    )

    return this.httpClient
      .get<
        IResponse<IPaginationResponse<IStudentList[]>>
      >(`${BASE_URL}/external-exam-detail/students-with-and-without-question-wise-marks`, queryParams)
      .pipe(first())
  }

  updateStudentPresentStatus({
    courseGroupId,
    courseReportId,
    sessionId,
    testCenterId,
    assessmentId,
    academicNos,
    isPresent,
    evaluationNumber,
    canReset
  }: {
    courseGroupId: string
    courseReportId: string
    sessionId: string
    testCenterId: string
    assessmentId: string
    academicNos: string[]
    isPresent: boolean
    evaluationNumber: number
    canReset: boolean
  }): Observable<IResponse> {
    return this.httpClient
      .put<IResponse>(`${BASE_URL}/external-exam-detail/student-present-status`, {
        courseGroupId,
        courseReportId,
        sessionId,
        testCenterId,
        assessmentId,
        academicNos,
        isPresent,
        evaluationNumber,
        canReset
      })
      .pipe(first())
  }

  updateStudentTotalMarks({
    courseGroupId,
    courseId,
    sessionId,
    testCenterId,
    evaluationNumber,
    students,
    courseReportId,
    fileName
  }: {
    courseGroupId: string
    courseId: string
    sessionId: string
    testCenterId: string
    evaluationNumber: number
    students: Partial<{ academicNo: string; totalMarks: number; isPresent: boolean }>[]
    courseReportId: string
    fileName: string
  }): Observable<IResponse<IUpdateMarkStudents>> {
    return this.httpClient
      .put<IResponse<IUpdateMarkStudents>>(`${BASE_URL}/external-exam-detail/total-marks`, {
        courseGroupId,
        courseId,
        sessionId,
        testCenterId,
        evaluationNumber,
        students,
        courseReportId,
        ...(fileName && { fileName })
      })
      .pipe(first())
  }

  updateItemMarks({
    assessmentId,
    courseGroupId,
    students,
    revisionNumber,
    evaluationNumber,
    fileName
  }: {
    assessmentId: string
    courseGroupId: string
    revisionNumber: number
    evaluationNumber: number
    students: {
      academicNo: string
      isPresent: boolean
      marks: number
      itemId: string
    }[]
    fileName: string
  }): Observable<IResponse<IUpdateMarkStudents>> {
    return this.httpClient
      .put<IResponse<IUpdateMarkStudents>>(`${BASE_URL}/external-exam-detail/marks`, {
        assessmentId,
        courseGroupId,
        students,
        revisionNumber,
        evaluationNumber,
        ...(fileName && { fileName })
      })
      .pipe(first())
  }

  updateQuestionWiseMark({
    addQuestionWiseMarks,
    courseGroupId,
    courseReportId,
    assessmentId
  }: {
    addQuestionWiseMarks: boolean
    courseGroupId: string
    courseReportId: string
    assessmentId: string
  }): Observable<IResponse> {
    const queryParams = this.getQueryParamsFromObject({
      courseGroupId,
      courseReportId,
      assessmentId
    })
    return this.httpClient
      .patch<IResponse>(
        `${BASE_URL}/external-exam-detail/toggle-question-wise-marks`,
        { addQuestionWiseMarks },
        queryParams
      )
      .pipe(first())
  }

  getOutComeMetricsForExternalMarks({
    courseReportId,
    evaluationNumber,
    addQuestionWiseMarks
  }: {
    courseReportId: string
    evaluationNumber: number
    addQuestionWiseMarks: boolean
  }): Observable<IResponse> {
    const queryParams = this.getQueryParamsFromObject({
      courseReportId,
      evaluationNumber,
      addQuestionWiseMarks
    })

    return this.httpClient
      .get<IResponse>(`${BASE_URL}/external-exam-detail/outcome-metrics`, queryParams)
      .pipe(first())
  }

  getUserManagementFaculties(): Observable<IExtenalExamFaculty[]> {
    const queryParams = this.getQueryParamsFromObject({
      isPaginate: false
    })

    return this.staffNames?.length
      ? of(this.staffNames)
      : this.httpClient
          .get<
            IResponse<IExtenalExamFacultyResponse>
          >(`${BASE_URL}/external-exam-detail/invigilators`, queryParams)
          .pipe(
            map(({ data }) => {
              this.staffNames = data.data

              return this.staffNames
            })
          )
  }

  getFacultyDetails({ id }: { id: string }): Observable<IResponse<IExtenalExamFaculty>> {
    const queryParams = this.getQueryParamsFromObject({ id })

    return this.httpClient
      .get<
        IResponse<IExtenalExamFaculty>
      >(`${BASE_URL}/external-exam-detail/invigilator`, queryParams)
      .pipe(first())
  }

  getAuthors({
    courseHierarchyId,
    roleType,
    pagination,
    isPaginate
  }: {
    courseHierarchyId?: string
    roleType?: string
    pagination: IPagination
    isPaginate?: boolean
  }): Observable<IResponse<IPaginationResponse<IExtenalExamFaculty[]>>> {
    const url = courseHierarchyId ? 'faculties-by-course' : 'faculties'
    const queryParams = this.getQueryParamsFromObject({
      ...(courseHierarchyId && { courseHierarchyId }),
      ...(roleType && { roleType }),
      ...pagination,
      isPaginate
    })

    return this.httpClient
      .get<
        IResponse<IPaginationResponse<IExtenalExamFaculty[]>>
      >(`${BASE_URL}/external-exams/${url}`, queryParams)
      .pipe(first())
  }

  deleteAuthors({
    courseReportId,
    courseGroupId,
    authors
  }: {
    courseReportId: string
    courseGroupId: string
    authors: IExternalExamAuthor[]
  }): Observable<IResponse> {
    return this.httpClient
      .request<IResponse>('delete', `${BASE_URL}/external-exams/authors`, {
        body: { courseReportId, courseGroupId, authors }
      })
      .pipe(first())
  }

  getAssessmentDetails({
    assessmentId,
    courseReportId
  }: {
    assessmentId: string
    courseReportId: string
  }): Observable<IResponse<IExternalExamAssessmentInfo>> {
    const queryParams = this.getQueryParamsFromObject({
      assessmentId,
      courseReportId
    })

    return this.httpClient
      .get<
        IResponse<IExternalExamAssessmentInfo>
      >(`${BASE_URL}/external-exam-detail/assessment`, queryParams)
      .pipe(first())
  }

  updateAssessmentInfo({
    assessmentId,
    courseGroupId,
    totalMarks,
    totalItems,
    mapping,
    subject,
    topic,
    canReset = false
  }: {
    assessmentId: string
    courseGroupId: string
    totalMarks: number
    totalItems?: number
    mapping: IExternalExamAssessmentInfoMapping
    subject: IExternalExamCourseSubjectTopic
    topic: IExternalExamCourseSubjectTopic
    canReset?: boolean
  }): Observable<IResponse> {
    return this.httpClient
      .put<IResponse>(`${BASE_URL}/external-exam-detail/assessment`, {
        assessmentId,
        courseGroupId,
        totalMarks,
        totalItems,
        mapping,
        subject,
        topic,
        canReset
      })
      .pipe(first())
  }

  addToReport({
    courseGroupId,
    courseReportId,
    evaluationNumber,
    assessmentId
  }: {
    courseReportId?: string
    courseGroupId: string
    evaluationNumber: number
    assessmentId?: string
  }) {
    const type = evaluationNumber > 1 ? 'completed-evaluation' : 'add-to-report'

    return this.httpClient
      .put<IResponse>(`${BASE_URL}/external-exam-detail/${type}`, {
        courseGroupId,
        courseReportId,
        assessmentId,
        evaluationNumber
      })
      .pipe(first())
  }

  getTotalMarkSettings({
    courseReportId
  }: {
    courseReportId: string
  }): Observable<IResponse<{ hasOnlyTotalMarks: boolean }>> {
    const queryParams = this.getQueryParamsFromObject({
      courseReportId
    })

    return this.httpClient
      .get<
        IResponse<{ hasOnlyTotalMarks: boolean }>
      >(`${BASE_URL}/external-exam-detail/total-marks-settings`, queryParams)
      .pipe(first())
  }

  getTaxonomyMapping(): Observable<IResponse<IExternalExamMappingTaxonomy[]>> {
    return this.httpClient
      .get<IResponse<IExternalExamMappingTaxonomy[]>>(`${BASE_URL}/external-exam-detail/mapping`)
      .pipe(first())
  }

  getCloMapping({
    courseHierarchyId,
    itemId
  }: {
    courseHierarchyId: string
    itemId?: string
  }): Observable<IResponse<IExternalExamClo>> {
    const queryParams = this.getQueryParamsFromObject({
      courseHierarchyId,
      ...(itemId && { itemId })
    })

    return this.httpClient
      .get<IResponse<IExternalExamClo>>(`${BASE_URL}/external-exam-detail/clo-mapping`, queryParams)
      .pipe(first())
  }

  getCourseSubjects({
    courseHierarchyId,
    includeTopic
  }: {
    courseHierarchyId: string
    includeTopic?: boolean
  }): Observable<IResponse<IExternalExamCourseSubjectTopic[]>> {
    const queryParams = this.getQueryParamsFromObject({
      courseHierarchyId,
      ...(includeTopic && { includeTopic })
    })

    return this.httpClient
      .get<
        IResponse<IExternalExamCourseSubjectTopic[]>
      >(`${BASE_URL}/external-exam-detail/subjects`, queryParams)
      .pipe(first())
  }

  getCourseTopics({
    courseHierarchyId,
    subjectId
  }: {
    courseHierarchyId: string
    subjectId?: string
  }): Observable<IResponse<IExternalExamCourseSubjectTopic[]>> {
    const queryParams = this.getQueryParamsFromObject({
      courseHierarchyId,
      ...(subjectId && { subjectId })
    })

    return this.httpClient
      .get<
        IResponse<IExternalExamCourseSubjectTopic[]>
      >(`${BASE_URL}/external-exam-detail/topics`, queryParams)
      .pipe(first())
  }

  moveStudents({
    courseGroupId,
    sessionId,
    testCenterId,
    academicNos,
    overAll,
    originSessionId,
    originTestCenterId
  }: {
    courseGroupId: string
    sessionId: string
    testCenterId?: string
    academicNos: string[]
    overAll: boolean
    originSessionId?: string
    originTestCenterId?: string
  }): Observable<IResponse> {
    return this.httpClient
      .put<IResponse>(`${BASE_URL}/external-exam-detail/move-student`, {
        courseGroupId,
        sessionId,
        testCenterId,
        academicNos,
        overAll,
        ...(originSessionId && { originSessionId }),
        ...(originTestCenterId && { originTestCenterId })
      })
      .pipe(first())
  }

  getItemsList({
    assessmentId,
    isPaginate,
    pagination
  }: {
    assessmentId: string
    isPaginate: boolean
    pagination?: IPagination
  }): Observable<IResponse<IPaginationResponse<IExternalExamItem[]>>> {
    const queryParams = this.getQueryParamsFromObject({
      assessmentId,
      isPaginate,
      ...pagination
    })

    return this.httpClient
      .get<
        IResponse<IPaginationResponse<IExternalExamItem[]>>
      >(`${BASE_URL}/external-exam-detail/item`, queryParams)
      .pipe(first())
  }

  updateItem({
    assessmentId,
    courseGroupId,
    itemId,
    subject,
    topic,
    itemType,
    canReset,
    type,
    mark
  }: {
    assessmentId: string
    courseGroupId: string
    itemId: string
    subject?: IExternalExamCourseSubjectTopic
    topic: IExternalExamCourseSubjectTopic
    itemType?: IItemType
    canReset?: boolean
    type: 'subject' | 'topic' | 'itemType' | 'mark' | 'delete'
    mark: number
  }): Observable<IResponse> {
    const url = type === 'itemType' ? 'item-type' : type === 'mark' ? 'item-mark' : 'item-hierarchy'

    return this.httpClient
      .put<IResponse>(`${BASE_URL}/external-exam-detail/${url}`, {
        assessmentId,
        courseGroupId,
        itemId,
        ...(subject && type === 'subject' && { subject }),
        ...(topic && type === 'topic' && { topic }),
        ...(itemType && type === 'itemType' && { itemType }),
        ...(itemType && type === 'mark' && { itemType: itemType.code }),
        ...(['itemType', 'mark'].includes(type) && { canReset }),
        ...(mark !== undefined && { mark })
      })
      .pipe(first())
  }

  createNewItem({
    assessmentId,
    courseGroupId,
    count
  }: {
    assessmentId: string
    courseGroupId: string
    count: number
  }): Observable<IResponse> {
    return this.httpClient.post<IResponse>(`${BASE_URL}/external-exam-detail/item`, {
      assessmentId,
      courseGroupId,
      count
    })
  }

  removeItem({
    courseGroupId,
    assessmentId,
    itemId,
    canReset = false
  }: {
    assessmentId: string
    courseGroupId: string
    itemId: string
    canReset: boolean
  }): Observable<IResponse> {
    return this.httpClient
      .request<IResponse>('delete', `${BASE_URL}/external-exam-detail/item`, {
        body: { courseGroupId, assessmentId, itemId, canReset }
      })
      .pipe(first())
  }

  getItemDetails({
    id,
    assessment
  }: {
    id: string
    assessment: string
  }): Observable<IResponse<IAssessmentItem>> {
    const queryParams = this.getQueryParamsFromObject({
      id,
      assessment
    })

    return this.httpClient.get<IResponse<IAssessmentItem>>(
      `${BASE_URL}/external-exam-detail/item-detail`,
      queryParams
    )
  }

  finalizeItem({
    id,
    assessment,
    courseGroupId
  }: {
    id: string
    assessment: string
    courseGroupId: string
  }): Observable<IResponse> {
    const queryParams = this.getQueryParamsFromObject({
      id,
      assessment,
      courseGroupId
    })

    return this.httpClient.patch(
      `${BASE_URL}/external-exam-detail/ready-for-review`,
      null,
      queryParams
    )
  }

  protected getQueryParamsFromObject(params: object, checkIsEmpty = true) {
    let queryParams = new HttpParams()

    Object.keys(params).forEach((key) => {
      if (params[key] === undefined) {
        return
      }
      if (checkIsEmpty && params[key] === '') {
        return
      }

      queryParams = queryParams.append(key, params[key])
    })

    return { params: queryParams }
  }

  getIndividualAssessmentDetails({
    assessmentId
  }: {
    assessmentId: string
  }): Observable<IResponse<IExternalExamAssessmentInfo>> {
    const queryParams = this.getQueryParamsFromObject({ id: assessmentId })

    return this.httpClient
      .get<
        IResponse<IExternalExamAssessmentInfo>
      >(`${BASE_URL}/external-exams/assessment`, queryParams)
      .pipe(first())
  }

  setQuestionLineLimit({
    id,
    itemId,
    questionId,
    itemTypeCode,
    lineLimit
  }: {
    id: string
    itemId: string
    questionId?: string
    itemTypeCode: string
    lineLimit: number
  }): Observable<IResponse> {
    const queryParams = this.getQueryParamsFromObject({
      id,
      itemId,
      questionId
    })
    const body = { noOfAnsweringLines: lineLimit, itemTypeCode }

    return this.httpClient.patch<IResponse>(
      `${BASE_URL}/external-exams/answering-lines`,
      body,
      queryParams
    )
  }

  deleteExternalExam({ courseGroupId }: { courseGroupId: string }): Observable<IResponse> {
    return this.httpClient
      .request<IResponse>('delete', `${BASE_URL}/external-exams`, {
        body: { courseGroupId }
      })
      .pipe(first())
  }

  getExamTypeBasedOnMode(): Observable<IResponse<IExamTypeLocation[]>> {
    return this.httpClient.get(`${BASE_URL}/${ROUTES.PAGE_EXTERNAL_EXAM}/exam-type`).pipe(first())
  }

  getStudentGroups({
    params
  }: {
    params: IExternalMarkStudentGroupPayload
  }): Observable<IResponse<IScheduledGroups>> {
    const queryParams = this.getQueryParamsFromObject(params)

    return this.httpClient
      .get(`${BASE_URL}/${ROUTES.PAGE_EXTERNAL_EXAM}/group`, queryParams)
      .pipe(first())
  }

  getStudentGroupInstitutionCalendar({
    isRefreshCalender,
    academicYearId
  }: {
    isRefreshCalender: boolean
    academicYearId?: string
  }): Observable<IResponse> {
    const url = `${BASE_URL}/${ROUTES.PAGE_EXTERNAL_EXAM}/institution-calendar`
    const queryParams = this.getQueryParamsFromObject({ isRefreshCalender: true, academicYearId }) // need to remove true

    return this.httpClient.get<IResponse>(url, queryParams).pipe(first())
  }
}
