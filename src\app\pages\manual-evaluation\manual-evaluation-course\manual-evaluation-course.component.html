<div class="digi-container digi-p-25">
  <div class="digi-mb-25" fxLayout="row" fxLayoutAlign="none center">
    <div class="digi-p-5 digi-cursor" (click)="onClickBack()">
      <mat-icon style="color: rgba(47, 128, 237, 1)" class="mat-icon-rtl-mirror">
        keyboard_backspace
      </mat-icon>
    </div>
    <div fxLayout="row wrap" class="digi-title">
      <span>
        {{
          (programCode === 'all'
            ? 'reportingAndAnalytics.manualEvaluationCourseDashboard.manualEvaluationCourse.revaluationResults'
            : 'reportingAndAnalytics.manualEvaluationCourseDashboard.manualEvaluationCourse.manualEvaluationResult'
          ) | translate
        }}:
      </span>

      <span
        class="digi-text-clip"
        maxWidth="50vw"
        contentType="template"
        [tooltip]="formatCourseDetailsContent"
      >
        <ng-container *ngTemplateOutlet="formatCourseDetailsContent"></ng-container>
        <ng-template #formatCourseDetailsContent>
          {{ formatCourseDetails }}
        </ng-template>
      </span>
    </div>
  </div>
  <div class="digi-center-container">
    <div class="digi-p-15 digi-bg-white digi-assessment-title-container" fxLayout="column">
      <div fxLayout="row">
        <div class="filter-box digi-mb-10">
          <mat-label class="filter-name">{{ 'common.attemptType' | translate }}</mat-label>

          <mat-select
            #selectRef
            class="filter-select"
            typeaheadDebounceInterval="0"
            disableOptionCentering="true"
            (keydown)="preventKeyboardEvents($event)"
            (keypress)="preventKeyboardEvents($event)"
            (keyup)="preventKeyboardEvents($event)"
            (input)="preventKeyboardEvents($event)"
            (valueChange)="onClickStatus({ type: selectedTab })"
            [(value)]="selectedAttemptType"
          >
            <mat-option
              style="text-transform: uppercase"
              *ngFor="let attemptType of attemptTypes"
              [value]="attemptType?.name"
            >
              {{ attemptType?.name }}
            </mat-option>
          </mat-select>
        </div>
        <div class="filter-box digi-mb-10 digi-ml-10">
          <mat-label class="filter-name">{{ 'common.conductedOn' | translate }}</mat-label>

          <mat-select
            class="filter-select"
            [(value)]="selectedConductedLocations"
            (valueChange)="onClickStatus({ type: selectedTab })"
          >
            <mat-option
              class="digi-text-uppercase"
              *ngFor="let conductedLocation of conductedLocations"
              [value]="conductedLocation"
            >
              {{ conductedLocation?.name | translate }} -
              {{ 'common.digiAssess' | translate }}
            </mat-option>
          </mat-select>
        </div>
      </div>

      <div fxLayout="column">
        <div *ngIf="statusTabs?.length" class="digi-blue digi-text-upper" style="font-size: 12px">
          {{ 'common.resultStatusFilter' | translate }}
        </div>
        <div class="digi-pb-10" fxLayout="row" fxLayoutAlign="space-between center">
          <section fxLayout="row" fxLayoutAlign="none center">
            <digi-tag
              *ngFor="let statusTab of statusTabs; index as i"
              [selected]="selectedTab === statusTab?.code"
              (click)="onClickStatus({ type: statusTab?.code })"
            >
              {{ statusTab?.name | translate }} {{ statusTab?.value | localeNumber }}
            </digi-tag>
          </section>
          <aside>
            <digi-search
              [debounce]="500"
              [filter]="true"
              [text]="
                'reportingAndAnalytics.manualEvaluationCourseDashboard.manualEvaluationCourse.searchModule'
                  | translate
              "
              [width]="'250px'"
              (search)="onSearch({ searchText: $event })"
            ></digi-search>
          </aside>
        </div>
      </div>

      <div class="digi-table digi-p-15">
        <div fxLayout="row" fxLayoutAlign="none center" class="digi-table-header">
          <div class="td td0 digi-pt-10 digi-pb-10">
            {{ 'common.programYearLevelCourse' | translate }}
          </div>
          <div class="td td1 digi-pt-10 digi-pb-10">
            {{
              'reportingAndAnalytics.manualEvaluationCourseDashboard.manualEvaluationCourse.noOfCourses'
                | translate
            }}
          </div>
        </div>

        <div class="digi-table-body">
          <div *ngIf="report?.programTermBasedOnYearAndLevel.length; else noData">
            <div
              *ngFor="let programTermBasedOnYearAndLevel of report?.programTermBasedOnYearAndLevel"
            >
              <div fxLayout="row" class="digi-table-body-content">
                <div
                  class="td td0 digi-mt-5 digi-cursor"
                  fxLayout="row"
                  (click)="onClickExpandProgram({ program: programTermBasedOnYearAndLevel })"
                >
                  <mat-icon
                    class="digi-mr-10 digi-blue digi-cursor digi-icon-size mat-icon-rtl-mirror"
                  >
                    {{
                      programTermBasedOnYearAndLevel?.expandable ? 'arrow_drop_down' : 'arrow_right'
                    }}
                  </mat-icon>
                  <span>{{ programTermBasedOnYearAndLevel?.name }}</span>
                </div>
                <div class="td td1">
                  {{ programTermBasedOnYearAndLevel?.count?.manualEvaluation | localeNumber }}
                </div>
              </div>
              <div *ngIf="programTermBasedOnYearAndLevel?.expandable">
                <div
                  fxLayout="column"
                  fxLayoutAlign="none center"
                  class="digi-table-body-content"
                  *ngFor="let yearLevel of programTermBasedOnYearAndLevel?.yearLevel"
                >
                  <div fxLayout="row">
                    <div
                      class="td td0 digi-cursor"
                      (click)="onClickExpandYear({ yearAndLevel: yearLevel })"
                    >
                      <mat-icon
                        class="digi-cursor digi-icon-size digi-ml-20 digi-subdirectory-icon mat-icon-rtl-mirror"
                      >
                        subdirectory_arrow_right
                      </mat-icon>
                      <mat-icon
                        class="digi-mr-10 digi-blue digi-cursor digi-icon-size mat-icon-rtl-mirror"
                      >
                        {{ yearLevel?.expandable ? 'arrow_drop_down' : 'arrow_right' }}
                      </mat-icon>
                      <span>{{ yearLevel?.name }}</span>
                    </div>
                    <div class="td td1">
                      {{ yearLevel?.count?.manualEvaluation | localeNumber }}
                    </div>
                  </div>
                  <div *ngIf="yearLevel?.expandable">
                    <div
                      class="part5 digi-mt-10 digi-mb-10"
                      fxLayout="row"
                      fxLayoutAlign="none center"
                    >
                      <div id="table" class="digi-table-cont digi-bg-white">
                        <div fxLayout="column">
                          <div class="digi-thead" fxLayout="row">
                            <div
                              class="{{ 'td td' + i }}"
                              *ngFor="let header of headers; index as i"
                            >
                              {{ header | translate }}
                            </div>
                          </div>
                          <div class="digi-tbody" fxLayout="column">
                            <div *ngFor="let course of yearLevel?.courses">
                              <div
                                class="row part1"
                                fxLayout="row"
                                fxLayoutAlign="none center"
                                *ngFor="
                                  let evaluation of getLastEvaluation({
                                    evaluations: course?.evaluations
                                  })
                                "
                              >
                                <div class="td td1">
                                  <div class="digi-blue">
                                    {{ course?.courseHierarchyCode || course?.courseName }}
                                  </div>
                                </div>
                                <div
                                  class="td td2 digi-cursor"
                                  (click)="
                                    onClickCourse({
                                      course: course,
                                      assessmentId: course?.assessment?._id,
                                      evaluation: evaluation
                                    })
                                  "
                                >
                                  <div
                                    fxLayout="column"
                                    class="digi-status"
                                    [ngClass]="{
                                      yellow: evaluation?.status === 'IN_EVALUATION',
                                      green: evaluation?.status === 'EVALUATED',
                                      gray: evaluation?.status === 'NOT_STARTED',
                                      green2: evaluation?.status === 'Re-Evaluated'
                                    }"
                                  >
                                    <div class="digi-status-padding">
                                      {{ getEvaluationStatus(evaluation) | translate }}
                                    </div>
                                  </div>
                                </div>
                                <div class="td td3 digi-p-0">
                                  <div fxLayout="column">
                                    <div class="digi-pl-10">
                                      {{ evaluation?.itemsEvaluated | localeNumber }}
                                      /
                                      {{ evaluation?.totalItems | localeNumber }}
                                    </div>
                                  </div>
                                </div>
                                <div
                                  *ngIf="
                                    canCancelReEvaluation({ evaluations: course?.evaluations })
                                  "
                                  class="td td4"
                                >
                                  <button
                                    class="digi-cursor"
                                    mat-icon-button
                                    [matMenuTriggerFor]="menu"
                                    aria-label="Example icon-button with a menu"
                                  >
                                    <mat-icon style="color: #2f80ed">more_vert</mat-icon>
                                  </button>
                                  <mat-menu #menu="matMenu">
                                    <button
                                      (click)="
                                        onClickCancelReEvaluation({
                                          course: course,
                                          evaluation: evaluation
                                        })
                                      "
                                      mat-menu-item
                                    >
                                      <span class="digi-text-uppercase">{{
                                        'reportingAndAnalytics.manualEvaluationCourseDashboard.manualEvaluationCourse.cancelRe-evaluation'
                                          | translate
                                      }}</span>
                                    </button>
                                  </mat-menu>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <ng-template #noData>
            <div style="padding: 40px; text-align: center">
              {{ (isLoading ? 'common.loading' : 'common.noResultsFound') | translate }}
            </div>
          </ng-template>
        </div>
      </div>
    </div>
  </div>
</div>
