import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'

import { TranslateService } from '@ngx-translate/core'
import cloneDeep from 'lodash/cloneDeep'
import { ToastrService } from 'ngx-toastr'
import { switchMap } from 'rxjs/operators'

import { LocaleNumberPipe } from '@appcore/app/core/pipes/locale-number.pipe'
import {
  IAcademicYear,
  IAttemptType,
  IConductedOnLocation,
  IEvaluations,
  IItemRevisionCourse,
  IProgramAndTermWise,
  IRevisionResult,
  IStatusCount,
  IStatusTab,
  IYearAndLevel
} from '@appcore/app/models'
import { ManualEvaluationService } from '@appcore/app/pages/manual-evaluation/manual-evaluation.service'
import { ErrorHandlerService, RouteService } from '@appcore/app/services'
import { GlobalService, NameConversionService } from '@appcore/services/index'

import { ReportService } from '../../reporting/report.service'
import { MatSelect } from '@angular/material/select'

@Component({
  selector: 'digi-manual-evaluation-course',
  templateUrl: './manual-evaluation-course.component.html',
  styleUrls: ['./manual-evaluation-course.component.css']
})
export class ManualEvaluationCourseComponent implements OnInit, AfterViewInit {
  @ViewChild('selectRef')
  selectRef!: MatSelect

  statusTabs: IStatusTab[] = [
    { name: 'common.all', code: 'All', key: 'all', value: 0 },
    {
      name: 'common.notStarted',
      code: 'NOT_STARTED',
      key: 'notStarted',
      value: 0
    },
    {
      name: 'reportingAndAnalytics.manualEvaluationCourseDashboard.manualEvaluationCourse.inEvaluation',
      code: 'IN_EVALUATION',
      key: 'inEvaluation',
      value: 0
    },
    {
      name: 'reportingAndAnalytics.manualEvaluationCourseDashboard.manualEvaluationCourse.evaluated',
      code: 'EVALUATED',
      key: 'evaluated',
      value: 0
    }
  ]
  selectedTab = 'All'
  headers = ['common.course', 'common.status']
  report: IRevisionResult
  allReports: IRevisionResult
  academicYear: IAcademicYear
  examTypeCode = ''
  programCode: string
  termCode = ''
  isLoading = true
  attemptTypes: IAttemptType[]
  examCategory: string
  conductedLocations: IConductedOnLocation[]
  selectedConductedLocations: IConductedOnLocation
  programFullName: string

  constructor(
    private reportService: ReportService,
    private errorHandler: ErrorHandlerService,
    private routeService: RouteService,
    public nameConversionService: NameConversionService,
    private toastr: ToastrService,
    private translateService: TranslateService,
    private localNumberPipe: LocaleNumberPipe,
    private globalService: GlobalService,
    private activatedRoute: ActivatedRoute,
    private route: Router,
    private manualEvaluationService: ManualEvaluationService
  ) {}

  set selectedAttemptType(selectedAttemptType: string) {
    this.reportService.selectedAttemptType = selectedAttemptType
  }

  get selectedAttemptType(): string {
    return this.reportService.selectedAttemptType
  }

  ngOnInit(): void {
    this.globalService.setHeader({
      headerName: 'reportingAndAnalytics.currentReportDashboard.reportAnalytical',
      secondHeader: 'reportingAndAnalytics.currentReportDashboard.assessmentResult',
      activeHeader: 'common.manualEvaluation'
    })

    this.initRouteParamSubscriber()
  }

  ngAfterViewInit(): void {
    if (this.selectRef) {
      this.globalService.disableAllKeyboardFunctions({ selectRef: this.selectRef })
    }
  }

  private initRouteParamSubscriber() {
    this.conductedLocations = this.reportService.conductedLocations

    this.activatedRoute.queryParams.subscribe(
      ({
        examCategory = '',
        attemptType = '',
        mode = '',
        examTypeCode = '',
        programName = '',
        programFullName = ''
      }) => {
        if (this.examTypeCode || this.examCategory) {
          return
        }

        this.examTypeCode = examTypeCode
        this.examCategory = examCategory
        this.selectedAttemptType = attemptType
        this.programFullName = programFullName
        this.selectedConductedLocations = this.conductedLocations.find(
          (conductedLocation) => conductedLocation.code === mode
        )

        const lastIndex = programName.lastIndexOf('-')
        const programCode = programName.substring(0, lastIndex).trim()
        const termCode = programName.substring(lastIndex + 1).trim()

        this.programCode = programCode
        this.termCode = termCode
        let evaluatedType = ''
        if (termCode?.includes('all')) {
          // programCode === 'all' denotes re-evaluation page
          this.statusTabs = []
          this.selectedTab = 'RE_EVALUATED'
          evaluatedType = 're-'
        }
        this.headers.push(
          this.translateService.instant(
            'reportingAndAnalytics.manualEvaluationCourseDashboard.manualEvaluationCourse.itemEvaluated',
            { evaluatedType }
          )
        )
        this.headers.push('')
        this.termCode = termCode || ''
      }
    )
    this.isLoading = true
    this.activatedRoute.paramMap
      .pipe(
        switchMap((params) => {
          const academicYear = params.get('academicYear')
          const [starYear, endYear] = academicYear.split('-')
          this.formatAcademicYear({ starYear, endYear })
          if (!this.selectedConductedLocations) {
            this.selectedConductedLocations = this.conductedLocations[0]
          }

          return this.reportService.getAttemptType({
            examTypeCode: this.examTypeCode,
            programCode: this.programCode,
            type: this.selectedTab,
            academicYearStart: Number(starYear),
            academicYearEnd: Number(endYear)
          })
        })
      )
      .subscribe(({ data }) => {
        this.attemptTypes = data
        if (!this.selectedAttemptType) {
          this.selectedAttemptType = data[0]?.name
        }

        this.setQueryParams()

        return this.reportService
          .getItemManualEvaluation({
            ...(this.programCode !== 'all' && { programCode: this.programCode }),
            examTypeCode: this.examTypeCode,
            academicYearStart: this.academicYear.startDate,
            academicYearEnd: this.academicYear.endDate,
            type: this.selectedTab,
            attemptType: this.selectedAttemptType,
            examCategory: this.examCategory,
            mode: this.selectedConductedLocations.code
          })
          .subscribe(
            ({ data: report }) => {
              this.allReports = cloneDeep(report)

              this.report = report
              this.expandByDefault()
              this.setStatusCount({ status: this.report.count })
              this.isLoading = false
            },
            (err) => {
              this.errorHandler.errorLog(err)
              this.isLoading = false
            }
          )
      })
  }

  preventKeyboardEvents(event: KeyboardEvent): void {
    if (event) {
      this.globalService.preventKeyboardEvents({ event })
    }
  }

  onClickStatus({ type }: { type: string }) {
    this.selectedTab = type
    this.reportService
      .getItemManualEvaluation({
        ...(this.programCode !== 'all' && { programCode: this.programCode }),
        examTypeCode: this.examTypeCode,
        academicYearStart: this.academicYear.startDate,
        academicYearEnd: this.academicYear.endDate,
        type,
        attemptType: this.selectedAttemptType,
        examCategory: this.examCategory,
        mode: this.selectedConductedLocations?.code
      })
      .subscribe(
        (res) => {
          this.allReports = cloneDeep(res.data)

          this.report = res.data
          this.expandByDefault()
          this.setStatusCount({ status: this.report.count })
          this.setQueryParams()
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  private setQueryParams() {
    this.route.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: {
        attemptType: this.selectedAttemptType,
        mode: this.selectedConductedLocations?.code
      },
      queryParamsHandling: 'merge'
    })
  }

  formatAcademicYear({ starYear, endYear }: { starYear: string; endYear: string }) {
    this.academicYear = {
      startDate: starYear,
      endDate: endYear
    }
  }

  private setStatusCount({ status }: { status: IStatusCount }) {
    Object.keys(status).forEach((key) => {
      this.statusTabs.forEach((tab) => {
        if (tab.key === key) {
          tab.value = status[key]
        }
      })
    })
  }

  private expandByDefault() {
    this.onClickExpandProgram({ program: this.report[0] })
    this.onClickExpandYear({ yearAndLevel: this.report[0]?.yearLevel })
  }

  onClickExpandProgram({ program }: { program: IProgramAndTermWise }) {
    this.report.programTermBasedOnYearAndLevel.forEach((programTerm) => {
      if (programTerm?.programId === program?.programId) {
        return (programTerm.expandable = !programTerm.expandable)
      }
      programTerm.expandable = false
      programTerm.yearLevel.forEach((year) => (year.expandable = false))
    })
  }

  onClickExpandYear({ yearAndLevel }: { yearAndLevel: IYearAndLevel }) {
    this.report.programTermBasedOnYearAndLevel.forEach((programTerm) => {
      programTerm.yearLevel.forEach((year) => {
        if (year?.yearId === yearAndLevel?.yearId) {
          return (year.expandable = !year.expandable)
        }

        year.expandable = false
      })
    })
  }

  onClickBack() {
    this.routeService.transitionToReportDashboard()
  }

  get formatCourseDetails() {
    const programCode =
      this.programCode !== 'all'
        ? `${this.programCode ? this.programCode + ' - ' + this.programFullName + ' - ' : ''} ${
            this.termCode?.toLowerCase() === 'all'
              ? this.translateService.instant('common.all')
              : this.termCode || ''
          }`
        : ''

    return `${this.examTypeCode ? this.examTypeCode + '-' : ''} ${
      this.academicYear?.startDate
        ? this.localNumberPipe.transform(Number(this.academicYear?.startDate)) + '-'
        : ''
    } ${
      this.academicYear?.endDate
        ? this.localNumberPipe.transform(Number(this.academicYear?.endDate))
        : ''
    }  ${programCode || ''}`
  }

  onClickCourse({
    course,
    assessmentId,
    evaluation
  }: {
    course: IItemRevisionCourse
    assessmentId?: string
    evaluation: IEvaluations
  }) {
    if (course.examType?.examCategory?.mode?.toUpperCase() === 'OUT') {
      return this.routeService.transitionToExternalExamDashboard({
        queryParams: {
          examType: course.examType?._id,
          examCategory: course.examType?.examCategory?._id,
          attemptType: course.attemptType?._id
        }
      })
    }

    if (evaluation?.status === 'NOT_STARTED' && !evaluation.evaluatedBy) {
      this.manualEvaluationService.startManualEvaluation({ courseId: course?._id }).subscribe(
        ({ message }) => {
          this.globalService.showSuccess(message)

          this.routeService.transitionToEvaluateItems({
            assessmentId,
            evaluationNumber: evaluation.number,
            queryParams: course?.itemTypes
          })
        },
        (err) => this.errorHandler.errorLog(err)
      )

      return
    }

    this.routeService.transitionToEvaluateItems({
      assessmentId,
      evaluationNumber: evaluation.number,
      queryParams: course?.itemTypes
    })
  }

  onSearch({ searchText }: { searchText: string }) {
    if (!searchText) {
      this.report = cloneDeep(this.allReports)
      this.report.programTermBasedOnYearAndLevel.forEach((program) => {
        program.expandable = true
        program.yearLevel.forEach((year) => (year.expandable = true))
      })
      return
    }

    searchText = searchText.toLowerCase()
    const reportClone: IRevisionResult = cloneDeep(this.allReports)
    const filteredPrograms = reportClone.programTermBasedOnYearAndLevel.reduce(
      (accPrograms, currentProgram) => {
        const filteredYears = currentProgram.yearLevel.reduce((accYearLevels, currentYearLevel) => {
          const filteredCourses = currentYearLevel.courses.filter(
            ({ courseCode, courseName, courseHierarchyCode }) =>
              courseCode.toLowerCase().includes(searchText) ||
              courseName.toLowerCase().includes(searchText) ||
              courseHierarchyCode.toLowerCase().includes(searchText)
          )
          if (filteredCourses.length) {
            currentYearLevel.courses = filteredCourses
            accYearLevels.push(currentYearLevel)
          }
          return accYearLevels
        }, [])

        if (filteredYears.length) {
          currentProgram.yearLevel = filteredYears
          accPrograms.push(currentProgram)
        }
        return accPrograms
      },
      []
    )

    filteredPrograms.forEach((program) => {
      program.expandable = true
      program.yearLevel.forEach((year) => (year.expandable = true))
    })

    this.report.programTermBasedOnYearAndLevel = filteredPrograms
  }

  getLastEvaluation({ evaluations }: { evaluations: IEvaluations[] }): IEvaluations[] {
    return Array(evaluations[evaluations?.length - 1])
  }

  onClickCancelReEvaluation({
    course,
    evaluation
  }: {
    course: IItemRevisionCourse
    evaluation: IEvaluations
  }) {
    const revisionNumber = course.revisions
      .reverse()
      .find((revision) => revision.evaluationNumber === evaluation?.number)?.revisionNumber

    this.reportService
      .cancelReRevision({
        params: { courseId: course?._id, isEvaluation: true, revisionNumber }
      })
      .subscribe(
        ({ message }) => {
          this.toastr.success(message)
          this.initRouteParamSubscriber()
        },
        (err) => this.errorHandler.errorLog(err)
      )
  }

  canCancelReEvaluation({ evaluations }: { evaluations: IEvaluations[] }): boolean {
    return (
      evaluations?.length > 1 && evaluations[evaluations?.length - 1]?.status === 'IN_EVALUATION'
    )
  }

  getEvaluationStatus({ status }: { status: string }) {
    const evaluationStatus = {
      notStarted: 'common.notStarted',
      inEvaluation:
        'reportingAndAnalytics.manualEvaluationCourseDashboard.manualEvaluationCourse.inEvaluation',
      evaluated:
        'reportingAndAnalytics.manualEvaluationCourseDashboard.manualEvaluationCourse.evaluated',
      reEvaluated: 'settings.reportsAnalyticsSettings.itemRevision.reEvaluated'
    }

    return evaluationStatus[this.nameConversionService.convertToCamelCase(status)]
  }
}
