import { IProgramData, IProgramHierarchy, ITopic, ITreeData } from '@appcore/app/models'

interface ISubject {
  _id: string
  name: string
  code: string
  topics: ITopic[]
  isExpanded?: boolean
  nodeId?: number
}

export interface ICourseInputPayload extends Partial<ITopic> {
  courseName: string
  type: string
  topics?: ITopic[]
  subjectId?: string
}

export interface ISubjectAndTopic {
  subjects: ISubject[]
  topics: ITopic[]
  nodeId?: number
}

export interface ISubjectTopicData {
  type: string
  courseName: string
  nodeId: number
  topicId?: string
  subjectId?: string
  subTopicId?: string
  actionType?: string
  name?: string
  code?: string
  content?: string
}

export interface IUpdateSubjectTopicPayload {
  courseName: string
  type: string
  name?: string
  code?: string
  subjectId?: string
  topicId: string
  content?: string
}

export interface ICloMappingQuery {
  programId: string
  termId: string
  yearId: string
  curriculumId: string
  levelId: string
  courseId: string
}

export interface IUpdateCloPayload extends ICloMappingQuery {
  frameworkId?: string
  domainId?: string
  cloMappingId?: string
  clo?: { name: string; desc: string }
  course?: { _id: string }
}

export interface IUpdateFramework extends ICloMappingQuery {
  frameworkId?: string
  cloMappingId?: string
  curriculumCode?: string
  curriculumName?: string
  type?: string
}

export interface IPloList {
  name: string
  code: string
  _id: string
  hierarchy: ITreeData[]
  selected?: boolean
}

export interface IPloPayload {
  programdata?: ITreeData[]
  curriculumCode?: string
  curriculumName?: string
  coursedata?: { cloMappingId: string }
}

export interface IUpdatePloMappingPayload {
  program: { _id: string }
  term: { _id: string }
  year: { _id: string }
  curriculum: { _id: string }
  cloMappingId: string
  frameworkId: string
  domainId: string
  cloId: string
  plo: { _id: string }
  type: string
  mappedValue: string
}

export interface ICourseManagementPayload {
  pageNo: number
  limit: number
  search?: string
}

export interface IProgramCheckboxData {
  selected: boolean
  isIntermediateCheckBox: boolean
  hierarchy: IProgramHierarchy[]
  baseData: IProgramData
}
