import { HttpClient, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'

import { Observable } from 'rxjs'
import { first } from 'rxjs/operators'
import { environment } from 'src/environments/environment'

import {
  IClo,
  IExamDetail,
  IFrameworkForMapping,
  IMappingType,
  IResponse,
  ITreeData
} from '@appcore/app/models'

import {
  ICourseInputPayload,
  ICourseManagementPayload,
  IPloList,
  IPloPayload,
  ISubjectAndTopic,
  IUpdateCloPayload,
  IUpdateFramework,
  IUpdatePloMappingPayload,
  IUpdateSubjectTopicPayload
} from './my-courses.interface'

const BASE_URL = `${environment.endPoint}/course`
const MAPPING_BASE_URL = `${BASE_URL}/mapping`

@Injectable()
export class MyCoursesService {
  constructor(private httpClient: HttpClient) {}

  protected getQueryParamsFromObject(params: object, type?: string) {
    let queryParams = new HttpParams()

    Object.keys(params).forEach((key) => {
      if (type === 'filter' ? params[key] === undefined || params[key] === '' : !params[key]) {
        return
      }

      queryParams = queryParams.append(key, params[key])
    })

    return { params: queryParams }
  }

  getCoursesForCourseInput(): Observable<IResponse<IExamDetail[]>> {
    const url = `${environment.endPoint}/faculty/courses`

    return this.httpClient.get(url).pipe(first())
  }

  createSubjectOrTopics({ payload }: { payload: ICourseInputPayload }): Observable<IResponse> {
    const url = `${BASE_URL}/input`

    return this.httpClient.post(url, payload).pipe(first())
  }

  getSubjectsAndTopicsForCourse({
    courseName
  }: {
    courseName: string
  }): Observable<IResponse<ISubjectAndTopic>> {
    const url = `${BASE_URL}/input`
    const queryParams = this.getQueryParamsFromObject({ courseName })

    return this.httpClient.get(url, queryParams).pipe(first())
  }

  updateSubjectOrTopic({
    payload
  }: {
    payload: IUpdateSubjectTopicPayload
  }): Observable<IResponse> {
    const url = `${BASE_URL}/input`

    return this.httpClient.patch(url, payload).pipe(first())
  }

  deleteSubjectAndTopic({
    courseName,
    topicId,
    subjectId,
    subTopicId,
    type,
    isContent
  }: {
    courseName: string
    subjectId?: string
    topicId?: string
    subTopicId?: string
    type?: string
    isContent?: boolean
  }): Observable<IResponse> {
    const url = `${BASE_URL}/input`
    const queryParams = this.getQueryParamsFromObject({
      courseName,
      subjectId,
      topicId,
      subTopicId,
      type,
      isContent
    })

    return this.httpClient.delete(url, queryParams).pipe(first())
  }

  getFrameworks(): Observable<IResponse<IFrameworkForMapping[]>> {
    const url = `${MAPPING_BASE_URL}/framework`

    return this.httpClient.get(url).pipe(first())
  }

  updateFramework({ payload }: { payload: IUpdateFramework }): Observable<IResponse> {
    const url = `${MAPPING_BASE_URL}/framework`

    return this.httpClient.post(url, payload).pipe(first())
  }

  getCloMapping({
    query
  }: {
    query: {
      programId: string
      curriculumId: string
      courseCode: string
      curriculumCode
      curriculumName
    }
  }): Observable<
    IResponse<{
      _id: string
      framework: IFrameworkForMapping
      canUpdateFrameWork: boolean
      canUpdateMappingType: boolean
    }>
  > {
    const url = `${MAPPING_BASE_URL}/clo-mapping`
    const queryParams = this.getQueryParamsFromObject(query)

    return this.httpClient.get(url, queryParams).pipe(first())
  }

  updateClo({
    query,
    payload
  }: {
    query: { id: string }
    payload: IUpdateCloPayload
  }): Observable<IResponse> {
    const url = `${MAPPING_BASE_URL}/clo-mapping`
    const queryParams = this.getQueryParamsFromObject(query)

    return this.httpClient.put(url, payload, queryParams).pipe(first())
  }

  addClo({ payload }: { payload: IUpdateCloPayload }): Observable<IResponse<IClo>> {
    const url = `${MAPPING_BASE_URL}/clo-mapping`

    return this.httpClient.post(url, payload).pipe(first())
  }

  deleteClo({ id, payload }: { id: string; payload: IUpdateCloPayload }): Observable<IResponse> {
    const url = `${MAPPING_BASE_URL}/clo-mapping?id=${id}`

    return this.httpClient.request('DELETE', url, { body: payload }).pipe(first())
  }

  getPloMappings({
    programId,
    curriculumId,
    cloMappingId,
    curriculumName,
    curriculumCode
  }: {
    programId: string
    curriculumId: string
    cloMappingId: string
    curriculumName: string
    curriculumCode: string
  }): Observable<IResponse<IPloList[]>> {
    const url = `${MAPPING_BASE_URL}/plo-mapping`
    const queryParams = this.getQueryParamsFromObject({
      programId,
      curriculumId,
      cloMappingId,
      curriculumName,
      curriculumCode
    })

    return this.httpClient.get(url, queryParams).pipe(first())
  }

  getCloAndPloMappings({ payload }: { payload: IPloPayload }): Observable<IResponse<ITreeData[]>> {
    const url = `${MAPPING_BASE_URL}/plo-mapping`

    return this.httpClient.post(url, payload).pipe(first())
  }

  updateCloAndPloMapping({
    payload
  }: {
    payload: IUpdatePloMappingPayload
  }): Observable<IResponse<{ canUpdateMappingType: boolean }>> {
    const url = `${MAPPING_BASE_URL}/plo-mapping`

    return this.httpClient.put(url, payload).pipe(first())
  }

  getMappingTypes(): Observable<IResponse<IMappingType[]>> {
    const url = `${MAPPING_BASE_URL}/type`

    return this.httpClient.get(url).pipe(first())
  }

  updateMappingType({ payload }: { payload: IUpdateFramework }): Observable<IResponse> {
    const url = `${MAPPING_BASE_URL}/mapping-type`

    return this.httpClient.put(url, payload).pipe(first())
  }

  getCoursesManagement({
    payload
  }: {
    payload: ICourseManagementPayload
  }): Observable<IResponse<{ courses: IExamDetail[]; count: number; totalPages: number }>> {
    const url = `${environment.endPoint}/faculty/courses`
    const queryParams = this.getQueryParamsFromObject(payload)

    return this.httpClient.get(url, queryParams).pipe(first())
  }
}
