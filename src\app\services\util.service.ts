import { Location } from '@angular/common'
import { Injectable } from '@angular/core'
import { Dom<PERSON>anitizer, SafeUrl } from '@angular/platform-browser'

import { TranslateService } from '@ngx-translate/core'
import { fromArabic, toArabic } from 'arabic-digits'
import * as ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'

import { IHeirarchy, INameCode, IStudentGroup } from '@appcore/app/models'
import { IOutComeProgram } from '@appcore/app/pages/reporting'

import { environment } from '../../environments/environment'
import { EGender, EItemType, EXCEL_EXTENSION, EXCEL_TYPE } from '../core/constants'
import { ITimeFormat } from '../models/exam-center-management'
import { IInstitutionCalendar } from '../models/exam-readiness/exam-readiness-on-going.interface'
import { AuthService } from './auth.service'
import { DateTimeService } from './date-time.service'
import { FileService } from './file.service'
import { NameConversionService } from './name-conversion.service'
import { SettingsService } from './settings.service'
import { GlobalService } from './global.service'

@Injectable()
export class UtilService {
  readonly subjectReviewerNames = ['subjectExpertReviewer', 'subject']
  readonly itemTypes = EItemType

  constructor(
    private translateService: TranslateService,
    private settingsService: SettingsService,
    private authService: AuthService,
    private nameConversionService: NameConversionService,
    private dateTimeService: DateTimeService,
    private location: Location,
    private sanitizer: DomSanitizer,
    private fileService: FileService,
    private globalservices: GlobalService
  ) {}

  removeDuplicateObjects({ array = [], props = [] }) {
    if (!Array.isArray(array)) {
      throw Error('Invalid source to remove duplicates')
    }
    if (!Array.isArray(props)) {
      throw Error('Invalid prop to remove duplicates')
    }

    if (!array.length) {
      return array
    }

    if (!props.length) {
      return [...new Set(array.map((arr) => JSON.stringify(arr)))].map((arr) => JSON.parse(arr))
    }

    let unique = []

    props.forEach((prop, index) => {
      if (index === 0) {
        // if (prop === IDENTITY) {
        //   unique = [...new Map(unique.map((item) => [item[prop].no, item])).values()]
        // } else {
        unique = [...new Map(array.map((item) => [item[prop], item])).values()]
        // }
      }

      // if (prop === IDENTITY) {
      //   unique = [...new Map(unique.map((item) => [item[prop].no, item])).values()]
      // } else {
      unique = [...new Map(unique.map((item) => [item[prop], item])).values()]
      // }
    })

    return unique
  }

  removeDuplicatesByProperty<T, M extends keyof T>({
    array,
    keyProperty,
    maxArrayProperty
  }: {
    array: T[]
    keyProperty: Extract<keyof T, string>
    maxArrayProperty?: M
  }): T[] {
    const seen = new Map<string, T>()
    const result: T[] = []

    array.forEach((item) => {
      const keyValue = item[keyProperty]
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const itemArray = maxArrayProperty ? (item[maxArrayProperty] as unknown as any[]) : undefined

      if (
        !seen.has(keyValue.toString()) ||
        (itemArray &&
          itemArray.length >
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            ((seen.get(keyValue.toString())?.[maxArrayProperty] as unknown as any[]).length || 0))
      ) {
        seen.set(keyValue.toString(), item)
      }
    })

    seen.forEach((value) => {
      result.push(value)
    })

    return result
  }

  getTranslatedSurveyCodeLabel({ surveyLabel }: { surveyLabel: string }) {
    const surveyCodeLabel = {
      les: 'common.les',
      las: 'common.las',
      ls: 'common.ls',
      es: 'common.es',
      rs: 'common.rs',
      oe: 'common.oe'
    }

    return surveyCodeLabel[surveyLabel.toLowerCase()]
  }

  getTranslatedOptions({ studentResponse }: { studentResponse: string }) {
    const studenResponseCode = {
      a: 'alphabets.a',
      b: 'alphabets.b',
      c: 'alphabets.c',
      d: 'alphabets.d',
      e: 'alphabets.e',
      f: 'alphabets.f',
      'tr*': 'common.textualResponse',
      'ua*': 'common.ua*',
      absent: 'reportingAndAnalytics.studentReport.ab',
      exam_not_started: 'reportingAndAnalytics.studentReport.ns'
    }

    return studenResponseCode[studentResponse.toLowerCase()]
      ? studenResponseCode[studentResponse.toLowerCase()]
      : studentResponse
  }

  getTranslateItemType({
    itemType,
    isTranslate = true
  }: {
    itemType: EItemType | string
    isTranslate?: boolean
  }) {
    const itemTypeCode = {
      EMQ: {
        name: 'itemTypes.extendedMatchingQuestions',
        code: 'itemTypes.emq'
      },
      MCQ: {
        name: 'itemTypes.multipleChoiceQuestion',
        code: 'itemTypes.mcq'
      },
      CQ: {
        name: 'itemTypes.comprehensiveQuestions',
        code: 'itemTypes.cq'
      },
      EQ: {
        name: 'itemTypes.explanatoryQuestions',
        code: 'itemTypes.eq'
      },
      SAQ: {
        name: 'itemTypes.shortAnswerQuestions',
        code: 'itemTypes.saq'
      },
      MQ: {
        name: 'itemTypes.matchingQuestions',
        code: 'itemTypes.mq'
      },
      TF: {
        name: 'itemTypes.trueOrFalse',
        code: 'itemTypes.tf'
      },
      CSAQ: {
        name: 'itemTypes.comprehensiveShortAnswerQuestion',
        code: 'itemTypes.csaq'
      },
      HQ: {
        name: 'itemTypes.hotspotQuestions',
        code: 'itemTypes.hq'
      },
      MIT: {
        name: 'itemTypes.mixedQuestions',
        code: 'itemTypes.mst'
      },
      [this.itemTypes.MRA]: {
        name: 'itemTypes.multipleRightAnswer',
        code: 'itemTypes.mra'
      },
      CPE: {
        name: 'itemTypes.clinicalPracticalExam',
        code: 'itemTypes.cpe'
      },
      OQ: {
        name: 'itemTypes.orderingQuestions',
        code: 'itemTypes.oq'
      },
      DDQ: {
        name: 'itemTypes.dragAndDropSequencing',
        code: 'itemTypes.ddq'
      }
    }

    const typeData = itemTypeCode[itemType]

    if (!typeData) {
      return { name: itemType, code: itemType }
    }

    return {
      name: isTranslate ? this.translateService.instant(typeData.name) : typeData.name,
      code: isTranslate ? this.translateService.instant(typeData.code) : typeData.code
    }
  }

  getTranslatedRoleName({ roleName }: { roleName: string }) {
    const roleNameCode = {
      PROCTOR: { name: 'common.proctor' },
      PRIMARY: { name: 'common.primary' },
      SECONDARY: { name: 'common.secondary' },
      STUDENT: { name: 'common.student' },
      EXAM_COORDINATOR: { name: 'common.examCoordinator' }
    }

    return {
      name: this.translateService.instant(roleNameCode[roleName].name)
    }
  }

  getTranslatedGenderCode({ genderCode }: { genderCode: EGender }) {
    const gender = {
      M: { code: 'alphabets.m', name: 'common.male' },
      F: { code: 'alphabets.f', name: 'common.female' },
      COMMON: { code: 'alphabets.c', name: 'common.common' },
      MIXED: { code: 'common.mg', name: 'common.mixed' }
    }

    return {
      name: this.translateService.instant(gender[genderCode].name),
      code: this.translateService.instant(gender[genderCode].code)
    }
  }

  convertArabicNumberToDigit({
    num,
    type,
    ignoreZero = false
  }: {
    num: string
    type?: string
    ignoreZero?: boolean
  }): number | string {
    const isEmpty = ignoreZero ? !num && Number(num) !== 0 : !num

    if (isEmpty) {
      return type === 'char' ? '' : 0
    }

    if (!this.checkIsArabicText({ text: num, type: 'number' })) {
      return num
    }

    return type === 'char' ? fromArabic(num) : Number(fromArabic(num))
  }

  convertDigitToArabicNumber({ num }: { num: number | string }): number | string {
    return this.settingsService.getOptions().language === 'ar' ? toArabic(num) : num
  }

  getTranslateGender({ gender }: { gender: string }) {
    if (!this.checkIsArabicText({ text: gender, type: 'char' })) {
      return gender
    }

    const gen = {
      ث: 'F',
      ذ: 'M'
    }

    return gen[gender]
  }

  getTranslateTimeFormat({ format }: { format: string }) {
    if (!this.checkIsArabicText({ text: format, type: 'char' })) {
      return format
    }

    const timeFormat = {
      ص: 'AM',
      م: 'PM'
    }

    return timeFormat[format]
  }

  checkIsArabicText({ text, type }: { text: string; type: string }) {
    const arabicCharUniCode = /[\u0600-\u06FF]/
    const arabicNumberUniCode = /[\u0660-\u0669]/

    return type === 'char' ? arabicCharUniCode.test(text) : arabicNumberUniCode.test(text)
  }

  allowOnlyNumbers(event: KeyboardEvent) {
    const charCode = event.which ? event.which : event.keyCode
    if ((charCode < 48 || charCode > 57) && charCode !== 46) {
      event.preventDefault()
      return false
    } else {
      return true
    }
  }

  allowOnlyAlphaNumeric(event: KeyboardEvent) {
    const charCode = event.which ? event.which : event.keyCode
    if (
      (charCode > 64 && charCode < 91) ||
      (charCode > 96 && charCode < 123) ||
      charCode === 8 ||
      charCode === 32 ||
      (charCode >= 48 && charCode <= 57)
    ) {
      return true
    } else {
      event.preventDefault()
      return false
    }
  }

  getTranslatedProctoringTypeLabel({ proctoringType }: { proctoringType: string }) {
    const proctoringLabelCode = {
      'remote+p': {
        name: 'settings.assessmentSettings.endOfModule.remote+p',
        code: 'settings.globalExamSettings.examTypes.r+p'
      },
      'remote-p': {
        name: 'settings.assessmentSettings.endOfModule.remote-p',
        code: 'settings.globalExamSettings.examTypes.r-p'
      },
      'onsite+p': {
        name: 'settings.assessmentSettings.endOfModule.onsite+p',
        code: 'settings.globalExamSettings.examTypes.o+p'
      },
      'onsite-p': {
        name: 'settings.assessmentSettings.endOfModule.onsite-p',
        code: 'settings.globalExamSettings.examTypes.o-p'
      }
    }

    return {
      name: this.translateService.instant(
        proctoringLabelCode[
          this.getReplaceSpaceFromString({ value: proctoringType }).toLocaleLowerCase()
        ].name
      ),
      code: this.translateService.instant(
        proctoringLabelCode[
          this.getReplaceSpaceFromString({ value: proctoringType }).toLocaleLowerCase()
        ].code
      )
    }
  }

  getReplaceSpaceFromString({ value }: { value: string }) {
    return value.replace(/\s/g, '')
  }

  exportTemplateAsExcel({
    templateToExcel,
    fileName,
    sheetName
  }: {
    templateToExcel: string[][]
    fileName: string
    sheetName?: string
  }) {
    if (sheetName?.length > 31) {
      sheetName = sheetName.substring(0, 28) + '...'
    }
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('import users')

    const headers = templateToExcel[0]
    worksheet.addRow(headers)
    templateToExcel.slice(1).forEach((row) => {
      worksheet.addRow(row)
    })

    workbook.xlsx.writeBuffer().then((buffer: any) => {
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      saveAs(blob, `${fileName}.xlsx`)
    })
  }

  // exportToXLSX({ worksheet, fileName }: { worksheet: XLSX.WorkSheet; fileName: string }) {
  //   const workbook: XLSX.WorkBook = { Sheets: { data: worksheet }, SheetNames: ['data'] }
  //   const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
  //   const excelData: Blob = new Blob([excelBuffer], {
  //     type: EXCEL_TYPE
  //   })

  //   this.fileService.downloadBlobAsFile({
  //     blob: excelData,
  //     filename: `${fileName}.${EXCEL_EXTENSION}`
  //   })
  // }

  exportJsonToExcel<T>({
    jsonData,
    fileName,
    sheetName,
    columnWidths,
    isWithData = true
  }: {
    jsonData: T[]
    fileName: string
    sheetName: string
    columnWidths?: Record<string, number>
    isWithData?: boolean
  }) {
    const headers = Object.keys(jsonData[0])
    const data = isWithData ? jsonData.map((item) => Object.values(item)) : []

    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet(sheetName || fileName)

    worksheet.columns = headers.map((header, index) => ({
      header,
      key: header,
      width: columnWidths && columnWidths[header] ? columnWidths[header] : 15
    }))

    if (isWithData) {
      worksheet.addRows(data)
    }

    workbook.xlsx.writeBuffer().then((buffer: any) => {
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      saveAs(blob, `${fileName}.xlsx`)
    })
  }

  getFormatTime({ selectedTime }: { selectedTime: string }) {
    if (!selectedTime) {
      return
    }

    const [hr, mm] = selectedTime.split(':')
    const hour = !Number(hr) ? 12 : hr

    return Number(hour) > 12 ? `${Number(hour) - 12}:${mm}` : `${hour}:${mm}`
  }

  getFormatedTime({ time }: { time: string }) {
    let hoursMinitue: { hour: string; minute: string }
    const splitDate = time.split(':')
    const [hour, minute] = splitDate
    hoursMinitue = { hour, minute }

    return hoursMinitue
  }

  getFormattedMeridian({
    selectedTime,
    isTranslate = false
  }: {
    selectedTime: string
    isTranslate?: boolean
  }) {
    if (!selectedTime) {
      return
    }

    const [hr] = selectedTime.split(':')
    const meridian = Number(hr) >= 12 ? 'PM' : 'AM'

    return isTranslate
      ? this.translateService.instant(`common.${meridian.toLowerCase()}`)
      : meridian
  }

  getTranslatedText({
    text,
    key,
    lang
  }: {
    text: string
    key: string
    lang: { from: string; to: string }
  }) {
    if (
      (lang.to === 'ar' && this.checkIsArabicText({ text, type: 'char' })) ||
      (lang.to === 'en' && !this.checkIsArabicText({ text, type: 'char' }))
    ) {
      return text
    }

    const arabicTranslationObject = this.translateService.getParsedResult(
      this.translateService.translations[lang.from],
      key
    )

    const translationKey = `${key}.${Object.keys(arabicTranslationObject).find(
      (obj) => arabicTranslationObject[obj] === text
    )}`

    return this.translateService.getParsedResult(
      this.translateService.translations[lang.to],
      translationKey
    )
  }

  getRoleName({ role }: { role: string }) {
    if (this.subjectReviewerNames.some((name) => role.includes(name))) {
      return this.authService.loggedUser.subjectExpertReviewerRoleName
    }

    if (role.includes('medical')) {
      return this.authService.loggedUser.medicalEducationistRoleName
    }

    if (role.includes('itemAuthor')) {
      return this.authService.loggedUser.itemAuthorRoleName
    }

    if (role.includes('assessmentAuthor')) {
      return this.authService.loggedUser.assessmentAuthorRoleName
    }

    return this.translateService.instant(
      `assessment.itemCreate.${this.nameConversionService.convertToCamelCase(role)}`
    )
  }

  getTranslatedCloPlo({ translatedCloPlo }: { translatedCloPlo: string }) {
    const [learningOutcome, numbers] = translatedCloPlo.split(' ')

    return `${this.translateService.instant(`common.${learningOutcome.toLowerCase()}`)} ${numbers}`
  }

  getTranslateGradeName({ text }: { text: string }) {
    if (!text) {
      return
    }

    if (this.checkIsArabicText({ text, type: 'char' })) {
      return { name: text }
    }

    const gradeName = {
      all: { name: 'common.all' },
      'first class': { name: 'common.first_class' },
      'second class': { name: 'common.second_class' },
      a: { name: 'alphabets.a' },
      'a+': { name: 'common.a+' },
      b: { name: 'alphabets.b' },
      c: { name: 'alphabets.c' },
      d: { name: 'alphabets.d' },
      e: { name: 'alphabets.e' },
      f: { name: 'alphabets.f' }
    }

    text = text.includes('1st class') ? 'First Class' : text

    return {
      name: this.translateService.instant(gradeName[text.toLowerCase()]?.name ?? '')
    }
  }

  calculateCount({ objArray, key }: { objArray: object[]; key: string }): number {
    if (!objArray || !key) {
      return
    }

    return objArray
      .map((obj) => {
        return obj[key] ? obj[key] : 0
      })
      .reduce((prev, curr) => prev + curr, 0)
  }

  getReplaceText({
    text = '',
    replaceText = '',
    replaceableText = ''
  }: {
    text: string
    replaceText: string
    replaceableText: string
  }) {
    if (text && replaceText && replaceableText) {
      return text.toString().split(replaceText).join(replaceableText)
    }
    return text
  }

  hasFormula({ content }: { content: string }) {
    return content.includes('<math')
  }

  formatTimeWithTimeZone({ time, isTimezone = true }: { time: string; isTimezone?: boolean }) {
    if (!time) {
      return
    }

    if (isTimezone) {
      time = this.dateTimeService.getTimeByCurrentTimeZone({
        timestamp: time,
        timezone: this.authService.currentTimeZone
      })
    }

    const [timestamp, type] = time.split(' ')
    const [hour, minute] = timestamp.split(':')

    return `${hour}:${minute} ${this.translateService.instant(`common.${type.toLowerCase()}`)}`
  }

  formatCustomTime({ time }: { time: ITimeFormat }) {
    if (!time) {
      return
    }

    const convertTimeToString = `${time.hour.toString().padStart(2, '0')}:${time.minute
      .toString()
      .padStart(2, '0')} ${time.format}`

    return this.formatTimeWithTimeZone({ time: convertTimeToString, isTimezone: false })
  }

  getSplitTime({ time }: { time: string }) {
    if (!time) {
      return
    }

    const splitTime = time.match(/(\d+):(\d+) (\w+)/)
    const hours = Number(splitTime[1])
    const minutes = Number(splitTime[2])
    const meridian = splitTime[3]

    return { hours, minutes, meridian }
  }

  hasValidHTMLTags({ text }: { text: string }): boolean {
    const parser = new DOMParser()
    const doc = parser.parseFromString(text, 'text/html')

    return !Array.from(doc.body.childNodes).some((node) => node.nodeName.includes('text'))
  }

  getSerializedText({ text }: { text: string }): string {
    if (this.hasValidHTMLTags({ text })) {
      return text
    } // Returns original text if valid HTML tags are present

    return text.replace(/(?<!<\/?\w+(?:\s+[^>]+)?>)(<[^>]*>)/g, (match) => {
      return match.replace(/</g, '< ').replace(/>/g, ' >')
    })
  }

  setRouteQueryParams({ key, value }: { key: string; value: string | boolean | number }) {
    const currentUrl = this.location.path() // Get the current URL
    const queryParams = new URLSearchParams(currentUrl.split('?')[1])
    queryParams.set(key, value.toString())
    const updatedUrl = `${currentUrl.split('?')[0]}?${queryParams.toString()}`
    this.location.replaceState(updatedUrl)
  }

  paginate({
    array = [],
    pageSize = 10,
    pageNumber = 1
  }: {
    array
    pageSize: number
    pageNumber: number
  }) {
    return array.slice((pageNumber - 1) * pageSize, pageNumber * pageSize)
  }

  get clientConfig() {
    return environment?.clientConfig
  }

  get helpDeskUrl() {
    return environment?.helpDeskURL
  }

  sortRegularTermFirst({ programs }: { programs: IOutComeProgram[] }) {
    // Find the index of the "Regular" term
    programs.forEach(({ terms }) => {
      const regularTermIndex = terms.findIndex((term) =>
        ['regular', 'regular term'].includes(term?.name?.toLowerCase())
      )

      if (regularTermIndex > -1) {
        // Remove the "Regular" term from its current position
        const [regularTerm] = terms.splice(regularTermIndex, 1)
        // Add the "Regular" term to the 0th index
        terms.unshift(regularTerm)
      }
    })
  }

  reverseArray<T>({ arr }: { arr: T[] }): T[] {
    return arr?.slice()?.reverse()
  }

  checkIsEmpty({ value }: { value: string | number | boolean }): boolean {
    return ['', null, undefined].includes(value as string) || value?.toString() === 'undefined'
  }

  checkIsNotEmpty({ value }: { value: string | number | boolean }): boolean {
    return !this.checkIsEmpty({ value })
  }

  roundToTwoDecimalPlaces({ value }: { value: number }) {
    return Math.round(value * 100) / 100 || 0
  }

  formatDigitToArray({ digit }: { digit: number }) {
    return Array.from(Array(digit).keys()).map((num) => num + 1)
  }

  formatRangeDigitArray({ from, to }: { from: number; to: number }) {
    return Array.from({ length: to - from + 1 }, (_, i) => from + i)
  }

  checkIsFloat({ value }: { value: number }) {
    if (!value) {
      return '0'
    }

    const [integer, float] = value.toString()?.split('.')

    return float === '00' || !float
      ? this.convertToFixedPoint({ value: Number(integer), count: 0 })
      : this.convertToFixedPoint({ value: Number(value), count: 2 })
  }

  convertToFixedPoint({ value, count }: { value: number; count: number }) {
    return value.toFixed(count)
  }

  getTransformedStudentGroups({ groups = [] }: { groups: IStudentGroup[] }): IStudentGroup[] {
    return (
      groups?.reduce((acc, group) => {
        const deliveryType = group.deliveryType as INameCode

        if (!deliveryType?.code) {
          acc.push(group)
        } else {
          const groupCode = group.code.split('-')
          const lastIndex = groupCode.length - 1
          group.groupCode = groupCode[lastIndex]

          const name = groupCode.slice(0, lastIndex).join('-')
          const existingGroup = acc.find((g) => g.name === name)

          if (existingGroup) {
            existingGroup.groupCodes.push(group.groupCode)
          } else {
            acc.push({
              name,
              groupCodes: [group.groupCode]
            })
          }
        }

        return acc
      }, []) ?? []
    )
  }

  generateArrayFromNumber({ num }: { num: number }): number[] {
    return Array.from({ length: num })
  }

  getArrayParams({
    values,
    key = '',
    isFirstParam = false
  }: {
    values: string[]
    key: string
    isFirstParam: boolean
  }): string {
    return (
      values
        ?.map((value, index) => `${index === 0 && isFirstParam ? '?' : '&'}${key}=${value}`)
        ?.join('') ?? ''
    )
  }

  getInstitutionNameWithDate({
    institutionCalendar
  }: {
    institutionCalendar: IInstitutionCalendar
  }) {
    const startDate = this.dateTimeService.formatDateWithSlash(institutionCalendar.startDate)
    const endDate = this.dateTimeService.formatDateWithSlash(institutionCalendar.endDate)

    return `${institutionCalendar.name} (${startDate} - ${endDate})`
  }

  sanitizeUrl({ url }: { url: string }): SafeUrl | string {
    return this.sanitizer.bypassSecurityTrustUrl(url)
  }

  truncateDecimalPlaces({
    value = 0,
    decimalPlaces = 2
  }: {
    value: number
    decimalPlaces?: number
  }) {
    const multiplier = 10 ** decimalPlaces
    return Math.floor(value * multiplier) / multiplier
  }

  concatAndTrim({
    suffix,
    text,
    maxLength
  }: {
    suffix: string
    text: string | number
    maxLength: number
  }) {
    return `${suffix}${text}`.slice(-maxLength)
  }

  customSliderToggle({ id, customWidth }: { id: string; customWidth?: string }) {
    const sliderContentArea = document.getElementById(id) as HTMLElement
    const windowWidth = window.innerWidth

    if (sliderContentArea) {
      const sliderContent = sliderContentArea.querySelector(
        '.digi-custom-slider-content'
      ) as HTMLElement

      if (!sliderContentArea.classList.contains('show')) {
        sliderContent.style.width = '0'
        sliderContentArea.classList.add('show')
        requestAnimationFrame(() => {
          sliderContent.style.width = customWidth ? customWidth : `${windowWidth * 0.9}px`
        })
        this.globalservices.isSliderOpen = true
      } else {
        sliderContent.style.width = '0'
        setTimeout(() => {
          sliderContentArea.classList.remove('show')
        }, 300)
        this.globalservices.isSliderOpen = false
      }
    }
  }

  getCourseDetails({
    hierarchy,
    programLabel = 'name',
    termLabel = 'name',
    yearLabel = 'name',
    curriculumLabel = 'name',
    levelLabel = 'name',
    rotationGroupLabel = 'code'
  }: {
    hierarchy: IHeirarchy
    programLabel?: string
    termLabel?: string
    yearLabel?: string
    curriculumLabel?: string
    levelLabel?: string
    rotationGroupLabel?: string
  }) {
    const { program, term, curriculum, level, rotationGroup, year } = hierarchy || {}

    const separator = `<span style="
    display: inline-block;
    width: 5px;
    height: 5px;
    margin: 0 6px;
    border-radius: 50%;
    color: #6b7280;
    vertical-align: middle;
  "> • </span>`

    return [
      program?.[programLabel],
      term?.[termLabel],
      year?.[yearLabel],
      curriculum?.[curriculumLabel],
      level?.[levelLabel],
      rotationGroup?.[rotationGroupLabel]
    ]
      .filter(Boolean)
      .join(separator)
  }

  sortStudentsByAcademicNo({ students }: { students: any[] }) {
    students.sort((prev, curr) => {
      const prevAcademicNo = prev?.academicNo || ''
      const currAcademicNo = curr?.academicNo || ''

      const prevNum = parseInt(prevAcademicNo, 10)
      const currNum = parseInt(currAcademicNo, 10)

      if (!isNaN(prevNum) && !isNaN(currNum)) {
        return prevNum - currNum
      }

      return prevAcademicNo.localeCompare(currAcademicNo, undefined, {
        numeric: true,
        sensitivity: 'base'
      })
    })
  }

  getHierarchyType({ type }: { type: string }) {
    if (!type) {
      return
    }

    const hierarchyTypes: { [key: string]: string } = {
      program: this.translateService.instant('common.program'),
      term: this.translateService.instant('assessment.questionBank.term'),
      year: this.translateService.instant('common.year'),
      curriculum: this.translateService.instant('assessment.questionBank.curriculum'),
      level: this.translateService.instant('assessment.questionBank.level'),
      rotationGroup: this.translateService.instant('settings.basicList.basicModule.rotationGroup'),
      course: this.translateService.instant('common.course'),
      module: this.translateService.instant('settings.basicList.basicModule.module'),
      elective: this.translateService.instant('settings.basicList.basicModule.elective'),
      subject: this.translateService.instant('common.subject'),
      topic: this.translateService.instant('common.topic'),
      subTopic: this.translateService.instant('settings.basicList.basicModule.subTopic'),
      slo: this.translateService.instant('settings.basicList.basicModule.slo')
    }

    return hierarchyTypes[type] || type
  }
}

export function cloneDeep(value) {
  if (!value) {
    return
  }

  return JSON.parse(JSON.stringify(value))
}

const getNestedValue = ({ obj, keys }) => {
  return keys.reduce((nestedObj, key) => nestedObj[key], obj)
}

export function calculateSumByProperty(arr, propertyName) {
  let sum = 0
  for (const arrElement of arr) {
    const value = getNestedValue({ obj: arrElement, keys: propertyName.split('.') })
    const num = Number(value)
    if (!isNaN(num)) {
      sum += num
    }
  }

  return sum
}
