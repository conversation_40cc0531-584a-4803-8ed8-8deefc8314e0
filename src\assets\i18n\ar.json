{"alphabets": {"a": "أ", "b": "ب", "c": "ت", "d": "ث", "e": "ج", "f": "ح", "g": "خ", "h": "د", "i": "ذ", "j": "ر", "k": "ز", "l": "س", "m": "ش", "n": "ص", "o": "ض", "p": "ط", "q": "ظ", "r": "ع", "s": "غ", "t": "ف", "u": "ق", "v": "ك", "w": "ل", "x": "م", "y": "ن", "z": "ه"}, "common": {"failedToLoad": "فشل التحميل", "digivalItSolutionsUAE": "دي جي فال لحلول تكنولوجيا المعلومات - الإمارات العربية المتحدة", "copyright": "حقوق النشر", "pvtLtdIndia": "(في شراكة تقنية مع شركة دي جي فال الخاصة المحدودة ، الهند{{appVersion}})", "superAdmin": "مشرف", "add": "إضافة", "edit": "تعديل", "cancel": "إلغاء", "confirm": "تأكيد", "done": "تم", "previous": " السابق", "prev": "السابق", "next": " التالي", "save": " <PERSON><PERSON><PERSON>", "publish": "نشر", "unPublish": "إلغاء النشر", "approve": "موافقة", "reject": "<PERSON><PERSON><PERSON>", "instructions": "تعليمات", "analysis": "تحليل", "settings": "الإعدادات", "notSuitable": "غير مناسب", "suitable": "من<PERSON><PERSON><PERSON>", "correct": "صحيح", "unAnswered": "<PERSON>ير مجاب", "close": "إغلاق", "closeUpperCase": "إغلاق", "wrong": "خطأ", "share": "شارك", "smallExport": "تصدير", "capsExport": "تصدير", "noResultsFound": "لم يتم العثور على نتائج", "showAll": "إظهار الكل", "expandAll": "توسيع الكل", "collapseAll": "تقليص الكل", "another": "آخر", "assign": "تعيين", "noDataFound": "لم يتم العثور على بيانات", "noStudentsFound": "لم يتم العثور على أي طلاب", "Dashboard": "لوحة التحكم،", "personalDetails": "تفاصيل شخصية", "firstName": "الاسم الأول", "firstNameRequired": "الاسم الأول مطلوب", "middleName": "الاسم الأوسط", "lastName": "اسم العائلة", "lastNameRequired": "اسم العائلة مطلوب", "universityEmailID": "البريد الإلكتروني للجامعة", "personalEmailIDRequired": "الب<PERSON>يد الإلكتروني الشخصي مطلوب", "mobileNumber": "رقم الهات<PERSON> المحمول", "phoneNumber": "رقم الهاتف", "mobileNumberRequired": "رقم الهات<PERSON> المحمول مطلوب", "mobileValidation": "يجب ان يكون ٩ ارقام", "dob": "تاريخ الميلاد", "dobRequired": "تاريخ الميلاد مطلوب", "gender": "الجنس", "male": "ذكر", "female": "<PERSON>ن<PERSON>ى", "genderRequired": "الجنس مطلوب", "uploadDocuments": "رفع المستندات", "residentIDNationalID": "رقم الإقامة / الرقم القومي", "attach": "أر<PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "view": "<PERSON><PERSON><PERSON>", "grade": "الدرجة", "grade:": ":  الدرجة", "employeeID": "معرف الموظف", "academicDetails": "التفاصيل الأكاديمية", "employeeIDAlreadyExist": "معرف الموظف موجود مسبقاً", "employeeIDIsRequired": "معرف الموظف مطلوب", "designation": "تعيين", "correctionRequired": "مطلوب التصحيح ", "designationRequired": "التعيين مطلوب", "residentNationalIdRequired": "رقم الإقامة / الرقم القومي مطلوب", "residentNationalIdAlreadyExist": "رقم الإقامة / الرقم القومي موجود مسبقاً", "invalidResidentNationalId": "رقم الإقامة / الرقم القومي منتهية الصلاحية", "nationalIDResidentID": "رقم الإقامة / الرقم القومي ", "pleaseProvideFollowingProfileDetails": "يرجى تقديم التفاصيل التالية للملف الشخصي", "submit": "إرسال", "passportNumberShouldBeAlphanumeric": "يجب أن يكون رقم جواز السفر حرفيًا رقميًا", "passportNumberAlreadyExist": "رقم جواز السفر موجود مسبقاً", "provideNewPassword": "وفر كلمة مرور جديدة", "passwordIsRequired": "كلمة المرور مطلوبة", "reTypePasswordIsRequired": "<PERSON><PERSON><PERSON> كتابة كلمة المرور ", "enterNewPassword": "أدخل كلمة مرور الجديدة", "attach&Upload": "إرفاق و رفع", "uploading": "رفع...", "resetPassword": "إعادة ضبط كلمة المرور", "resetPasswordLinkSent": "تم إرسال رابط إعادة ضبط كلمة المرور إلى", "resetPasswordLinkIsExpired": "انتهت صلاحية رابط إعادة تعيين كلمة المرور", "ok": "حسناً", "invalidEmailId": "البريد الإلكتروني غير صحيح", "emailRequired": "البريد الإلكتروني مطلوب", "assessment": "التقييم", "assessmentStatus": "حالة التقييم", "selectedCourse": "المقرر التعليمي المختار", "selectProgram": "<PERSON><PERSON><PERSON> ال<PERSON><PERSON><PERSON><PERSON><PERSON>ج", "selectCurriculum": "<PERSON><PERSON><PERSON> المنهج", "selectUser": "اختر المستخدم", "userName": "اسم المستخدم", "userId": "معرف المستخدم", "ipAddress": "عنوان IP", "framework": "إطار العمل", "domainTheme": "المجال / الموضوع", "selectYear": "<PERSON><PERSON><PERSON> السنة", "clo": "نتيجة مستوى المنهج", "plo": "نتيجة مستوى المنهج", "plo##": "نتيجة مستوى البرنامج ##", "map": "الخريطة", "addPlo": "إضافة نتيجة مستوى البرنامج", "noRecordsFound": "لا توجد سجلات", "medicalProgram": "البرنامج الطبي", "expand": "وسع", "customize": "خصص", "no": "لا", "yes": "نعم", "archive": "أرشيف", "year": "عام", "addLevel": "<PERSON><PERSON><PERSON> المستوى", "required": "مطلوب", "description": "وصف", "attemptType": "نوع المحاولة", "resultStatusFilter": "مرشح حالة النتيجة", "programYearLevelCourse": "البرنامج / العام / المستوى / المقرر التعليمي", "publishAndShare": "انشر وشارك", "readyToPublish?": "جاهز للنشر؟", "readyToPublish": "جاهز للنشر", "itemRevision": "مراجعة العنصر", "itemReRevision": "إعادة مراجعة العنصر", "manualEvaluation": " التقييم اليدوي", "youDontHaveAccessToEvaluation": "ليس لديك صلاحية للتقييم. يرجى الاتصال بمدير المقرر التعليمي", "youDontHaveAccessToSchedule": "ليس لديك صلاحية للجدولة.", "currentResults": "النتائج الحالية", "history": "تاريخ", "reports": "التقارير", "proceed": "واصل", "reset": "إعادة ضبط", "roles": "الأدوار", "activate": "تفعيل", "modules": "الوحدات", "pages": "الصفحات", "tabsSubTabs": "علامات التبويب / علامات التبويب الفرعية", "createRole": "إنشاء دور", "updateRole": "تحديث الدور", "roleName": "اسم الدور", "successfully": "بنجاح", "deactivate": "تعطيل", "list": "قائمة", "email": "الب<PERSON>يد الإلكتروني", "shouldBe9Digits": " يجب أن يكون {{number}} أرقام", "firstDigitShouldContainNumberBetween5To9": "يجب أن يحتوي الرقم الأول على رقم بين {{start}} إلى {{end}}", "mobileNumberAlreadyExist": "رقم الهاتف المحمول موجود مسبقاً", "exam": "الامتحان", "date": "التاريخ", "time": "الوقت", "marksDetails": "تفاصيل الدرجات", "marksScored": "الدرجات المحرزة", "percentage": "النسبة المئوية", "itemTypeWiseMarks": "نوع العنصر من ناحية الدرجات ", "itemType": "نوع العنصر", "studentType": "نوع الطالب", "totalMarks": "مجموع درجات", "totalTime": "مجموع الوقت", "program": "البرنامج", "programRequired": "البرنامج مطلوب", "addressDetails": "تفاصيل العنوان", "buildingNumberStreetName": "رقم المبنى، اسم الشارع", "buildingNumberStreetNameRequired": "رقم المبنى، اسم الشارع مطلوبان", "regionName": "اسم المنطقة", "regionRequired": "اسم المنطقة المطلوب", "cityName": "اسم المدينة", "cityRequired": "اسم المدينة مطلوب", "districtName": "اسم الحي", "districtRequired": "اسم الحي مطلوب", "zipCode": "الر<PERSON>ز البريدي", "zipCodeIsRequired": "الرمز البريدي مطلوب", "shouldBeValid5Digits": "يجب أن تكون {{number}} أرقام صحيحة", "unitCode": "<PERSON><PERSON><PERSON> الوحدة", "unitCodeRequired": "رمز الوحدة مطلوب", "passwordRequired": "كلمة المرور مطلوبة", "reTypePasswordRequired": "مطلوب إعادة كتابة كلمة المرور", "allAnnouncements": "كل الإعلانات", "latest": "الأحدث", "marks": "الدرجات", "current": " الحالي:", "noResultsPublished": "لم يتم نشر نتائج", "course": "المقرر التعليمي", "day": "اليوم", "sessionTime": "وقت الجلسة", "selectedSessionTime": "وقت الجلسة المحدد", "informationIsCorrect/Wrong": "الرجاء إخبارنا إذا كانت المعلومات التي لدينا عنك صحيحة أو غير صحيحة", "tapCorrect/Wrong": "اختر صحيح أو غير صحيح", "academicNumber": "الرقم الأكاديمي", "academicNo": "الرقم الأكاديمي", "enrollmentYear": "سنة التسجيل", "studentId": "هوية الطالب", "residentNationalIdIsRequired": "رقم الإقامة / الرقم القومي مطلوب", "correctionRequiredForIdentifiedData": "التصحيح مطلوب للبيانات المحددة", "enrollmentYearRequired": "سنة التسجيل المطلوبة", "enter4DigitValidYear": "{{number}} <PERSON><PERSON><PERSON>ل", "academicNumberShouldBe6-9Digits": "{{end}} إلى {{start}} يجب أن يحتوي الرقم الأكاديمي على رقم بين", "academicNumberAlreadyExist": "الرقم الأكاديمي موجود مسبقاً", "academicNumberRequired": "الرقم الأكاديمي مطلوب", "shouldBeLessThanCurrentYear": "يج<PERSON> أن يكون أقل من العام الحالي", "noAssessmentsFound": "لم يتم العثور على تقييمات", "flag": " راية ", "noItem": "لم يتم العثور على عناصر", "search": "ب<PERSON><PERSON>", "upload": "رفع", "update": "تحديث", "to": "إ<PERSON><PERSON>", "from": "من", "send": "إرسال", "sNo": "الرقم التسلسلي", "academicYear": "السنة الأكاديمية", "pending": "قيد الانتظار", "evaluated": "تم التقييم", "individual": "فرد", "theme": "موضوع", "all": "الجميع", "unSelectAll": "إلغاء تحديد الكل", "status": "الحالة", "loading": "جلب البيانات...", "selectCompleteAssessments": "اختر تقييمات كاملة أو اختر عناصر مجمعة حسب الموضوعات من تقييمات سابقة", "none": "لا يوجد", "total": "مجموع", "updatedSuccessfully": "تم التحديث بنجاح", "name": "اسم", "copyTo": "صورة إلى", "role": "دور", "download": "تحميل", "error": "خطأ", "type": "نوع", "items": "عناصر", "student": "طالب", "typeReason": "اكتب السبب*", "browse": "تصفح", "create": "أنشئ", "programs": "برامج", "newSession": "جلسة جديدة", "display": "<PERSON><PERSON><PERSON>", "4Digits": "يجب أن يكون ٤ ارقام", "subject": "المادة", "manage": "إدارة", "digiAssess": "دي جي أسس", "digiClass": "ديجي كلاس", "actions": "إجراءات", "archivedSuccessfully": "تمت أرشفته بنجاح", "import": "استيراد", "export": "تصدير", "wrongTimeFormat": "تنسيق الوقت غير صحيح", "address": "عنوان", "exportData": "تصدير البيانات", "invigilatorsUnassigned": "مراقبين غير معينين", "examManagement": "إدارة الامتحانات", "testCenter360": "مركز الاختبار 360", "assessmentType": "نوع التقييم", "examType": "نوع الامتحان", "assessmentAuthor": "مؤلف التقييم", "testCenters": "مرا<PERSON><PERSON> الامتحان", "noOfStudents": "<PERSON><PERSON><PERSON> الطلاب", "primaryInvigilator": "المراقب الأساسي", "invigilatorWithIndex": "المراقب {{index}}", "invigilator2": "مرا<PERSON>ب ثان", "courseModule": "المقرر / الوحدة", "plannedStudentsMale": "العد<PERSON> المخطط للطلاب الذكور ", "plannedStudentsFemale": "العد<PERSON> المخطط للطالبات الإناث", "plannedMale": "ذكر المخطط", "plannedFemale": "أنثى مخطط لها", "schedulePublishedOn": "تم نشر الجدول في", "studentsImportedMale": "استورد الطلاب ذكور", "studentsImportedFemale": "استورد الطالبات الإناث", "studentsImportedMaleFemale": "استقدام طلاب وطالبات", "importedMale": "ذكر مستورد", "importedFemale": "انثى مستوردة", "importStudents": "الطلاب المستوردون \n ذ/ث", "assessmentPublishedOn": "تم نشر التقييم بتاريخ", "tableView": "عرض الجدول", "itemView": "عرض العنصر", "mappings": "التعيينات", "clos": "نتائج مستوي المنهج", "studPerf": "أداء الطالب", "timesUsed": "عدد المرات المستخدمة", "lastUsed": "آخر استخدام", "enrollYear": "سنة التسجيل", "nationalIdIqamaId": "رقم الإقامة / الرقم القومي", "cardView": "عرض البطاقات", "formatAcademicYear": "{{endYear}} - {{startYear}}", "sessionTiming": "{{startHour}}:{{startMin}} {{startFormat}} إلى {{endHour}}:{{endMin}} {{endFormat}}", "am": "ص", "pm": "م", "studentListPreview": "معاينة قائمة الطلاب", "invigilatorListPreview": "معاينة قائمة المراقبين", "staffName": "اسماء أعضاء هىئة التدريس", "formatTime": "{{hour}}:{{min}}", "noInternetConnection": "الإنترنت غير متوفر. الرجاء التأكد من حالة اتصال الشبكة", "key": "مفتاح", "revision": "مراجعة", "finalized": "تم الانتهاء منه", "scheduler": "المجدول", "createTimetable": "إنشاء جدول زمني", "testCenterCapacity": "سعة مركز الامتحان", "allocated": "المخصصة", "somethingWentWrong": "هناك خطأ ما. الرجاء معاودة المحاولة في وقت لاحق", "errorConnectingServer": "خطأ في الاتصال بالخادم. الرجاء معاودة المحاولة في وقت لاحق", "payloadTooLarge": "الملف الذي تحاول تحميله كبير جدًا. الرجاء تقليل حجم الملف إلى 30 ميجابايت والمحاولة مرة أخرى.", "timeOutError": "لقد نفذ الوقت", "remove": "إزالة", "archiveMessage": "أنت على وشك أن الارشفة", "question": "سؤال", "a": "أ", "a+": "أ+", "b": "ب", "c": "ج", "d": "د", "e": "ه", "f": "ث", "g": "ز", "q": "س", "m": "ذ", "mb": "م.ب.", "ua*": "لاج *", "mg": "ملغ", "itemAuthor": "مؤلف العنصر", "reviewer": "المراجع", "reviewer1": "المراجع1", "subjectExpertReviewer": "خبير مراجعة الموضوع", "medicalEducationist": "أخصائي التعليم الطبي", "createItem": "إنشاء عنصر", "groupCount": "ع<PERSON><PERSON> المجموعة", "finalize": "إنهاء", "notStarted": "لم يبدأ", "published": "منشور", "publishedR": "من<PERSON>و<PERSON> (R)", "onGoing": "جارية", "readyForFinalization": "جاهز للإنهاء", "reviewCompleted": "اكتملت المراجعة", "approved": "موا<PERSON><PERSON> عليه", "selectCourse": "<PERSON><PERSON><PERSON> المق<PERSON>ر", "original": "أصلي", "autoEvaluated": "تم التقييم تلقائيًا", "final": "نهائي", "studentResponses": "<PERSON><PERSON><PERSON><PERSON> الطالب", "version": "الإصدار", "stronglyDisagree": "لا أوافق بشدة", "disagree": "لا اوافق", "neutralDisagree": "لا اوافق بحيادية", "neutral": "م<PERSON>اي<PERSON>", "agree": "موافق", "stronglyAgree": "موافق بشدة", "collegeName": "كلية ابن سينا ​​الوطنية", "collegePlace": "المحجر بجدة", "low": "من<PERSON><PERSON>ض", "medium": "متوسط", "high": "مرتفع", "fetchingCourses": "جاري احضار المقررات", "testCenter": "مركز الامتحانات", "shareReports": " شارك التقارير", "publishSharesSelectedCourses": "نشر ومشاركة التقارير - الدورات المختارة", "select": "المحدد", "answerKey": "م<PERSON><PERSON><PERSON><PERSON> الإجابة", "taxonomy": "التصنيف", "dashboard": " لوحة القيادة", "examSchedule": "جدول الامتحانات", "examResults": "نتائج الامتحان", "facultyManagement": "إدارة أعضاء هيئة التدريس", "forgotPassword": "هل نسيت كلمة السر؟", "signUp": "اشتراك", "pageNotFound": "الصفحة غير موجودة", "studentManagement": "إدارة الطلاب", "mapping": "التعيين", "reportAnalytics": "التقارير والتحليلات", "survey": " الاستطلاع", "facultyVerifyProfileDetails": "التحقق من تفاصيل الملف الشخصي للكلية", "facultyVerifyProfile": "التحقق من الملف الشخصي للكلية", "studentVerifyProfile": "التحقق من الملف الشخصي للطالب", "accountVerify": "التحقق من الحساب", "uploadPermittedStudents": "تحميل الطلاب المسموح لهم", "uploadMorePermittedStudents": "تحميل أكثر من الطلاب المسموح لهم", "assessmentSettingsDashBoard": "التقييم (الإعدادات ولوحة القيادة)", "assignRolesPermissionStaffsAssigned": "تعيين الأدوار والأذونات - طاقم العمل - المعين", "EvaluationSettingsDashBoard": "التقييم (الإعدادات ولوحة القيادة)", "examManagementTestCenterCourseGroup": "إدارة الاختبارات (مركز الاختبار ومجموعة الدورات)", "rolesPermission": "الأدوار والأذونات", "examManagementOnGoingAndPrevious": "إدارة الامتحانات (الجارية والسابقة)", "studentPortal": "بوابة الطالب", "pastExam": "الامتحان السابق", "login": "دخول", "permissionDenied": "! الإذن مرفوض", "domain": "نطاق", "mins": "دقائق", "proctor": "مراقب الامتحانات", "primary": "المراقب الأساسي", "secondary": "الثاني", "students": "الطالب", "examCoordinator": "منس<PERSON> الامتحان", "impact": "تأثير", "alignment": "محاذاة", "textualResponse": "*ا.ن.", "assistantInvigilator": "م<PERSON><PERSON><PERSON><PERSON> المراقب", "selectFilter": "<PERSON><PERSON><PERSON> عامل تصفية", "outcome": "حصيلة", "otherStaffs": "هيئات التدريس الأخري", "zoomIn": "تكبير", "zoomOut": "تصغير", "les": ".م.مد.ل", "las": ".م.مو.ل", "ls": ".<PERSON>.خ", "es": ".م.ع", "rs": ".م.ت", "oe": "س.ه", "month": "شهر", "week": "أسبوع", "hour": "ساعة", "minute": "دقيقة", "second": "ثانيا", "years": "سنين", "months": "شهور", "weeks": "أسابيع", "days": "أيام", "hours": "ساعات", "minutes": "دقيقة", "seconds": "ثانية", "h": "س", "s": "ث", "just_now": "الآن", "ago": "منذ", "o": "م", "r": "ب", "p": "مر", "+p": "مر +", "-p": "مر -", "allPrograms": "جميع البرامج", "allYears": "جميع السنوات", "first_class": "الفصل الأول", "second_class": " الفصل الثاني", "back": "الى الخلف", "saveAsDraft": "ح<PERSON>ظ كمسودة", "draft": "مسودة", "extraMarks": "علامات إضافية", "highestMark": "أعلى علامة في هذا التقييم {{maxMarks}} يمكنك إعطاء درجات إضافية بحد أقصى {{maximumExtraMark}} لجميع الطلاب", "maximumMarks": "تم تحقيق الحد الأقصى للعلامة ، لا يمكن إضافة هذه الدرجات الإضافية لهذا الطالب", "studentList": "قائمة الطلاب", "maxExtraMarks": "الحد الأقصي للعلامات الاضافية", "originalMarks": "العلامات الأصلية", "revisedMarks": "العلامات المراجعة", "apply": "تطبيق", "code": "<PERSON><PERSON><PERSON>", "selectExamDate": "<PERSON><PERSON><PERSON> موعد الامتحان", "expandDatePicker": "توسيع منتقي التاريخ", "scheduled": " مجدول", "notScheduled": "<PERSON>ير مجدول", "selectedDate": "التاريخ المحدد", "overallReports": "التقارير الشاملة", "courseCode": "كو<PERSON> الدورة", "category": "فئة", "selectedAcademicNO": "الأكاديمية المختارة", "chooseDate": "اختر التاريخ", "present": "حا<PERSON>ر", "nextItem": "العنصر التالي", "topic": "عنوان", "startTime": "وقت البدء", "endTime": "وقت النهاية", "targetBenchMark": "الهد<PERSON> المعياري", "batchAttainment": "التحصيل دفعة", "studentImprovisationReport": "تقرير ارتجال الطالب", "staffId": "معرف الموظفين", "studentInMultiSession": "طالب في جلسات متعددة", "selectAssessment": "قم بتحديد تقييم", "selectAttemptType": "حدد نوع التجربة", "defaultAssessmentValues": "قيم التقييم الافتراضية", "absent": "غائب", "supported": "<PERSON><PERSON><PERSON>", "denied": "<PERSON><PERSON><PERSON>", "authenticationFailed": "المصادقة فشلت", "requested": "مطلوب", "ploCloDescription": "{{description}} {{translatedMappingMode}}", "ploCloUserManagementNumber": "{{number}} {{translatedMappingMode}}", "taxonomyAddLevelPlaceHolder": "وصف التصنيف", "addHotspot": "إضافة نقطة اتصال", "editHotspot": "تحرير نقطة اتصال", "common": "مشترك", "mixed": "مختلط", "addLevelPlaceHolder": "وصف المستوي", "allItems": "جميع العناصر", "topics": "المواضيع", "uploadStudents": "تحميل الطلاب", "bulkImports": "استيراد الجملة", "individualStudent": "الطالب الفردي", "importStudentGrouping": "الاستيراد من مجموعة الطلاب", "getDetails": "الحصول على التفاصيل", "show": "يعرض", "diffIndex": "مؤشر الصعوبة", "discrIndex": "مؤشر التمييز", "biserial": "ثنائي", "inference": "استنتاج", "acceptabilityCriteria": "معايير القبول", "acceptabilityCriteriaFor": "معايير القبول لـ", "in": "في", "out": "<PERSON><PERSON><PERSON><PERSON>", "searchByCourse": "بح<PERSON> بالدورة", "downloadTemplate": "تنزيل النموذج", "examCategories": "فئات الامتحان", "conductedOn": "أجريت على", "externalExam": "الامتحان الخارجي", "externalExamDetails": "تفاصيل الامتحان الخارجي", "examCategory": "فئة الامتحان", "selectCategory": "اختر الفئة", "noColumnsSelected": "لم يتم تحديد أعمدة", "changeSettings": "تغيير الاعدادات", "viewAll": "عر<PERSON> الكل", "mapped": "تم تعيينها", "charLimit": "<PERSON><PERSON> <PERSON>ل<PERSON><PERSON><PERSON><PERSON>", "gradePlay": "اللعب الصف", "createAllRequest": "إنشاء كافة الطلبات", "gradeBook": "كتاب الصف", "author": "مؤلف", "continue": "يكمل", "deletedSuccessfully": "حذ<PERSON> بنجاح", "saveAndNext": "حفظ والتالي", "benchmark": "المعيار", "threshold": "عتبة", "goBack": "عُد", "item": "غرض", "taggedItem": "البند الموسومة", "taggedItems": "العناصر الموسومة", "viewMore": "عر<PERSON> المزيد", "addedSuccessfully": "اضي<PERSON> بنجاح", "rebuildReport": "إعادة بناء التقرير", "reRunReportNote": "يرجى إعادة تشغيل التقرير بسبب التغييرات التي تم إجراؤها أثناء إعادة التقييم أو إعادة المراجعة.", "label": "متوسط ​​جميع الـ {{label}}", "component": "عنصر", "allMaleStudents": "جميع الطلاب الذكور", "allFemaleStudents": "جميع الطالبات", "excludeAll": "استبعاد الكل", "includeAll": "تضمين الكل", "exclude": "استبعاد", "include": "يشمل", "applyAndSave": "تطبيق وحفظ", "filter": "مر<PERSON>ح", "note": "ملاحظة", "onlyNumbersAllowed": "مسموح بالأرقام فقط", "showPdf": "عرض قوات الدفاع الشعبي", "hidePdf": "إخفاء قوات الدفاع الشعبي", "viewAttachedPdf": "عرض قوات الدفاع الشعبية المرفقة", "optional": "(خياري)", "youDontHavePermission": " ليس لديك إذن للوصول إلى هذه الصفحة", "moved": "تم نقله", "windows": "Windows", "mac": "<PERSON>", "iPad": "IPad", "examMode": "وضع الامتحان", "questions": "الأسئلة", "duplicate": "مكرر", "unassigned": "غير معين", "stem": "ينبع", "examAndAssessmentType": "نوع الامتحان والتقييم", "dateAndSession": "التاريخ ووقت الجلسة", "selectExamDetail": "تفاصيل الامتحان المحدد", "bySelf": "بنفسي", "other": "آخر", "confirmSelection": "تأكيد الاختيار", "selectStudents": "<PERSON><PERSON><PERSON> الطلاب", "assignEvaluator": "تعيين مقيم", "courseAuthor": "مؤلف الدورة", "otherGeneralUser": "مستخدم عام آخر", "chooseUser": "اختر المستخدم", "evaluatedMark": "علامة التقييم", "dateAndTime": "التاريخ والوقت", "allFieldsMandatory": "جميع الحقول إلزامية", "examPreference": "تفضيلات الامتحان", "modeOfConduction": "طريقة التوصيل", "systemRequirement": "متطلبات النظام", "examAuthentication": "مصادقة الامتحان", "browser": "المتصفح", "headSetWithMic": "سماعة رأس مع ميكروفون", "headSetWithOutMic": "سماعة رأس بدون ميكروفون", "camera": "آلة تصوير", "fullScreenMode": "وضع ملء الشاشة", "generalUser": "المستخدم العام", "examStartTime": "وقت بدء الامتحان", "enterDigits": "أد<PERSON>ل الأرقام", "enterInstruction": "أد<PERSON>ل التعليمات", "reAllocateStudents": "إعادة توزيع الطلاب", "or": "أو", "addNewType": "أضف نوع {{ type }} جديد", "nameOfType": "اسم {{ النوع }}", "enterName": "أ<PERSON><PERSON><PERSON> الاسم", "enterCode": "<PERSON><PERSON><PERSON><PERSON> الرمز", "startsBy": "يبد<PERSON> بواسطة", "allocateStudents": "تخصيص الطلاب", "digiBrowser": "ديجي براوزر", "normalBrowser": "المتص<PERSON><PERSON> العادي", "startExam": "بد<PERSON> الامتحان", "goToExam": "الذها<PERSON> إلى الامتحان", "refresh": "تحديث", "confirmDelete": "تأكيد الحذف", "yesDelete": "نعم ، احذف", "noKeepIt": "لا ، احتفظ به", "studentAlreadyAssigned": "تم تعيين الطالب بالفعل", "changeItemType": "تغيير نوع العنصر", "areYouSureYouWantToChangeItemType": "هل أنت متأكد أنك تريد تغيير نوع العنصر؟", "notProvided": "لم يتم توفيره", "upcoming": "القادمة", "clickToUpload": "انقر هنا لتحميل الطلاب", "searchByAcademicNo": "البحث حسب الرقم الأكاديمي...", "pleaseNote": "ير<PERSON>ى الملاحظة", "noGoBack": "لا ، عد", "table": "الجدول", "unSupportedAttachmentType": "نوع المرفق غير مدعوم", "unSupportedAudioType": "نوع الصوت غير مدعوم ، يدعم فقط wav", "unSupportedVideoType": "نوع الفيديو غير مدعوم ، يدعم فقط mp4 و webm", "notClosed": "<PERSON>ير مغلق", "notConducted": "لم يتم اجراءه", "testCenterOwnedDevice": "جهاز مملوك لمركز الاختبار", "clickOnceCompleted": "انقر مرة واحدة للانتهاء", "virtualTestCenter": "مركز الاختبار الافتراضي", "requestedItems": "العناصر المطلوبة", "markAsCorrected": "وضع علامة على أنه تم تصحيحه", "changeAuthor": "تغيير المؤلف", "genericConfig": "تكوين عام", "default": "افتراضي", "commentSection": "قسم التعليقات", "goToDashboard": "انتقل إلى لوحة التحكم", "uploadNewItems": "تحميل عناصر جديدة", "uploadNewItem": "تحميل عنصر جديد", "alertMessage": "رسالة تنبيه", "requestStatus": "حالة الطلب", "replying": "جارٍ الرد...", "withdraw": "س<PERSON><PERSON>", "enterText": "أدخل النص هنا...", "today": "اليوم", "start": "بدء", "allLogs": "جميع السجلات", "viewLog": "عرض السجل", "logs": "سجلات", "safeExamBrowser": "متص<PERSON><PERSON> الامتحان الآمن", "tcod": "رمز مركز الاختبار", "assignee": "المكلف", "answers": "الإجابات", "studentMarks": "علامات الطالب", "viewCourses": "انقر لعرض الدورات", "viewExams": "عرض الامتحانات", "availableSlots": "الفتحات المتاحة", "availableSlotsFor": "الفتحات المتاحة ل", "scheduledExams": "الامتحانات المقررة", "fileSizeExceeded": "يجب أن يكون حجم الملف ≤ {{size}} MB", "importExcel": "استيراد Excel"}, "assessment": {"dashboard": {"dashboardTitle": "عنوان لوحة التحكم", "itemManagement": "إدارة العناصر", "attempt": "محاولة", "itemsAdded": "تمت إضافة العناصر", "newItems": "عناصر جديدة", "oldItems": "العناصر القديمة", "createdBy": "انش<PERSON> من قبل", "requestedBy": "بتوصية من", "rejectedBy": "مرفوض من قبل", "release": "ي<PERSON><PERSON><PERSON>", "nameOf": "اسم", "times": "مرات", "used": "استخدم", "last": "اخير", "timesUsed": "عدد مرات الاستخدام", "noOfItems": "<PERSON><PERSON><PERSON> العناصر", "noOfTask": "<PERSON><PERSON><PERSON> المهام", "stem": "رأس السؤال", "stems": "رؤوس الأسئلة", "selectUser": "اختر المستخدم", "selectItemAuthor": "اختر مؤلف العنصر", "selectReviewer": "اختر المراجع", "oldItemQuestion": "العناصر القديمة المطابقة", "pastAssessmentTopic": "اختر عناصر من تقييمات سابقة في المواضيع المختارة", "createAssessments": "إنشاء تقييمات", "itemStatus": "العنصر وحالته", "assessmentsStatus": "التقييمات ووضعها", "dueDate": "تاريخ الاستحقاق", "createAssessment": "إنشاء تقييم", "selectItems": "اختيار العناصر", "monitorItemStatus": "مراقبة حالة عنصر", "reviewPublish": "مراجعة ونشر", "createReview": "إنشاء / مراجعة", "inReview": "قيد المراجعة", "sentToReviewer": "تم إرساله إلى المراجع", "prepareForApproval": "التحضير للموافقة", "reviseForApproval": "مراجعة للموافقة", "inApproval": "قيد الموافقة", "requestFulfilled": "تم تنفيذ الطلب", "review": "مراجعة", "reviewed": "تمت المراجعة", "inCorrection": "في التصحيح", "approve": "الموافقة", "searchItemAuthor": "بحث عن مؤلف العنصر", "reviewsRequired": "المراجعات / مطلوبة", "reasonForDiscard": "السبب في التجاهل", "confirmationRequired": "التأكيد مطلوب", "areYouSureWantToSendThisItem": "هل أنت متأكد من أنك تريد إرسال هذا العنصر؟", "onceSendCannotEditDuringReview": "بمجرد إرسالها ، لا يمكنك تحريرها أثناء مرحلة المراجعة.", "clickYesToProceed": "انقر فوق 'نعم' للمتابعة", "clickViewEquation": "انقر لعرض المعادلة", "modificationRestricted": "التعديل مقيد", "createItemsBySelf": "إنشاء العناصر بنفسك", "areYouGoingToCreateItemsByYourself": "هل ستقوم بإنشاء عناصر بنفسك؟"}, "questionBank": {"questionBank": "بنك الأسئلة", "itemsHistory": "تاريخ العناصر", "itemsHistoryNotAvaliable": "تاريخ العنصر غير موجود", "allAssessment": "كل التقييمات", "allReviewedItemBatches": "كافة دفعات العناصر التي تم مراجعتها", "pastExam": "الامتحان السابق", "term": "فصل دراسي", "curriculum": "منهج دراسي", "level": "مستوى", "rotationGroup": "مجموعة التناوب", "rotationalGroup": "مجموعة تناوبية", "examFilters": "مرشحات الامتحان", "academicYear": "السنة الأكاديمية", "courseFilters": "مرشحات المقرر", "created": "أُنشئ", "on": "في", "reviewed": "تمت مراجعته", "dateSessionFilter": "مرشح التاريخ والجلسة", "session": "جلسة", "cancelled": "ألغيت", "importTopicItems": "استيراد عناصر الموضوع", "noOfQuestions": "ع<PERSON><PERSON> الأسئلة"}, "createItem": {"selectAndRequestItems": "تحديد وطلب عناصر", "requestItems": "طلب (جديد ومراجعة) عناصر", "createNewAssessment": "طلب تقييم جديد", "itemsGrouped": "طلب جديد أو اختيار قديم من عناصر مجمعة حسب الموضوعات", "extractFullAssessment": "استخراج التقييم كامل", "allSelectedItems": "كافة العناصر المحددة", "selectedRequired": "محدد / مطلوب", "old": "قديم", "new": "جديد", "reason": "سبب", "reAttach": "إعادة إرفاق", "move": "نقل", "moveTo": "انقل الي", "resetSection": " إعادة ضبط القسم", "section": "قسم", "requestCorrection": "طل<PERSON> التصحيح", "notAllowedToEdit": "غير مسموح بالتحرير نظرًا لأن العنصر مطلوب للتصحيح، يمكنك التعديل بمجرد إجراء تصحيح العنصر بواسطة مؤلف العنصر", "replace": "استبدال", "taxonomy": "التصنيف", "viewAllItems": "عرض كل العناصر", "rejected": "مرفو<PERSON>", "respond": "الرد", "createNew": "إنشاء جديد", "addTwoItems": "أض<PERSON> <PERSON><PERSON>ى الأقل عنصرين إلى النوع المختلط الموجود أولاً", "requestNew": "+ طل<PERSON> جديد", "attachNew": "إر<PERSON><PERSON><PERSON> جديد", "invalidRequest": "طلب غير صالح", "requestedCorrection": "تص<PERSON><PERSON><PERSON> مطلوب", "noItems": "لا توجد عناصر", "addAnItem": "أضف عن<PERSON>ر إلى الموضوع عن طريق الضغط على الزر أعلاه", "addNewOldItem": "أضف عن<PERSON>ر جديد/قديم إلى الموضوع عن طريق الضغط على الزر أعلاه", "assessmentInformation": "معلومات التقييم", "aboutToArchive": "{{name}} أنت على وشك أرشفة التقييم", "archive": "أرشيف", "noOfItems": "<PERSON><PERSON><PERSON> العناصر", "archiveFile": "أرش<PERSON> الملف ", "finalizeAssessment": "إنهاء التقييم", "groupedByItemType": "مجمعة حسب نوع العنصر", "groupedByNew": "مجمعة حسب الأحدث", "groupedByOld": "مجمعة حسب الأقدم", "groupedByStatus": "مجمعة حسب الحالة", "completeAssessmentItems": "جميع عناصر التقييم ", "totalReceived": "إجمالي المستلم", "completeAssessment": "التقييم كامل", "examInstructions": "تعليمات الامتحان", "examDetails": "تفاصيل الامتحان", "examPeriod": "فترة الامتحان", "noOfSection": "<PERSON><PERSON><PERSON> الأقسام", "totalItems": "مجموع العناصر", "instructions": "تعليمات ترتيب الإجابة", "sectionSequence": "ترتيب القسم", "restricted": "مقي<PERSON>", "fixedOrder": "إجابة الأقسام بترتيب ثابت", "needToSubmit": "يجب إرسال إجابة السؤال الحالي قبل التقدم", "needToSubmitAnswers": "تحتاج إلى إرسال الإجابات خلال المدة المحددة، تخطي الوقت إلى السؤال التالي", "unrestricted": "<PERSON>ي<PERSON> مقيد", "attendSection": "جاوب على الأقسام بأي ترتيب خلال فترة الامتحان.", "itemsBooked": "يمكن وضع إشارة مرجعية على العناصر و الرجوع إليها لاحقاً.", "submitAnswers": "تحتاج إلى تسليم الإجابات خلال وقت الامتحان المحدد", "itemSequence": "ترتيب العنصر", "attendItemsFixedOrder": "جاوب على العناصر بالترتيب المعطى.", "attendItemsAnyOrder": "جاوب على العناصر بأي ترتيب", "duringExams": "ملاحظة: أثناء الامتحانات", "internetNotAvailable": "لن يتوفر الإنترنت", "noSoftwareAccess": "لن يمكن الوصول إلى جميع البرامج الأخرى.", "readyForApproval": "جاهز للموافقة", "item": "عنصر", "publishAssessment": "نشر التقييم؟", "unPublishAssessment": "هل تريد إلغاء نشر التقييم؟", "unPublishAssessmentContent": "هل أنت متأكد أنك تريد إلغاء نشر التقييم للدورة؟", "paperBasedQuestionAlert": "إذا كان السؤال يتطلب إجابة مكتوبة/ورقية، فلن تعمل ميزة التبديل العشوائي مع تلك الأسئلة المحددة، مما يعني أنه لن يتم خلط هذه الأسئلة عشوائيًا.", "sureWantToPublish": " هل أنت متأكد من أنك تريد نشر التقييم للمقرر؟ {{courseName}}", "itemsRequested": "العناصر المطلوبة", "fileAttachment": "هذا العنصر له مرفق", "drawTheHotspot": "ارسم نقطة الاتصال", "singleAnswerPoints": "نقاط إجابة واحدة", "uploadImage": "تحميل الصور", "markTheCorrectAnswer": "اشر على الاجابة الصحيحة", "clickToMark": "انقر لوضع علامة", "clearPoints": "نقاط واضحة", "viewPoints": "عرض النقاط المحددة", "singleHotspot": "نقطة اتصال واحدة", "chooseItemsCount": "ا<PERSON><PERSON><PERSON> ع<PERSON><PERSON> العناصر", "addItems": "إضافة عناصر", "mixedItemType": "نوع العنصر المختلط", "counts": "العد", "allowIAtoAlter": "السماح لمؤلف العنصر بتغيير أنواع العناصر 'التهم' ض<PERSON><PERSON> الحد الأقصى للأعداد", "IACanChange": "*يمكنك تغيير أعداد نوع العنصر ض<PERSON>ن الحد الأقصى لعدد العناصر", "IACannotChange": "*لا يمكنك تغيير أعداد نوع العنصر ضمن الحد الأقصى لعدد العناصر", "itemTypeChanged": "تم تغيير نوع العنصر بنجاح", "itemsLinked": "تم ربط العناصر بنجاح", "unlinked": "تم إلغاء الارتباط بنجاح", "linkItem": "عنصر الارتباط", "allowItemLinking": "السماح بربط العناصر", "itemShow": "عرض العنصر للطلاب يعتمد على الرابط", "clickToLink": "انقر للارتباط", "backToAssessment": "العودة إلى صفحة مؤلف التقييم", "viewChangeScheduling": "عرض / تغيير الجدولة", "generatedByHEBA": "تم إنشاؤه بواسطة HEBA.ai", "overWritten": "(تم الكتابة فوقه)", "clearItemTitle": "تأ<PERSON>يد مسح العنصر", "clearItemContent": "هل أنت متأكد أنك تريد مسح هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.", "showHotspotToStudents": "إظهار النقاط الساخنة للطلاب", "disableWeightage": "قم بتعطيل الترجيح لتمكين هذه الطريقة", "provideFullMark": "تقديم العلامة الكاملة إذا", "any": "أي", "answerCorrect": "الإجابات (الإجابات) صحيحة", "addWeightage": "<PERSON><PERSON><PERSON> الوزن", "addWeightageAnswerKey": "أضف وزنًا إلى مفتاح الإجابة", "differentMarks": "* يمكن لكل النقاط الساخنة أن تحمل علامات مختلفة", "chooseAnswerKey": "اختر مفتاح الإجابة", "weightage": "الوزن %", "targetPassMark": "علامة تمرير الهدف (%)", "targetBenchMark": "المؤشر المستهدف (%)", "difficultyIndexDiscriminationIndex": "لحسابات مؤشر الصعوبة ومؤشر التمييز", "changeItemType": "تغيير نوع العنصر؟", "changingItemTypeStage": "قد يؤدي تغيير نوع العنصر في هذه المرحلة إلى إزالة البيانات التي تم إدخالها مسبقًا بشكل دائم", "areYouSureYouWantToChange": "هل أنت متأكد أنك تريد التغيير؟", "unlinkToChange": "قم بإلغاء ربط السؤال لتغيير نوع العنصر", "selectAnswerKey": "<PERSON><PERSON><PERSON> مفتاح الإجابة", "noOfCorrectAnswerKey": "عد<PERSON> مفاتيح الإجابات الصحيحة", "addItemToExisting": "أضف عنصرًا إلى نوع العنصر المختلط الموجود أولاً", "maxWeightage": "الوزن الأقصى يجب أن يكون ١٠٠", "totalCorrectAnswers": "إجمالي الإجابات الصحيحة لا يجب أن يساوي أو يزيد عن إجمالي الخيارات.", "imageUpload": "قم بتحميل الصورة وحدد منطقة معينة كإجابة.", "clearAll": "ا<PERSON><PERSON><PERSON> الكل", "hotspotsAdded": "تمت إضافة النقاط الساخنة", "hotspotQuestion": "سؤال النقطة الساخنة", "attachedItem": "العنصر المرفق"}, "autoGenerationByHeba": {"chooseTheRandomness": "اختر العشوائية لتوليد العنصر*", "chooseTheRandomnessToolTip": "في جوهرها، يمكن أن تؤدي العشوائية الأعلى في إنتاج الأسئلة إلى المزيد من الاستكشاف والأسئلة المحتملة خارج الصندوق، بينما توليد العشوائية الأدنى يولد أسئلة أكثر تنبؤًا ومركزة مبنية على السياق المقدم"}, "selfItemCreate": {"createNewItem": "إنشاء عناصر جديدة", "createNewItemUnderMyTopics": "إنشاء عناصر جديدة - ضمن موضوعاتي", "finalizeAll": "إنهاء الكل", "sendForApproval": "إرسال للموافقة", "sendForReview": "إرسال للمراجعة", "createNew": "إنشاء جديد", "turnOffCreateItemsBySelfTurnOffWarning": "إذا قمت <b>بإيقاف تشغيل \"الإنشاء بنفسك\"،</b> فسيتم <b>نقل العناصر الموجودة إلى بنك الأسئلة، وإزالتها من هنا.</b>", "turnOffCreateItemsBySelfTurnOffInfo": "هل ترغب في متابعة إيقاف تشغيل خيار \"الإنشاء بنفسك\"؟", "turnOffCreateItemsBySelfTurnOnWarning": "إذا قمت <b>بتشغيل \"الإنشاء بنفسك\"،</b> فسيتم <b>نقل العناصر الموجودة إلى بنك الأسئلة، وإزالتها من هنا.</b>", "turnOffCreateItemsBySelfTurnOnInfo": "هل ترغب في متابعة تشغيل خيار \"الإنشاء بنفسك\"؟"}, "itemCreate": {"question": "سؤال", "notifyVia": "إعلام مؤلفي العناصر عبر", "subjectName": "{{subjectName}} الموضوع", "topicsName": "{{topicName}} الموضوعات", "assignedItemAuthor": "مؤلف العنصر المعين", "newItems": "عناصر جديدة", "reviewOld": "مراجعة قديمة", "totalRequired": "الإجمالي المطلوب", "noTopicsHasMultipleAuthors": "لا يوجد موضوع له مؤلفون متعددون!", "item": "عنصر", "selection": "اختيار", "single": "مُفرَد", "addNewChoice": "إضافة خيار جديد", "qNo": "رقم السؤال", "selectionSingle": "الاختيار: مفرد", "choices": "اختيارات", "addChoiceAnswerKey": "أضف خيارًا لتعيين مفتاح الإجابة", "reply": "الرد", "clickOnQuestion": "انقر فوق مربع نص السؤال لاختيار أو عرض ", "answerKey": "م<PERSON><PERSON><PERSON><PERSON> الإجابة", "feedback": "تعليقات", "feedbackForCorrectAnswers": "تعليقات على الإجابات الصحيحة", "enterFeedbackCorrectAnswers": "أدخل تعليقات على الإجابات الصحيحة (اختياري)", "feedbackForWrongAnswers": "تعليقات على الإجابات الخاطئة", "enterFeedbackWrongAnswers": "أدخل تعليقات على الإجابات الخاطئة (اختياري)", "true": "صحيح", "false": "<PERSON>ا<PERSON><PERSON>", "questionTheme": "موضوع السؤال", "leadIn": "مقدمة", "addPrompt": "+ إضافة توجيه", "addQuestion": "+ إضافة سؤال", "launch": "أطلق", "itemHistoryNotAvailable": "تاريخ العنصر غير متوفر", "itemsDetails": "تفاصيل العناصر", "initiatedBy": " مبدوء بـ", "assessmentAuthor": "مؤلفو التقييمات", "itemAuthor": "مؤلف العنصر", "subjectExpertReviewer": "خبير مراجعة الموضوع", "medicalEducationist": "أخصائي التعليم الطبي", "qualityAssuranceReviewer": "مراجع ضمان الجودة", "createdOn": "تم إنشاؤها", "lastOpened": "آخر فتح", "lastModified": "آ<PERSON>ر تعديل", "assessmentDetails": "تفاصيل التقييم", "courseName": "اسم المقرر ", "topicName": "اسم الموضوع", "itemMapping": "تعيين العنصر", "addAQuestion": "أضف سؤالاً", "courseLevelOutcomes": "مخرجات مستوى المقرر", "noClosMapped": "عدد مخرجات مستوى (نتائج مستوي المنهج) المقرر التي تم تعيينها", "noTaxonomyMapped": "لم يتم تعيين التصنيف", "addTaxonomyInSettings": "أضف تصنيف في الإعدادات", "chooseAnyOne": "اختر أي واحد", "chooseAnyAnswers": "اختر أي {{noOfAnswerKeys}} إجابات", "chooseAnswer": "اختر إجابة", "chooseTheCorrectAnswer": "اختر الإجابة الصحيحة", "createNew": "إنشاء عنصر جديد", "prompt": "موجه", "enterChoice": "{{index}} أدخل الاختيار", "pickNewItemFromQuestionBankToAttach": "اختر عنصرًا جديدًا من بنك الأسئلة لإرفاقه", "newItemsThatMatch": "العناصر الجديدة المطابقة", "noViewAccess": "لا يوجد وصول للعرض لدبك", "subject": "المادة", "enterReply": "أ<PERSON><PERSON><PERSON> الرد هنا", "viewAttachment": "عرض المرفق", "provideShortAnswer": "قدم إجابة قصيرة أدناه", "typeAnswer": "اكتب إجابتك هنا", "typeAnswerHere": "اكتب إجابتك هنا. يجب ألا تتجاوز الإجابة <1000> حرفًا", "answerShouldNotExceedLimit": "يجب ألا تتجاوز الإجابة <1000> حرفًا.", "selectAnswer": "اختر إجابة", "matchTheFollowing": "قم بمطابقة ما يلي مع الاختيارات الصحيحة", "maxPromptsReached": "تم الوصول إلى الحد الأقصى لعدد المطالبات", "hotspotDeletePopupTitle": "حذف النقاط الساخنة؟", "hotspotDeletePopupMessage": "سيتم فقدان جميع نقاط الاتصال الفعالة ولا يمكن التراجع عن هذا الإجراء، هل أنت متأكد من رغبتك في حذف النقاط الفعالة؟", "closeHotspotPopupTitle": "تم العثور على تغييرات غير محفوظة", "closeHotspotPopupMessage": "يحتوي سؤال نقطة الاتصال هذا على تغييرات غير محفوظة، يرجى مراعاة الحفظ.", "clearAllHotspotTitle": "مسح كافة النقاط الساخنة؟", "clearAllHotspotMessage": "سيتم فقدان جميع نقاط الاتصال المميزة ولا يمكن التراجع عن هذا الإجراء، هل أنت متأكد من رغبتك في مسح نقاط الاتصال؟", "showHotspotToStudentsTitle": "عرض النقاط الساخنة للطلاب؟", "showHotspotToStudentsContent": "يمكن أن يؤدي استخدام هذه الطريقة إلى فقدان إعدادات الوزن في حالة تكوينها", "showHotspotToStudentsInfo": "إذا تم تمكينه، تحقق من الإجابات الصحيحة وحدد الوزن", "canApplyWeightageTitle": "إيق<PERSON><PERSON> التبديل؟", "clickAddHotspot": "*انقر فوق <b>“إضافة نقطة اتصال”</b> لإضافة منطقة محددة على الصورة كإجابة.", "dragAndDrop": "اسحب وأفلت ملفك للتحميل", "uploadFile": "تحميل ملف", "deleteImageHotspot": "حذف الصورة والنقاط الساخنة؟", "imageHotpotReset": "سيتم حذف الصورة وجميع النقاط الساخنة المُنشأة نهائياً، ولن يمكن التراجع عن هذا الإجراء.", "deleteImageHotspotContent": "هل أنت متأكد من أنك تريد حذف هذه الصورة مع النقاط الساخنة المعلمة؟", "resetWeightage": "إعادة تعيين الوزن", "courseRequiredForAddItem": "يرجى تحديد دورة قبل إضافة العنصر", "duplicateOptionText": "غير مسموح بتكرار الخيار", "myQuestions": "أسئلتي", "myReview": "مراجعتي"}, "itemRequest": {"reviewOldItems": "مراجعة العناصر القديمة -", "createNewItems": "إنشاء عناصر جديدة -", "sendToAssessmentAuthor": "ارسل إلى مؤلف التقييم", "sentToAssessmentAuthor": "تم إرساله إلى مؤلف التقييم", "reviewedRequired": "تمت المراجعة / مطلوب ", "oldItems": "العناصر القديمة", "requestedNewItems": "العناصر الجديدة المطلوبة", "createdRequired": "تم إنشاءه / مطلوب", "enterReason": " اكتب سبب", "itemRequested": "العنصر المطلوب", "reviewItems": "مراجعة العناصر", "enterCommentsHere": "أدخل التعليقات هنا...", "discardFaultyitem": "تجاهل - العنصر المعيب", "enterTheReason": "<PERSON><PERSON><PERSON><PERSON> السبب", "confirmItem": "تأكيد استخراج العنصر", "confirmAssessment": "تأكيد استخراج التقييم", "discardSelected": "يحتوي العنصر المح<PERSON>د على {{item}} عنصر. هل تريد تجاهل هذه العناصر واستبعادها من النسخة المستخرجة؟", "assessmentDiscard": "يحتوي التقييم على {{item}} عنصر. هل تريد تجاهل هذه العناصر واستبعادها من النسخة المستخرجة؟", "includeDiscard": "لتضمين جميع العناصر (بما في ذلك العناصر المجازف بها) في النسخة المستخرجة، انقر على تأكيد.", "removeDiscard": "إزالة - العنصر المجازف به", "removeDiscardItem": "هل أنت متأكد من رغبتك في إزالة العنصر المجازف به؟", "createNewItem": "إنشاء عنصر جديد", "discardItem": "تجاهل العنصر", "useItem": "هل أنت متأكد أنك تريد استخراج العناصر المهملة؟", "noOfTimesDiscard": "عدد مرات التجاهل"}, "stepWizard": {"inReview": "في المراجعة", "corrections": "تصحيحات", "inApproval": "في الموافقة", "confirmation": "تأكيد"}, "sentReview": {"itemSentForReview": "تم إرسال العنصر للمراجعة", "chooseAlternateReplacement": "اختر احتياطي كبديل", "attachPublishedItem": "إرفاق العنصر المنشور", "moveRequestCreateNew": "نقل الطلب لإنشاء جديد", "headerName": "إدارة العنصر> مؤلف العنصر> للمراجعة", "courseRequiredForReviewer": "يرجى اختيار دورة قبل الإرسال إلى المراجع"}, "itemsReview": {"allItems": "جميع العناصر", "pendingItems": "العناصر في الانتظار", "reviewedItems": "العناصر التي تم مراجعتها", "sendToItemAuthor": "ارسل إلى مؤلف العنصر", "sendToAA": "أرسل إلى مؤلف التقييم", "sendToME": "{{ME}} ارسل إلى", "itemsReviewInSelectedAssessment": "عناصر للمراجعة في التقييم المحدد"}, "itemForReview": {"itemForReview": "العناصر للمراجعة:", "sendForCorrection": "ارسل للتصحيح", "checkList": "قائمة التدقيق ", "noteChangesAutoSaved": "ملاحظة: سيتم حفظ التغييرات تلقائيًا", "textEmptyError": "يجب ألا يكون حقل الإدخال فارغًا", "texonomyMappingEmptyError": "لا يمكن إزالة جميع ربط التصنيف الضريبي", "attachmentError": "لا يمكن حذف المرفق"}, "questionByAssessment": {"questionBank": "بنك الأسئلة", "archiveAssessment": "أرش<PERSON> التقييم", "archiveThisAssessment": "هل أنت متأكد من أرشفة التقييم؟", "questionByAssessment": "سؤال عن طريق التقييم", "questionByTopic": "سؤال عن طريق الموضوع", "archiveItem": "أرشفة العنصر", "archiveItemsConfirmation": "عنصر/عناصر {{count}}  هل أنت متأكد أنك تريد أرشفة", "confirmArchiveRemainingItems": "قد تكون بعض العناصر مؤرشفة بالفعل. هل تريد المتابعة في أرشفة العناصر المتبقية؟", "archiveAllItemsConfirmation": "هل أنت متأكد من أنك تريد أرشفة جميع العناصر؟", "alreadyArchivedAllItems": "جميع العناصر المحددة مؤرشفة بالفعل.", "alreadyArchivedAllItem": "العنصر المحدد مؤرشف بالفعل.", "createItemOrReview": "إنشاء / مراجعة العنصر"}, "assessmentCheckList": {"checkList": "قائمة تدقيق", "done": "تم", "notDone": "لم يتم ", "notApplicable": "لا ينطبق", "selectItem": "حدد عنصرًا لعرض قائمة المراجعة الخاصة به"}, "assessmentCardPopup": {"similarAssessmentIsFoundIn": "تم العثور على تقييم مماثل في", "wouldYouLikeToUseThis?": "هل تريد استخدام هذا؟", "if": "لو", "proceedExisting": "يمكنك المتابعة باستخدام التقييم الحالي ونشره ", "assessmentCreation": "سيتم إنشاء تقييم جديد ولن يتم نشر التقييم الحالي", "impact": " وافق إذا فهمت التأثيرات المذكورة أعلاه", "permanentlyAssign": " ستؤدي المتابعة إلى تعيينك بصفة دائمة بصفتك {{role}} لهذا التقييم "}, "assessmentCardGroup": {"assessments": "التقييمات", "showAll": "عرض جميع المهام", "ongoingWithMe": "جارٍ - معي", "withItemAuthor": "مع مؤلف العنصر", "sentToAA": "تم الإرسال إلى مؤلف التقييم", "sentToIA": "تم الإرسال إلى مؤلف العنصر", "itemAuthorCompleted": "تم إكمال مؤلف العنصر", "inAssessmentAuthor": "في مؤلف التقييم", "sentToItemAuthor": "تم إرساله إلى مؤلف العنصر", "withdrawnItem": "تم سحب العنصر", "fulfilledItemAuthor": "تم التنفيذ - من مؤلف العنصر", "ongoingItems": "العناصر الجارية", "inReviewSubjectExpert": "قيد المراجعة ({{subjectExpertReviewerRoleName}})", "reviewCorrections": "مراجعة / تصحيحات", "inReviewMedicalEducationist": "قيد المراجعة ({{medicalEducationistRoleName}})", "approveCorrections": "الموافقة / التصحيحات", "inApprovalMedicalEducationist": "قيد الموافقة ({{medicalEducationistRoleName}})", "sentAssessmentAuthor": "مرسل (إلى مؤلف التقييم)", "ongoingInReview": "قيد المراجعة", "sentItemAuthorForCorrections": "مرسل الي (مؤلف العنصر) للتصحيحات", "reviewAfterCorrections": "المراجعة بعد التصحيحات"}, "extractBulkOldQuestions": {"oldAssessmentsThatMatch": " التقييمات القديمة المطابقة", "extract": "استخرج", "assessmentAuthors": "مؤلفو التقييمات", "studentPerformance": "اداء الطالب", "replace": "استبدال"}, "createAssessmentPopup": {"createAssessment": "إنشاء تقييم جديد", "coursesList": "قائمة المقررات التعليمية", "dueDate": "الموعد المقرر (اختياري)", "noTopicsFound": "لم يتم العثور على موضوعات"}, "createNewAssessmentPopup": {"requestItem": "اطلب من مؤلفي العناصر إنشاء عناصر جديدة أو مراجعة العناصر القديمة", "allTopics": "جميع الموضوعات", "topicMultipleAuthor": "مواضيع من مؤلفين متعددين", "itemsDueDate": "الموعد المقرر للعناصر", "sendRequest": "ارسل طلب", "show": "<PERSON><PERSON><PERSON> "}, "status": {"alternate": "بديل", "approvedMe": "({{ME}}) موافقة", "archived": "مؤرشف", "correctedMe": "({{ME}}) مصحح", "correction": "تصحيح", "correctionFromMe": "{{ME}} تصحيح من", "correctionFromSe": "{{SE}} تصحيح من", "discard": "تجاهل", "expired": "منتهية الصلاحية", "finalizedWr": "تم الانتهاء منه (ب.م.)", "forConfirmation": "للتأكيد", "forReview": "للمراجعة", "fulfilled": "تم الوفاء", "imported": "مستورد", "invalidRequest": "طلب غير صالح", "inApprovalHyphen": "قيد الموافقة", "inApprovalMeHyphen": "({{ME}}) في الموافقة", "inApproval": "في الموافقة", "inApprovalMe": "({{ME}}) في الموافقة", "inReviewHyphen": "قيد المراجعة", "inReviewMeHyphen": "({{ME}}) في مراجعة", "inReviewSeHyphen": "({{SE}}) في مراجعة", "inReview": "قيد المراجعة", "inReviewMe": "({{ME}}) في مراجعة", "inReviewSe": "({{SE}}) في مراجعة", "newRequest": "<PERSON><PERSON><PERSON> جديد", "notSuitable": "غير مناسب", "ongoing": "جارية", "publishedWr": "من<PERSON>ور (ب.م.)", "readyForApproval": "جاهز للموافقة", "readyForReview": "جاهز للمراجعة", "reviewed": "تمت المراجعة", "suitable": "من<PERSON><PERSON><PERSON>", "retain": "يحتفظ", "requestedCorrection": "طل<PERSON> التصحيح", "reviewedMe": "({{ME}}) تمت المراجعة", "reviewedSe": "({{SE}}) تمت المراجعة", "updatedByAA": "تم التحديث بواسطة مؤلف التقييم", "onGoingWithAA": "مستمر مع مؤلف التقييم", "facultyNeedCorrection": "تصحيح من هيئة التدريس"}, "itemsTab": {"questionStem": "رأس السؤال", "history": "السجل", "details": "التفاصيل", "mapping": "التعيين", "preview": "معاينة"}, "title": {"studentVerifyProfileDetails": "تحقق من تفاصيل الملف الشخصي للطالب", "addIndividualStudent": "إضافة طالب", "assessmentAuthorDashboard": "لوحة تحكم مؤلف التقييم", "itemAuthorDashboard": "لوحة تحكم إنشاء العناصر", "medicalEducationistDashboard": "لوحة تحكم خبير التعليم الطبي", "subjectExpertReviewerDashboard": "لوحة تحكم خبير مراجعة  الموضوع", "questionBank": "بنك الأسئلة", "itemDetail": "تفاصيل العنصر", "reviewCreateAssessment": "مراجعة / إنشاء تقييم", "reviewItem": "مراجعة العنصر", "reviewAssessment": "مراجعة التقييم", "conductExamsSettings": "إعدادات إجراء الاختبارات", "remoteExamsSettings": "إعدادات الاختبارات عن بُعد"}, "fileDetails": "تفاصيل الملف", "matchingQuestion": "سؤال مطابقة", "extractAssessmentPopup": {"noItemCountSelected": "لم يتم تحديد عدد العناصر", "difficultyIndexRange": "نطاق مؤشر الصعوبة", "discriminationIndexRange": "نطاق مؤشر التمييز", "oldAssessments": "التقييمات القديمة", "oldItems": "العناصر القديمة", "newItems": "عناصر جديدة", "autoExtractItems": "عناصر الاستخراج التلقائي", "selectItemCount": "<PERSON><PERSON><PERSON> ع<PERSON>د العناصر", "itemType": "نوع العنصر", "requiredCount": "الع<PERSON><PERSON> المطلوب", "subItemsPerSet": "العناصر الفرعية لكل مجموعة", "addItemType": "إضافة نوع العنصر", "biserialIndexRange": "نطاق مؤشر ثنائي التسلسل", "selectItemCountExtract": "حد<PERSON> استخر<PERSON>ج عدد العناصر"}}, "settings": {"basicList": {"basicList": "القائمة الأساسية", "serviceSetting": "إعد<PERSON> الخدمة", "addBasicList": "أض<PERSON> قائمة أساسية", "addDomain": "إضافة المجال / الموضوع", "frameworks": "إطارات العمل", "selectFrameworks": "<PERSON><PERSON><PERSON> الإطار", "surveyItemType": "نوع عنصر الاستطلاع ", "basicModule": {"itemsSelectedList": "عناصر للقائمة المختارة", "newProgramName": "اسم البرنامج الجديد", "newShortCode": "<PERSON><PERSON><PERSON> قصير جديد", "shortCode": "<PERSON><PERSON><PERSON> قصير", "selectType": "اختر نوع", "programsCreatedSuccessfully": "تم إنشاء البرنامج بنجاح.", "itemNameRequired": "اسم العنصر مطلوب", "itemCodeRequired": "رمز العنصر مطلوب", "treeNodeIdMissing": "معرف عقدة الشجرة مفقودة لا يمكن إضافة جديد", "program": "برنامج", "programName": "برنامج {{name}}", "term": "فصل دراسي", "year": "سنة", "curriculum": " المنهج ٢", "level": "المستوى", "rotationGroup": "المجموعة التناوبية ٢", "course": "المقرر", "module": "الوحدة", "elective": "المادة اختيارية", "subject": "المادة", "topic": "موضوع الجلسة", "slo": "نتائج جلسة التعليم", "topics": "المواضيع", "subTopic": "الموضوع الفرعي", "programAddedSuccessfully": "تم إضافة البرنامج بنجاح", "programUpdatedSuccessfully": "تم تحديث البرنامج بنجاح", "programArchivedSuccessfully": "تم أرشفة البرنامج بنجاح", "termAddedSuccessfully": "تم إضافة الفصل االدراسي بنجاح", "termUpdatedSuccessfully": "تم تحديث الفصل الدراسي بنجاح", "termArchivedSuccessfully": "تم أرشفة الفصل الدراسي بنجاح", "yearAddedSuccessfully": "تمت إضافة السنة بنجاح", "yearUpdatedSuccessfully": "تم تحديث السنة بنجاح", "yearArchivedSuccessfully": "تم أرشفة السنة بنجاح", "curriculumAddedSuccessfully": "تم إضافة المنهج بنجاح", "curriculumUpdatedSuccessfully": "تم تحديث المنهج بنجاح", "curriculumArchivedSuccessfully": "تم أرشفة المنهج بنجاح", "levelAddedSuccessfully": "تم إضافة المستوى بنجاح", "levelUpdatedSuccessfully": "تم تحديث المستوى بنجاح", "levelArchivedSuccessfully": "تم أرشفة المستوى بنجاح", "rotationGroupAddedSuccessfully": "تم إضافة المجموعة التناوبية بنجاح", "rotationGroupUpdatedSuccessfully": "تم تحديث المجموعة التناوبية بنجاح", "rotationGroupArchivedSuccessfully": "تم أرشفة المجموعة التناوبية بنجاح", "courseAddedSuccessfully": "تم إضافة المقرر بنجاح", "courseUpdatedSuccessfully": "تم تحديث المقرر بنجاح", "courseArchivedSuccessfully": "تم أرشفة المقرر بنجاح", "moduleAddedSuccessfully": "تم إضافة الوحدة بنجاح", "moduleUpdatedSuccessfully": "تم تحديث الوحدة بنجاح", "moduleArchivedSuccessfully": "تم أرشفة الوحدة بنجاح", "electiveAddedSuccessfully": "تم إضافة المادة الاختيارية بنجاح", "electiveUpdatedSuccessfully": "تم تحديث المادة الاختيارية بنجاح", "electiveArchivedSuccessfully": "تم أرشفة المادة الاختيارية بنجاح", "subjectAddedSuccessfully": "تم إضافة المادة بنجاح", "subjectUpdatedSuccessfully": "تم تحديث المادة بنجاح", "subjectArchivedSuccessfully": "تم أرشفة المادة بنجاح", "topicAddedSuccessfully": "تم إضافة موضوع الجلسة بنجاح", "topicUpdatedSuccessfully": "تم تحديث موضوع الجلسة بنجاح", "topicArchivedSuccessfully": "تم أرشفة موضوع الجلسة بنجاح", "subTopicArchivedSuccessfully": "تم أرشفة الموضوع الفرعي بنجاح", "sloAddedSuccessfully": "تم إضافة نتائج جلسة التعليم بنجاح", "sloUpdatedSuccessfully": "تم تحديث نتائج جلسة التعليم بنجاح", "sloArchivedSuccessfully": "تم أرشفة نتائج جلسة التعليم بنجاح", "deleteChildError": "ارشف العناصر التابعة لهذا العنصر الأساسي لمواصلة هذا الإجراء", "surveyItemTypeAddedSuccessfully": "تم إضافة عنصر الاستطلاع بنجاح", "surveyItemTypeUpdatedSuccessfully": "تم تحديث عنصر الاستطلاع بنجاح", "surveyItemTypeArchivedSuccessfully": "تم أرشفة عنصر الاستطلاع بنجاح", "cloUpdatedSuccessfully": "تم إضافة نتائج جلسة المقرر بنجاح", "areYouSureWantToDelete": "هل أنت متأكد أنك تريد الحذف؟", "invalidShortCode": "الرمز القصير صالح", "addTopicContentTitle": "أ<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>ى إلى هذا الموضوع", "addContent": "إضافة محتوى", "editContent": "تحرير المحتوى", "changeDutyApproach": "هل أنت متأكد من أنك تريد تغيير نهج الواجب؟", "changeDutyApproachNote": "سيؤدي تغيير نهج الواجب إلى فقدان جميع البيانات السابقة المتعلقة بالواجبات، بما في ذلك الواجبات التي تم تعيينها بالفعل للطلاب."}, "itemType": {"itemTypeAddedSuccessfully": "تم إضافة عنصر الاستطلاع بنجاح", "itemTypeUpdatedSuccessfully": "تم تحديث نوع العنصر بنجاح"}, "contentUnderTheTopic": "المحتوى تحت عنوان"}, "globalExamSettings": {"header": "الخدمة والإعداد > إعدادات الامتحان العامة", "title": "دي جي أسس | {{label}} - إعدادات الاختبار العامة ", "globalExamSettings": "إعدادات الامتحان العامة", "changingSettings": "سيؤثر تغيير الإعدادات هنا على وظائف متعددة في البرنامج ولن يؤثر على البيانات السابقة", "detail": "تفاصيل الوحدة المختارة", "assessmentTypes": "أنواع التقييم", "attemptTypes": "أنواع المحاولة", "examTypeWise": "نوع الامتحان:  اختيارات الامتحان الافتراضية", "coursesExams": "المقررات التعليمية و أنواع الامتحانات", "examinationTimeSlots": "الفترات الزمنية للامتحانات", "examDatesAllotment": "تخصيص تاريخ للامتحانات", "proctorSelection": "إعدادات اختيار المراقبين", "serviceSetting": "الخدمة والإعداد > إعدادات الامتحان العامة", "globalExamSettingsTitle": "{{label}} - إعد<PERSON><PERSON><PERSON> الاختبار العامة | دي جي أسس", "examTypeStable": {"examName": "اسم الامتحان", "shortForm": "نموذج قصير"}, "academicYear": {"academicYear": "العام الدراسي الحالي", "pastAcademicYear": "الأعوام الدراسي الماضية", "addEndDateAcademicYear": "الرجاء إضافة تاريخ بداية ونهاية للعام الدراسي الحالي", "courseGroupAlreadyGenerated": "تم إنشاء مجموعة المقرر التعليمي مسبقاُ ، ولا يمكن تحديثها حتى العام الدراسي القادم", "rangeDatePlaceholder": "حدد تاريخ البدء - تاريخ الانتهاء"}, "examinationDateAllotment": {"typeOfExam": "نوع الامتحان", "startDate": "مو<PERSON><PERSON> البدء", "endDate": "موعد الانتهاء", "pastDatesNotAllowed": "التواريخ الماضية غير مسموح بها"}, "examTypes": {"assessmentType": "حدد نوع التقييم", "examCategory": "فئات الامتحانات", "courseModuleGroup": "مجموعات المقررات / الوحدات ", "examCategoryNotMapped": "لم يتم تعيين فئة الاختبار مع أنواع الاختبارات المعروضة", "examCategoryNotMappedAssessmentType": "لم يتم تعيين فئة الاختبار مع أنواع الاختبارات المعروضة", "o+p": "موقع + مراقب", "o-p": "موقع - مراقب", "r-p": "عن بعد - مراقب", "r+p": "عن بعد + مراقب", "customTestCenter": "موقع + مراقب (مركز الاختبار المخصص)", "centralizedTestCenter": "موقع + مراقب (مركز الاختبار المركزي)", "noTestCenter": "عن بعد - مر<PERSON><PERSON><PERSON> (لا يوجد مركز اختبار)", "virtualTestCenter": "(مركز الاختبار الافتراضي) عن بعد + مراقب", "remoteWithProctor": "R+P (VTC)", "remoteWithoutProctor": "R-P", "onsiteWithProctor": "O+P (CTC)", "listOfTaggedExamTypes": "قائمة أنواع الامتحانات الموسومة"}, "proctorSettings": {"course": "عرض 'المقرر' ", "role": "عرض 'الدور' ", "proctor": "عرض 'مرات المراقبة' ", "assign": "عرض 'آخر تعيين' ", "showPhoneNumber": "عرض 'رقم الهاتف' ", "proctorStatus": "<PERSON><PERSON><PERSON> المراقبين لكل امتحان"}, "courseTypesList": {"course": "دورة", "configure": "تكوين", "activate": "فعل", "mode": "وضع", "tcUsage": "استخدام TC", "tcAllocationRequest": "طلب تخصيص TC", "centralized": "مركزية", "customized": "<PERSON><PERSON><PERSON> الطلب", "both": "كلاهما", "notApplicableForCustomized": "لا ينطبق على الإعدادات المخصصة", "modeInfo": "اختر 'IN' إذا تم إجراء الامتحان داخل DigiAssess؛ اختر 'OUT' إذا كان خارج المنصة.", "TcUsageInfo": "اختر 'مركزي' لاستخدام مراكز الاختبار الموجودة من إدارة مراكز الاختبار، 'مخصص' لإنشاء مراكز جديدة غير مدرجة، أو 'كلاهما' في حال استخدام أي من الخيارين.", "TcAllocationInfo": "بالنسبة لاستخدام المراكز المركزية، اختر 'نعم' لإرسال الجدول الزمني وطلبات المراكز للموافقة قبل النشر؛ اختر 'لا' للنشر المباشر بدون موافقة لمنشئ التقييم."}}, "assessmentSettings": {"noAssessmentsFound": "لم يتم العثور على تقييمات", "itemReviewUniDirectional": "مراجعة العنصر في اتجاه واحد", "itemStem": "عنصر رأس السؤال", "itemChoice": "اختيار العنصر", "itemAnswerKey": "مف<PERSON><PERSON><PERSON> إجابة العنصر", "accessItemStem": "الوصول للسؤال", "accessItemChoice": "الوصول للاختيار", "accessItemAnswerKey": "الوصول إلى مفتاح الإجابة", "uniDirectionalNote": "* ملاحظة: عند اختيار 'تشغيل' مراجعة العنصر في اتجاه واحد، سيرسل مراجعون العناصر التصحيحات إلى مؤلف العنصر بمجرد عمل التصحيحات، بدلاً من إرسالها إلى مؤلف التقييم", "none": " ليس ", "view": "<PERSON><PERSON><PERSON>", "edit": "تعديل", "assessmentSettings": "إعدادات التقييم", "allSaved": "كل ما تم حفظه", "configurations": "إعدادات:", "backToSchedulingPage": "العودة إلى صفحة الجدولة", "resetAllItems": "إعادة تعيين كافة عناصر التقييم", "confirmResetAssessmentItems": "هل أنت متأكد أنك تريد متابعة إعادة تعيين كافة العناصر؟ يرجى ملاحظة أنه بمجرد إعادة التعيين، سيتم إعادة تعيين كل من العناصر الجديدة والحالية إلى حالتها الافتراضية.", "recallCard": "بطاقات الاستدعاء من مؤلف العنصر", "renewCardFromItemAuthor": "هل أنت متأكد أنك تريد متابعة طلب تجديد العنصر؟ يرجى ملاحظة أنه بمجرد تجديد العنصر، سيتم إرجاع كل من العناصر الجديدة والحالية.", "renewItem": "تجديد طلب السلعة", "resetRequestedCards": "إعادة تعيين البطاقات التي تم إنشاؤها أو المرفقة", "requestedCardsReset": "إذا قمت بتغيير الإعدادات المتعلقة بنوع العنصر، فسيتم إعادة تعيين جميع البطاقات التي تم إنشاؤها والمرفقة، هل أنت متأكد من أنك تريد المتابعة؟ ", "noteUpdateItemType": "ملاحظة: إذا كنت بحاجة إلى تغيير نوع العنصر أو الإعدادات، فقم أولاً بتجديد العنصر من مؤلف العنصر وإجراء التحديثات.", "noteUpdateTime": "ملاحظة: إذا كنت بحاجة إلى تغيير الوقت والعلامة، فقم أولاً بإعادة ضبط البطاقة من القسم وإجراء التحديثات.", "filters": "المرشحات", "changeSettings": "هل أنت متأكد أنك تريد تغيير الإعدادات؟", "basicSettingsLabel": "الإعدادات الأساسية", "advanceSettingsLabel": "الإعدادات المتقدمة (اختياري)", "advanceSetting": "الإعدادات المتقدمة", "basicSettings": {"totalNoOfItems": "إجمالي عدد العناصر", "totalMarks": "إجمالي العلامات", "totalTimeDuration": "إجمالي مدة الوقت (بالدقائق)", "reviewerRequired": "مطلوب مراجع", "oldItems": "يجب مراجعة العناصر القديمة بواسطة مؤلف العنصر"}, "advanceSettings": {"shufflingOfSections": "<PERSON>لط الأقسام", "answeringMethod": "طريقة الإجابة", "showSectionsInInstructionPage": "إظهار الأقسام في صفحة التعليمات", "showSectionsItemTypesInInstructionPage": "إظهار أنواع العناصر في صفحة التعليمات", "sectionReArrangement": "إعادة ترتيب الأقسام", "shufflingOfChoices": "خلط الاختيارات", "shufflingItems": "خلط العناصر داخل القسم", "shufflingGroups": "خلط العناصر داخل المجموعات (ينطبق: EMQ، CQ، MQ، CSAQ، MIT)", "allowNavigatingToNextItemWithoutAnswering": "السماح بالانتقال إلى العنصر التالي دون الإجابة", "studentsMandatorySubmitExam": "إلزام الطلاب بالإجابة على جميع العناصر لتقديم الامتحان", "restrictStudentLateEntry": "تقييد دخول الطلاب بعد وقت الدخول المتأخر", "maximumLateEntryDuration": "تحديد الح<PERSON> الأقصى لمدة الدخول المتأخر", "minimumTimeStudentMustAttendExam": "تحدي<PERSON> الح<PERSON> الأدنى للوقت الذي يجب أن يحضره الطالب في الامتحان", "reduceLateEntryTotalTime": "تقليل وقت الدخول المتأخر من إجمالي الوقت", "onlyAllowedWriteSafeExamBrowser": "السماح بالكتابة فقط في متصفح الامتحانات الآمن", "revisitingItem": "إعادة النظر في العنصر", "revisitingSections": "إعادة النظر في الأقسام", "allowMaximumLateEntryTimePercentageTotalExamDuration": "السماح بحد أقصى لوقت الدخول المتأخر كنسبة مئوية (%) من إجمالي مدة الامتحان"}, "examType": {"examTypes": "أنواع الامتحانات", "general": "عام", "customize": "تخصيص", "custom": "مخصص", "selectAssessment": "قم بتحديد تقييم", "manageConfigurations": "إدارة الإعدادات ", "noAssessmentSelected": "لم يتم تحديد تقييم / لا يوجد نوع امتحان للتقييم المحدد", "customSettings": "إعدادات مخصصة", "course": "نوع المقرر + الامتحان", "allConfigurations": "جميع الإعدادات المخصصة المحفوظة", "addCustomSetting": "+ إضافة إعدادات خاصة بالدورة والاختبار", "genericConfigurations": "العامة {{examTypeCode}} نماذج", "examDesc": "{{assessmentName}} إنشاء و حفظ التهيئة المفضلة لاختبارات"}, "endOfModule": {"saveAs": "ح<PERSON><PERSON> باسم", "customizedSetting": "قم بتشغيل زر التبديل للسماح بإعداد تقييم مخصص", "proctorType": "لتغيير نوع المراقبة انتقل إلى", "proctorTypeWithStatus": "لتغيير نوع المراقبة ({{proctoringType}})، انتقل إلى", "examTypeWise": "نوع الامتحان : اختيارات الامتحان الافتراضية", "selectedConfiguration": "الإعدادات المحددة لجميع أنواع الامتحانات المماثلة (يمكن للمسؤول فقط التغيير)", "noVersionsSelected": "لم يتم تحديد إصدارات", "select": "اخ<PERSON><PERSON>ر", "defaultAssessmentValues": "قيم التقييم الافتراضية", "itemAuthoring": "تأليف العنصر", "feedbackRequired": "التعليقات مطلوبة", "newItemsCreated": "العناصر الجديدة التي تم إنشاؤها بواسطة مؤلف العنصر بواسطة", "reviewerChecklist": "مطلوب قائمة تحقق {{subjectExpertReviewerRoleName}}", "medicalEducationist": "أكمل {{medicalEducationistRoleName}} مراجعة مجموعة العناصر.", "itemReview": "مراجعة العنصر في اتجاه واحد", "authorHeader": "المؤلف> التقييم> لوحة التحكم", "itemsRequested": "العناصر المطلوبة", "assessmentAuthoring": "تأليف التقييم", "oldItems": "يجب مراجعة العناصر القديمة بواسطة مؤلف العنصر", "totalSize": ".الحد الإجمالي لحجم الملف (م.ب)", "totalLimit": "حد الزمن الإجمالي (دقيقة)", "totalMarksLimit": "الحد الإجمالي للدرجات", "totalItemsLimit": "الحد الإجمالي لعدد العناصر", "itemsWithoutSection": "عناصر بدون قسم", "marks": "درجات", "generalSettings": "الاعدادات العامة", "eachItem": "كل عنصر:", "timeMin": "الوقت (دقيقة)", "itemType": "نوع العنصر", "shuffleOfItems": "<PERSON>لط العناصر", "shuffleChoice": "خلط الخيارات - داخل العناصر", "answeringSequenceItems": "تسلسل الإجابة على العناصر", "itemsWithSection": "العناصر مقسمة ", "commonAllSections": "مشترك بين جميع الأقسام", "allSections": "جميع الأقسام", "quantity": "كمية:", "itemsWithin": "العناصر داخل", "eachSection": "كل قسم:", "shufflingOfSections": "<PERSON>لط الأقسام", "answeringSequenceSection": "تسلسل الإجابة على الأقسام", "sectionItemsWithin": "قسم وعناصره", "sectionSettings": "إعدادات القسم:", "numberOfItems": "عد<PERSON> العناصر داخل هذا القسم", "numberOfGroupsLimit": "ع<PERSON><PERSON> حدو<PERSON> المجموعة هذا القسم", "marksLimit": " حد الدرجات داخل هذا القسم", "timeLimit": "حد الزمن داخل هذا القسم (دقيقة)", "itemSettings": "إعدادات العنصر:", "distributionOfMarks": "توزيع <span class=\"digi-mr-5 digi-ml-5 digi-font-500\"> العلامات </span>   على العناصر داخل القسم", "distributionOfTime": "توزيع  <span class=\"digi-mr-5 digi-ml-5 digi-font-500\"> الوقت  </span> على العناصر داخل القسم", "sequenceOfItems": "تسلسل العناصر داخل القسم", "shufflingOfChoices": "خلط <span class=\"digi-mr-5 digi-ml-5 digi-font-500\"> الاختيارات </span> داخل القسم (في {{MCQ}},{{CQ}},{{MQ}},{{EMQ}})", "shufflingOfItems": "خلط <span class=\"digi-mr-5 digi-ml-5 digi-font-500\"> العناصر </span> داخل القسم", "shufflingOfItemGroup": "خلط العناصر داخل <span class=\"digi-mr-5 digi-ml-5 digi-font-500\"> المجموعة </span> (في {{EMQ}},{{CQ}},{{MQ}},{{CSAQ}})", "onsite": "بالموقع", "remote": "<PERSON><PERSON> بعد", "remote-p": " عن بعد بدون مراقب", "remote+p": "عن بعد بمراقب", "onsite+p": "في الموقع + مر", "onsite-p": "في الموقع - مر", "endOfAssessment": "نهاية التقييم", "duringAssessment": "أثناء التقييم", "both": "كلاهما", "equal": "متساوي", "unEqual": "غير متساوي", "restricted": "مقيد (بدون إعادة النظر)", "freeSequence": "التسلسل الحر (إعادة النظر)", "mins": "دقائق", "examTypesNav": "إعدادات > إعدادات التقييم > أنواع الامتحانات", "totalSessionTimeLimit": "لا يتطابق حد الزمن الإجمالي مع حد الزمن لكل قسم", "totalSessionMarksLimit": "لا يتطابق الحد الإجمالي للدرجات مع درجات كل قسم ", "totalSessionItemsLimit": "لا يتطاب<PERSON> الحد الإجمالي للعناصر مع عدد عناصر كل قسم", "decimalValue": "يجب ألا تكون العناصر قيم عشرية", "invalidTime": "الوقت غير صالح في القسم / كل عنصر داخل القسم، الرجاء إدخال وقت صالح", "publishDate": "تاريخ النشر", "itemDecimalValue": "العناصر / الوقت يجب ألا يكونا قيم عشرية", "anyChanges": "أي تغييرات تم إجراؤها على الإعدادات", "saveAsNew": "حفظ الإعدادات كجديد", "saveAsDraft": "حفظ الإعدادات كمسودات", "chooseConfiguration": "اختر الإعدادات", "examsReport": "الامتحانات - التقارير المطلوبة", "exams": "الامتحانات", "customName": "اسم مخصص", "descHint": "في الموقع، مراقب في الموقع، مع مراجعة العنصر ، مع القسم ، مع الاستطلاع", "genericType": "النوع العام", "assessmentTitle": "إعدادات التقييم > إعدادات الرسائل", "itemsLimitError": "يج<PERSON> ألا يكون حد عدد العناصر أكبر / أقل من الحد الإجمالي لعدد العناصر", "multipleItemsLimitError": "يجب أن يكون حد عناصر المجموعة أكثر من ١", "minChoiceValidationError": "يج<PERSON> أن يكون الحد الأدنى لاختيار نوع العنصر أكثر من 1", "marksLimitError": "يجب ألا يكون حد الدرجات أكبر / أقل من حد إجمالي الدرجات", "timeLimitError": "يجب ألا يكون حد الزمن أكبر / أقل من حد الزمن الإجمالي", "groupLimitError": "يجب ألا يكون حد المجموعة أكبر/أقل من عدد حد المجموعة في هذا القسم", "itemReviewUnidirectional": "مراجعة العنصر في اتجاه واحد", "unidirectionalInformation": " ملاحظة : عند اختيار 'تشغيل' مراجعة العنصر في اتجاه واحد، سيرسل مراجعون العناصر التصحيحات إلى مؤلف العنصر بمجرد عمل التصحيحات، بدلاً من إرسالها إلى مؤلف التقييم", "itemChoice": "اختيار العنصر", "studentsMandatorySubmitExam": " للطلاب إلزامية للإجابة على جميع المواد لتقديم الامتحان", "navigateNextItemAnswering": "انتقل إلى العنصر التالي دون إجابة", "restrictStudentLateEntry": "تقييد الدخول المتأخر للطلاب ", "maximumLateEntryDuration": "الحد الأقصى لمدة الدخول المتأخر (٪ من إجمالي وقت الامتحان)", "minimumTimeStudentMustAttendExam": " الح<PERSON> الأدنى من الوقت الذي يجب أن يحضر فيه الطالب الامتحان (٪ من إجمالي وقت الامتحان)", "reduceLateEntryTotalTime": "تقليل وقت الدخول المتأخر من إجمالي الوقت", "onlyAllowedWriteSafeExamBrowser": "يسمح فقط للكتابة في متصفح الامتحان الآمن", "studentAuthMethod": "طريقة مصادقة الطالب للامتحان", "skipStudentAuthMethod": "يمكن تخطي التحقق من الوجه على أساس الجنس", "totalTimeLimit": "إجمالي الحد الزمني (دقيقة)", "giveExtraTimeRestrictedSequence": "السماح بإعطاء وقت إضافي للتسلسل المقيد", "itemsSelected": "{{length}} العناصر المحددة", "itemWithMinimumChoice": "عنصر مع الحد الأدنى من الاختيار", "itemWithDefaultChoice": "عنصر مع الاختيار الافتراضي", "itemTypes": "أنواع العناصر", "forAllSections": "لجميع الأقسام", "totalNoOfSection": "العد<PERSON> الإجمالي. من القسم ", "itemsType": "نوع العناصر", "totalGroups": "إجمالي  المجموعات", "totalItems": "إجمالي  العناصر", "sections": "الأقسام", "separateItemInMultipleSection": "عنصر التوزيع في قسم متعدد", "userCanAddMoreSectionsBasedOnItemType": " يمكن للمستخدم إضافة المزيد من الأقسام بناءً على نوع العنصر", "noOfGroups": "ع<PERSON><PERSON> المجموعات", "selectGroup": "اختر المجموعة", "g": "مج", "showSectionsInInstructionPage": "عرض الأقسام في صفحة التعليمات", "showSectionsItemTypesInInstructionPage": "عرض أنواع العناصر في القسم في صفحة التعليمات", "whenRearrangeSectionStarts": "عند بدء إعادة ترتيب القسم", "notes": "ملاحظات", "whenRearrangeSectionStartsNote1": "سيتم تقليل الوقت الإجمالي المطلوب لإعادة ترتيب القسم.", "whenRearrangeSectionStartsNote2": "تمكين خيار تحديد قبل الامتحان يعرض أقسام أنواع العناصر في صفحة التعليمات.", "whenRearrangeSectionStartsNote3": "إذا تم تمكين إعادة ترتيب القسم قبل بدء الامتحان، يمكن للطلاب المتأخرين ما زالوا يعيدون ترتيب القسم بعد بدء الامتحان.", "beforeExam": "قبل الامتحان", "afterExam": "<PERSON>ع<PERSON> الامتحان", "showItemTypesInInstructionPage": "عرض أنواع العناصر في صفحة التعليمات", "facial": "وجهي", "password": "كلمة المرور", "surveyType": "نوع المسح", "canChangeDefaultValue": "يمكن لمؤلف التقييم تغيير التحديدات والقيم الافتراضية", "percentageCannotBeMore": "لا يجوز أن تزيد النسبة عن 100", "groupNotMappedError": "عدد المجموعات مفقود أو لم يتم تعيين المجموعة", "groupLimitOver": "ان<PERSON><PERSON>ى حد المجموعة", "groupAlreadySelected": "قامت المجموعة بالفعل بتحديد قسم آخر", "sectionSplitError": "لا يمكن تقسيم {{item}} مجموعات أو عناصر بالتساوي بين أقسام {{noOfSections}}. يرجى تقسيم المجموعات والعناصر يدويا.", "enterSectionItemCount": "الرجاء إدخا<PERSON> عدد العناصر لهذا القسم", "itemCountGroupCountMismatch": "عد<PERSON> المجموعة المحددة وعدد العناصر لهذا القسم غير متطابقين", "groupAndItemCountMisMatch": "<PERSON><PERSON><PERSON> عدد المجموعة لا يتطابق مع عدد العناصر", "requiredItemTypes": "أنواع العناصر المطلوبة", "doNotAllowToRevisit": "لا تسمح بإعادة الزيارة", "allowToRevisit": "السماح بإعادة الزيارة", "navigateToNextItemWithoutAnswering": "انتقل إلى العنصر التالي دون إجابة", "setMinimumTimeStudentMustAttendExam": "تعيين الحد الأدنى من الوقت كنسبة مئوية (%) يجب على الطالب حضور الامتحان", "assessmentTotalItemCountDoesNotMatchTheSelectedItemTypesCount": "لا يتطابق إجمالي عدد عناصر التقييم مع عدد أنواع العناصر المحددة", "allowAssessmentAuthorToChangeDefaultSelectionsValues": "السماح لمؤلف التقييم بتغيير الاختيارات والقيم الافتراضية", "allowRearrangingSectionOrderBeforeExamStarts": "السماح بإعادة ترتيب ترتيب الأقسام أثناء الامتحان", "allowMaximumLateEntryTimePercentageTotalExamDuration": "السماح بحد أقصى لوقت الدخول المتأخر كنسبة مئوية (%) من إجمالي مدة الامتحان", "worksOnlyWithOnsiteExamProctor": "يعمل فقط مع الاختبار في الموقع + المراقب"}, "allSavedFormativeConfigurations": {"archivingImpact": "سيكون للأرشفة تأثير على إنشاء التقييم\n", "archive": "هل مازلت تريد أرشفة هذا؟", "impacts": "التأثيرات:", "noConfiguration": "حاليا لا يوجد تقييم يستخدم هذه الإعدادات"}, "assessmentGlobalSettings": {"configurationList": "قائمة الإعدادات > عرض الإعدادات"}, "assessmentSettingsPopup": {"createCustomSetting": "إنشاء إعداد مخصص للتقييم", "selectExam": "اختر نوع امتحان", "selectCourse": "اختر مقرر تعليمي", "startSelectingProgram": "ابدأ باختيار البرنامج وباقي التفاصيل من القوائم المنسدلة للعثور على المقرر التعليمي المطلوب"}, "attendanceManagement": {"conductingExams": "إجراء الامتحانات > تسليم التقييم", "attendanceManagement": "إدارة الحضور"}, "discardItemMarkDistribution": {"title": "اختر كيفية التعامل مع العلامات من عناصر التقييم المهملة", "directGraceMarks": "العلامات المهملة مباشرة", "directGraceMarksDescription": "تتم إضافة العلامات من الأسئلة المرفوضة مباشرة إلى إجمالي علامات الطالب.", "Redistribution": "إعادة توزيع العلامات على الأسئلة غير الملغاة", "RedistributionDescription": "تُوزّع علامات العناصر المُهمَلة على أنواع أسئلة مُماثلة. اختر طريقة إعادة التوزيع أدناه:", "distributedSimilar": "العلامات التي سيتم توزيعها على أنواع العناصر المماثلة", "distributedSimilarDescription": "يتم توزيع العلامات بالتساوي على عناصر نوع الأسئلة المتشابهة (على سبيل المثال، من اختيار متعدد إلى اختيار متعدد).", "distributedAll": "سيتم توزيع العلامات على جميع أنواع العناصر", "distributedAllDescription": "يتم توزيع العلامات بالتساوي على جميع أنواع الأسئلة المتبقية بغض النظر عن النوع.", "assessmentPlanAdjustment": "تعديل خطة التقييم", "assessmentPlanAdjustmentDescription": "تعديل هيكل التقييم عن طريق تقليل العدد الإجمالي للأسئلة والعلامات بشكل ديناميكي عند تجاهل العناصر (على سبيل المثال، 60 سؤالاً بقيمة 60 علامة مع إزالة 5 منها تصبح 55 سؤالاً بقيمة 55 علامة).", "discardedItemMarkDistribution": "توزيع علامة العناصر المهملة", "selectDistributionMethod": "حدد طريقة التوزيع"}, "conductExamSettings": {"cloudServer": "الخادم السحابي", "riskMitigation": "تخفيف المخاطر", "internetNotAvailable": "في حالة أن الإنترنت غير متاح في بداية الامتحان أو في أثناءه، يمكن للطالب إكمال الامتحان في فترة السماح", "hrs": "ساعات", "secondaryServer": "الخادم الثانوي", "assessmentDownloads": "عمليات التحميل التقييمات إلى الخادم الثانوي", "dailyNight": "كل ليلة قبل يوم الامتحان المقرر", "notificationSent": "سيتم إرسال الإخطارات إلى", "examCoordinator": "منس<PERSON> الامتحان", "addUserRole": "أضف دور للمستخدم الذي سيتم إرسال الإخطارات اليه", "chooseOption": "اختر خيارًا", "option": "خيار", "conductingExamAssessment": "إجراء الامتحانات > تقييم الامتحان", "startEnd": "تعليمات بداية و نهاية الامتحان", "globalSettings": "حسب الإعدادات العامة للتقييم و نوع امتحانه", "abbreviatedCourseCode": "الرمز المختصر للمقرر التعليمي", "noOfSections": "<PERSON><PERSON><PERSON> الأقسام", "restrictedSequence": "التسلسل مقيد، أجب على الأقسام بالترتيب", "submitAnswer": "يجب تقديم الإجابات في القسم الحالي ، قبل التقدم ", "freeSequence": "تسلسل حر، من الممكن لكم العودة إلى الأقسام السابقة", "attendNextSessions": "من الممكن الإجابة على الأقسام التالية بدون الإجابة على القسم الحالي", "restrictedSequenceQuestion": "التسلسل مقيد، أجب على الأسئلة بالترتيب", "submitAnswerQuestion": "يجب الإجابة على السؤال الحالي قبل التقدم", "updateContent": "سيقوم بتحديث المحتوى", "linkExam": "رابط لتقويم ميعاد الامتحان"}, "coursesExamSpec": {"coursesExamSpec": "مواصفات امتحان الدورات", "defineEditExamSpec": "تحديد / تعديل مواصفات الامتحان لكل دورة ونوع امتحان ", "startSelectProgram": "ابد<PERSON> بتحديد البرنامج والقوائم المنسدلة الأخرى للعثور على الدورة التدريبية المطلوبة", "defined": "معرفة", "notDefined": "غير معرف", "testSpecGraph": " الرسم البياني لمواصفات الامتحان ", "itemsDistribution": "توزيع العناصر - خريطة الحرارة", "exportTestSpec": "تصدير مواصفات الامتحان ", "searchByCourse": "البحث عن طريق الدورة"}, "itemUnderEachClo": {"overallClo": "مخرجات تعلم المنهج - وزن العناصر المحسوب من البيانات أدناه", "totalItemsAlloted": "يجب تخصيص إجمالي العناصر في كافة أنواع العناصر", "totalItemsMapped": "يجب تعيين إجمالي العناصر", "topic": "موضوع", "items": "عناصر"}, "testSpecSetting": {"testSpecSetting": "إعدادات مواصفات الامتحان", "courseExamSpecification": "مواصفات امتحان المقررات", "defineExamSpecification": "تحديد/تحرير مواصفات الاختبار لكل دورة ونوع الاختبار", "statusNotes": "ملاحظة: تتو<PERSON>ر 10 قيم لظلال اللون كحد أقصى ، فوق 10 يتم تطبيق قيم الألوان الأخيرة", "eachCourseAndExamType": "كل دورة ونوع الامتحان", "generalExamSpecification": "مواصفات الامتحان العام"}}, "messagingSettings": {"messagingSettings": "إعدادات الرسائل", "messagingTypes": "أنواع الرسائل", "smsEmailInApp": "الرسائل القصيرة ، البريد الإلكتروني ، رسائل داخل التطبيق", "eventBasedAlerts": "تنبيهات الأحداث", "timeBasedDueDatesReminders": "تواريخ الاستحقاق و التذكيرات", "assessmentSettings": "إعدادات التقييم", "messageSettings": "إعدادات الرسالة", "messagingSettingsAnnouncement": "إعدادات الرسائل > الإعلانات", "questionBankPermission": "بنك الأسئلة > قائمة الصلاحيات", "listPermission": "قائمة الصلاحيات", "admin": "المشرف", "notificationType": {"title": "إعدادات الرسائل > أنواع الرسائل", "sms": "رسالة", "email": "الب<PERSON>يد الإلكتروني", "notification": "إخطار داخل البرنامج", "digiassess": "Digiassess"}, "eventBasedAlert": {"title": "إعدادات الرسائل > إخطارات الحدث", "creatingAssessment": "طلب إنشاء تقييم على أساس الجدول الزمني", "finalizedRequest": "مؤلف العنصر انتهى من الدفعة المطلوبة من العناصر", "itemAuthor": "مؤلف العنصر", "assessmentAuthorRequest": "مؤلف التقييم طلب دفعة من عناصر.", "subjectReview": "أكمل {{subjectExpertReviewerRoleName}} مراجعة مجموعة من العناصر ", "medicalReview": "أخصائي التعليم الطبي انتهى من مراجعة دفعة عناصر.", "subjectExpertReviewer": "خبير مراجعة الموضوع", "reviewItems": "مؤلف العنصر، طلب مراجعة دفعة عناصر", "medicalEducationalist": "أخصائي التعليم الطبي", "correctionsItems": "مؤلف العنصر انتهى من تصحيح دفعة عناصر"}, "messageDueDate": {"title": "إعدادات الرسائل > مواعيد تواريخ الاستحقاق و التذكيرات", "dueDates": "موا<PERSON>يد الاستحقاق", "assessmentPublish": "مواعيد استحقاق نشر التقييمات", "gracePeriod": "(عد<PERSON> الأيام (فترة الإمهال) قبل تاريخ بدء كل الامتحانات)", "chooseDays": "اختر الأيام", "itemRequest": "الميعاد المحدد لطاب العنصر", "beforePublish": "ع<PERSON><PERSON> ال<PERSON><PERSON><PERSON><PERSON> قبل موعد نشر التقييم", "applicable": "ينطبق على مؤلف العنصر ، {{subjectExpertReviewerRoleName}} ، {{medicalEducationistRoleName}}", "reminders": "تذكيرات", "beforeAssessmentPublish": "<PERSON><PERSON><PERSON> ال<PERSON><PERSON><PERSON><PERSON> قبل تاريخ نشر التقييم", "time": "زمن", "repeat": "كرر", "itemAuthor": "مؤلف العنصر ، {{subjectExpertReviewerRoleName}} ، {{medicalEducationistRoleName}}", "daily": "يومي", "weekly": "أسبوعي", "monthly": "شهري", "day": "يوم", "days": "أيام", "maximumDays": "يجب أن تكون أيام التكرار بحد أقصى {{days}}"}}, "reviewerSetting": {"reviewerSetting": "إعدادات المراجع", "reviewerSettings": "اعدادات المراجع", "checklist": "قائمة تدقيق", "title": "إعدادات > الإعدادات العامة> إعدادات المراجع > قائمة التدقيق", "checkListBreadCrum": "قائمة التحقق  > {{reviewerType}} > {{itemType}}", "subjectExpertReviewerSetting": {"addSection": "+ <PERSON><PERSON><PERSON> قسم", "sectionTitle": "... عنوان القسم", "checklist": "ابدأ في إنشاء قائمة التحقق عن طريق إضافة قسم، انقر فوق الزر \"أضف قسم \" أعلاه", "addItem": "<PERSON><PERSON><PERSON>", "itemContent": "... أدخل محتوى العنصر", "archive": "{{name}} أنت على وشك أرشفة", "subjectExpertReviewer": "خبير مراجعة الموضوع", "medicalEducationist": "أخصائي التعليم الطبي"}}, "questionBankSettings": {"questionBankSettings": "إعدادات بنك الأسئلة", "importFileFormatsAllowed": "صيغ الملفات المسموح باستيرادها", "importItemTemplate": "نموذج استيراد العنصر", "exportFileFormatsAvailable": "صيغ الملفات الممكن تصديرها", "title": "إعدادات > إعدادات بنك الأسئلة > صيغ الملفات المسموح باستيرادها", "docx": "docx.", "singleItemTypes": ".س.م.ا, س.ش., .س.ا.ق, .ص.ا.خ ", "questionBankImportFileFormats": {"importTitle": "إعدادات > إعدادات بنك الأسئلة > صيغ الملفات المسموح باستيرادها", "exportTitle": "إعدادات > إعدادات بنك الأسئلة > صيغ الملفات الممكن تصديرها", "importFileFormats": "صيغ تنسيقات الملفات", "exportFileFormats": "صيغ تنسيقات الملفات", "importFormats": "صيغ الاستيراد", "exportFormats": "صيغ التصدير", "importFile": "بنك الأسئلة > صيغ الملفات المسموح باستيرادها", "exportFile": "بنك الأسئلة > صيغ الملفات الممكن تصديرها", "permissionFile": "بنك الأسئلة > تصدير تنسيقات الملفات المسموح بها", "msWordDocument": "مستند MS Word", "csvFile": "CSV ملف ", "xmlFile": "XML ملف ", "importTemplateFormat": "مستند MS Word (docx)", "forPermittedCourse": "للدورات المسموح بها", "itemAuthorUseAnother": "مشاركة العنصر بين المؤلفين", "itemSharingPermissionStatus": "حالة إذن مشاركة العنصر"}, "questionBankImportItemTemplate": {"title": "بنك الأسئلة > نموذج استيراد العنصر", "importingItem": "نموذج استيراد العناصر", "format": "تنسيق", "itemTemplate": "تحميل نموذج العنصر الخاص بك", "importItemTemplate": "إعدادات> إعدادات بنك الأسئلة > استيراد نموذج عنصر", "itemTemplateDownload": "قالب عربي.docx"}, "questionBankContentPopup": {"importItem": "MS Word استيراد العناصر باستخدام مستند ", "sampleTemplate": "ادخل ملف نموذج تجريبي", "browseFile": "اضغط لتصفح الملف", "browse": "تصفح"}}, "surveySettings": {"surveySettings": "إعدادات الاستطلاعات", "includeSurvey": "تضمين المسح العام", "inThisCourseSurveyToBeConductedAtThe": "في هذا المقرر التعليمي ، سيتم إجراء الاستطلاع في", "selectExamType": "حدد نوع الامتحان", "generalSurveyItems": "عناصر الاستطلاع العامة", "createSurveyQuestion": "قم بإنشاء سؤال استطلاع", "generalSurveyItem": "إعدادات الاستطلاع > إعدادات الاستطلاع العامة", "generalSettings": "إعدادات > الإعدادات العامة", "questionCreated": "تم انشاء سؤال الاستطلاع بنجاح", "questionArchived": "تمت ارشفة سؤال الاستطلاع بنجاح", "surveyItems": "عناصر الاستطلاع", "selectedOutcomes": "* سيتم نشر نتائج مختارة للاستطلاع", "selectAll": "اختر الكل", "createSurveyItem": "إنشاء عنصر استطلاع:", "enterSurveyQuestion": "... أدخل سؤال الاستبيان ", "choice/options": "الاختيار / الخيارات", "surveyQuestionsSettings": "الاستطلاع > إعدادات أسئلة الاستطلاع", "surveySettingTree": {"surveyItems": "{{name}} عناصر الاستطلاع ", "nameSurveyItems": "عناصر الاستطلاع {{name}}"}, "likertAgreeScale": "مقياس توافق ليكرت", "linearScale": "مق<PERSON><PERSON><PERSON> خطي", "emotionalScale": "مقياس عاطفي", "ratingScale": "مقياس التصنيف", "likertExtentScale": "مقياس مدى ليكرت", "openEnded": "مفتوحة النهاية", "greatExtent": "كبير المدي", "moderateExtent": "متوسط ​​المدى", "someExtent": "<PERSON><PERSON><PERSON> المدى", "veryLittleExtent": "صغير المدي", "notAtAll": "لا على الإطلاق", "conductSurveyAnonymous": "إجراء المسح مجهول", "setCharacterLimit": "تعيين عدد من حدود الأحرف"}, "onsiteConductingExams": {"onsiteConductingExams": "إجراء الامتحانات في الموقع", "remoteConductingExams": " إجراء الامتحانات عن بعد", "maxLateEntryTime": "الح<PERSON> الأقصى لوقت الدخول", "facialId": "معرف الوجه قيد التشغيل", "conductingExamsStudents": "اجراء الامتحانات > الطلاب", "globalServiceSettings": "إعدادات الخدمة العامة", "conductingExamsSettings": "إعدادات إجراء الامتحانات", "remoteExams": "امتحانات عن بعد", "onsiteExams": "الامتحانات في الموقع", "managingProctor": {"columnsShownForProctor": "أعمدة تظهر إعدادات إعادة اختيار المراقب", "showCourse": "عرض 'المقررات التعليمية'", "showRole": "عرض 'الدور'", "showTimesProctored": "عرض عدد المرات المراقبة", "showLastAssigned": "عرض عدد المرات المراقبة", "showPhoneNumber": "عرض رقم الهاتف"}, "examManagementTestCenter": {"pauseExamTestCenter": "أوق<PERSON> الامتحان لأحد / جميع مراكز الامتحان", "allowPauseExamForIndividualTestCenter": "السماح لمنسق الامتحانات أن يوقف الامتحانات لأحد مراكز الامتحانات ", "allowPauseExamForAllTestCenter": "السماح لمنسق الامتحانات أن يوقف الامتحانات لجميع مراكز الامتحان", "limitTimeForPauseExam": "تحديد المدة الزمنية لإيقاف الامتحان مؤقتًا", "maximumTimeForPausingAllTestCenter": "أقصى وقت للإيقاف المؤقت لجميع مراكز الامتحان (دقائق)", "maximumTimeForPauseingIndividualTestCenter": "أقصى وقت للإيقاف المؤقت لأحد مراكز الامتحانات (دقائق)", "timeDurationForPausing": "خيارات مدة الوقت لإيقاف الامتحان مؤقتًا", "addCustomTime": "السماح لمنسق الامتحانات بإضافة وقت مخصص", "timeDurationOptions": "خيارات الفترات الزمنية (دقائق)", "reasonForPausingExam": "أسباب التوقف المؤقت للامتحان'", "addCustomReason": "السماح لمنسق الامتحان بإضافة سبب مخصص", "resetExamIndividualTestCenter": "السماح لمنسق الامتحانات بإعادة تعيين الامتحانات لأحد مراكز الامتحانات", "resetExamAllTestCenter": "السماح لمنسق الامتحانات بإعادة الامتحانات لجميع مراكز الامتحانات", "resetExamReason": "أس<PERSON><PERSON><PERSON> إعادة الامتحان", "terminateExam": "إنهاء الامتحان", "terminateExamReason": "أسباب إنهاء الامتحان", "terminateExamForIndividualTestCenter": " السماح لمنسق الامتحانات بإنهاء الامتحانات لأحد مراكز الامتحانات", "terminateExamForAllTestCenter": "السماح لمنسق الامتحانات بإنهاء الامتحانات لجميع مراكز الامتحانات", "giveExtraTimeForIndividualTestCenter": "اسمح لمنسق الامتحانات بإعطاء وقت إضافي لمراكز الامتحان الفردية", "giveExtraTimeForAllTestCenter": "السماح لمنسق الامتحانات بإعطاء وقت إضافي لجميع مراكز الامتحانات", "reasonForExtraTime": "أس<PERSON><PERSON><PERSON> إعطاء الوقت الإضافي", "giveExtraTime": "امنح وقتًا إضافيًا", "maxTimeIndividualTestCenters": "أقصى وقت إضافي يمكن إعطاءه لأحد مراكز الامتحانات (دقائق)", "maxTimeAllTestCenters": "أقصى وقت إضافي يمكن إعطاءه لجميع مراكز الامتحانات (دقائق)", "allowToAddCustomTime": "السماح لمنسق الامتحانات بإضافة وقت مخصص", "reasonOptions": "خيارات الأسباب", "examManagement": "إدارة الامتحانات - لمراكز الامتحانات", "conducting": "إجراء الامتحانات> منسق الامتحانات", "reset": "إعادة الامتحان", "addedTimeDurations": "المدد الزمنية المضافة (دقائق)", "addAnotherTimeDuration": "أض<PERSON> مدة زمنية أخرى", "enterNewReasonToAddToTheList": "أدخل سبب جديد لإضافته إلى القائمة"}, "proctorAttendanceManagement": {"attendanceManagement": "إدارة الحضور", "maximumLateEntryProctor": "الحد الأقصى للدخول المتأخر للمراقبين قبل وقت البدء المحدد (دقائق) "}, "proctorExamManagement": {"proctorPauseExam": "السماح للمراقب الامتحانات بإيقاف امتحانات الطلاب مؤقتًا", "maximumTimeForPauseingIndividualStudent": "أقصى وقت لإيقاف أحد الطلاب مؤقتًا (دقائق)", "pauseExam": "إيقاف مؤقت للامتحان", "allowToAddCustomTime": "السماح لمراقب الامتحانات بإضافة وقت مخصص", "allowToAddCustomReason": "السماح لمراقب الامتحانات بإضافة سبب مخصص", "conductingExam": "إجراء الامتحانات > مرا<PERSON><PERSON> الامتحانات"}, "proctorStudentManagement": {"misbehaviourActivity": "نشاط سوء السلوك", "tagMisbehaviourActivity": "السماح لمراقب الامتحانات بإبلاغ نشاط سوء السلوك للطلاب", "reasonForTaggingMisbehaviour": "قائمة أسباب إبلاغ نشاط سوء السلوك للطلاب", "extraTimeForStudents": "السماح لمراقب الامتحانات بإعطاء وقت إضافي للطلاب", "disturbingOthers": "إزعاج الآخرين", "suspectedCheating": "اشتباه بالغش ", "caughtCheating": "وجد يغش", "aggressiveBehaviour": "سلوك عدواني", "physicalRage": "الغ<PERSON><PERSON> الجسدي", "addedReasons": "الأسباب المضافة"}, "studentAttendanceManagement": {"maximumExtraTIme": " أقصى وقت إضافي يتم إعطاءه لأحد الطلاب (دقائق)", "listTimeDurationOptions": "قائمة خيارات المدة الزمنية", "listReasonForExtraTime": "قائمة أسباب توفير الوقت الإضافي", "maximumLateEntryStudent": "الح<PERSON> الأقصى للدخول المتأخر للطلاب من وقت بدء الامتحان (دقائق)", "restrictStudentLateEntry": "تقييد الدخول المتأخر للطلاب ", "attendanceManagement": "إدارة الحضور"}, "studentAuthentication": {"studentAuthentication": "مصادقة الطالب", "facialAuthentication": "المصادقة ببصمة الوجه", "facialIdAuthentication": "المصادقة ببصمة الوجه", "fingerprintAuthentication": "المصادقة ببصمة الإصبع", "performOnpermiseTitle": "إجراء مصادقة الوجه على الخادم عند الإذن لإجراء الاختبارات في مقر الشركة", "performOnpermiseDescription": ".ملاحظة: لا تتطلب مصادقة الوجه اتصالاً بالإنترنت"}, "onsiteMenu": {"examCoordinator": {"examManagement": "إدارة الامتحانات", "examCoordinator": "منس<PERSON> الامتحان", "managingProctors": "إدارة مراقبين الامتحانات", "ProctorReselectionSettings": "إعدادات إعادة اختيار مراقب الامتحانات", "ExamManagementForTestCenters": "إدارة الامتحانات - لمراكز الامتحانات", "examManagementDesc": "إيقاف الامتحانات مؤقتًا، و إعادة الامتحانات، و إنهاء الامتحانات، و إعطاء وقت إضافي لواحد / جميع مراكز الامتحانات"}, "proctor": {"proctor": "مراقب الامتحانات", "attendanceManagement": "إدارة الحضور", "maximumLateEntryTimeForProctors": "الحد الأقصى للدخول المتأخر للمراقبين ", "pauseExamUploadAnswers": "امتحان وقفة", "studentManagement": "إدارة الطلاب", "authenticationTagMisbehaviourGiveExtraTime": "للمصادقة، للإبلاغ عن سوء السلوك، لمنح وقت إضافي"}, "student": {"student": "الطالب", "attendanceManagement": "إدارة الحضور", "MaximumLateEntryTime": "الحد الأقصى للدخول المتأخر", "studentAuthentication": "مصادقة الطالب", "facialIdOnFingerprintON": "معرف الوجه", "facialId": "معرف الوجه"}}}, "assessmentItemType": {"newItemType": "نوع عنصر جديد", "selectShortCode": "اختر رمز قصير", "itemType": "نوع العنصر", "shortCode": "<PERSON><PERSON><PERSON> قصير"}, "reportsAnalyticsSettings": {"reportAndAnalyticsSettings": "إعدادات التقارير و التحليلات", "settingsImpactExamReports": "الإعدادات التي تؤثر على تقارير الامتحان وتقارير النتائج", "exportReport": "تصدير التقارير", "permissionFormatsLists": "صلاحيات، تنسيقات، قائمة التقارير المراد تصديرها", "publishReport": "نشر التقارير", "difficultyIndexDiscriminationIndex": "مؤشر الصعوبة ، مؤشر التمييز ، عوامل التشتت ، الصلاحيات", "listOfReportsPublished": "قائمة التقارير التي سيتم نشرها", "grading": "وضع الدرجات", "gradeSystem": "نظام الدرجات", "outcomeMetricsAchievement": "مقاييس النتائج - الإنجازات", "outcomeMetricsItems": " عناصر مقاييس النتائج", "TargetAchievementLowAchievementValues": "الإنجاز المستهدف، قيم الإنجاز المنخفضة", "faultyItemsLowQualityItems": "العناصر المعيبة ، العناصر ذات الجودة المنخفضة", "markOutcomeNotValid": "النسبة المئوية للدرجات المطلوبة لتحقيق نتائجها غير صحيحة. من فضلك أدخل رقما صالحا", "outcomeAchievementForSAQ": "تحقيق النتائج ل س.ق.ا و س.ا.ق.ا و س.ش. أسئلة ونتيجة الامتحانات الخارجية", "markRequiredToAchieveOutcome": "النسبة المئوية للدرجات المطلوبة لتحقيق نتائجها", "permissionActivatedSuccessfully": "تم تفعيل الصلاحيات بنجاح", "permissionDeactivatedSuccessfully": "تم إلغاء تفعيل الصلاحيات بنجاح", "weightageNotMatching": "الوزن غير مطابق، يجب أن يطابق 100%", "exportReports": {"studentResultsOriginal": "نتائج الطالب - الأصلية", "studentResultsFinalized": "نتائج الطلاب - النهائية", "studentResponseReportOriginal": "تقرير رد الطلاب - الأصلي", "studentResponseReportFinalized": "تقرير رد الطلاب - النهائي", "itemAnalysisReportOriginal": "تقرير تحليل العناصر - الأصلي", "itemAnalysisReportFinalized": "تقرير تحليل العناصر - النهائي", "detailedItemAnalysisReportOriginal": "تقرير تحليل العناصر المفصل - الأصلي", "detailedItemAnalysisReportFinalized": "تقرير تحليل العناصر المفصل - النهائي", "exportReports": "تصدير التقارير", "exportFormats": "تصدير النماذج", "allFormats": "جميع النماذج", "individualReportsExported": "يسمح بتصدير تقارير الطلاب الفردية", "rolesPermission": "صلاحيات دورك ستحدد إذا كان من الممكن تعديل الإعدادات أدناه.", "moreInformation": "لمزيد من المعلومات ، اتصل بالمسؤول النظام.", "allReports": "كل التقارير", "csv": "csv", "xlsx": "xlsx", "pdf": "pdf", "selectTypeOfReports": "حدد أنواع التقارير التي سيتم تصديرها للدورات المحددة", "selectGender": "<PERSON><PERSON><PERSON>  الجن<PERSON>"}, "publishReports": {"individualGradeReport": "تقرير الدرجات الفردي", "cloPloAnalysisReport": "نتيجة مستوى المنهج و نتيجة مستوى البرنامج تقرير تحليل", "consolidatedGradeReport": "تقرير الدرجات الموحد", "outcomeReport": "تقرير النتائج", "gradeReport": "تقرير الدرجة ", "reportAnalyticsSettings": "إعداد التقارير والتحليلات", "publishReports": "نشر التقارير", "reportsAfterPublished": "يمكن مشاركة التقارير بعد نشرها", "systemNumber": "رقم النظام", "detailedReport": "تقرير مفصل", "studentViewIndividualGradeReport": "يمكن للطالب عرض العلامات الفردية لكل عنصر في تقرير الدرجات", "nationalAccreditationReport": "تقرير معايير الاعتماد الوطنية", "studentOutcomesAnalysis": "تحليل نتائج الطلاب", "nationalAccreditationStandards": "بيانات الامتثال لمعايير الاعتماد الوطنية", "comprehensiveGradeAnalysis": "تحليل شامل للدرجات في جميع الدورات", "learningOutcomeAssessment": "تقييم نتائج التعلم ومقاييس الإنجاز", "studentCriteriaEvaluation": "تقييم نتائج الطلاب والمعايير", "chooseReport": "اختر التقرير", "cloAndPloreport": "تقرير نتائج تعلم الدورة ونتائج تعلم البرنامج", "itemAnalysis": "تحليل العناصر", "noOfTimesRevised": "عدد مرات المراجعة", "noOfTimesEvaluated": "عدد مرات التقييم", "selectOneToSeeDetails": "اختر واحدة لرؤية التفاصيل", "reEvaluate": "إعادة التقييم", "reRevise": "إعادة المراجعة", "cancelReRevision": "إلغاء إعادة المراجعة", "noTopicsFound": "لم يتم العثور على مواضيع"}, "itemRevision": {"itemRevisionForAssessmentResults": "مراجعة البند لنتائج التقييم", "minimumSampleSizeNotValid": "الحد الأدنى لحجم عينة مؤشر التمييز غير صالح. من فضلك أدخل رقما صالحا.", "difficultyIndex": "مؤشر الصعوبة", "difficultyItem%": "عنصر صعب٪", "suitableItem%": "عنصر مناسب٪", "easyItem%": "عنصر سهل٪", "excellent": "مم<PERSON><PERSON><PERSON>", "good": "<PERSON>ي<PERSON>", "poor": "ضعيف", "worst": "أسوأ", "discriminationIndex": "مؤشر التمييز", "showDiscriminationIndex": "إظهار مؤشر التمييز,", "sampleSizeAbove": "فقط عندما يكون حجم العينة (إجما<PERSON>ي عدد الطلاب الذين حضروا الامتحان) أعلى من", "distractors": "المشتتات", "lowFunctionalStudentResponse": "المشتتات الوظيفية المنخفضة أو الغير مشتتة للانتباه - استجابة الطالب (٪)", "highFunctionalStudentResponse": "المشتتات الوظيفية المرتفعة - استجابة الطالب أعلى (٪)", "allowMarkingFaulty": "السماح بوضع علامة \"معيب - يحتاج إلى تصحيح\" ، أثناء مراجعة العنصر", "allowRevertRevisedResult": "السماح بـ \"إعادة النتائج المراجعة إلى الأصل\" للطالب الفردي قبل الانتهاء", "permissions": "صلاحيات", "difficult": "صعب", "suitable": "من<PERSON><PERSON><PERSON>", "easy": "سهل", "finalizedToPublishAssessment": "نهائي وجاهز للنشر لنتائج التقييم:", "itemRevisionForAssessment": "مراجعة البند لنتائج التقييم:", "publishedAssessmentResults": "نتائج التقييم المنشورة:", "reRevisedAssessmentResults": "إعادة مراجعة نتائج التقييم:", "reevaluationTriggeredSuccessfully": "تم تشغيل إعادة التقييم بنجاح", "publishCourse": "نشر بالطبع", "finalizedReadyToPublish": "نهائي وجاهز للنشر", "reRevised": "إعادة المراجعة", "reEvaluated": "تم إعادة تقييمها", "inRevision": "قيد المراجعة", "inRevisionMe": "قيد المراجعة - أخصائي التعليم الطبي", "courseAndStatus": "المقرر والحالة", "reviseItems": "مراجعتها", "gradeDistributionCurve": "منحنى توزيع الدرجات", "itemQualityDistribution": "توزيع جودة الصنف", "itemsEvaluatedTotalItems": "توزيع جودة الصنف", "afterRevision": "بعد المراجعة", "beforeRevision": "قبل المراجعة", "searchModule": "بحث حسب الدورة / الوحدة", "originalResults": "النتيجة الأصلية", "impact": "التأثير", "finalResult": " النتيجة النهائية", "originalResult": "النتيجة الأصلية", "onlyAutoEvaluatedItems": "العناصر التي تم تقييمها تلقائيًا فقط", "afterManualEvaluation": "بعد التقييم اليدوي", "finalAfterFurtherRevision": "نهائي (بعد المراجعة )", "track": "مسار"}, "outcomeMetricAchievement": {"commonValueAchievement": "تحقيق الهدف المشترك", "target": "هد<PERSON>", "outcomeWiseValueAchievement": "النتيجة الحكيمة الهدف الإنجاز", "pleaseEnterValidNumber": "النسبة المئوية للعلامة المطلوبة لتحقيق نتائجها غير صحيحة. من فضلك أدخل رقما صالحا.", "targetBenchMarkSIR": "المعيار المستهدف لتحسين الطلاب", "courseSIR": "المقرر / الوحدة", "subjectSIR": "الموضوع", "topicSIR": "الموضوع الفرعي"}, "reportGrading": {"alphaNumeric": "رقمي غير مسموح به. يرجى تقديم ألفا رقمي / ألفا بأحرف خاصة", "activeGradeSystem": "نظام الدرجات النشط", "addGradeSystem": "+ إضافة نظام درجات", "addNewGrade": "+ إضافة درجة جديدة", "noGradeSystem": "لا يوجد نظام درجات", "editName": "تعديل الاسم", "addGrade": "إضافة درجة", "gradeName": "اسم الدرجة", "editGrade": "تعديل الدرجة", "addAGradeSystem": "أضف نظام درجات", "gradeSystemName": "اسم نظام الدرجات", "editGradeSystem": "تعديل نظام الدرجة", "studentGradeFail": "يعتبر الطالب الحاصل على هذه الدرجة راسبًا", "doYouWishProceedWithoutSaving": "هل ترغب في المضي قدما دون حفظ", "nameAlreadyExist": "الاسم موجود مسبقا", "rangeAlreadyExist": "النطاق موجود مسبقا", "from": "(٪) من ", "to": "(٪) لـ ", "selectActiveGrade": "اختر الدرجة النشطة", "selected": "مخ<PERSON><PERSON>ر", "added": "مضاف", "active": "(نشط)", "fail": "(فشل)", "archiveErrorMessage": "<PERSON>ير قادر على أرشفة نظام الصف النشط", "gradeLimitError": " 'يجب أن تكون قيمة 'من' أقل من قيمة 'إلى"}}, "frameWorks": {"domainTheme": "المجال / الموضوع من أجل إطار العمل المختار", "newFrameWorkName": "اسم إطار العمل الجديد", "frameWorkCode": "ر<PERSON>ز إطار العمل ", "code": "<PERSON><PERSON><PERSON>", "addDomainTheme": "{{mode}} {{for}}", "domainThemeTitle": "المجال / الموضوع"}, "facial": {"faceCapture": "التقاط الوجه", "capture": "التقاط", "captureFace": "التقط الوجه", "autoRenew": "يجدد تلقائياً", "retake": "إعادة الالتقاط", "selectedCamera": "كاميرا المختارة", "webCamera": "ويب كاميرا", "moreThanOneFaceIsDetected": "تم اكتشاف أكثر من وجه", "pleaseRetakeAgain": "الرجاء إعادة الالتقاط مرة أخرى", "currentPictureDoesNotMatch": "الصورة الحالية لا تتطابق مع الصورة السابقة. الرجاء إعادة التقاط هذه الصورة", "uploadFacial": "تحميل الوجه"}, "specialSetting": {"specialSettings": "إعدادات خاصة", "assessmentResetMessage": "بسبب إعادة تعيين تاريخ الاستحقاق ، إعادة تعيين بطاقة التقييم ،يرجي تغيير إعدادات التقييم ", "specialAssessmentSetting": "إعدادات خاصة> مؤلف التقييم", "dueDateReset": "إعادة تعيين تاريخ الاستحقاق", "assessmentCardReset": "إعادة تعيين بطاقة التقييم", "editAssessmentSetting": "تعديل إعدادات التقييم", "selectExamType": "حدد نوع الاختبار", "changeDueDate": "تغيير تاريخ الاستحقاق", "scheduleDate": "تاريخ الجدولة", "cardTakenBy": "تم أخذ البطاقة بواسطة", "cardStatus": "حالة البطاقة", "confirmCourseCode": "للتأكيد ، اكتب \" {{courseCode}} \" في حقل إدخال النص أدناه", "dueDateAA": "تاريخ استحقاق التقييم ينشر المؤلف التقييم", "dueDateIA": "تاريخ استحقاق لمؤلف العنصر لإنشاء جديد أو مراجعة قديمة"}, "advanceOutcome": {"title": "إعدادات النتائج المتقدمة", "performanceSettings": "إعدادات مستوى الأداء", "benchmarkSettings": "الإعدادات القياسية", "impactSettings": "تحليل خرائط التأثير", "contentSetting": "تحليل خرائط المحتوى", "performanceSettingsDescription": "تسمح لك ميزة مستوى الأداء بتعيين وتتبع نطاقات الأداء بأسماء وأوصاف ورموز ألوان فريدة.", "benchmarkSettingsDescription": "تسمح لك ميزة قياس الأداء بتعيين الحد الأدنى من معايير الأداء للطلاب وتتبع تقدمهم مقابل هذه المعايير.", "impactSettingsDescription": "تتيح ميزة تحليل التأثير للمستخدمين تقييم أهمية العناصر المختلفة داخل النظام أو العملية من خلال تعيين تسميات مثل مرتفع أو متوسط ​​أو منخفض، مما يساعد على تحديد أولويات التأثير الذي حدث أثناء رسم خرائط المصفوفة.", "contentSettingDescription": "يعمل تخطيط المحتوى على مواءمة محتوى الدورة مع الأهداف أو المعايير التعليمية، مما يضمن الملاءمة وتحديد الثغرات في المنهج الدراسي.", "changeHeader": "تغيير الرأس", "changeDescription": "قم بتغيير الوصف", "institution": "مؤسسة", "percentageRange": "نطاق النسبة المئوية", "minPercentage": "دقيقة. نسبة مئوية", "weightage": "الوزن", "label": "ملصق", "descriptionOptional": "وصف (اختياري)", "color": "لون", "addLevel": "<PERSON><PERSON><PERSON> المستوى", "minimumValue": "الح<PERSON> الأدنى للقيمة", "maximumValue": "القيمة القصوى", "componentManagement": "إدارة المكونات", "addRange": "إضافة نطاق", "max4character": "الح<PERSON> الأقصى 4 أحرف فقط", "lettersExceedMax4character": "تجاوزت الحروف. الح<PERSON> الأقصى 4 أحرف فقط", "chooseDifferentColor": "يرجى اختيار ألوان فريدة لكل من القيم الأربعة. الألوان المكررة غير مسموح بها."}, "componentManagement": {"dynamicComponentsCreation": "إنشاء المكونات الديناميكية", "systemComponents": "مكونات النظام", "componentMapping": "رسم خرائط المكونات", "createNewComponent": "إنشاء مكون جديد", "dynamicMappingConfiguration": "تكوين الخرائط الديناميكية", "programName": "إسم البرنامج", "curriculumName": "اسم المنهج", "coursesMapped": "الدورات المعينة", "courseList": "قائمة الدورة", "giveComponentName": "إعطاء المكون اسما", "labelTheHierarchy": "قم بتسمية التسلسل الهرمي", "soComponents": "مكونات SO", "showThisComponent": "إظهار هذا المكون في منطقة إنشاء العنصر", "chooseFrameworkOptional": "اختر الإطار (اختياري)", "chooseNumbering": "اختر الترقيم", "dynamicComponentCreation": "إنشاء المكونات الديناميكية", "courseMatrixMapping": "رسم خرائط مصفوفة الدورة", "componentName": "اسم المكون", "curriculum": "مق<PERSON><PERSON>", "sharedCourses": "الدورات المشتركة", "sharedCourseInfo": "الدورات مشتركة مع برامج أخرى. تعتبر CLO الخاصة بالمقررات الدراسية التي تم تعيينها مع PLO المتعددة بمثابة دورات مشتركة", "totalCourses": "إجمالي الدورات", "filters": "المرشحات", "clearAll": "ا<PERSON><PERSON><PERSON> الكل", "level": "مستوى", "row": "صف", "column": "<PERSON><PERSON><PERSON><PERSON>", "coursesOf": "دورات", "flowPreview": "معاينة التدفق", "so": "لذا", "sharedTo": "مشترك ل", "sharedFrom": "مشترك من", "searchCourses": "البحث في الدورات", "noComponentsCreated": "لم يتم إنشاء مكونات تحت البرامج للعرض.", "applyFilter": "تطبيق مرشح", "filterBy": "مصنف بواسطة", "introductory": "استهلالي", "reinforcement": "تعزيز", "emphasize": "يؤكد", "contentMapping": "رسم خرائط المحتوى", "courseName": "اسم الدورة التدريبية", "heatTransfer": "انتقال الحرارة", "soDescription": "وضع افتراضات مبررة بشكل معقول للمعلومات المفقودة بناءً على المعايير أو أفضل الممارسات", "nameRequired": "اسم المكون مطلوب", "curriculumRequired": "مطل<PERSON><PERSON> المنهج", "hierarchyLabel": "تسمية التسلسل الهرمي مطلوبة", "threeLevelsRequired": "مطلوب 3 مستويات على الأقل لكل تسلسل هرمي", "descriptionShouldBeEmpty": "يجب ألا يكون الوصف فارغًا", "contentMappingConfiguredInSettings": "يرجى التأكد من تكوين تعيين المحتوى في الإعدادات قبل المتابعة", "doYouWantToProceed": "هل ترغب في متابعة إنهاء العملية أم الاستمرار؟ إذا كنت تفضل إنهاء الأمر، فيمكنك تكوينه مرة أخرى في أي وقت.", "goBackToPreviousScreen": "العودة إلى الشاشة السابقة", "allDataAutoSaved": "يتم حفظ جميع البيانات المدخلة تلقائيًا. يمكنك الاستمرار في إنشاء المكون في أي وقت. هل أنت متأكد أنك تريد العودة؟ ", "allProgramUnderInstitution": "يمكن لجميع البرامج التابعة لهذه المؤسسة الوصول إلى هذا المكون", "thresholdIsRequired": "يرجى تقديم قيمة العتبة", "weightageWarning": "يرجى ملاحظة أن تطبيق الترجيح سيؤدي إلى إعادة إنشاء تقرير التحليل. بمجرد التقديم، يجب عليك إكمال عملية التحليل لإضافة تقرير الدورة إلى تحليل مستوى البرنامج. هل ترغب في المتابعة؟", "avgOfSubunits": "متوسط الوحدات الفرعية", "avgOfStudentMarks": "متوسط علامات الطلاب", "avgOfStudentMarksInfo": "متوسط ​​جميع درجات الطلاب للأسئلة التي تم وضع علامة عليها ضمن وحدة معينة لأداء المقياس هذا.", "chooseCalculationMethod": "اختر طريقة الحساب:", "calculationMethodRequired": "طريقة الحساب مطلوبة"}, "common": {"selectParentWillAppliedToChildren": "ما يتم تحديده للعنصر الأساسي سيتم تطبيقه تلقائيًا على العناصر التابعة.", "selectChildWillNotAppliedToParent": "لن يتم تطبيق ما يتم تحديده للعنصر التابع على العنصر الأساسي أو العناصر التابعة الأخرى.", "newItemName": "اسم العنصر الجديد", "availableType": "الأنواع المتوفرة", "examTitle": "دليل المصطلحات", "onSite": "الامتحان في الموقع", "remote": "الامتحان عن بعد", "proctor": " بمراقب", "notProctor": "بدون مراقب", "fullScreen": "عرض بملء الشاشة", "shortForm": "نموذج قصير", "onsiteProctor": "في الموقع بمراقب", "remoteProctor": "في الموقع بدون مراقب", "selectExportType": "حدد نوع التصدير", "examCategories": "فئات الامتحان", "selectExamCategories": "حدد فئات الامتحان", "onsiteExamConductInDigiassess": "إجراء امتحان وجودي في DigiAssess", "offsiteExamConductInOutSide": "إجراء امتحان خارجي", "settingsUpdatedSuccessfully": "تم تحديث الإعدادات بنجاح", "configurationNotes": "ملاحظة: سيعمل التكوين فقط لإنشاء التقييم الذاتي", "applicableFor": "ينط<PERSON><PERSON> على"}}, "dashboard": {"userManagement": {"userManagement": "إدارة المستخدمين", "overallImportStatus": {"title": "حالة الاستيراد العامة", "totalImportedUsers": "إجمالي المستخدمين المستوردين (الطلاب وأعضاء هيئة التدريس)", "validated": "تم التحقق من صحة بيانتهم", "pendingValidation": "في انتظار التحقق من صحة بيانتهم", "studentsPendingValidation": "الطلاب في انتظار التحقق من صحة بيانتهم", "requestNotSent": "لم يتم إرسال الطلب", "dataVerificationPending": "في انتظار التحقق من البيانات", "biometricVerificationPending": "التحقق البيومتري معلق", "staffsPendingValidation": " أعضاء هيئة التدريس الذين في انتظار التحقق من صحة بيانتهم"}}, "schedule": {"schedule": "جدول", "ongoingSchedule": {"title": "الجدول الجاري", "showing": "اظهار", "coursesCount": "عدد المقررات الدراسية", "viewDetails": "عرض التفاصيل", "permittedStudents": "الطلاب المسموح لهم", "assessmentsPublished": "تم نشر التقييمات"}, "upcomingScheduledAssessment": {"title": "التقييمات المجدولة القادمة"}}, "exams": {"exams": "الامتحانات", "overallAverage": "المتوسط ​​العام", "examAdmin": "مشر<PERSON> الامتحان", "liveDashboardAllModule": "لوحة التحكم المباشرة - جميع الوحدات", "testCenterManagement": "إدارة مراكز الاختبارات", "seatAllocation": "تخصيص المقعد", "macAddress": "عنوان ماك", "seatNo": "رقم المقعد", "deleteSystem": "حذ<PERSON> النظام", "confirmDeleteSystem": "هل أنت متأكد أنك تريد حذف هذا النظام؟", "courseGroupsManagement": "إدارة مجموعات المقررات", "manageActivities": "إدارة الأنشطة", "dashboardExams": "لوحة القيادة |  الإمتحانات", "dashboardSchedule": "لوحة القيادة | جدول", "dashboardActivityLog": "لوحة القيادة | سجل الأنشطة", "dashboardUserManagement": "لوحة القيادة | إدارة المستخدمين", "componentElements": "العناصر المكونة", "currentExamSession": {"title": "جلسة الامتحان الحالية", "live": "مبا<PERSON>ر", "sessionTiming": "ميع<PERSON> الجلسة", "session": "الجلسة ١", "noCurrentExamSessionFound": "لا يوجد جلسة امتحان حالياً", "SessionDetailsOfEachCourse": "تفاصيل الجلسة لكل مقرر دراسي", "courses": "المقررات الدراسية", "please": "حاول مرة أخرى", "loading": "...تحميل"}, "currentExamSessionProgress": {"testCentres": "مرا<PERSON>ز الامتحانات", "studentsReported": " الطلاب الذين حضروا", "allocatedForTheSession": "مخصص للجلسة", "permittedForTheSession": "مسموح للجلسة", "total": "مجموع", "male": "ذكر", "female": "<PERSON>ن<PERSON>ى"}, "sessions": {"upcomingSessions": "الجلسات القادمة", "sessions": "الجلسات", "sessionNum": "جلسة", "students": "طلاب", "testCentres": "مرا<PERSON>ز الامتحانات", "courses": "المقررات الدراسية", "NoUpcomingExamSessionFound": "لم يتم العثور على جلسة اختبار قادمة"}, "notification": {"notifications": "إخطارات", "dailyUpdates": "التحديثات اليومية", "markAllAsRead": "أشر على الجميع بالقراءة", "updatesForToday": "تحديثات اليوم", "youReadAllTheNotifications": " لقد قرأت كل الإخطارات", "by": "بواسطة", "reportAnalytics": "تقرير التحليلات"}, "registeredStudentsCount": {"title": "<PERSON><PERSON><PERSON> الطلاب المسجلين", "students": "طلاب"}, "overAllCountStatus": {"overall": "إجمالي", "male": "ذكر", "female": "<PERSON>ن<PERSON>ى"}, "studentsProgramCount": {"students": "طلاب", "male": "ذكر", "female": "<PERSON>ن<PERSON>ى"}, "registerStaffCount": {"title": "عدد أعضاء هيئة التدريس المسجلين", "noRolesAssigned": "لم يتم تعيين أدوار", "staffs": "هيئة التدريس"}, "smileyError": {"uhOh": "اه اوه!", "sorrySomethingWentWrong": "عذرا، هناك خطأ ما", "pleaseTryAgain": "يرجى المحاولة مرة أخرى وإعادة تحميل الصفحة", "tryAgain": "حاول مرة أخرى", "reloadPage": "إعادة تحميل الصفحة بعد بعض الوقت"}, "trackAssessmentsLive": {"title": "متابعة التقييمات المباشرة للعام الدراسي الحالي", "program": "برنامج", "total": "المجموع", "scheduled": "مجدولة", "assessmentPreparation": "إعداد التقييم", "inProgress": "جاري", "currentExamLive": "الامتحان الحالي مباشر", "resultsReports": "النتائج والتقارير", "generated": " تم إنشاؤها", "pleaseSelectType": "يرجى تحديد نوع الامتحان ونوع المحاولة", "noRecordsFound": "لا توجد سجلات", "noExamTypeAndAttemptTypeFound": "لا يوجد نوع امتحان ونوع محاولة موجودين", "noExamTypeFound": "لم يتم العثور على نوع الامتحان", "noAttemptTypeFound": "لم يتم العثور على نوع المحاولة"}}}, "exams": {"common": {"academicYear": "السنة الأكاديمية", "addStudentsIndividually": "أ<PERSON><PERSON> الطلاب بشكل فردي", "uploadFiles": "رفع الملفات", "academicNumber": "الرقم الأكاديمي", "filteredGroupsNotFound": "المجموعات التي تمت تصفيتها غير موجودة", "roomNo": "رقم الغرفة", "for": "الي", "scheduleNewRequest": "جدولة طلب جديد", "noRequestScheduledYet": "لا توجد طلبات مجدولة حتى الآن", "pleaseSelectCourse": "الرجاء تحديد الدورة مع التسلسل الهرمي لإنشاء طلب الجدول الزمني.", "scheduleRequestsSentSuccessfully": "تم إرسال طلبات الجدول الزمني بنجاح", "scheduleRequestApprove": "طلب الجدول/موافقة", "assignQuestionPaperIncharge": "تعيين مؤلف الورقة الأسئلة", "approvedSuccessfully": "تم الموافقة بنجاح"}, "sendNotification": {"publishScheduleSendNotification": "أنشر الجدول الزمني وارسل إخطار", "customizeMessage": "تخصيص الرسالة", "collapseMessage": "صغر الرسالة", "AllGroups": "كل المجموعات", "notificationsNotSent": "لم يتم إرسال إخطارات", "sendEmail": "ارسل بريد إلكتروني", "sendSMS": "ارسل رسالة نصية قصيرة", "sendAppNotification": "إرسال إخطار في التطبيق", "publishTimetable": "هل أنت متأكد من نشر الجدول الزمني للمقررات المجدولة المختارة؟", "selectedCourse": "بالنسبة للمقررات المحددة ، سيتم إرسال الإخطارات", "proctors": "المراقبون", "assessmentAuthors": "مؤلفين التقييم", "expand": "وسع لعرض كل القرارات", "sent": "ارسل", "notScheduled": "الامتحان غير مجدول بعد", "noCourse": "لم يتم العثور على مقررات تعليمية", "notified": " الطلاب المبلغون", "newTiming": "أدخل وقت جديد لجلسة الامتحان", "repeat": "كرر للقادم", "sessionDateTime": "موعد و وقت الجلسة", "yearsLevels": "سنوات ومستويات", "courseGroups": "مجموعات المقررات التعليمية", "noStatus": "لا يوجد حالة حتى الآن", "createTimetable": "أنشئ جدول زمني للبدء", "programme": "برنامج", "scheduledExamDate": "تاريخ الامتحان المقرر", "examTiming": "توقيت الامتحان", "studentsUploadedLastPublish": "<PERSON><PERSON><PERSON> الطلاب الذين تم رفعهم منذ آخر نشر", "studentsUploadedRegistered": "إجمالي عدد الطلاب الذين تم رفعهم / تسجيلهم", "studentsSentUploaded": "إرسال / رفع إخطارات الطلاب", "notificationsProctor": "الإخطارات إلى المراقب: تم ارسالها", "notificationsAssessmentAuthor": "إخطارات إلى مؤلف التقييم: تم ارسالها", "appNotification": "إشعار التطبيق", "editorChangeText": "عزيزي اسم المستخدم ، {{text}}  \n شكرًا لك ،  \n امضاء-المشرف", "replaceTestCenter": "في مركز الاختبار & lt؛ testCenterName & gt؛", "assessmentPublished": "(الاختبار منشور)", "scheduledExamDateTime": "موعد ووقت الامتحان المقرر", "viewEmailTemplate": "عرض قالب الب<PERSON>يد الإلكتروني", "studentNotifications": "إشعارات الطلاب", "proctorNotifications": "إشعارات المراقب", "newStudentsUploaded": "تم تحميل الطلاب الجدد", "authorNotifications": "إشعارات المؤلف", "exactExamStartAndEndTime": "الوقت الدقيق لبداية ونهاية الامتحان.", "lastNotificationPublish": "ع<PERSON><PERSON> الطلاب المضافين منذ نشر الإشعار الأخير.", "numberOfStudentsUploadedRegistered": "إجمالي عدد الطلاب المسجلين/المسجلين", "notificationStudents": "الإشعارات المرسلة/المحملة للطلاب", "notificationProctor": "الإشعارات إلى بروكتور: تم الإرسال", "notificationAssessmentAuthor": "الإشعارات إلى مؤلف التقييم: تم الإرسال", "allMemberTemplates": "جميع قوالب الأعضاء", "mailMembers": "الأعضاء: المراقبون والطلاب ومؤلفو التقييم"}, "examsManagement": {"editInvigiliator": "تعديل المراقب", "deleteTestCenter": "حذ<PERSON> <PERSON>ر<PERSON><PERSON> الاختبار", "assignInvigilator": "عين مراقبين", "removeInvigilator": "إزالة المراقبين", "areYouSureRemoveInvigilators": "هل أنت متأكد من إزالة المراقبين؟", "selectTCManually": "أختر مركز الأمتحان", "selectTC": "اختر مركز الاختبار", "ongoing": "جاري التنفيذ", "previousExams": "الامتحانات السابقة", "areYouSure": "هل أنت متأكد؟", "areYouSureMessage": "هل تريد إزالة الطلاب المختارين من <b>{{testCenterName}}</b>", "showStudentName": "إظهار اسم الطالب", "selectScheduleAction": "حدد إجراء الجدول الزمني ", "searchTextExamType": "البحث حسب اسم الامتحان أو رمزه", "courseGroupManagement": {"course": "إنشاء مجموعات المقررات ورمزها المختصر لكل برنامج", "none": "لا يوجد ", "generate": "إنشاء", "courseGroupGeneration": "تكوين مجموعة الدورة", "courseManagementList": {"programList": "قائمة البرامج", "programName": "اسم البرنامج", "course": "قائمة المقررات في إطار البرنامج المختار", "programGroupName": "اسم البرنامج / أسماء المجموعات", "syllabus": "المنهج ", "noCourse": "قائمة المقررات غير متاحة", "shortCodes": "رموز قصيرة"}}, "testCentreManagement": {"search": "البحث عن طريق مركز الامتحانات ، رقم الغرفة، الموقع...", "testNumber": "رقم مركز الامتحانات", "max": "الأقصى", "capacity": "السعة", "location": "المواقع", "seat": "المقاعد المحجوزة", "number": "رقم", "usable": "صالح للاستعمال"}, "pastExam": {"pastExamsData": "بيانات الامتحانات السابقة", "examCoordinator": "منسق الامتحانات", "viewExam": "عرض تفاصيل الامتحان", "previousExamData": "بيانات الامتحانات السابقة", "dateConducted": "تاريخ إجرائها", "postExamHeader": "ما بعد الامتحان", "studentList": "قائمة الطلاب", "activityLog": "سجل النشاط", "authentication": "المصادقة", "search": "اسم البحث أو المعرف الأكاديمي", "pastExamActivity": {"noSessionsFound": "لم يتم العثور على جلسة في التاريخ المحدد", "selectExam": "<PERSON><PERSON><PERSON> الامتحان", "selectTestCenter": "حد<PERSON> مركز الامتحانات", "selectRole": "<PERSON><PERSON><PERSON> الدور", "pastExamActivity": "سجل نشاط الامتحان السابق للامتحان المحدد / الامتحانات المحددة ", "students": "الطلاب الحاليون / إجمالي الطلاب", "courseExamId": "معرف الامتحان المقرر", "rolePersonName": "اسم / دور الشخص", "trigger": "التنبيه - الاجراء", "time": "وقت", "action": "اجراء", "designation": "تعيين", "timeDate": "الوقت والتاريخ", "selectAcademicId": "أختر الرقم الأكاديمي"}}, "examsManagement": {"examManagementCreateTimetable": "إدارة الاختبارات - إنشاء جدول زمني", "monitorExam": "مراقبة الاستعداد المسبق للامتحان", "selectExamType": "حدد نوع الامتحان - نوع المحاولة ", "createTimetable": "انشاء تقويم", "publishSchedule": "انشر الجدول", "rescheduleExam": "<PERSON><PERSON><PERSON> جدولة الامتحان", "cancelExam": "إلغاء الامتحان", "noPermission": "لم يتم اختيار صلاحية لاسخراج البيانات", "selectDateTime": "ح<PERSON><PERSON> التاريخ والوقت وتخصيص مركز الاختبار", "scheduleNow": "جدولة الآن", "courseDetails": "تفاصيل الدورة", "plannedStudentCount": "<PERSON><PERSON><PERSON> الطلاب المخطط له", "examDateRange": "نطاق تاريخ الامتحان", "examDurationMin": "مدة الامتحان (دقيقة)", "reCheck": "إعادة التحقق", "chooseTCFromTableBelow": "اختر مركز الاختبار من الجدول أدناه", "autoSelectTc": "مركز الاختبار الاختياري التلقائي", "autoSelectConfirmation": "هل أنت متأكد من أنك تريد تلقيح مراكز الاختبارات التلقائية؟", "autoSelectAdditionalNote": "سيتم تلقيح مراكز الاختبارات التلقائية وسيتم إنشاء طلب الجدول", "tcRoomName": "اسم مركز الاختبار", "available": "متاح", "seatsBooked": "المقاعد محجوزة", "setAllocationCount": "تعيين عدد التخصيص", "checkTestCenterAvailability": "التحقق من توفر مركز الاختبار", "viewTcs": "عرض مرا<PERSON>ز الاختبار", "testCenterAvailability": "توفر مركز الاختبار", "availableTcs": "مراكز الاختبار المتاحة مدرجة!", "endsAt": "ينتهي في", "setDate": "قم بتعيين التاريخ والوقت والمدة، ثم انقر فوق “توفر مركز الاختبار”", "autoAllocateStudents": "تخصيص تلقائي للطلاب", "examDate": "م<PERSON><PERSON><PERSON> الامتحان", "noScheduledExamsForThisDate": "لا توجد امتحانات مجدولة لهذا التاريخ", "viewScheduledExams": "عرض الامتحانات المجدولة", "requestTC": "طلب مركز الاختبار", "changeSchedule": "تغيير الجدول", "autoAllocateStudentNote": "يقوم تلقائيًا بتعيين الطلاب عبر مراكز الاختبار المحددة بناءً على السعة المتاحة"}, "courseModule": {"allCourseGroups": "كل مجموعات المقرر التعليمي", "unscheduled": "<PERSON>ير مجدول", "studentsUnfilled": "الأماكن الشاغرة", "unpublishedAssessments": "التقييمات الغير منشورة"}, "examTimeTable": {"program": "برنامج - فصل دراسي - عام - منهج -مستوى", "noOfCourses": "عدد الدورات/الوحدات المجدولة", "invigilatorsAssigned": "تعيين مراقبين لـ", "assessmentsPublished": "تم نشر التقييمات لـ", "assessmentPlanStatus": "حالة خطة التقييم", "studentUploadStatus": "حالة رفع الطلاب", "downloadTemplateStudents": "قم بتنزيل النموذج لتحميل الطلاب"}, "testTable": {"invigilator3": "مراقب ٣", "testCenterCapacity": "سعة مركز الامتحنات", "changeTC": "تغيير مركز الاختبار", "changeTestCenter": "تغيير مركز الاختبار", "selectedStudentsTC": "يتم نقل الطلاب المحددين من مركز الاختبار إلى مركز الاختبار آخر"}}, "uploadPermittedStudents": {"addStudent": "<PERSON><PERSON><PERSON>", "currentUploads": "المرفوعات الحالية", "pastUploads": "المرفوعات السابقة", "uploadFilesPermitted": "ارفع ملفات بالطلاب المسموح بحضور الامتحانات المحددة", "uploadStudentList": "ارفع قائمة الطلاب المسموح بهم إلى دي جي أسس ", "pastStudentUploads": "مرفوعات الطلاب السابقة", "listPastStudent": "قائمة مرفوعات الطلاب السابقين لـ", "downloadTemplate": "تنزيل النموذج", "getTemplate": "احصل على النموذج", "downloadFileCoursegroupManagement": "تحميل الملف من إدارة مجموعات المقررات", "removeStudent": "إزالة طالب", "studentList": "الطلاب المسموح بهم في دي جي أسس", "viewFiles": "عرض الملفات", "viewStudents": "عر<PERSON> الطلاب", "startBySelectingExams": "اب<PERSON><PERSON> بتحديد الامتحانات", "filesUploadedValidated": "تم رفع الملفات والتحقق من صحتها", "filesUploaded": " تم رفع المفات:", "file": "مل<PERSON>", "filesNotProcessed": "لن تتم معالجة هذه الملفات. ستحتاج إلى إعادة رفعها بعد تصحيحها", "errorFound": "تم العثور على خطاء", "noFilesUploaded": "لم يتم رفع أي ملفات", "uploadFiles": "رفع الملفات", "standardNaming": "اصطلاح التسمية القياسي الذي يجب اتباعه بدقة للمجموعات:", "courseName": "ط.أ-ع-س٢-ط.أ٢.٠-م١ -م.ت٢-وبس٢٠١ ", "uploadFormat": "2019-2020-MP-RT-5Y-MPCL2.0-9L-RG2-FAM535-M", "uploadCompleted": "اكتمل رفع {{completed}} من {{total}}", "error": "خطأ", "groups": "مجموعات", "emailID": " الب<PERSON>يد الإلكتروني", "dateUploaded": " تاريخ الرفع", "batch": "دفعة", "studentsUploaded": "تم رفع الطلاب", "noRecordFound": "لا توجد سجلات", "addStudentToGroup": "أض<PERSON> طالب للمجموعة", "invalidAcademicNumber": "رقم أكاديمي غير صحيح", "errorAcademicNumber": "يجب أن يتكون الرقم الأكاديمي من ٤ إلى ١٥ أرقام", "errorFirstName": "الاسم الأول مطلوب", "errorMiddleName": "الاسم الأوسط مطلوب", "lastNameRequired": "اسم العائلة مطلوب", "csvFilesSelected": "عدد ملفات (xlsx) المحددة", "csvFilesSelectedZero": "عدد ملفات (xlsx) المحددة - ٠", "selectStudentPermitted": "حد<PERSON> الطلاب المسموح لهم بدخول الامتحان", "examManagementUploadStudent": "إدارة الاختبارات > تحميل الطلاب المسموح لهم في دي جي أسس", "regularTerm": "ترم عادي", "year5": "السنة ٢", "level9": "المستوى ١", "courseCode": "<PERSON><PERSON><PERSON> المقرر", "academicNo": "الرقم الاكاديمي", "currentUploadTemplate": "نموذج_التحميل_الحالي", "allFieldsRequired": "كافة الحقول مطلوبة", "fromValidation": "من المصادقة", "verifiedSuccessfully": "تم التحقق بنجاح", "noFileFound": "لم يتم العثور على ملف", "fileDoesNotHaveMandatoryColumn": "الملف لا يحتوي على عمود إلزامي", "pleaseImportValidFile": "csv الرجاء استيراد ملف صالح ", "fileTypeValidation": "التحقق من نوع الملف", "examSelected": "تم تحديد الامتحان", "uploadedStudentsInfo": "تم تحميل عدد قليل من الطلاب داخل هذا الملف مسبقًا. قم بإجراء التغييرات وفقًا لذلك وأعد التحميل.", "removeFile": "إزالة الملف", "remove&Proceed": "إزالة والمتابعة", "mp": "ط.أ", "rt": "ع", "5y": "س٢", "mp2": "ط.أ٢.٠", "9l": " م١", "rg": " م.ت٢", "fam": "وبس٢٠١", "failedStudents": "الطلاب الراسبون", "doNotLeaveSpaceAcademicNumber": "لا تترك مساحة في الرقم الأكاديمي", "excel": {"academicNo": "الرقم الاكاديمي", "email": "الب<PERSON>يد الإلكتروني", "gender": "الجنس", "message": "الرسالة"}, "courseNameMismatch": "لا يتعرف النظام على الاسم المستخدم في الملف. صحح اسم الملف وأعد التحميل", "dataMismatch": "تم اكتشاف حالات عدم تطابق في تحميل بيانات الطالب", "invalidStudentsDetected": "تم اكتشاف طلاب غير صالحين، انقر فوق متابعة لتحديث الطلاب الصالحين فقط", "invalidInactiveStudentsDetected": "تم اكتشاف الطلاب غير الصالحين/غير النشطين.", "exportError": "قائمة أخطاء التصدير", "errorStudentList": "قائمة الطلاب المخطئة", "uploadStudentsError": "<PERSON><PERSON><PERSON> الطلاب الذين تم تحميلهم أكبر من السعة المخططة", "filled/plannedStudent": "الطلاب المكتملون/المخططون {{gender}}: {{filledStudent}}/{{plannedStudent}}", "currentUploadCount": "التحميلات الحالية: {{count}}", "needCapacity": "تحتاج إلى سعة لعدد {{gender}} من الطلاب المتبقين: {{count}}", "selectTestCenter": "حد<PERSON> مر<PERSON>ز الاختبار", "notificationHint1": "سيتلقى المجدول إشعارًا يفيد بأن المراقبين غير متاحين لهذه الدورة التدريبية في مركز الاختبار المحدد.", "notificationHint2": "سيتلقى المجدول إشعارًا يفيد بأنه تم تحميل عدد أكبر من الطلاب لهذه الدورة التدريبية", "testCenter": "مركز الاختبار", "remaining": "متبقي", "filled": "مملوء", "total": "المجموع", "addTestCenter": "إضافة مركز الاختبار", "viewLog": "سجل عرض", "dateAndTime": "التاريخ و الوقت", "username": "اسم المستخدم", "action": "فعل", "response": "إجابة", "proctorNotAssigned": "(بروكتر غير معين)", "importedStudents": "تم استيراد طلاب {{gender}}", "showMore": "أ<PERSON>ه<PERSON> المزيد", "showLess": "تظهر أقل", "studentNameInAnotherTC": "اسم الطالب في نصف يوم تجريبي آخر", "assignedTcs": "نصف يوم تجريبي مخصص", "alreadyStudentsInAnotherTC": "الطلاب المذكورين محجوزون بالفعل في نصف يوم تجريبي آخر لمقرر آخر في نفس التاريخ والوقت. هل ترغب في الانتقال إلى هذا النصف يوم التجريبي الحالي؟", "clickProceedOrCancel": "انقر على 'متابعة' أو 'إلغاء' لإزالته من القائمة والمحاولة مرة أخرى.", "duplicateStudents": "تكرار الطالب في الملف المحمل", "fileProcessingError": "خطأ في معالجة الملف"}, "uploadStudentError": {"cannotMoveToThisTC": "لا يمكن النقل إلى TC هذا", "cantMoveTestCenter": " مركز الاختبار المحدد مخصص لطلاب {{selectedTc}} فقط. لا يمكنك نقل طلاب {{selectedGender}} إلى مركز الاختبار هذا.", "removeSelection": "إزالة التحديد:", "removeSelectionHint": "قم بإزالة تحديد الطالب {{selectedGender}} وانتقل إلى مركز الاختبار هذا.", "mixedTestCenter": "مركز الاختبار المختلط:", "mixedTestCenterHint": "اختر مركز اختبار مختلط ليناسب كلا الجنسين معًا.", "testCenterFull": "مركز الاختبار كامل", "testCenterFullHint": " مركز الاختبار ممتلئ بالفعل. لاستيعاب هذا الطالب، يرجى إزالة أي طالب من المركز أو تعيينه في مركز اختبار آخر متاح", "allotSeatsForMixedTC": "تخصيص مقاعد لـ TC المختلط", "enterTheMaleAndFemaleSeatsForMixedTC": "أدخل مقاعد الذكور والإناث لـ TC المختلط", "modifyCapacity": "تعديل القدرة", "autoAssignTC": "تعيين TC تلقائيًا", "withoutMixed": "هل أنت متأكد أنك تريد تعيين الطلاب في مركز الاختبار تلقائيًا بناءً على المقاعد المتاحة؟ سيتم إضافة الطلاب النشطين/الصالحين فقط.", "uploadedStudents": "الطلاب الذين تم تحميلهم: {{count}}", "totalTCCapacity": "إجمالي سعة TC: {{upload}}/{{total}}", "withMixedHint": "يتم تعيين الطلاب المتبقين في مركز الاختبار المتبقي تلقائيًا بناءً على المقاعد المتاحة", "testCenterLimitExceed": "تجاوز حد مركز الاختبار", "testCenterLimitExceedMessage": "مركز الاختبار تم الوصول إلى السعة القصوى. لا يمكن تحميل أكثر من {{tcCount}} من الطلاب", "mixedTcConfirmation": "قد يؤدي تقليل عدد الطلاب إلى إعادة تعيين قائمة الطلاب المعينين، وسيتم إعادتهم إلى الحالة الافتراضية. ومن الضروري إعادة تعيين الطلاب إلى مركز الاختبار.", "importedDataIsEmpty": "البيانات المستوردة فارغة", "mixedTcCountError": "مطلوب مركز اختبار مختلط/عدم تطابق العدد المخطط له", "testCenterAddedSuccessfully": "تمت إضافة مركز الاختبار بنجاح.", "studentUploadDenied": "تم رفض تحميل الطالب", "studentInActive": "تم اكتشاف الطلاب غير النشطين", "inactiveStudentMessage": "اكتشف النظام طلابًا غير نشطين في المجموعة المحددة. هل ترغب في تضمين هؤلاء الطلاب أو استبعادهم؟", "inactiveStudents": "الطلاب غير النشطين", "studentName": "<PERSON><PERSON><PERSON> الطالب"}, "assignDateTimeHeader": {"maleStudents": "الطلاب الذكور:", "femaleStudents": "الطالبات:", "academicYear": "السنة الأكاديمية", "change": "غير", "assignDateTime": "تعيين تاريخ و وقت الجلسة", "rescheduleDateTime": "أعادة جدولة تاريخ و وقت الجلسة", "dateAssignedSuccess": "تم تعيين التاريخ والوقت بنجاح", "examDuration": "مدة الامتحان", "selectStartTime": "حدد وقت البدء", "defaultTime": "الوقت الافتراضي (وقت بدء الجلسة)", "enterStartTime": "أدخل وقت البدء", "duration": "{{duration}} دقيقة", "selectCommonTestCenter": "حد<PERSON> مر<PERSON>ز الاختبار المشترك", "genderWiseSegregate": "الفصل حسب الجنس", "mixedGender": "جن<PERSON> مختلط", "uploadStudentsToTc": "تحميل الطلاب إلى مركز الاختبار", "autoAssignTC": "تعيين تلقائي لمركز الاختبار", "plannedStudents": "الطلاب المخطط لهم", "totalTcCapacity": "سعة المركز الاختبار الإجمالية", "selectedTcCapacity": "سعة المركز الاختبار المحددة", "examCode": "الامتحان : {{examCode}}"}, "assignDateTimePicker": {"freeTc": " مراكز الاختبار الخالية:", "addSessionTime": "أضف وقت الجلسة", "timeRepeatedSuccessful": "تم إنشاء وقت الجلسة الجديد بنجاح", "examManagement": "إدارة الامتحانات > المجدول > إنشاء جدول زمني", "availableSeats": "{{numberSeatsMale}} - <PERSON><PERSON><PERSON>{{totalMaleTestCenter}} | {{numberSeatsFemale}} - انثي{{totalFemaleTestCenter}}", "customTimeError": "لا يمكن جدولة الاختبار لأن مدة الاختبار تتجاوز الفترة الزمنية المحددة.", "sessionTimeMsg": "تم استخدام مركز الاختبار بالفعل في جلسة أخرى ({{startHour}}. {{startMin} {{startFormat}} - {{endHour}}. {{endMin}} {{endFormat}}) إذا كنت تريد هذا المساهم الأساسي ، فحدد الجلسة أعلاه", "actAs": "بمثابة {{gender}} ح", "termsAndCondition": "الشروط والأحكام", "agreeToFillMale": "الموافقة على ملء الطلاب الذكور أولاً عند اختيار مركز الاختبار المختلط,", "mixedTestCenterWarning": "تحذير: سيتم تعيين الطلاب المتبقين تلقائيًا إلى مركز الاختبار المختلط", "pleaseSelectMaleTc": "يرجى اختيار مركز اختبار واح<PERSON> على الأقل للذكور.", "pleaseSelectFemaleTc": "الرجاء تحديد مركز اختبار واحد على الأقل للإناث.", "pleaseAllocateSeatForAllTcs": "الرجاء تخصيص مقعد وا<PERSON><PERSON> على الأقل لكل مركز اختبار", "maleSeatAllocation": "عد<PERSON> المقاعد المخصصة للذكور أقل من العدد المخطط له. يرجى تخصيص المزيد من المقاعد للذكور للمتابعة.", "femaleSeatAllocation": "عد<PERSON> المقاعد المخصصة للإناث أقل من العدد المخطط له. يرجى تخصيص المزيد من المقاعد للإناث للمتابعة.", "maleSeatAllocationExceeds": "عد<PERSON> المقاعد المخصصة للذكور يتجاوز العدد المخطط له.", "femaleSeatAllocationExceeds": "عد<PERSON> المقا<PERSON>د المخصصة للإناث يتجاوز العدد المخطط له.", "noSeatsAvailable": "المقاعد غير متاحة في أيٍّ من مراكز الاختبار. يُرجى تحديث القائمة والمحاولة مجددًا للتحقق من مراكز الاختبار المتاحة إن وُجدت.", "allocationExceeds": "المقاعد المخصصة تتجاوز العدد المخطط له", "autoAllocateStudents": "تعيين الطلاب تلقائيًا إلى مراكز الاختبار", "manualAllocateStudents": "تعيين الطلاب يدويًا إلى مراكز الاختبار"}, "assignInvigilatorDialog": {"addInvigilators": "إضافة مراقبين:", "invigilatorNameRequired": "مطلو<PERSON> اسم المراقب", "staffLeastAssigned": "الموظفين الأقل تعييناً للمراقبة هذا العام الدراسي", "timesInvigilated": "عدد مرات المراقبة", "lastAssigned": "آخر تعيين", "noStaffsFound": "لم يتم العثور على أعضاء هيئة تدريس", "belongsToTheSameCourse": "ينتمي إلى نفس المقرر", "staffId": "معرف الموظفين", "invigilatorAlreadyAssigned": "المراقب معيين بالفعل", "availability": "التوفر", "availabilityDescription": "يتم تحديد التوفر بناءً على جداول DigiClass وتعيينات المراقبين للفترة الزمنية المحددة."}, "createTimeTableHeader": {"examLocationAndProctoringType": "مكان الامتحان ونوع المراقبة", "createCompleteList": "لإنشاء القائمة الكاملة لمجموعات المقررات التعلمية تلقائيًا ، يرجى التأكد من الإعلان عن نوع الامتحانات لكل مقرر", "createTimeTableExam": "إنشاء جدول زمني لنوع الامتحان التالي", "selectScheduleRescheduleCourse": "حدد الدورات المجدولة لإعادة جدولة الاختبار", "selectScheduleCancelCourse": "حدد الدورات المجدولة لإلغاء جدولة الاختبار", "editTimeTableExam": "تعديل الجدول الزمني للنوع التالي من الامتحان"}, "createTimeTableList": {"invigilator1": "مر<PERSON><PERSON><PERSON> أول", "invigilatorAssignedSuccessfully": "تم تعيين المراقب بنجاح", "plannedStudent": "تم تحديث الطلاب المستهدفين بنجاح", "plannedStudentCount": "يجب أن يكون عدد الطلاب المخطط له أكبر من صفر للذكور أو الإناث ", "plannedNoOfStudentsMaleFemale": "الطلاب المخططون ذكورا وإناثا", "plannedNoOfStudentsFemale": "الع<PERSON><PERSON> المخطط له من الطالبات", "plannedNoOfStudentsMale": "العد<PERSON> المخطط للطلاب الذكور", "courseModule": "الدورة/الوحدة", "plannedNoOfStudents": "<PERSON><PERSON><PERSON> الطالبات المخطط لهن", "selectedSessionTime": "وقت الجلسة المحدد ", "assignInvigilator": "عين مراقب", "sureReschedule": "هل أنت متأكد من إعادة الجدولة؟", "cancelCourseExam": "إلغاء الامتحان للمقرر المختارة والامتحان", "addCustomSettings": "إضافة إعدادات مخصصة", "allocatedCount": "الع<PERSON><PERSON> المخصص", "secondaryInvigilator": "مرا<PERSON>ب ثانوي", "plannedFemale": "أنثى مخططة", "plannedMale": "ذكر مخطط"}, "exportPopup": {"selectStaff": "<PERSON><PERSON><PERSON> حدد أعضاء هيئة التدريس", "selectDate": "ح<PERSON><PERSON> تاريخ", "selectSessionTime": "حدد وقت الجلسة", "exportList": " تصدير القائمة حسب مراكز الامتحانات والجدول الزمني لـ", "notScheduled": "لم تتم جدولة جميع المقررات. هل ترغب في المتابعة على أي حال؟", "allInvigilators": "جميع المراقبين", "allTestCenter": "جميع مراكز الاختبار", "individualInvigilators": "مراقب فردي", "otherInvigilators": "المراقبين الآخرين في نفس مركز الامتحان", "invigilator": "المراقب", "signature": "توقيع", "dateExam": "تاريخ الامتحان", "individualInvigilatorList": "معاينة قائمة المراقب الفردي", "invigilatorType": "نوع المراقب", "thereIsNoTestCenterAvailable": "لا يوجد مركز اختبار متاح في وقت الجلسة ", "noRecordAvailable": "لا يوجد سجل متوفر"}, "exportPdf": {"individualCourseExport": {"student": {"testCenter": "{{testCenterName}}, رقم الغرفة, {{roomNumber}}", "testCenterRemote": "{{testCenterName}}", "examDate": "تاريخ الامتحان : {{date}}", "sessionTime": "وقت الجلسة : {{time}} ", "startTime": "وقت البدء : {{time}}", "signature": "التوقيع", "testCenterCapacity": " السعة \n مركز الاختبار ", "noOfStudents": "الط<PERSON><PERSON><PERSON> \n عدد  ", "studentFileName": "{{examType}} - ك<PERSON> قائمة الطلاب", "allInvigilatorFileName": "{{examType}} - كل قائمة المراقبين", "invigilatorFileName": "{{staffName}} -قائمة مركز الاختبار", "staffName": "{{staffName}} : اسم عضو هيئة التدريس ", "startDate": "{{date}} : تاريخ البدء ", "endDate": "{{date}} : تاريخ الانتهاء ", "invigilationDuty": "واجب المراقبة", "allCoursesAndAllExamType": "جميع الدورات وجميع أنواع الاختبارات", "testCenters": "مرا<PERSON>ز الامتحنات", "dateOfTheExam": "الامتحان \n تاريخ ", "sessionTimeColumn": "وقت الجلسة", "examName": "اسم الامتحان", "invigilatorType": "نوع المراقب", "otherInvigilator": "مركز الامتحان \n في نفس \n مركز الامتحانات", "nameOfInvigilators": "اسم المراقبين", "signatureOfInvigilators": "توقيع المراقبين", "noOfAbsent": ":رقم الغائب", "totalPresent": ":إجمالي الحاضر", "total": ": المجموع", "secondaryInvigilator": "مرا<PERSON>ب ثانوي"}}}, "importDigiClass": {"importFromDigiClass": "استيراد من DigiClass", "importStudentDigiClass": "استيراد الطلاب من DigiClass", "institutionCalendar": "تقويم المؤسسة", "selectedStudents": "الطلاب المختارون", "filledPlannedStudents": "الطلاب الممتلئون/المخططون", "foundationGroup": "مجموعة الأساس", "clearAll": "ا<PERSON><PERSON><PERSON> الكل", "deliveryType": "نوع التوصيل", "searchByStudent": "البحث حسب اسم الطالب، الرقم الأكاديمي، المجموعة", "studentName": "<PERSON><PERSON><PERSON> الطالب", "deliveryGroup": "مجموعة التسليم", "deniedStatus": "حالة الرفض", "importedFrom": "مستورد من", "selectedStudentsExceed": "لقد تجاوز الطلاب المختارون العدد المخطط له", "selectNumberGroups": "<PERSON><PERSON><PERSON> عدد المجموعات المطلوبة لهذه الدورة.", "firstBatch": "الدفعة الأولى لإدارة الجودة", "importStudents": "استيراد الطلاب", "createStudentGroup": "إنشاء مجموعة طلابية", "downloadImportTemplate": "تنزيل قالب الاستيراد", "studentGrouping": "تجمع الطلاب"}, "studentGrouping": {"noAssessmentAuthorFound": "يرجى تحديد على الأقل مؤلف تقييم للمتابعة", "noStudentsFound": "<PERSON><PERSON><PERSON> الطلاب من تجمع الطلاب أو استيراد على الأقل طالب للمتابعة", "assignStudent": "تعيين طالب", "groups": "مجموعات", "fromTo": "من {{from}} <PERSON><PERSON><PERSON> {{to}}", "clickToViewGroups": "انقر لعرض المجموعات", "refreshGroups": "تحديث المجموعات", "groupName": "اسم المجموعة", "groupCode": "رمز المجموعة", "denialStatus": "حالة الرفض", "addStudentGroups": "إضافة مجموعات الطلاب", "studentGroups": "مجموعات الطلاب", "addStudentGroup": "إضافة مجموعة طلابية", "editStudentGroup": "تحرير مجموعة الطلاب", "studentGroupsRequired": "عدد مجموعات الطلاب المطلوبة", "restrictAuthorAccess": "تقييد وصول المؤلف", "enterStudentGroupName": "أد<PERSON>ل اسم مجموعة الطلاب", "studentGroupName": "اسم مجموعة الطلاب", "chooseGender": "اختر الجنس", "authorsPrepareAssessments": "السماح لمؤلفي التقييم المحددين فقط بإعداد التقييمات لهذه المجموعة.", "selectedItemAuthorsPrepare": "السماح لمؤلفي العناصر المحددة فقط بإعداد العناصر لهذه المجموعة.", "enabledOnlyDesignated": "عند التمكين، سيتمكن مؤلفو العنصر المعينون فقط من الوصول لعرض طلبات العناصر وإدارتها لمجموعة الطلاب هذه في بوابة مؤلف العنصر.", "enabledOnlyDesignatedAssessment": "عند التمكين، سيكون لمؤلفي التقييم المعينين فقط حق الوصول لعرض وإدارة طلبات التقييم لمجموعة الطلاب هذه في بوابة مؤلف التقييم.", "selectNumberGroups": "<PERSON><PERSON><PERSON> عدد المجموعات المطلوبة لهذه الدورة.", "firstBatch": "الدفعة الأولى لإدارة الجودة", "importStudents": "استيراد الطلاب", "createStudentGroup": "إنشاء مجموعة طلابية", "downloadImportTemplate": "تنزيل قالب الاستيراد", "studentGrouping": "تجمع الطلاب", "listOfAllGroups": "قائمة بجميع المجموعات", "nameOfTheStudentGroup": "اسم المجموعة الطلابية", "noOfImportedStudents": "<PERSON><PERSON><PERSON> الطلاب المستوردين", "studentsUnderFirstBatch": "طلاب وطالبات الدفعة الأولى", "symbol": "<PERSON><PERSON><PERSON>", "group": "مجموعة", "allStudentGroup": "كل مجموعة الطلاب", "maleGroup": "مجموعة الذكور", "femaleGroup": "مجموعة أنثى", "mixedGroup": "مجموعة مختلطة", "importTitle": "استيراد الطلاب - {{ name }}", "addStudent": "إضافة طالب", "studentGroup": "مجموعة طلابية", "maleCount": "<PERSON><PERSON><PERSON> الطلاب المخططين (ذكور)", "femaleCount": "ع<PERSON><PERSON> الطلاب المخططين (إناث)", "studentGroupDeleteMessage": "الطلاب موجودون بالفعل في هذه المجموعة. هل أنت متأكد أنك تريد حذف ذلك؟", "infoAssignEvaluators": "استخدم هذا لتخصيص العديد من المقيمين اليدويين لمجموعة محددة من الطلاب.", "assignManualEvaluator": "تعيين مقيم يدوي", "autoAssignInfo": "سيؤدي التعيين التلقائي إلى توزيع الطلاب بالتساوي بين المقيمين اليدويين المتاحين بناءً على إجمالي عدد الطلاب.", "messageAutoAssignEvaluators": "هل أنت متأكد أنك تريد تعيين المقيمين تلقائيا؟", "totalStudents": "إجمالي الطلاب", "selectedEvaluatorGroup": "المقيم المختار لهذه المجموعة", "selectStudents": "<PERSON><PERSON><PERSON> الطلاب", "selectEvaluator": "<PERSON><PERSON><PERSON> المقيم", "autoAssign": "انتقاء ذاتي", "selectGroupAndSchedule": "حد<PERSON> المجموعة وجدولة التاريخ والوقت", "assignAssessmentAuthor": "تعيين مؤلف التقييم", "assignedAssessmentAuthors": "مؤلفو التقييم المعينون", "assignedManualEvaluators": "المُقيّمون اليدويون المعينون", "assignAssessmentAuthors": "تعيين مؤلفي التقييم", "assignManualEvaluators": "تعيين مقيمين يدويين", "manualEvaluators": "المقيمون اليدويون", "studentGroupingSearch": "البحث بالاسم، الرمز", "scheduleForUnselectedGroup": "الجدول الزمني للمجموعة غير المحددة", "assignGroupsSchedule": "تعيين المجموعات والجدول الزمني", "scheduleForUnselectedGroups": "الجدول الزمني للمجموعات غير المحددة", "refreshStudentList": "تحديث قائمة الطلاب", "noStudentFound": "لم يتم العثور على أي طالب", "saveAndNext": "حفظ والتالي", "createGroup": "إنشاء مجموعة", "createGroupAndProceed": "إنشاء مجموعة والمضي قدما", "proceedWithoutGroup": "المضي قدما بدون مجموعة", "doYouWantToProceedWithoutGroup": "هل تريد متابعة التحميل بدون مجموعة", "groupDetails": "تفاصيل المجموعة", "deliveryGroup": "مجموعة التسليم", "listOfStudentGroups": "قائمة مجموعات الطلاب ({{ groupLength }})", "studentGroupingManagement": "إدارة تجمعات الطلاب", "institutionCalendar": "تقويم المؤسسة", "deliveryType": "نوع التوصيل", "refreshCalendar": "تحديث التقويم", "pleaseNoteCourse": "يرجى ملاحظة الدورات التدريبية للامتحانات التالية", "addStudentStatus": "نشط/غير نشط", "noStudentFoundPleaseRefreshStudentListAndCheckAgain": "لم يتم العثور على أي طالب، يرجى تحديث قائمة الطلاب والمحاولة مرة أخرى", "assignedEvaluators": "المقيمون المعينون", "resetEvaluators": "إعادة تعيين المقيمين", "createUnselectedGroup": "إنشاء مجموعة غير محددة", "seat": "<PERSON><PERSON><PERSON><PERSON>"}, "assignProctor": {"listOfScheduledTestCenters": "قائمة مراكز الاختبار المجدولة", "noScheduledExamsAvailable": "لا توجد اختبارات مجدولة متاحة", "assigningProctor": "تعيين-مراقب", "proctorsWillAssignedForTheseTimings": "سيتم تعيين مراقبين لهذه التوقيتات", "notAssigned": "<PERSON>ير مخصص", "primary": "أساسي", "multiple": "<PERSON><PERSON>ي<PERSON>", "contact": "اتصال", "selectPrimaryError": "ير<PERSON>ى تحديد المراقب الأساسي قبل التعيين", "proctorDutyAllotment": "تعيين المراقبين", "weekView": "عرض الأسبوع", "assigned": "معين", "assign": "+ تعيين", "editDutyTimings": "تعديل أوقات المراقبين", "overlapping": "متداخل", "outOfSlot": "<PERSON><PERSON><PERSON><PERSON> الفترة", "notStarted": "غير مبدئي", "live": "حالي", "ended": "منتهي", "noPrimaryProctorAssigned": "لم يتم تعيين مراقب أساسي "}, "proctorDutyAllotment": {"dateRange": "المدى الزمني", "noDateRangeAdded": "- لا يوجد مدى زمني مضاف بعد -", "proctorDutyDateRange": "مدى زمني لتعيين المراقبين", "proctorDutyTimings": "أوقات تعيين المراقبين", "noProctorDutyDateRangeAdded": "- لا يوجد مدى زمني لتعيين المراقبين مضاف بعد -", "slot": "فترة", "proctorDuty": "تعيين المراقبين", "classDuty": "واجب الطبقة", "assignDutyExamTiming": "تعيين واجب لتحديد توقيت الامتحان", "assignDutyProctorDutySlot": "تعيين واجب لفتحة واجب بروكتور", "duties": "الواجبات", "upcoming": "القادم", "past": "السابق", "slotDetails": "تفاصيل الفتحة", "noSlotsCreated": "لم يتم إنشاء أي فتحات"}}, "cropImage": {"cropImage": "قص الصورة", "cropSave": "اقتصاص وحفظ"}, "login": {"signIn": "تسجيل حساب", "signUp": "اشتراك", "emailNotYetVerified": "لم يتم التحقق من البريد الإلكتروني بعد", "reTypePassword": "اعد ادخال كلمة المرور", "logout": "تسجيل خروج", "password": "كلمة المرور", "profileInformation": "معلومات الملف الشخصي", "digiAssess": "دي جي أسس", "screener": "فرز", "digiAssessDescription": "نظام إدارة تقييم شامل مدعوم بالذكاء الاصطناعي يضمن نزاهة الامتحانات وتحليل الأداء بدقة وموثوقية", "screenerDescription": "منصة متوافقة عالميًا ومدعومة بالذكاء الاصطناعي مصممة لإجراء امتحانات القبول والتوظيف والمؤهلات بأمان، في أي وقت ومن أي مكان"}, "mappings": {"common": {"noPlo": "رقم ن.م.ب", "cloDone": "تم ن.م.م", "mapPloClo": "تعيين ن.م.ب - ن.م.م", "addClo": "أضف ن.م.م", "addPlo": "أضف ن.م.ب", "cloPloMappingGraph": "خريطة تعيين ن.م.ب - ن.م.م", "clo": "ن.م.م"}, "dashboard": {"mapping": "التخطيط", "dashboard": "لوحة التحكم", "mappingDashboard": " لوحة التحكم - التخطيط", "mappingTaxonomy": "التصنيف - التخطيط", "mappingGraph": "الرسم البياني للتعيينات - التخطيط", "mappingStandardRangeSettings": "إعدادات النطاق القياسي - التخطيط", "areYouSure": "هل أنت متأكد؟", "frameworkAutoDelete": "بمجرد تغيير إطار العمل، سيتم حذف البيانات السابقة ضمن إطار العمل المحدد تلقائياً", "yes": "نعم", "manageProgramMapping": "إدارة تعيينات البرامج - يس<PERSON><PERSON><PERSON> على متابعة أداء الطلاب", "dependency": "التبعية: لبدء هذه العملية ، يجب تحديد البرامج ومناهجها وأطر العمل المستخدمة في المؤسسة في قسم تعريف البيانات الأساسية (القوائم الأساسية).", "addFrameworkManageCloPlo": "إضافة إطار عمل، و إدارة نتائج مستوى البرنامج و نتائج مستوي المنهج لكل مناهج البرنامج", "selectProgram": "<PERSON><PERSON><PERSON> ال<PERSON><PERSON><PERSON><PERSON><PERSON>ج", "collapseAll": "تصغير الكل", "expandAll": "توسيع الكل", "noFrameworkSelected": "لم يتم تحديد إطار عمل", "addPlo": "أضف مخرجات تعلم البرنامج نتيجة مستوى البرنامج ", "addClo": "أضف مخرجات تعلم المقرر نتيجة مستوى المنهج", "noRecordsFound": "لا توجد سجلات", "mapPloClo": "تعيين نتيجة مستوى البرنامج-نتيجة مستوى المنهج", "cloPloMappingGraph": "رسم الخرائط نتيجة مستوى المنهج-نتيجة مستوى البرنامج", "noPlo": "عدد مخرجات تعلم البرنامج نتيجة مستوى البرنامج ", "cloDone": "تم القيام نتيجة مستوى المنهج ", "mapType": "نوع التعيين", "contentMap": "خريطة المحتوى", "optional": "اختياري", "required": "مطلوب", "mapRequired": "الخريطة اختياري", "mapOptional": "مطلوب الخريطة", "selectRange": "حد<PERSON> نطاق السنوات", "clo": "نتيجة مستوى المنهج (حصيلة مستوى المقرر التعليمي)", "plo": "نتيجة مستوى البرنامج (حصيلة مستوى برنامج)"}, "addPlo": {"addPloTitle": "أضف نتيجة مستوى البرنامج للمقرر التعليمي المختار", "selectedCourse": "للمقرر التعليمي المختار", "programLevelOutcome": {"number": "عدد {{ mode }}", "description": "وصف {{mode}}", "addClo": "+ أضف مخرجات تعلم المنهج", "addPlo": "+ أضف مخرجات تعلم البرنامج"}}, "addClo": {"title": "أضف نتيجة مستوى المنهج (مخرجات تعلم المقرر) للمقرر التعليمي المختار"}, "mappingPloClo": {"title": "خريطة نتائج مستوي المنهج تحت نتائج مستوى البرنامج للمقرر التعليمي المختار", "selectedCourse": "المقرر المختار:", "selectPrograms": "<PERSON><PERSON><PERSON> البرامج وإطارات العمل ونوع التعيين", "cloPloMapping": "تعيين نتيجة مستوى المنهج - نتيجة مستوى البرنامج ", "tip": "معلومةٌ: لتعيين نتائج مستوي المنهج", "year": "معلومةٌ: عام ، حدد جميع البرامج ضمن أطر عمل المختلفة التي تتبعها مؤسستك", "stepTwo": "انتقل إلى الخطوة الثانية", "medicalYearTableList": {"plo": "نتيجة مستوى البرنامج", "cloName": "نتيجة مستوى المنهج {{ cloName }}", "l": "من", "m": "مت", "h": "مر"}}, "contentMapping": {"title": "تعيين المحتوى للمقرر التعليمي المختار", "contentMappingGridTab": {"plo": "نتيجة مستوى البرنامج ##", "cloName": "نتيجة مستوى المنهج {{ cloName }}"}}, "taxonomy": {"taxonomy": "التصنيف", "listOFTaxonomy": "قائمة التصنيفات ومستوياتها", "addTaxonomy": "أضف تصنيف", "listOfTaxonomy": {"noDescriptionFound": "لم يتم العثور على وصف"}, "taxonomyTable": {"levels": "المستويات", "levelCount": "مستوى", "levelName": "اسم المستوى", "levelDescription": "وصف المستوى", "addLevel": "+ أ<PERSON><PERSON> المستوى", "shortCodeLevel": "ر<PERSON>ز قصير للمستوى", "descriptionText": "الوصف في سطر واحد أو أكثر", "deleteTaxonomy": "هل أنت متأكد أنك تريد الأرشفة؟"}, "taxonomyPopup": {"edit": "تعديل", "add": "إضافة", "taxonomy": "التصنيف", "level": "مستوى", "taxonomyName": "اسم التصنيف", "levelName": "اسم المستوى", "mappingMethod": "طريقة تعيين", "addTaxonomy": "إضافة تصنيف", "editTaxonomy": "تعديل تصنيف", "addLevel": "عدل التصنيف", "editLevel": "عدل مستوي", "singleMapping": "تعيين واحد", "multipleMapping": "متعدد التعيينات"}}, "mappingGraph": {"title": "مصفوفة تعيين نتيجة مستوى المنهج - نتيجة مستوى البرنامج لمنهج برنامج ", "selectProgram": "<PERSON><PERSON><PERSON> ال<PERSON><PERSON><PERSON><PERSON><PERSON>ج", "selectCurriculum": "<PERSON><PERSON><PERSON> المنهج", "framework": "إطار العمل", "selectedDomainTheme": "المجال / الموضوع المحدد", "selectedYear": "السنة المختارة", "theme": "موضوع", "viewBy": "عرض بواسطة", "compareWithDefined": "قارن مع المحدد", "frameworkStandardRange": "النطاق القياسي لإطار العمل", "rangeDefined": "النطاق المحدد لـ", "editRanges": "عدل النطاقات", "settings": "الإعدادات", "cloMappedPlo": "عدد نتيجة مستوى المنهج المعينة نتيجة مستوى البرنامج", "domainsThemes": "المجالات / المواضيع", "yearsSelectedProgram": "عدد السنوات في البرنامج المختار", "selectYear": "اختر السنة", "selectDomainTheme": "ح<PERSON><PERSON> المجال / الموضوع", "noOfClo": "عدد نتيجة مستوى المنهج المحاذاة مع الـنتيجة مستوى البرنامج للمقرر التعليمي المختار", "noOfCourses": "عد<PERSON> المقررات التعليمية", "themeLevelOutcome": "مخرجات مستوى الموضوع", "programLevelOutcome": "مخرجات مستوى البرنامج", "frameworkMustBeSelected": "يج<PERSON> تحديد إطار عمل لمنهج البرنامج لإنشاء الرسم البياني للتعيين", "cloPloNeedsToBeDone": "يجب إجراء تعيين نتيجة مستوى المنهج - نتيجة مستوى البرنامج لجميع المقررات لإنشاء الرسم البياني للتعيين", "programLevelOutcomeTable": {"edit": "تعديل", "total": "مجموع"}, "themeGraph": {"theme": "موضوع", "year": "سنة"}}, "standardRangeSettings": {"mappingSettingsStudentCurriculumOutcome": "إعدادات التعيين> حصيلة منهج الطالب", "outcomeStandardRangeSettings": " إعدادات النطاق القياسي للنتائج", "note": "ض<PERSON>ن كل إطار عمل ، لكل منهج دراسي للبرنامج ، حدد الحد الأقصى والحد الأدنى لعدد نتائج مستوي المنهج المطلوب تعيينها مع نتائج مستوى البرنامج ضمن كل مجال / موضوع.", "frameworkDefinition": "يمكن تطبيق النطاق القياسي لإطار العمل المحدد في إطار العمل على جميع مناهج البرنامج التي تتبعه.", "selectNatureOfProgram": "<PERSON><PERSON><PERSON> طبيعة البرنامج", "yearProgram": "{{year}} سنة البرنامج", "specificTo": "مخ<PERSON><PERSON> إلى", "followingDefaultValues": "اتباع القيم الافتراضية", "higherLimit": "ال<PERSON><PERSON> الأعلى", "lowerLimit": "ال<PERSON><PERSON> الأدنى", "settingSuccessMsg": " تم تحديث الإعدادات بنجاح", "standardRange": "النطاق القياسي", "standardRangeSettings": "إعدادات النطاق القياسي"}, "alignmentImpactMapping": {"contentMappingForSelectedCourse": "تعيين المحتوى للمقرر التعليمي المختار", "medicalProgram": "البرنامج الطبي", "yearLevel": "السنة 2 - المستوى 3", "course": " المقرر التعليمي 1", "introductoryProficientAdvanced": "تم- تمهيدي؛ مح - محترف؛ مت - متقدم", "i": "تم", "p": "مح", "a": "مت", "mc": "<PERSON><PERSON><PERSON>.", "mappingContent": "م.خ. - م<PERSON><PERSON><PERSON><PERSON> الخرائط", "introductory": "تم- تمهيدي", "proficient": "مح - <PERSON><PERSON><PERSON><PERSON><PERSON>", "advanced": "مت- متقدم", "lowImpactMediumImpactHighImpact": "تأثير منخفض ، تأثير متوسط ، تأثير مرتفع", "l": "من-التأثيرمنخفض", "m": "مت-التأثيرمتوسط", "h": "مر-التأثيرمرتفع"}, "typeOfMapping": {"typeOfLevelsAndTheirDescription": "نوع المستويات ووصفها", "nameOfLevel": "اسم المستوى", "nameIs": "الاسم هو", "shortCodeToBeUsedWhileAligningInTable": "رمز قصير لاستخدامه أثناء محاذاة الجدول", "shortCodeIs": "الرمز القصير هو", "noLevelsAddedYet": "لا توجد مستويات مضافة حتى الآن"}, "contentMappingGridTab": {"plo": "## نتيجة مستوى البرنامج", "cloName": "{{ cloName }} نتيجة مستوى المنهج {{ cloName }}"}, "questionMappingSetting": {"headerName": "الرسم البياني للتعيينات", "activeHeader": "إعدادات"}}, "otp": {"otp": "كلمة المرور", "enterOtp": "أد<PERSON>ل كلمة المرور ", "otpSentToYourMobileNumber": "تم إرسال كلمة المرور إلى رقم هاتفك المحمول", "otpSentToYourEmail": "تم ارسال كلمة المرور الي بريدك الإلكتروني", "otpIsRequired": "كلمة المرور مطلوبة", "invalidOtp": "كلمة المرور غير صالحة", "expiresIn": "ينتهي في", "resendOtp": "إعادة ارسال كلمة المرور"}, "pageNotFound": {"backToPrevious": "العودة إلى السابق", "goToHome": "اذه<PERSON> إلى الصفحة الرئيسية"}, "reportingAndAnalytics": {"assessmentResult": {"publishResultsBySelectingSingle/MultipleCourses": "انشر النتائج عن طريق اختيار مقررات تعليمية فردية أو متعددة", "itemsRevisionRecommended": "يوصى بمراجعة العناصر", "allResults": "كل النتائج", "pendingResults": "النتائج المعلقة", "publishedResults": "النتائج المنشورة", "reEvaluate": "إعادة التقييم", "reRevise": "إعادة المراجعة", "cancelRe": "إلغاء إعادة", "groupDiscard": "تم تجاهل عنصر واحد أو أكثر في هذه المجموعة", "startRevision": "بدء المراجعة", "cancelReRevision": "إلغاء إعادة المراجعة", "evaluateManuallyAlignmentWithAccreditation": "تتيح هذه الوحدة للمستخدم إجراء التقييم يدويًا بما يتماشى مع معايير الاعتماد.", "startManualEvaluation": "ابد<PERSON> التقييم اليدوي", "startRevisionConfirmation": "هل أنت متأكد أنك تريد البدء في مراجعة العنصر؟", "startRevisionAdditionalInfo": "سيؤدي هذا إلى نقلك إلى صفحة مراجعة العنصر.", "startManualEvaluationConfirmation": "هل أنت متأكد أنك تريد بدء التقييم اليدوي؟", "startManualEvaluationAdditionalInfo": "سيؤدي هذا إلى نقلك إلى صفحة التقييم اليدوي."}, "consolidateGradeReport": {"consolidateGradeReport": "تقرير الدرجة الموحدة", "selectColumnAction": "ح<PERSON><PERSON> إجراء العمود"}, "reportAndAnalytics": {"reportsAnalytics2020-2021": " تقارير و تحليلات ٢٠٢٠-٢٠٢١", "contentWillBeSoon": "المحتوى سيكون قريبا"}, "revisionPublished": {"reRevisionForPublishedResults2020-2021": "إعادة مراجعة النتائج المنشورة: ٢٠٢١ - ٢٠٢٠", "resultStatusFilter": "مرشح حالة النتيجة", "13Nov": "13نوفم<PERSON><PERSON>", "cancelReRevision": "إلغاء إعادة التقييم"}, "showPublishedReport": {"showingPublishedReportsForMpRegularTerm": "إظهار التقارير المنشورة لـ MP -الفصل الدراسي المنتظم"}, "studentPerformanceTab": {"step2": "تم تخطي الخطوة ٢ لاكتمال التقييم اليدوي للمقرر التعليمي المختار", "youMayDoItemRevision&FinalizeResults.": "يمكنك إجراء مراجعة العنصر وإنهاء النتائج.", "manualEvaluation": "التقييم اليدوي", "beforeManualEvaluation": "(قبل التقييم اليدوي)", "afterManualEvaluation": "(بعد التقييم اليدوي)", "finalizedResult": "النتيجة النهائية", "finalizeResult": "انهي صياغة النتيجة ", "doneRevision": "تمت المراجعة", "doneReRevision": "تمت إعادة المراجعة", "result": "نتيجة", "reviewResult": "مراجعة النتائج المنشورة", "reviewOriginal": "مراجعة النتيجة الأصلية", "reRevision": "إعادة المراجعة", "gradeRevision": "مراجعة الدرجات", "manualEvaluationItemsPending": "هذا التقرير يشمل فقط العناصر التي تتطلب التقييم اليدوي"}, "analysisReportTabs": {"viewReport": "عرض التقارير المنشورة للعام الدراسي المحدد", "originalStudent": "طالب أصلي", "consolidate": "دمج", "cloPloAnalysis": "تحليل نتيجة مستوى المنهج / نتيجة مستوى البرنامج", "studentReports": "تقرير الطالب", "individualGrade": "تقارير الدرجات الفردية", "consolidatedGrade": "تقارير درجات موحدة", "cloPloAnalysisReports": "نتيجة مستوى البرنامج و نتيجة مستوى المنهج تقارير تحليل", "studentImprovisationReports": "تقارير تحسين الطلاب", "courseReport": "تقرير الدورة", "cloAndPloreport": "تقرير نتائج تعلم الدورة ونتائج تعلم البرنامج", "numberOfTimesCourseReportGenerated": "عدد مرات إنشاء تقرير الدورة"}, "batchItemPerformanceTabs": {"batchPerformance": "أداء الدفعة", "itemResponses": "ردود بالإشارة للعنصر"}, "cloPloAnalysisReport": {"searchCourse": "بح<PERSON> عن دورة", "cloPloAnalysisReports": " تقرير تحليل نتيجة مستوى المنهج و نتيجة مستوى البرنامج", "selectCourse": "حد<PERSON> المقرر لعرض تحليل نتيجة مستوى المنهج-نتيجة مستوى البرنامج ومقارنة التقارير بالعين", "coursePublishProgram": "القيم المبينة أدناه عبارة عن عدد المقررات المنشورة / إجمالي عدد المقررات لكل برنامج", "cloPloDetailsTab": {"cloPloAnalysis": "تحليل نتيجة مستوى المنهج و نتيجة مستوى البرنامج لـ:", "dataBasedOnResult": "تستند البيانات الموضحة أدناه إلى النتيجة الأصلية وقبل المنحنى لأعلى", "ploAnalysis": "تحليل نتيجة مستوى البرنامج", "multiplePrograms": "نظرًا لأن هذه المقرر يتماشى مع برامج متعددة ، حدد البرنامج لإدراج طلابه", "cloAnalysis": "تحليل نتيجة مستوى المنهج", "reportsAnalyticsAssessment": "التقارير والتحليلات > نتائج التقييم", "allPublishedReports": "جميع التقارير المنشورة", "selectAnalysis": "<PERSON><PERSON><PERSON> التحليل", "marksPercentage": "نسبة العلامات: {{percentage}}", "percentage": "النسبة المئوية", "cloAverage": "متوسط نتائج المستوى الدراسي للمقرر: {{percentage}}", "ploAverage": "متوسط نتائج المستوى البرنامج: {{percentage}}", "collegeName": "كلية ابن سينا الأهلية", "collegePlace": "المحجر بجدة", "cloPloAnalysisChartAccordion": {"graphShow": "يظهر الرسم البياني للمختار", "target": "الهد<PERSON> المعياري", "student": "انجازات جميع الطلاب", "select": "المحدد"}, "tooltipTemplate": {"targetBenchMark": "المعيار المستهدف", "inference": "الاستدلال", "achievement": "تحصيل", "reliability": "موثوقية الامتحان", "gapWithClass": "فارق التحصيل مع الدرجة", "gapWithTarget": "فارق الإنجاز مع الهدف", "low": "قليل", "high": "مرتفع", "achieved": "<PERSON><PERSON><PERSON>", "classAchievement": "الإنجاز الصفي", "standardErrorOfMeasurement": "الخطأ المعياري للقياس"}}, "common": {"na": "<PERSON>ير متوفر", "studentPerformance": "أداء الطالب", "overall": "بشكل عام", "overallMale": "الذكور بشكل عام", "overallFemale": " الإناث بشكل عام", "exam": " إجمالي تراكمي الامتحانات", "achievement": "انجاز"}}, "consolidateGradeTable": {"cumulativeGrade": "الدرجة التراكمية", "cumulativePercentage": "النسبة المئوية التراكمية", "unMerge": "إلغاء الدمج", "unGroup": "فك التجميع", "mergedColumn": "العمود المدمج", "groupedColumn": "ع<PERSON>و<PERSON> مجمع", "tooltipUnMerge": "انقر لإلغاء دمج الأعمدة", "tooltipUnGroup": "انقر لفك تجميع الأعمدة", "noOfTimeCourseReportGenerated": "عدد مرات إنشاء تقرير الدورة.", "courseMeter": "مقياس الدورة"}, "currentReportDashboard": {"manageResult": "إدارة نشر نتائج الامتحانات الجارية: العام الدراسي", "evaluate": "تقييم العناصر > تحليل العناصر ومراجعتها > نشر النتائج", "reportAnalytical": "التقارير والتحليلات", "assessmentResult": "نتائج التقييم", "allReports": "جميع التقارير المنشورة"}, "distractorTable": {"item": "بند/عنصر #:", "itemType": "نوع العنصر:", "topic": " موضوع:", "description": "وصف:", "plo": "نتيجة مستوى البرنامج", "all": "الجميع", "options": "الخيارات", "theme": "موضوع:", "question": "سؤال", "leadIn": "مقدمة", "prompt": "موجه:", "studentPercentageScores": "عد<PERSON> الطلاب / النسبة المئوية الذين حصلوا على درجات لكل خيار:", "show": "<PERSON><PERSON><PERSON>", "upper": "ال ١/٣ العلوي", "lower": "ال ١/٣ السفلي", "middle": " ال ١/٣ الأوسط", "media": "الوسائل في الاختيار", "attachment": "المرفق", "passPercentage": "نسبة النجاح", "benchmarkPercentage": "النسبة المئوية المرجعية", "achievedWeightage": "الوزن المتحقق"}, "exportReportsPopup": {"exportCourse": "حدد أنواع التقارير التي سيتم تصديرها للمقررات المحددة", "report": "أنواع التقارير", "exportReport": "تصدير التقارير", "resultOriginal": "نتائج الطالب - الأصلية", "resultFinal": "نتائج الطالب - النهائية", "responseOriginal": "تقرير استجابة الطلاب - الأصلي", "responseFinal": "تقرير استجابة الطلاب - النهائي", "itemAnalysisOriginal": "تقرير تحليل العنصر - الأصلي", "itemAnalysisFinal": "تقرير تحليل العنصر - النهائي", "detailItemAnalysisOriginal": "تقرير مفصل لتحليل العنصر - الأصلي", "detailItemAnalysisFinal": "تقرير مفصل لتحليل العنصر - النهائي", "selectSectionWise": "ح<PERSON><PERSON> عن  طريق القسم"}, "finalizeStudentsPerformance": {"revertOriginal": "إرجاع كل الانخفاضات إلى آخر نشر", "showGender": "اظهار حسب الجنس", "studentData": "بيانات الطالب", "itemData": "بيانات العنصر", "increased": " زيادة", "decreased": "انخفاض", "same": "نفس", "originalResult": "النتيجة الأصلية", "showRevisedResult": "إظهار النتيجة حسب حالة المراجعة", "individualStudentPopup": {"individualStudent": "رد<PERSON><PERSON> الطلاب الفردية"}, "itemRevision": "مراجعة  البند", "itemReRevision": "إعادة مراجعة  البند", "original": "إبداعي", "lastPublished": "الاصل", "revised": "مراجعة", "after": "بعد", "after_grade_revision": "بعد مراجعة الدرجة", "currentItemRevision": "الإصدار الحالي للبند"}, "functionalTable": {"highDistractor": "المشتت الوظيفي المرتفع", "nonFunctionalDistractor": "مشتت غير وظيفي", "correction": "يحتاج إلى تصحيح:", "discarded": "متجاهل", "itemAnalysis": "الاستدلال من تحليل العنصر", "capitalizeUnacceptable": "<PERSON>ير مقبول", "capitalizeBorderline": " ح<PERSON> فاصل", "capitalizeAcceptable": "مق<PERSON>ول", "capsUnacceptable": "<PERSON>ير مقبول", "capsBorderline": " ح<PERSON> فاصل", "capsAcceptable": "مق<PERSON>ول", "faultyNeedsCorrection": "تصحيح الاحتياجات المعيبة", "retain": "يحتفظ", "discardLow": "تجاهل - عنصر منخفض الجودة", "discardFaulty": "تجاهل", "answerKeyQuestion": "مف<PERSON><PERSON><PERSON> الإجابة على السؤال", "answerKeyQuestionGroup": "م<PERSON><PERSON><PERSON><PERSON> الإجابة على مجموعة الأسئلة", "changeAnswerKey": "تغيير مفتاح الإجابة", "oldAnswerKey": "المفتاح القديم للإجابات", "confirmAnswerKey": "تأكيد تغيير مفتاح الإجابة", "areYouSureChangeAnswerKey": "هل أنت متأكد أنك تريد تغيير مفتاح الإجابة لهذا العنصر؟", "changingAnswer": "سيؤثر تغيير مفتاح الإجابة على نتائج أي تقييمات مستقبلية وقد يؤثر على درجات الطلاب.", "changingAnswerMq": "سيؤثر تغيير مفتاح الإجابة على عنصر آخر في المجموعة ، والتقييمات المستقبلية وقد يؤثر على درجات الطلاب.", "changeAnswerKeySuccess": "تم تغيير مفتاح الإجابة بنجاح", "updatingAnswerKey": "تحديث مفتاح الإجابة", "pleaseWait": "ير<PERSON>ى الانتظار", "answerKeyUpdating": "تحديث مفتاح الإجابة ...", "reset": "إعادة ضبط", "resetOptions": "لتغيير مفتاح الإجابة، انقر فوق خيار إعادة التعيين", "changeAnswerKeyWeightageTitle": "تأكيد تغيير وزن مفتاح الإجابة", "weightageShouldProvided": "يجب توفير الوزن لمفتاح الإجابة المحدد", "changeAnswerKeyWeightageContent": "هل أنت متأكد أنك تريد تغيير <b>وزن مفتاح الإجابة</b> لهذا العنصر؟ </br></br>تغيير وزن مفتاح الإجابة سيؤثر على نتائج أي تقييمات مستقبلية وقد يؤثر على درجات الطلاب"}, "individualBatchStudent": {"responseSpeed": "سرعة الاستجابة (ثانية)", "responseStatus": "حالة الاستجابة", "incorrect": "<PERSON>ير صحيح", "filterResponse": "ترشيح حسب حالة الاستجابة"}, "individualGradeReport": {"courseName": "اسم المقرر ", "totalItem": "إجمالي العناصر", "cumulativePerformance": "الأداء التراكمي", "student": "مجموع الطلاب", "studentPassFailed": "<PERSON><PERSON><PERSON> الطلاب الناجحين / الراسبين", "gradeDistribution": "توزيع الدرجات", "individualGradeReports": "تقارير تقدير فردية"}, "itemQualityExpansion": {"itemQuality": "جودة العنصر", "distribution": "توزيع", "discriminative": "مؤشر التمييز", "quantification": "تحديد الكميات", "insufficientData": "البيانات غير كافية للحساب", "studentAssessment": "يجب أن يكون الطلاب قد حضروا التقييم", "atLeast": " ع<PERSON>ى الأقل", "gradeDistribution": "حصيلة توزيع الدرجات", "item": "العنصر", "analysis": "التحليل", "more": "أكثر من"}, "itemWiseResponses": {"itemNumber": "رقم العنصر", "textualResponse": ".الردود النصية _ النائب - نقاط العلامة _ ر. نص .بدون جواب _ ب. ج .لم يبدأ - لم.ي  غائب _ غ", "studentData": "بيانات الطالب", "itemData": "بيانات العنصر"}, "multiAxisPloAnalysis": {"sessionDuration": "مدة الجلسة", "pageView": "مشاهدات الصفحة"}, "pendingTaskTable": {"programTerm": "البرنامج - الفصل الدراسي", "publishCourse": "المقررات المنشورة", "finishPublish": "تم الانتهاء منه وجاهز للنشر", "totalPublish": "إجمالي المنشور", "evaluateCourse": " المقررات المعاد تقييمها", "revisedCourse": "المقررات المعاد مراجعتها"}, "performanceProgressPopup": {"performanceProgress": "تقدم الأداء لـ", "exam": "الامتحانات", "highestPerformance": "<PERSON><PERSON><PERSON><PERSON> أداء", "overallPercentage": "النسبة الإجمالية", "totalStudent": "مجموع الطلاب", "passFail": " نجاح/رسوب"}, "publishedDetails": {"publishDetail": "التفاصيل المنشورة", "resultPublish": " نشر النتيجة", "publish": "تم النشر والمشاركة مع", "role": "دور", "doneBy": "تم بواسطة"}, "publishPopup": {"assessmentAuthor": "بالإضافة إلى مؤلفي تقييم المقرر، قم بمشاركة التقارير مع", "sharedWith": "مشارك مع", "reportStudent": "شارك أيضًا التقارير مع الطلاب", "shareSpecificStaff": "مشاركتها مع أعضاء هيئة تدريس آخرين"}, "publishReportsPopup": {"publishStaff": "انشر إلى أعضاء هيئة تدريس آخرين", "publishResultAssessment": "هل ترغب في نشر النتائج إلى مؤلف التقييم المعني بالمقرر؟", "reportStudent": "شارك أيضًا التقارير مع الطلاب", "gradeReport": "تقرير الدرجات ", "outcomeReport": "تقرير النتيجة "}, "reportHistory": {"publishResult": "النتائج المنشورة للسنوات الأكاديمية السابقة", "selectExamType": "حدد نوع الامتحان والسنة الأكاديمية لبدء عرض النتائج", "publishedResultsForAcademicYear": "النتائج المنشورة للسنوات الأكاديمية السابقة"}, "reportingExamTabs": {"exam": "جميع الامتحانات:", "quickLink": "روابط سريعة", "reEvaluate": "إعادة التقييم", "reRevision": "إعادة المراجعة", "publishResults": "نشر النتائج", "requireProgramTrackSelect": "بالنسبة للبرامج المطلوبة ، تتبع وحدد المهام المعلقة لنشر النتائج"}, "reportOutcomeExpansion": {"reportOutcome": "تقرير وتفاصيل النتيجة", "beforeIr": "قبل  المراجعة", "afterIr": "بعد المراجعة", "maximumScore": "أقصى درجة", "minimumScore": "أدنى درجة", "rangeScore": "مدى الدرجات", "meanScore": " متوسط الدرجات", "medianScore": " وسيط الدرجات", "standardDeviation": "الانحراف المعياري", "outcome": "تحليل النتائج", "selectClo": "نتيجة مستوى المنهج حدد ", "itemNumbers": "أرقا<PERSON> العناصر", "itemWeightage": "وزن العنصر", "achievementClo": "تحقيق نتيجة مستوى المنهج", "achievementTarget": "<PERSON><PERSON><PERSON> الإنج<PERSON>ز", "achievementGap": "فارق الإنجاز", "achievementInference": "استدلال الإنجاز"}, "reportTables": {"answeredItem": "العناصر التي تمت الإجابة عليها", "correctItems": "العناصر الصحيحة", "incorrectItems": "عناصر الغير صحيحة", "unansweredItems": "العناصر التي لم تتم الإجابة عليها", "reviseResultStatus": "حالة النتيجة المراجعة", "originalMarks": "العلامات الأصلية", "item": "العنصر", "originalPercentage": "النسبة الأصلية", "originalGRADE": "الدرجة الأصلية", "finalResultStatus": "حالة النتيجة النهائية", "revert": "العودة إلى", "notAllowedToRevert": "لا يسمح لك بتغييرالعلامات. يرجى الاتصال بالمسؤول.", "originalResults": "النتائج الأصلية", "itemRevisionResults": "نتائج مراجعة العنصر الحالي", "gradeRevisionResults": "نتائج مراجعة الصف الحالية", "publishedResults": "النتائج المنشورة", "marks": "العلامات", "percentage": "النسبة المئوية", "grade": "الدرجة", "lastPublishedResults": "آخر النتائج المنشورة", "exceededMark": "تجاوز مارك", "studentStatus": "حالة الطالب"}, "studentReport": {"markBasedAnalysis": {"depthAnalysis": "<PERSON><PERSON><PERSON> الامتحان لتحليل معمق", "itemWisePerformance": "الأداء بالإشارة للعنصر", "selectItemType": "حدد نوع العنصر", "itemStem": " عنصر رأس السؤال ", "studentAnswer": "إجابة الطالب", "marksAchieved": " العلامات المحرزة", "leadIn": "مقدمة", "paragraph": "فقرة", "totalPercentage": "النسبة المئوية الإجمالية للدرجات", "achievementGap": "فارق التحصيل - تحصيل الفرقة الدراسية ", "incorrect": "مجاب عليها بشكل خاطئ", "correct": "مجاب عليها بشكل صحيح", "unanswered": "<PERSON>ير مجاب", "lowToHigh": "درجات منخفضة إلى مرتفعة", "highToLow": "درجات مرتفعة إلى منخفضة"}, "ns": "لم يب", "ab": "غ ب", "f": "ر ب", "absent": "غائب", "notStarted": "لم يبدأ", "complete": "مكتمل", "inComplete": "<PERSON>ير مكتمل", "withHold": "مع تعليق/انتظار"}, "manualEvaluationCourseDashboard": {"manualEvaluation": {"selectAssessment": "حدد تقييمًا وقم بتقييم إجابات الطالب المكتوبة للعناصر"}, "manualEvaluationCourse": {"manualEvaluationCoursesDashboard": "لوحة معلومات دورات التقييم اليدوي", "manualEvaluationAssessment": "تقييم التقييم اليدوي", "manualEvaluationItems": "عناصر التقييم اليدوي", "noOfCourses": "عد<PERSON> المقررات التعليمية للتقييم اليدوي", "cancelRe-evaluation": "إلغاء إعادة التقييم", "manualEvaluationResult": "التقييم اليدوي لنتائج التقييم", "revaluationResults": "إعادة تقييم النتائج المنشورة", "searchModule": "بحث حسب الدورة / الوحدة", "inEvaluation": "قيد التقييم", "reEvaluated": "تم إعادة تقييمها", "evaluated": "تم تقييمها", "evaluatedItem": "العناصر التي تم تقييمها / إجمالي العناصر", "itemEvaluated": "العناصر {{ evaluatedType }} التي تم تقييمها / إجمالي العناصر", "evaluation": "التقييم", "reEvaluation": "إعادة التقييم", "notStarted": "لم تبدأ", "Evaluated": "مقيَّم", "inReEvaluation": "في إعادة التقييم", "evaluationNotStarted": "لم يبدأ التقييم، يرجى البدء من خلال لوحة التقييم اليدوية"}, "manualEvaluationItem": {"selectItem": "حدد عنصرًا وقم بتقييمه", "exportAll": "تصدير الكل", "reportsZip": "التقارير.zip", "saq": "SAQ", "eq": "EQ", "completedEvaluation": "التقييم المكتمل", "completedReEvaluation": "استكمال إعادة التقييم", "evaluatedBy": "مقيم ب", "assignEvaluator": "تعيين مقيم", "assignEvaluators": "تعيين المقيمين", "reAssignEvaluator": "إعادة تعيين المقيم", "selectedItems": "تعيين المقيمين", "selectEvaluator": "ير<PERSON>ى تحديد أحد الخيارات التالية لتعيين المقيم", "assignCommonEvaluator": "تعيين مقيم مشترك", "allStudents": "تنطبق على جميع الطلاب", "assignEvaluatorByStudent": "تعيين مقيم عن طريق الفصل بين الطلاب", "whoShouldEvaluate": "من يجب أن يقيم", "readyforEvaluation": "من يجب أن يقيم", "previewManualEvaluator": "تعيين/معاينة المقيم اليدوي", "selectStudent": "<PERSON><PERSON><PERSON> الطلاب", "selectStudentContent": "هل تريد اختيار جميع الطلاب الذين يبلغ عددهم ({{total}}) أو فقط الطلاب الذين يبلغ عددهم ({{limit}}) المدرجين في هذه الصفحة؟", "selectAllUnAssignedStudentContent": "هل تريد اختيار جميع الطلاب الغير معينين ({{limit}})؟", "selectAssignedStudentContent": "هل تريد اختيار جميع الطلاب المعينين ({{limit}})؟", "selectUnassignedListedStudent": "حد<PERSON> الطلاب الغير معينين المدرجين ({{limit}})", "selectAssignedListedStudent": "حد<PERSON> الطلاب المعينين المدرجين ({{limit}})", "selectListedStudent": "حد<PERSON> الطلاب المدرجين ({{limit}})", "selectTotalStudent": "ح<PERSON><PERSON> جميع الطلاب ({{total}})", "manualEvaluation": "المقيم اليدوي", "selectedStudent": "الطلاب المختارون", "assignEvaluatorValidationErrorContent": "يرجى تعيين مقيم لجميع الطلاب والعناصر.", "selectItemsForEvaluation": "اختر أي عناصر لتعيين مقيم", "manualEvaluationConfiguration": "تكوين الوصول إلى تقييم العنصر", "selectConfigurationOption": "من فضلك اختر أحد الخيارات التالية لتقييم العنصر:", "anyOneCanEvaluate": "السماح باختيار أي شخص من الدورة التدريبية أو المستخدمين العموميين للتقييم", "itemAuthorShouldOnlyEvaluate": "يجب على مؤلف العنصر فقط تقييمه", "exclusionOfAuthorsForEvaluation": "استبعاد المؤلفين من قائمة اختيار المقيم", "exclusionItemAuthorFromEvaluation": "استبعاد مؤلف العنصر من التقييم", "exclusionAssessmentAuthorFromEvaluation": "استبعاد مؤلف التقييم من التقييم", "exclusionBothFromEvaluation": "استبعاد كل من مؤلف العنصر ومؤلف التقييم من التقييم", "assessmentAuthorShouldOnlyEvaluate": "يجب على مؤلف التقييم فقط تقييمه", "assessmentAuthorAndItemAuthorShouldOnlyEvaluate": "يجب على مؤلف التقييم ومؤلف العنصر فقط تقييمه", "selectAccessToShow": "ح<PERSON><PERSON> المربعات لإخفاء تفاصيل الطالب أثناء التقييم:", "yourChosenOption": "لقد اخترت الخيار", "wantToProceed": "هل أنت متأكد من رغبتك في المتابعة؟", "listOfStudentsAssigned": "قائمة الطلاب المعينين", "assignedItems": "العناصر المخصصة", "noEvaluatorsAssigned": "لم يتم تعيين مقيمين", "listOfStudent": "قائمة الطلاب وإجاباتهم ({{studentCount}})", "selectStudentToProceedNextStep": "<PERSON><PERSON><PERSON> الطلاب وانتقل إلى الخطوة التالية.", "confirmRemoveStudentTitle": "تأكيد الإزالة", "confirmRemoveStudentContent": "هل أنت متأكد أنك تريد إزالة ({{studentCount}}) الطلاب المحددين؟", "evaluationCompletedSuccessfully": "تم التقييم بنجاح"}, "manualEvaluationItemTable": {"displayItemNo": "عرض رقم (رقم الصنف)", "itemNo": "رقم العنصر", "statusMedia": "الحالة والوسائط", "statusStemMedia": "الحالة - الجذعية والوسائط", "stem": "رأس السؤال", "evaluate": "قيم", "evaluating": "التقييم", "notAddedYet": "لم يضاف بعد", "group": "مجموعة", "inferenceItemAnalysis": "استنتاج تحليل البند", "itemDetail": "تفاصيل العنصر", "subjectByTopic": "الموضوع/الموضوع", "assignedEvaluator": "المقيم المعين", "studentStatus": "حالة الطالب", "unassignedEvaluatorsErrorContent": "بالنسبة للعنصر رقم <b> {{errorMessage}} </b> لم يتم تعيين مُقيِّم حتى الآن. وحتى يتم تعيينه، لن يتمكن أحد من تقييم العنصر. يرجى اتخاذ الإجراء بحكمة!", "notAssigned": "- غير مخصص -"}, "manualEvaluationTable": {"assessmentName": "اسم التقييم", "items": "العناصر", "examDate": "م<PERSON><PERSON><PERSON> الامتحان", "lastUpdate": "التحديث الاخير"}, "studentResponse": {"outOf": "من", "noResponse": "لا توجد ردود متاحة للمرشح المطبق", "unAnswer": "لاج * - لم تتم الإجابة عليها ", "evaluateMark": "قم بتقييم إجابة كل طالب:", "exportStudentResponse": "تصدير جميع إجابات الطلاب", "typeAnswerKey": "اكتب في مفتاح الإجابة", "studentResponses": "استجابات الطلاب", "importMarks": "استيراد العلامات", "markOutOf": "العلامة من أصل", "studentDetails": "تفاصيل الطالب", "itemStem": "البند الجذعية", "assessmentDetails": "تفاصيل التقييم", "marks": "علامات", "academicId": "المعرف الأكاديمي", "studentId": "الرقم الأكاديمي", "filePasswordProtected": "الملف محمي بكلمة مرور. يرجى إزالة قفل كلمة المرور لاستيراد العلامات", "maxCharcLimit": "*الحد الأقصى لعدد الأحرف هو ٢٠٠٠", "evaluatedMarks": "العلامات المقدرة"}, "invalidMarksStudentsList": {"studentIdInvalidMark": "تحتوي معرفات الطلاب التالية على علامات فارغة أو غير صالحة:", "importFileAgain": "الرجاء تصحيح العلامات لهؤلاء الطلاب واستيراد الملف مرة أخرى."}}, "studentItemWiseTable": {"overAllAssessment": "على كل التقييم المباشر٪", "finalAssessment": "التقييم الغير المباشر النهائي ٪"}, "common": {"dataVisualization": "عرض مرئي للبيانات", "diffIndex": " مؤشر صعوبة", "discrmIndex": " مؤشر تمييز", "correctAnswer": "إجابة صحيحة", "incorrectAnswer": "إجابة خاطئة", "answerStatus": "حالة الإجابة", "totalNoOfCourse": "العدد الإجمالي للمقرر", "totalNoOfStudentsAttended": "إجمالي عدد الطلاب الذين حضروا", "totalNoOfExams": "العدد الإجمالي للامتحانات التي أجريت للمقررات", "totalNoOfStudents": " العدد الإجمالي للطلاب الذين أجابوا الاستطلاع", "cumulativeStudents": "أداء الطلاب التراكمي", "noOfStudent": "<PERSON><PERSON><PERSON> الطلاب", "academicId": "المعرف الأكاديمي", "studentName": "<PERSON><PERSON><PERSON> الطالب", "totalCourses": "مجموع المقررات التعليمية", "total": "مجموع", "courses": "مقررات تعليمية", "autoEvaluation": "التقييم التلقائي", "noOfCourses": "ع<PERSON><PERSON> المقررات المنشورة في كل برنامج", "publishReport": "نشر التقارير", "mentionUserName": "اذكر @أسماء المستخدمين الآخرين الذين تريدهم أن ينشروا", "itemStatus": "حالة العنصر", "prompt": " مطالبات", "answerKey": "م<PERSON><PERSON><PERSON><PERSON> الإجابة", "studentResponse": "إجابة الطالب", "mark": "درجة", "enterMark": "أد<PERSON>ل الدرجة", "overallStudent": " إجمالي الطلاب", "courseName": "اسم المقرر الدراسي", "totalExamConducted": "تم إجراء الامتحانات", "totalStudents": "مجموع الطلاب", "cummulativePerformance": "الأداء التراكمي", "passFail": "عد<PERSON> النجاحات / الفاشلة", "performanceProgress": "تقدم الأداء عبر الاختبارات المختلفة", "passMark": "علامة مرور", "individualStudent": "طالب فردي", "examData": "بيانات الاختبار", "reportsAnalyticsDashboard": "لوحة تحكم التقارير والتحليلات", "itemRevisionForCourse": "مراجعة مادة للدورة التدريبية", "itemRevisionForPublishedCourse": "مراجعة المادة للدورة المنشورة", "gradeReportItemRevisionForCourse": "مراجعة عنصر تقرير الدرجات للدورة التدريبية", "cloPloAnalysisReports": "نتيجة مستوى المنهج نتيجة مستوى البرنامج تقارير تحليل", "studentReportAnalysis": "تحليل تقرير الطالب", "publishCourse": "نشر الدورة", "individualAchievement": "الإنجاز الفردي", "authenticationStatus": "حالة المصادقة", "viewAttachment": "عرض المرفق", "status": "الحالة", "filterOriginalResults": "سيتم تطبيق هذا الفلتر فقط على نتيجة الاختبار الأصلية للطالب", "filterGradeRevisionResults": "سيتم تطبيق هذا الفلتر فقط على نتيجة مراجعة درجات الطالب", "reportsNotAvailableWithoutOutcomeMappings": "التقارير غير متوفرة لأنه يتم تحديث الاختبارات دون تعيينات النتائج", "assessmentResults": "نتائج التقييم", "courseReports": "تقارير الدورة - NCAAA", "courseAnalysisDashboard": "لوحة تحليل المقرر الدراسي", "studentGroup": "مجموعة الطلاب", "selectStudentGroups": "<PERSON><PERSON><PERSON> مجموعات الطلاب"}, "surveyReport": {"courseEvaluationSurveyReport": "تقارير استطلاع تقييم المقرر", "courseEvaluationReportFor": "تقارير استطلاع تقويم المسار لـ", "surveyItemWise": "استطلاع - بالإشارة للعنصر", "surveyStudentWise": "استطلاع - بالإشارة للطالب", "insightsOutcomeWise": "الرؤى - بالإشارة للحصيلة", "surveyDashboard": "لوحة تحكم الاستطلاع", "courseEvaluationReport": "تقرير تقييم الدورة", "surveyReportSettings": "إعدادات تقرير الاستطلاع", "surveyQuestionsSettings": "إعدادات أسئلة الاستطلاع", "itemWise": {"generalSurvey": "الاستطلاع العام", "outComeSurvey": "حصيلة الاستطلاع", "openEndedSurvey": "استطلاع مفتوح العضوية", "export": "يصدر", "itemType": "نوع العنصر", "option": "اختيار", "values": "القيم", "male": "الذكر", "female": "<PERSON>ن<PERSON>ى", "totalResponse": "إجمالي الاستجابة", "maleCount": "الذكر ({{count}})", "femaleCount": "أنثى ({{count}})", "totalCount": "مجموع ({{count}})", "malePercentage": "٪ الذكر ", "femalePercentage": "٪ الأنثى", "totalPercentage": "٪ الكل", "noExamType": "لا توجد نتائج اختبار منشورة ضمن نوع المحاولة المحدد", "readMore": "اقر<PERSON> أكثر", "readLess": "أق<PERSON><PERSON> أقل", "maleResponse": "رد الطالاب الذكور", "feMaleResponse": "استجابة الطالبات", "exportWithStudentId": "مع الهوية الأكاديمية للطالب", "exportWithoutStudentId": "بدون الهوية الأكاديمية للطالب"}}, "studentItemWise": {"student": "جميع الطلاب", "selectClo": "حدد نتيجة مستوى المنهج", "cloDomain": "مجالات نتيجة مستوى المنهج", "individualClo": " فردي نتيجة مستوى المنهج ", "selectQuestion": "اختر سؤالا", "outcomesNumber": "رقم المخرجات", "directAssessment": " التقييم المباشر", "indirectAssessment": "التقييم الغير المباشر", "dataCloDomain": "البيانات المعروضة لمجالات نتيجة مستوى المنهج المحددة", "dataSelectedStudent": "إظهار البيانات للطالب المختار", "markBased": "تحليل قائم على الدرجات", "outcomeBased": "التحليل القائم على النتائج", "selectTerm": "<PERSON><PERSON><PERSON> الفصل التعليمي", "selectAndView": "حد<PERSON> الطالب وافتح كل مقرر دراسي لعرض التقرير", "studentReports": "تقارير الطلاب", "reportsAnalytics": "التقارير والتحليلات > نتائج التقييم", "studentCourseListed": "لكل طالب داخل كل برنامج ، سيتم سرد المقررات التي التحق بها الطالب.", "describeAnatomical": "أوصف الأجزاء التشريحية المختلفة لجسم الإنسان", "header": ":تحليل الطالب الفردي"}, "studentSurvey": {"describeDifferentAnatomicalPart": " إلى أي مدى يمكنك وصف الأجزاء التشريحية المختلفة لجسم الإنسان؟"}, "surveyRatingTable": {"detailDescribe": "إلى أي مدى يمكنك وصف الأجزاء التشريحية المختلفة لجسم الإنسان؟", "likertScale": "مقياس ليكرت", "average": "متوسط"}, "surveySetting": {"surveySetting": "إعداد الاستطلاع", "studentSkip": "يمكن للطلاب تخطي أسئلة أو الاستطلاع بالكامل ", "startSurvey": "ابد<PERSON> الاستطلاع", "whenSurveyStart": "متى يجب أن يبدأ الاستطلاع", "whenSurveyStartLate": "عندما يأتي الطالب متأخرًا ، يحضر الاستطلاع بعد الامتحان", "note": "ملاحظة: ستؤثر عبارة \"لا تسمح\" على امتحان الكل", "beforeExam": "قبل الامتحان", "afterExam": "<PERSON>ع<PERSON> الامتحان", "skipSurvey": "تخطي الاستطلاع", "skipEntireSurvey": "تخطي الاستطلاع بأكمله", "skipSurveyQuestions": "تخطي أسئلة الاستطلاع", "allow": "السماح", "dontAllow": "لا تسمح"}, "insightWiseTable": {"studentWiseAnalysis": "تحليل الطالب نتيجة مستوى المنهج", "overallDirectAssessment": " ٪ على كل التقييم المباشر   ", "overallIndirectAssessment": "٪ على كل التقييم الغير المباشر", "totalOverAll": " ٪ الإجمالي على جميع"}, "studentImprovisation": {"studentImprovisationReportOverall": "تقرير تحسين الطالب - الإجمالي", "individualReports": "تقارير فردية", "needFocus": "بحاجة إلى التركيز", "doingGreat": "<PERSON><PERSON><PERSON> عظيم", "better": "<PERSON><PERSON><PERSON>ل", "studentAttainment": "تحصيل الطالب", "detailedReport": "تقرير مفصل", "examTypeInfo": "يجب أن تكون للمرشحات قيم افتراضية استنادًا إلى النتائج المنشورة", "selectExamReports": "اختر تقارير الامتحان", "gradeReports": "تقارير الدرجات", "studentImprovisationReports": "تقارير تحسين الطلاب", "view": "<PERSON><PERSON><PERSON>", "viewReportToolTip": "لن يكون التقرير مرئيًا في حالة غياب الطالب/ حصل على ٠ درجة"}, "sirIndividual": {"sirReportIndividual": "تقرير تحسين الطالب - فردي", "belowBenchmark ": "أقل من المعيار", "aboveBenchmark ": "فوق المعيار", "belowBatchAttainment": "أقل من تحقيق الدفعة", "aboveBatchAttainment": "فوق تحقيق الدفعة"}, "individualReportShare": {"shareWithStudent": "شارك مع الطلاب", "shareWithStaff": "شارك مع هيئة التدريس"}, "componentAnalysisReport": {"title": "تقرير تحليل المكونات", "createNewAnalysis": "إنشاء تحليل جديد", "analysisThroughMatrix": "يمكن إجراء التحليل عن طريق ربط التدفقات من خلال المصفوفة", "startConfiguration": "بدء التكوين", "creatingNewAnalysis": "إنشاء تحليل جديد", "nameOfTheAnalysis": "اسم التحليل", "courseAnalysis": "تحليل الدورة", "analysisMatrix": "ابدأ في إنشاء تحليل يمكن إجراؤه عن طريق ربط التدفقات عبر المصفوفة", "editAnalysis": "تحرير التحليل", "analysisAlreadyCreated": "تم إنشاء التحليل بالفعل", "allStudentsAbsent": "لا يمكن استخدام هذا الاختبار لتحليل المقرر الدراسي بسبب غياب جميع الطلاب"}, "dynamicMappingConfiguration": {"componentExamSelection": "اختيار المكونات والامتحانات", "matrixMapping": "رسم خرائط المصفوفة", "dataVisualization": "عرض مرئي للمعلومات", "componentSelection": "اختيار المكون", "selectComponent": "<PERSON><PERSON><PERSON> الم<PERSON>ون", "examSelection": "اختيار الامتحان", "internal": "داخلي", "external": "خارجي", "selected": "المحدد", "onlyRelatedComponents": "تم إدراج المكونات ذات الصلة بهذه الدورة فقط", "chosenComponent": "المكون المختار", "giveName": "ضع اسما هنا...", "noExam": "لا امتحان", "listOfAnalysis": "قائمة التحليل", "internalExams": "الامتحانات الداخلية", "externalExams": "الامتحانات الخارجية", "weightageApplied": "الوزن المطبق", "benchmarkApplied": "تم تطبيق المعيار", "subAnalysisNote": "لا يمكن تحديد الاختبارات التي تم إنشاؤها بموجب أي تحليل فرعي.", "createdUnder": "تم إنشاؤها أيضًا تحت", "notScheduledForAllSelectedGroups": "لم يتم جدولتها لجميع المجموعات المحددة!", "studentGroupsInvolved": "المجموعات الطلابية المعنية", "unableToSelect": "<PERSON>ير قادر على التحديد إذا لم يتم جدولته لجميع مجموعات الطلاب المحددة", "selectAll": "حد<PERSON> جميع الاختبارات لـ “{{examName}}”", "unSelectAll": "قم بإلغاء تحديد كل الاختبارات لـ “{{examName}}”", "numberOfExamsFound": "({{coursesLength}}) توجد الاختبارات في <b>“{{examName}}”</b> ضمن الاختبارات التي يتم إجراؤها لمجموعات طلابية فردية/متعددة بشكل فردي، لتحديد هذا الاختبار، يرجى تحديد جميع الاختبارات ذات الصلة التي تحتوي على جميع مجموعات الطلاب متضمن.", "unselectingExams": "سيؤدي إلغاء تحديد <b>“{{examName}}”</b> ضمن الاختبارات التي يتم إجراؤها لمجموعات طلابية فردية/متعددة بشكل فردي، إلى إلغاء تحديد جميع الاختبارات ذات الصلة. هل أنت متأكد أنك تريد إلغاء التحديد؟", "allSelectedGroupsInvolved": "جميع المجموعات المحددة معنية", "selectTheExamsType": "حدد نوع الامتحان", "unableToMerge": "<PERSON>ير قادر على دمج المجموعات نظرًا لأن إحدى المجموعات مستخدمة بالفعل في تحليل مختلف."}, "taggingComponentPopup": {"taggingComponent": "وضع علامات على المكونات مع الامتحانات", "applyTagging": "تطبيق العلامات", "chooseExam": "اختر الامتحان", "itemNumber": "رقم الشيء", "taggedComponents": "المكونات الموسومة", "clearAll": "ا<PERSON><PERSON><PERSON> الكل", "tagWith": "علامة مع", "tagAtlLeastOneItem": "يجب عليك وسم واح<PERSON> على الأقل", "taggingUpdated": "تم تطبيق العلامات بنجاح"}}, "studentManagement": {"studentProfileDataUpdate": "تحديث بيانات ملف الطالب", "studentDashboardAnnouncements": "لوحة قيادة الطالب | إعلان", "studentDashboardExamSchedule": "لوحة قيادة الطالب | جدول الامتحان ", "studentDashboardExamResults": "لوحة قيادة الطالب | نتائج الامتحانات", "studentDashboardUpcomingExams": "لوحة معلومات الطالب | الاختبارات القادمة"}, "basicAlert": {"misMatchInvalidHeader": "بيانات العنوان غير مطابقة / غير صالحة"}, "sideNav": {"youAre": "انت", "on": "تشغيل", "profile": "الملف الشخصي", "verify": "تحقق", "autoSave": "حفظ تلقائي", "verifyProfileForm": "تحقق من نموذج الملف الشخصي", "notifications": {"notifications": "الإخطارات", "markAllAsRead": "وضع علامة مقروء على الكل", "markAsRead": "وضع علامة مقروء", "due": "مستحق", "fetchingNotifications": "...جلب الإخطارات", "noNotificationsFound": "لم يتم العثور على الإخطارات", "createRequest": "إنشاء طلب", "create/Review/Request": "إنشاء / مراجعة الطلب", "reviewRequest": "طلب مراجعة", "finalizeRequest": "إنهاء الطلب", "createPending": "إنشاء معلق", "reviewPending": "مراجعة معلقة", "dueToday": "مستحق اليوم", "arrivalTillNow": "وصلت حتى الآن", "courseId": "معرف الدورة "}}, "textEditor": {"dear": "ع<PERSON>ي<PERSON>", "visitLink": "يرجى زيارة الرابط التالي وتسجيل الدخول للتسجيل عبر الإنترنت في دي جي أسس", "completeRegistration": "مطلوب منك إكمال التسجيل والانتهاء من التحقق من بيانات ملفك الشخصي خلال", "3Days": "(٣ أيام من إرسال هذا البريد الإلكتروني) {{date}}", "thankYou": "شكرا لك", "deanUniversity": "<PERSON><PERSON><PERSON><PERSON> الجامعة", "facultyFirstName": "الاسم الأول للكلية", "studentFirstName": "الاسم الأول للطالب", "examControlOffice": "مكتب مراقبة الامتحانات"}, "confirmDialogBox": {"contentTitle": " أنت على وشك حذف هذا نهائيًا", "messageText": "ستفقد مراكز الامتحان والمراقبين المعينين لوقت الجلسة المحدد.", "messageInfo": "يجب عليك تعيين مراقبين مرة أخرى لجميع مراكز الامتحان، عند اختيار تاريخ و وقت الجلسة الجديدين", "cancelExam": " متابعة الإلغاء الامتحان ستكون دائمة", "confirmCancelExam": "هل أنت متأكد من المتابعة؟", "assessmentConfiguration": "إعدادات التقييم نشطة حاليًا، لا يمكنك أرشفتها الآن. حدد إعدادات ليست مستخدمة الآن، أو استبدال الإعدادات النشطة التي تريد أرشفتها", "okay": "حسناً", "okay!": "تمام!", "notRecover": "لن تتمكن من استرداد هذا", "deletePermanently": "الحذف بشكل نهائي", "archiveThis": "هل أنت متأكد أنك تريد أرشفة ({{name}}) ؟", "cardStatusChange": "سوف تتغير حالة البطاقة من", "ongoingWithMe": "للاستمرار معي", "removeSession": "إزالة الجلسة", "removeSessionMessage": "هل أنت متأكد أنك تريد إزالة الجلسة؟ بمجرد إزالتها، لا يمكن استرجاع البيانات.", "removeAuthorTitle": "إزالة المؤلف", "removeAuthorMsg": "هل أنت متأكد أنك تريد إلغاء تعيين المؤلف؟"}, "conflictDialog": {"importBanner": "جلب قاعدة البيانات من الشعار"}, "createDialog": {"addTestCenter": "أض<PERSON> مر<PERSON>ز امتحانات", "addVirtualTestCenter": "أض<PERSON> مر<PERSON>ز امتحانات عن بعد", "testCenterName": "اسم مركز الامتحانات", "testCenterRequired": "اسم مركز الامتحانات مطلوب ", "invalidTestCenter": "اسم مركز الاختبار غير صالح", "selectGenderTestCenter": "حدد الجنس المحجوز لمركز الامتحانات", "genderRequire": "الجنس مطلوب", "roomNumberRequire": "رقم الغرفة مطلوب", "location": "موقع", "locationRequire": "الموقع مطلوب", "capacity": "السعة القصوى", "capacityRequire": "السعة القصوى مطلوبة", "reserve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reserveRequire": " الح<PERSON><PERSON> مطلوب ", "reason": " سبب", "reasonRequire": "الس<PERSON><PERSON> مطلوب", "testCenterDelete": "تم حذف مر<PERSON>ز الامتحانات بنجاح", "clickAddTestCenter": "انقر فوق '+ إضافة مركز امتحانات' للبدء", "testCenterCreatedSuccess": "تم إنشاء مركز الامتحات بنجاح", "testCenterUpdateSuccess": "تم تحديث مركز الامتحات بنجاح", "maxCapacityExceeded": "تم تجاوز الحد الأقصى للسعة", "totalReservedCapacity": "يجب أن تكون السعة الإجمالية المحجوزة أقل من السعة القصوى", "testCenterCapacity": "سعة مركز الاختبار", "tcName": "مركز الاختبار {{tcName}}", "rescheduleNecessaryCapacity": "الرجاء إعادة جدولة الامتحان التالي وإجراء السعة اللازمة", "rescheduleTheExam": "ير<PERSON>ى إعادة جدولة الامتحان التالي", "course": "المقرر", "examType": "نوع الامتحان", "attemptType": "نوع المحاولة", "scheduledDate": "تاريخ الجدولة", "commonTcNote": "*ملاحظة: يمكن لمركز الاختبار المشترك أن يعمل حسب الجنس أو مختلط بناءً على اختيار احتياجات الجدول الزمني", "shouldBeAtleastOne": "يجب أن تكون السعة واحدة على الأقل", "reserveNotZero": "لا ينبغي أن يكون الاحتياطي صفراً", "reserveReasonNotEmpty": "يجب ألا يكون سبب الاحتياطي فارغًا"}, "userManagement": {"student": {"studentManagement": "إدارة الطلاب", "profileUpdateTitle": "إدارة الطلاب > تحديث الملف الشخصي", "studentCorrectionTitle": "إدارة الطلاب> قيد الانتظار> التحقق من بيانات الملف الشخصي > تصحيحات", "title": "إدارة الطلاب", "importStudent": "استيراد الطالب", "importStudentsDb": "استيراد قاعدة بيانات الطلاب من اللافتة", "csvData": "بيانات ملف csv للطالب", "allStudents": "({{ count }}) كل الطلاب", "ValidatedStudents": "تم التحقق من صحة بياناتهم ({{ count }})", "pendingStudents": "في انتظار التحقق من صحة بياناتهم ({{ count }})", "studentSelectedPage": "الطالب المحدد في هذه الصفحة {{count}}", "facultySelectedPage": "الكلية المختارة في هذه الصفحة {{count}}", "facultiesSelectedPage": "الكليات المختارة في هذه الصفحة {{count}}", "studentSelected": "الطالب المحدد في هذه الصفحة", "studentTemplate": "نموذج_الطالب", "studentResponseXlsx": "استجابات الطلاب.xlsx", "enterAcademicNo": "أدخل الرقم الأكاديمي (اختياري)", "assignProgramName": "تعيين البرنامج", "detailMarks": {"selectCourse": "تفاصيل نتيجة المقرر المختار: ", "examDetails": "تفاصيل الامتحان", "itemTypeWiseMarks": "نوع العنصر عن طريق الدرجات "}, "studentBasicDetailsUpdate": {"profileDetail": "يرجى تقديم تفاصيل الملف الشخصي التالية"}, "studentPortalTab": {"detailExamResult": {"detailsOfSelectedCourse": "تفاصيل المقرر التعليمي المختار ", "detailedCourseList": "قائمة مفصلة بالنتائج مجمعة بالمقرر تعليمي ", "levelErrMessage": "السنة / المستوى المحدد غير متوفر", "selectAttemptType": "حدد نوع التجربة"}, "scheduleExamDetails": {"scheduleExamDetails": "تفاصيل جدولة الامتحان", "schedule": "جدول", "fetchingExams": "إحضار امتحاناتك المجدولة...", "doNotHaveScheduledExams": "ليس لديك أي امتحانات مجدولة", "tc": "مركز الامتحانات", "coursesLink": "الدورات وربطها بنتائجها", "notYetPublished": "لم تنشر بعد", "notShared": "لم تنشر بعد", "lauchUsingSEB": "إطلاق الاختبار باستخدام متصفح الامتحان الآمن", "launchExam": "بد<PERSON> الامتحان"}, "notificationTitle": "عنوان الإعلام", "notificationContent": "محتوى الإخطارات", "info": "معلومات", "results": "النتائج", "downloadResultTemplate": "قم بتنزيل قالب النتائج", "examStarts": "بد<PERSON> الاختبار", "examSchedule": "جدول الامتحانات", "link": "رابط", "tc": "مركز امتحان"}, "studentVerifyDetails": {"information": "الرجاء إخبارنا إذا كانت المعلومات التي لدينا عنك صحيحة أم خاطئة", "correctOrWrong": "اختر \"صحيح \" أو \"خطأ \"", "verifyData": "تحقق من البيانات"}}, "faculty": {"importFaculty": "استيراد أعضاء هيئة التدريس", "importFacultyData": "استيراد بيانات أعضاء هيئة التدريس", "testInputValue": "اختبار قيمة الإدخال", "allFaculties": "جميع أعضاء هيئة التدريس", "facultySelectedPage": "أعضاء هيئة التدريس المحددة في هذه الصفحة {{count}}", "facultyTemplate": "نموذج_أعضاء_هيئة_التدريس", "facultyUpdateProfile": "ملف تحديث هيئة التدريس", "adminFacultyProfileUpdate": {"facultyProfileUpdate": "تحديث ملف عضو هيئة التدريس", "headerName": "إدارة أعضاء هيئة التدريس> تحديث الملف الشخصي"}, "facultyAdminUpdate": {"correctionRequired": " مطلوب تصحيح البيانات المحددة", "headerName": "إدارة أعضاء هيئة التدريس > قيد الانتظار > التحقق من بيانات الملف الشخصي > تصحيح"}, "reallocateTopicsPopup": {"reallocateResponsibility": "أعد توزيع المسؤوليات المستخدم النشطة قبل إلغاءه ", "responsibilityRequirement": "* بعض المسؤوليات ليس لديها مستخدم بديل لإعادة التخصيص. إضافة مستخدم بالصلاحيات المطلوبة أولاً", "toSaveReallocation": "لحفظ عمليات إعادة التخصيص وإلغاء التنشيط لاحقًا", "understandReallocation": "أفهم أنه بم<PERSON><PERSON>د حفظ عمليات إعادة التخصيص ، لا يمكن تغييرها مرة أخرى", "saveReallocations": "حفظ عمليات إعادة التخصيص", "rolesResponsibilities": "الأدوار والمسؤوليات", "reassignUser": "إعادة تعيين المستخدم", "selectUser": "اختر المستخدم", "noAlternateUsersFound": "لم يتم العثور على مستخدمين بديلين", "reallocated": "أعيد تخصيصها", "noRolesAssigned": "لم يتم تعيين أدوار", "deactivateProfile": "إلغاء تفعيل ملف تعريف <{{userName}}>", "activateProfile": "تفعيل ملف تعريف <{{userName}}>"}}, "assignRoleTopic": {"staffsAssigned": "هيئة التدريس - معين ({{ count }})", "staffsUnassigned": "هيئة التدريس - غير معين ({{ count }})", "headerTitle": "تعيين الأدوار و الصلاحيات", "assignedRoleSuccessfully": "تم تعيين الدور بنجاح", "assignedTopicSuccessfully": "تم تعيين الموضوع بنجاح", "pleaseSelectFacultyAssign": "الرجاء تحديد عضو هيئة التدريس لتعيينه", "programDetailsAssignedSuccessfully": "تم تعيين تفاصيل البرنامج بنجاح", "noProgramTypesAvailable": "لا توجد أنواع برامج متاحة ، لمتابعة هذا الإجراء فقط احفظه", "roleChangedSuccessfully": "تم تغيير الدور بنجاح", "assignRolesPermission": " دي جي أسس | تعيين الأدوار و الصلاحيات {{ textLabel }}", "assignFaculty": {"staffAssign": "اختر عضو هيئة التدريس لتعيينه", "noFacultiesFound": "لم يتم العثور على أعضاء هيئة التدريس"}, "assignTopic": {"selectRoleToAssign": "حدد دور لتعيينه", "selectRoleAsDefault": "جعل الدور المحدد افتراضي", "noRoleData": "لم يتم العثور على بيانات الدور", "assignSubjectAndTopics": "اختر وعين المادة الدراسية وموضوعاتها للدور المحدد", "selectProgram": "ابد<PERSON> بتحديد البرنامج والقوائم المنسدلة الأخرى للعثور على المادة الدراسية المطلوبة وموضوعاتها"}, "roleTable": {"searchText": "... البحث عن طريق أعضاء هيئة التدريس، الموضوع، السنة", "manageRole": "إدارة صلاحيات الأدوار والمواد", "defaultRole": "الدور الافتراضي", "noRolesAssigned": "لم يتم تعيين دور ", "assign": "تعيين", "fullName": "الاسم الكامل", "topic": "الموضوع", "level": "مستوى", "curriculum": "المقرر", "term": "الفصل الدراسي", "facultyName": "اسم عضو هيئة التدريس", "defaultRoleUpdated": "تم تحديث الدور الافتراضي بنجاح"}, "role": {"describeRole": "وصف الدور", "selectUiElement": "حدد عناصر واجهة المستخدم", "selectPermission": " <PERSON><PERSON><PERSON> الصلاحيات", "permissionForParentElement": "يتم تطبيق الصلاحيات الممنوحة لعنصر واجهة المستخدم الأساسي تلقائيًا على جميع العناصر التابعة ، و لكن الصلاحيات المطبقة على عنصر واجهة مستخدم تابع لا يتم تطبيقه تلقائيًا على العنصر الأساسي.", "noPermissionFound": "لم يتم العثور على صلاحيات", "editPermission": "تعديل الصلاحيات", "permissionList": {"restore": "استعادة ", "deSelectAll": "إلغاء تحديد الكل"}}}, "rolesAndModules": {"rolesPermission": {"selectRole": "حد<PERSON> الدور أو الأدوار لأخذ الصلاحيات منها", "selectAtLeast1": "يج<PERSON> تحديد وا<PERSON><PERSON> على الأقل", "chooseCategory": "اختر فئة", "deleteRole": "<PERSON><PERSON><PERSON> الدور", "areYouSureToDelete": "هل أنت متأكد من حذف هذا الدور", "fromEntry?": "من القيد؟", "thisCannotBeUndone": "هذا لا يمكن التراجع عنه", "selectRoles": "حد<PERSON> الدور / الأدوار", "noCategory": "لم يتم اختيار فئة", "rolesPermission": "صلاحيات الأدوار", "roles&Permissions": "صلاحيات و الأدوار", "clearCategory": "ام<PERSON><PERSON> الفئة", "pleaseFillOutThisField": "الرجاء ملء هذه الخانة", "deActivatingRole": {"deActivatingRole": "تعطيل دور", "deactivate": "أنت على وشك إلغاء التنشيط", "noFaculties": "لم يتم تعيين أعضاء هيئة تدريس لهذا الدور", "defaultRoles": "قائمة أعضاء هيئة التدريس الذين تم تعيينهم هذا الدور كافتراضي.", "changeDefaultRole": "يرجى تغيير دورهم الافتراضي قبل إلغاء التنشيط", "followingStaffRole": "سيفقد أعضاء هيئة التدريس التاليين إمكانية الوصول إلى دي جي أسس نظرًا لأنه ليس لديهم أي دور بديل أو ليس لديهم أي أدوار نشطة لتعيينها"}, "deActivated": {"the": "الـ", "roleDeactivated": "تم إلغاء تنشيط / تحديث الدور من قبل المدير", "cannotProceed": "قد لا تتمكن من المضي قدمًا بهذا الدور", "in": "في", "seconds": "ثواني", "youWillBeLoggedOut": "سيتم تسجيل خروجك تلقائيًا", "youWillBeRedirectedToDefaultRole": "سيتم إعادة توجيهك إلى الصفحة الرئيسية لدورك الافتراضي <{{role}}> تلقائياً"}}, "page": "الصفحة", "tab": "علامة تبويب", "subTab": "علامة تبويب فرعية", "assessmentAuthor": "مؤلف التقييم", "itemAuthor": "مؤلف العنصر", "medicalEducationist": "أخصائي التعليم الطبي", "subjectExpertReviewer": "خبير مراجعة الموضوع"}, "common": {"exportStudent": "تصدير الطلاب", "selectRegistrationStatus": "<PERSON><PERSON><PERSON> حالة التسجيل", "selectStatus": "<PERSON><PERSON><PERSON> الحالة", "search": "البحث عن طريق الهوية الأكاديمية والاسم والبريد الإلكتروني", "userStatus": "ولاية", "dob": "تاريخ الميلاد", "passportNumber": "رقم جواز السفر", "nationalId": "الرقم القومي / رقم الإقامة.", "enrollYear": "التسجيل في يوم/شهر/سنة", "enrollYearBr": "السنة التسجيل ", "profileVerification": "التحقق من بيانات الملف الشخصي", "requestSent": "تم إرسال الطلب", "biometricVerification": "التحقق البيومتري", "sendMail": "ارسل بريد إلكتروني", "verificationRequestPending": "طلب التحق<PERSON> قيد الانتظار", "profileVerificationPending": "التحقق من الملف الشخصي قيد الانتظار", "biometricVerificationPending": "التحقق البيومتري قيد الانتظار", "deSelectAll": "إلغاء تحديد الكل", "selectAll": "اختر الكل", "cancelSelectAll": "إلغاء تحديد الكل", "active": "نشط", "deactivate": "إلغاء التنشيط", "activate": "تفعيل", "inactive": "غير نشط", "blocked": "م<PERSON><PERSON><PERSON><PERSON>", "nationalIdBr": "الرقم القومي/", "iqamaNo": " رقم الإقامة ", "academic": "الأكاديمي", "number": "رقم", "importStudentData": "استيراد بيانات الطالب", "testInputValue": "اختبار قيمة الإدخال", "errorMsgGettingStudents": "خطأ في الحصول على جميع بيانات الطلاب من الخادم.", "sendingEmail": "... إرسال البريد الإلكتروني", "nameOfFile": "اسم الملف", "provideDetail": "قدم التفاصيل الخاصة بك", "dashboard": " لوحة التحكم", "examSchedule": "جدول الامتحانات", "examResults": "نتائج الامتحانات", "logo": "شعار", "errGettingFromServer": "خطأ في الحصول على البيانات من الخادم!", "enterTimeDuration": "أدخل المدة الزمنية لإضافتها إلى القائمة (دقائق)", "valid": "صالح", "pending": "قيد الانتظار", "taskPending": "المهمة قيد الانتظار", "correctionRequired": "مطلوب تصحيح", "inValidFileType": "لقد قمت برفع نوع ملف غير صالح. الرجاء رفع نوع ملف صالح.\nصيغة الملف: CSV", "templateMismatchFaculty": "عدم تطابق النموذج عند استيراد أعضاء هيئة التدريس", "dataVerifiedSuccessfully": "تم التحقق من البيانات بنجاح", "templateMismatchStudent": "عدم تطابق النموذج عند استيراد الطلاب", "mismatchedHeaderData": "بيانات العنوان غير مطابقة / غير صالحة", "errorGetAllStudent": "خطأ في الحصول على جميع الطلاب", "errorOnImportStudent": "خطأ في استيراد الطالب", "fileSizeLimit": "تجاوز حجم المل<PERSON> الحد الأقصى البالغ ٦ م.ب. يرجى رفع ملف أصغر", "errorFileType": "لقد قمت بتحميل نوع ملف غير صالح. الرجاء رفع صيغة ملف صالحة\nالصيغ الصالحة: PNG، JPG، TIFF", "uploadedSuccessfully": "تم الرفع بنجاح", "imageDeletedSuccessfully": "تم حذف الصورة بنجاح", "markedSuccessfully": "تم وضع علامة بنجاح", "upcomingExams": "الامتحانات القادمة", "pastExams": "الامتحانات السابقة", "announcements": "الإعلانات"}}, "assessmentStatus": {"onGoing": "قيد التنفيذ", "notStarted": "لم يبدأ", "completed": "مكتمل", "ongoingWithAssessmentAuthor": "مستمر مع مؤلف التقييم", "ongoingWithItemAuthor": "مستمر مع مؤلف العنصر", "published": "تم النشر ", "cancelled": "تم الإلغاء", "revisionCompleted": "تمت المراجعة", "inRevision": "قيد المراجعة", "recommended": "موصى به", "notRequired": "<PERSON>ير مطلوب", "curveLeft": "منحني مائل إلى اليسار", "curveRight": "منحني مائل إلى اليمين", "withdrawn": "من<PERSON><PERSON><PERSON>", "correctionDone": "تم التصحيح"}, "itemTypes": {"comprehensiveQuestions": "سؤال استيعاب", "multipleChoiceQuestion": "سؤال متعدد الخيارات", "trueOrFalse": "ص<PERSON><PERSON><PERSON> أو خطأ", "shortAnswerQuestions": "سؤال إجابة قصيرة", "extendedMatchingQuestions": "سؤال مطابقة ممتد", "explanatoryQuestions": "سؤال شرح", "comprehensiveShortAnswerQuestion": "سؤال استيعاب قصير الإجابة", "matchingQuestions": "سؤال مطابقة", "hotspotQuestions": "أسئلة نقطة الاتصال", "mixedQuestions": "أسئلة مختلطة", "multipleRightAnswer": "سؤال بإجابات متعددة صحيحة", "clinicalPracticalExam": "الامتحان العملي السريري", "orderingQuestion": "سؤال ترتيب", "dragAndDropSequencing": "تسلسل السحب والإفلات", "cq": ".س.ا", "mcq": ".س.م.ا", "tf": ".ص.ا.خ", "saq": ".س.ا.ق", "mq": ".س.م", "emq": ".س.م.م", "eq": "س.ش.", "csaq": ".س.ا.ق.ا", "hq": ".ح.م", "mst": "م.س.ر", "mit": "م.س.ر", "mra": "مق (مرا)", "cpe": "معدات مواقع العملاء", "oq": "س.ت", "ddq": "دق"}, "profileSettings": {"personalSettings": "الإعدادات الشخصية", "changeYourLogo": "تغيير شعارك", "uploadYourLogo": "تحميل شعارك", "collegeName": "اسم الكلية", "enterCollegeName": "أد<PERSON>ل اسم الكلية", "enterCollegeAddress": "أدخل عنوان الكلية", "language": "اللغة", "defaultTimeZone": "المنطقة الزمنية الافتراضية", "fileShouldBeLess": ".jpeg .PNG يجب أن يكون الملف أقل من ١ ميغابايت ويمكن أن يكون التنسيق", "selectDefaultLanguage": "<PERSON><PERSON><PERSON> اللغة الافتراضية", "selectDefaultTimezone": "تحديد المنطقة الزمنية الافتراضية"}, "assessmentSavePopUp": {"total": "مجموع", "sections": "أق<PERSON>ام", "count": "<PERSON><PERSON><PERSON>", "time": "وقت", "marks": "الدرجات", "withReview": "مع المراجعة", "withoutReview": "بدون مراجعة", "equal": "متساوي", "unEqual": "غير متساوي", "none": "لا يوجد", "withoutSection": "بدون قسم", "plannedCount": "الع<PERSON> المخطط", "totalMinutes": "إجمالي الدقائق", "totalMarks": "إجمالي الدرجات", "itemCount": "<PERSON><PERSON><PERSON> العناصر"}, "403": {"permissionDenied": "تم رفض الإذن!", "permissionDeniedMessage": "ليس لديك إذن للوصول إلى البيانات المطلوبة. يرجى إضافة إذن أو تبديل الدور للمتابعة", "backToHome": "العودة إلى الصفحة الرئيسية"}, "testSpecPopup": {"testSpec": "تحديد / تعديل مواصفات الامتحان", "selectedCourse": "الدورة المحددة", "selectExam": "حدد نوع الامتح<PERSON> وحدد عدد العناصر لكل نوع عنصر", "itemsDistribution": "توزيع العنصر", "itemsUnderClo": " عدد العناصر الموجودة تحت كل نتيجة مستوي المنهج", "tasksUnderClo": " عدد العناصر الموجودة تحت كل نتيجة مستوي المنهج", "assessmentSettings": "إعدادات التقييم", "subjectsTopics": "المواد / الموضوعات", "total": "المجموع", "itemWeightage": "وزن العنصر", "taskWeightage": "وزن المهمة", "mcq": ".س.م.ا", "saq": ".س.ا.ق", "eq": ".س.ا.ق", "tOrF": ".ص.ا.خ", "mq": ".س.م", "emq": ".س.م.م", "cq": ".س.ا", "totalGroups": "إجمالي المجموعات", "totalStations": "محطات المجموع", "totalItems": "إجمالي العناصر", "totalTasks": "إجمالي المهام", "totalItemsCount": "إجمالي عدد العناصر", "listOfExam": "قائمة الامتحانات بنوع المحاولة", "quiz": "< اختبار (عادي)", "selectedConfiguration": "التكوين المحدد لجميع أنواع الاختبار  (يمكن للمسؤول فقط التغيير)", "overallItemsCount": "عد<PERSON> العناصر الإجمالية", "overallTaskCount": "ع<PERSON><PERSON> المها<PERSON> الإجمالي", "listExam": "قائمة الامتحانات بنوع المحاولة", "activeVersion": "الإصدار النشط", "testSpecification": "مواصفات الامتحان", "enableDisable": "تمكين / تعطيل مواصفات الامتحان", "saveAsOverwrite": "حفظ كالكتابة", "overrideNote": "ملاحظة: لن تظهر على الامتحانات المجدولة", "overrideDraftNote": "ملاحظة: \"سيتم تغيير إعدادات المسودات الحالية\"", "mismatchError": "العدد غير متطابق في نوع مفردة {{itemTypeCode}}", "chooseTestSpecification": "اختر مواصفات الاختبار", "createNew": "إنشاء جديد", "task": "مهمة", "item": "عنصر", "group": "مجموعة", "station": "محطة", "drafts": "نماذج", "manualCloTitle": "هل أنت متأكد أنك تريد تحديد / عدم تحديد؟", "manualCloMessage": "تتم إعادة تعيين جميع البيانات التي تقوم بتعيينها في CLO إذا تم تحديد / عدم تحديد خانة الاختيار.", "enterCloManually": "أدخل CLO يدويًا", "enterBelowTotalItems": "يجب أن تكون القيمة أقل من إجمالي عدد العناصر", "selectGroupEachSection": "please select at least one group in each section", "withSelectItemType": "لا يمكن المتابعة بدون تحديد '{{code}}' نوع العنصر", "noOfQuestion": "ع<PERSON><PERSON> الأسئلة", "newVersionCreateMessage": "تم إنشاء الإصدار الجديد بنجاح", "ossaiExam": "(امتحان أوساي)"}, "itemDistribution": {"itemDistributionHeatMap": "توزيع العنصر - خريطة الحرارة", "overallExamwise": "الامتحان الشامل", "totalExamwise": "الامتحان بشكل عام ", "topics": "المواضيع", "noOfQuestion": "ع<PERSON><PERSON> الأسئلة", "individualExam": "الامتحان الفردي", "regular": "عادي", "special": "خاص", "countMismatchError": "إجمالي العدد غير متطابق. يرجى إدخال رقم أقل من العدد الإجمالي المحدد", "addGroup": "أ<PERSON><PERSON> مجموعة أخرى", "addStations": "إضافة محطات أخرى"}, "testSpecPdf": {"fileDetails": "تفاصيل الملف", "reportName": "اسم التقرير", "exportDate": "تاريخ التصدير", "exportTime": "وقت التصدير", "courseDetails": "تفاصيل الدورة", "academicYear": "العام الأكاديمي", "courseCode": "ر<PERSON>ز الدورة", "courseName": "اسم الدورة", "selectedSubject": "الموضوع المختار", "selectedItemType": "نوع العنصر المختار"}, "ossai": {"ossai": "OSSAI", "viewSettings": "عرض الإعدادات", "editSettings": "تحرير الإعدادات", "createOssaiExam": "إنشاء امتح<PERSON> ossai", "createExam": "إنشاء الامتحان", "ossaiExamSettings": "إعدادات امتحان OSSAI", "stationDuration": "مدة المحطة", "minutes": "دقائق", "stations": "محطات", "station": "محطة", "task": "مهمة", "mark": "علامة", "ossaiExam": "امتحان أوساي", "resetExam": "إعادة الامتحان", "examSettings": "إعدادات الامتحان", "emptyStationMarks": "يجب ألا تكون علامة المحطة الإجمالية فارغة", "emptyDuration": "يجب ألا تكون المدة فارغة", "emptyStationTask": "يجب ألا تكون مهمة المحطة فارغة", "notMatchStationMarks": "علامات مهمة المحطة لا تتطابق مع إجمالي العلامات", "enterTotalMark": "أدخل العلامة الإجمالية", "enterStationDuration": "أدخل مدة المحطة", "examOssai": "امتحان {{ ossai }}", "novToOssaiExam": "انتقل إلى صفحة اختبار الروابط السريعة {{ ossai }} للوصول إلى الاختبارات والموارد.", "ossaiDashboard": "{{ossai}} لوحة التحكم"}, "passwordValidatorErrors": {"minLength": "يجب أن تتكون كلمة المرور من 8 أحرف على الأقل.", "uppercase": "يجب أن تحتوي كلمة المرور على حرف كبير واحد على الأقل.", "lowercase": "يجب أن تحتوي كلمة المرور على حرف صغير واحد على الأقل.", "digit": "يجب أن تحتوي كلمة المرور على رقم واحد على الأقل.", "specialCharacter": "يجب أن تحتوي كلمة المرور على حرف خاص واحد على الأقل.", "addGroup": "أ<PERSON><PERSON> مجموعة أخرى", "minimumTwoRequired": "مطلوب عنصرين على الأقل", "groupMismatchError": "إجمالي رقم المجموعة غير متطابق مع المجموعة المعينة", "stationGroupMismatchError": "إجمالي رقم المجموعة غير متطابق مع مجموعة المحطات", "groupCountMismatch": "إجمالي عدد المجموعات وعدد المجموعات الفرعية غير متطابقين", "moveSectionTitle": "هل تريد الاستمرار في هذا التغيير؟", "moveSectionMessage": "أنت تقوم بتغيير نوع العنصر في القسم {{currentSection}} إلى {{changeItemType}}. سيؤدي هذا إلى تبديل {{currentItemType}} الحالي في القسم {{currentSection}} إلى القسم {{changeSection}}."}, "externalMarks": {"listOfCourses": "قائمة المقررات للامتحان الخارجي", "importantInformation": "معلومات مهمة", "importantMessage": "هذه الصفحة مخصصة حصريًا لإضافة علامات الاختبارات التي يتم إجراؤها خارج DigiAssess.", "importantAssessmentMessage": "لا يحتوي هذا التقييم على عناصر، لذا لا يمكن إجراء مراجعة للعناصر، إذا كنت تريد مراجعة العناصر، فيرجى إنشاء عنصر في الاختبار الخارجي لهذه الدورة", "courseAuthors": "مؤلفو الدورة", "addMore": "<PERSON><PERSON><PERSON> المزيد", "addAuthors": "إضافة المؤلفين", "initiateBy": "البدء بواسطة", "addDetails": "إضافة تفاصيل", "noExamCreated": "لا يتم إنشاء أي اختبارات خارجية", "createNewExternalExam": "إنشاء امتحان خارجي جديد", "testCenterDetails": "تفاصيل مركز الاختبار", "externalInvigilator": "مرا<PERSON><PERSON> خارجي", "staffName": "اسم الموظفين", "place": "مكان", "addAnotherInvigilator": "إضافة مراقب آخر", "importTemplate": "استيراد من القالب", "addIndividualStudent": "إضافة طالب فردي", "sNo": "رقم س.", "qNo": "سؤال:", "addQuestionWiseMarks": "أضف علامات الاستفهام الحكيمة", "pleaseAddAssessment": "يرجى إضافة تفاصيل التقييم لإضافة العلامة الإجمالية لكل طالب على حدة", "viewAssessmentDetails": "انقر لإضافة/عرض تفاصيل التقييم", "importMarksInfo": "أثناء الاستيراد، اترك حقل الدرجات فارغًا أو ضع علامة - إذا كان الطالب غائبًا.", "floorName": "اسم الطابق", "addMoreStudents": "إضافة المزيد من الطلاب", "outcomeMetrics": "مقاييس النتائج", "addSessionDetails": "إضافة تفاصيل الجلسة", "addStudents": "إضافة الطلاب", "addMarks": "إضافة علامات", "addSession": "إضافة جلسة", "legend": "أسطورة", "markAllPresent": "وضع علامة على الكل كحاضر", "markAllAbsent": "وضع علامة على الكل على أنه غائب", "topics": "المواضيع", "subjects": "المواد", "importResults": "استيراد النتائج", "addCourseAuthors": "إضافة مؤلفي المقرر", "allStaffs": "جميع الموظفين", "courseStaffs": "موظفو المقرر", "moveToanotherSession": "الانتقال إلى جلسة أخرى", "completeEvaluation": "إكمال التقييم", "addToReport": "إضافة إلى التقرير", "compareExam": "امتحان المقارنة", "showAllExam": "عرض كل الامتحان", "mergeColumn": "د<PERSON><PERSON> العمود", "groupColumn": "عمو<PERSON> المجموعة", "goToReport": "اذ<PERSON><PERSON> إلى التقرير", "pleaseSelectStudent": "ير<PERSON>ى اختيار الطلاب", "importStudents": "استيراد الطلاب", "importDigiClass": "استيراد من ديجي كلاس", "importPrevious": "استيراد الطلاب من الامتحانات السابقة", "confirmTitle": "تأكيد: إضافة إلى التقرير", "confirmMessage": "هل أنت متأكد أنك تريد إضافة هذا إلى التقرير؟ بمجرد الإضافة، لا يمكنك تغيير البيانات هنا.", "totalMarksExceeds": "تجاوزت العلامات الكلية. يرجى إدخال علامات كلية صالحة.", "addedToReport": "تمت الإضافة إلى التقرير", "noSessionsFound": "لا توجد جلسات لنقل الطلاب إلى جلسة أخرى", "itemCountMisMatch": "عدم تطاب<PERSON> العدد المطلوب للعناصر", "itemMarksMisMatch": "عدم تطابق العلامات المطلوبة للعناصر", "emptyDataFound": "يجب ألا تكون العلامات فارغة", "emptyDateFound": "لم يتم العثور على تاريخ الجلسة الرجاء إدخال تاريخ صالح", "emptyTCNameFound": "الرجاء إدخال اسم مركز الاختبار", "itemsRequired": "العناصر المطلوبة", "marksRequired": "العلامات المطلوبة", "deleteConfirmation": "هل تريد حذف هذه الدورة؟", "studentCourseGrouping": "تجميع مقررات الطلاب", "assessmentDetails": {"assessmentDetails": "تفاصيل التقييم", "viewAttachment": "عرض المرفقات", "extractFullAssessment": "استخراج التقييم الكامل", "qno": "سؤال:", "topic": "عنوان", "itemType": "نوع العنصر", "stem": "ينبع", "mappingTaxonomy": "رسم الخرائط / التصنيف", "marks": "ماركس", "addNewItem": "أ<PERSON><PERSON> أداة جديدة", "attachFrom": "إرفاق من", "questionBank": "بنك الأسئلة", "mediaOrDocument": "الوسائط أو المستند", "documentType": "(.pdf, .doc, .jpg)", "viewMoreContent": "يؤدي النقر أو النقر في أي مكان إلى إظهار الجذع الكامل.", "viewMore": "عرض المزيد ...", "viewStemAttachment": "عرض المرفقات", "uploading": "تحميل", "previousPage": "العودة إلى الصفحة السابقة"}, "addExternalInvigilatorPopup": {"addExternalInvigilator": "إضافة مراقب خارجي", "editExternalInvigilator": "تحرير المراقب الخارجي", "addInternalStaff": "البيانات من إدارة المستخدمين (إضافة موظفين داخليين)", "enterStaffName": "أد<PERSON>ل اسم الموظف", "enterEmailId": "أدخل معرف ال<PERSON><PERSON>ي<PERSON> الإلكتروني", "enterMobileNumber": "أدخل رقم الجوال", "place": "مكان"}, "addViewAssessmentDetails": {"addAssessmentDetails": "إضافة تفاصيل التقييم", "editAssessmentDetails": "تحرير تفاصيل التقييم", "splitUpBasedOnTotalNumberOfItemsEntered": "تم تقسيم العلامات استنادًا إلى إجمالي عدد العناصر المُدخلة", "totalMarks": "أ<PERSON><PERSON><PERSON> العدد الإجمالي للعلامات", "totalItems": "أ<PERSON><PERSON><PERSON> العدد الإجمالي للعناصر", "overall": "إجمالي", "splitUp": "إنفصل", "itemsRequiredMsg": "عدم تطابق العدد المطلوب لعناصر التقييم/العلامات مع عدد العناصر المخلقة. يرجى مراجعة ذلك وإجراء التغييرات إذا لزم الأمر.", "itemsRequiredTitle": "عدم تطاب<PERSON> العدد المطلوب لعناصر التقييم/العلامات"}, "excel": {"sNo": "التسلسل", "academicId": "الرقم الأكاديمي", "studentName": "اسم الطالب", "status": "الحالة", "totalMarks": "العلامات الكلية({{totalMarks}})", "grade": "الدرجة", "percentage": "النسبة المئوية", "error": "الملف يحتوي على بيانات غير صالحة. يرجى اتخاذ الإجراءات اللازمة وإعادة تحميله مرة أخرى."}, "questionExportPdfPopup": {"educationalServices": "عمادة الخدمات التعليمية", "instructor": "مدرب", "studentName": "<PERSON><PERSON><PERSON> الطالب", "room": "غرفة", "section": "قسم", "generalInstruction": "تعليمات عامة", "messageOne": "اقرأ واتبع التعليمات الخاصة بكل سؤال بعناية.", "messageTwo": "يجب إغلاق الهواتف المحمولة والأجهزة الذكية ووضعها على الطاولة المخصصة من قبل المراقب أثناء الامتحان", "messageThree": "يجب إغلاق الهواتف الذكية التي تضعها في مكان محدد من المراقب قبل الامتحان", "messageFour": "للحصول على الدرجة الكاملة للسؤال، عليك إظهار جميع الخطوات المؤدية إلى الإجابة النهائية", "messageFive": "سيتم التعامل مع الغش بأي وسيلة بجدية وفقًا للوائح الجامعة", "answerAllQuestions": "الإجابة على جميع الأسئلة", "part": "ج<PERSON><PERSON>", "page": "صفحة", "preview": "معاينة", "setNumberOfLines": "تحديد عدد الأسطر لإجابة الطالب (الاختبار الورقي)"}, "withData": "مع رقم أكاديمي", "withoutData": "بدون رقم أكاديمي", "abToPErrorMessage": "يج<PERSON> إدخال الدرجات لجميع الطلاب الحاضرين أثناء إعادة التقييم", "pToABErrorMessage": "لا يمكن تحديث الدرجات للطلاب الغائبين أثناء إعادة التقييم", "noRecordsFoundRefineSearch": "لم يتم العثور على سجلات. يرجى تحسين معايير البحث الخاصة بك والمحاولة مرة أخرى."}, "learningOutcomeReports": {"reportsAndAnalytics": "التقارير والتحليلات", "learningOutcome": "تقارير نتائج التعلم", "comparisonAnalysis": "تحليل المقارنة", "frameWork": "نطاق", "filters": "المرشحات", "clearAll": "ا<PERSON><PERSON><PERSON> الكل", "applyFilters": "تطبيق المرشحات", "trendAnalysis": "تحليل الاتجاه", "outcomeAchievements": "إنجازات النتيجة", "filterBy": "مصنف بواسطة", "fullName": "اسم الدورة التدريبية", "achievementStatus": "حالة الإنجاز", "notAchieved": "غير منجز", "achieved": "Achieved", "overwriteStatus": "الكتابة فوق الحالة", "taggedExams": "الامتحانات الموسومة", "fes": "فاس", "domainWeightAge": "وزن المجال %", "overallDomainAchievement": "الإنجاز الإجمالي للمجال٪", "moreDetails": "المزيد من التفاصيل", "outcomesTagged": "النتائج", "knowledge": "معرفة", "cognitiveBased": "القائم على المعرفي", "interpersonal": "شخصي", "communication": "تواصل", "professionalism": "احترافية", "avg": "متوسط", "exclusionCriteria": "معايير الاستبعاد", "configuringWeightage": "تكوين الوزن", "weightedAverage": "متوسط ​​الوزن", "impactWeightage": "وزن التأثير", "choose": "يختار", "weightage": "الوزن", "chooseWeightage": "اختر إضافة الوزن", "excluding": "باستثناء", "excluded": "مستبعد", "hideUnhide": "يخفي / إظهار", "excludeIncludeItems": "استبعاد / تضمين عناصر من القائمة؟", "excludeIncludeItemSelections": "سيؤدي استبعاد / تضمين عناصر من القائمة إلى تعديل التقرير وفقًا للاختيارات", "sureWantExcludeInclude": "هل أنت متأكد أنك تريد استبعاد/تضمين العناصر؟", "hideItem": "إخفاء العنصر", "unhideItem": "إظهار العنصر", "excellent": "مم<PERSON><PERSON><PERSON>", "good": "<PERSON>ي<PERSON>", "satisfactory": "مق<PERSON>ول", "needsAttention": "يحتاج إلى اهتمام", "selectYear": "Select year", "selectLevel": "Select level", "selectProgramPlaceholder": "الرجاء تحديد أي برنامج لعرض التفاصيل", "weightagePlaceHolder": "اختر كل الحقول لبدء التكوين", "outcomeWiseTooltip": "البيانات تمثل جميع عدد العناصر", "itemTypeWiseTooltip": "تمثل البيانات متوسط جميع أنواع العناصر", "itemWiseTooltip": "البيانات تمثل {{itemType}} عناصر", "excludedItem": "هذا مستبعد، ولا يمكن إضافة المتوسط المرجح", "weightedAverageApplied": "لا يمكن استبعاد متوسط الوزن المطبق", "confirmExclude": "أنت على وشك استبعاد مكون يحتوي على متوسط مرجح. يرجى أن تكون على علم بأن هذا الإجراء سيؤثر على التقرير. هل أنت متأكد أنك تريد المتابعة؟", "weightedAverageInfo": "يتجاوز الحد: يجب أن يصل مجموع التخصيص إلى ١٠٠% لكل مستوى", "performanceLevels": "مستويات الأداء", "sharedTo": "مشارك مع", "sharedFrom": "مشارك من", "alsoSharedToMultiplePrograms": "أيضًا مشارك مع عدة برامج", "outcome": "حصيلة", "mapOutcome": "نتيجة الخريطة", "unMapOutcome": "إلغاء خريطة النتيجة", "mapDesc": "تم إجراء هذا الاختبار بدون عناصر ونوع التعيين بشكل عام، هل ترغب في متابعة التعيين", "unMapDesc": "هل أنت متأكد أنك تريد إلغاء تعيين هذه النتيجة", "clearedTag": "تم مسح العلامة", "listOfAllExams": "قائمة بجميع الامتحانات ل", "equivalenceWeightage": "الوزن المعادل", "weightageAllocated": "سيتم تخصيص الوزن داخل كل مستوى من التسلسل الهرمي، باستثناء المستويات إس إو و كيه بي آي و كيه بي آي إي. يجب أن يكون إجمالي التخصيص مساويًا لـ ١٠٠٪ لكل مستوى.", "studentAvgPerformance": "أداء الطالب المتوسط", "warning": "تحذير", "warningMessage": "سيؤدي هذا الإجراء إلى إعادة ضبط الوزن المطبق بالكامل لجميع العناصر. هل أنت متأكد من رغبتك في المتابعة دون تطبيق المتوسط ​​المرجح؟", "weightedAverageApply": "تم تطبيق المتوسط المرجح", "benchmarksApplied": "تم تطبيق المعايير المرجعية", "threshold": "عتبة", "thresholdContent": "لتحديد مستويات العتبة المقبولة وتحديد الانحرافات لمستوى لذا.", "yesInActive": "نعم غير فعال", "thresholdWarningHeader": "تأكيد تعطيل العتبة", "thresholdWarningContent": "هل أنت متأكد أنك تريد <b>غير نشط</b> الحد؟", "achievementPercentage": "إنجاز (٪)", "thresholdPercentage": "عتبة (٪)", "theresholdAchieved": " تم تحقيق العتبة", "theresholdNotAchieved": "لم يت حقق العتبة", "notUsed": "غير مستعمل", "appliedSuccessfully": "تم التطبيق بنجاح", "studentOutcomeAnalysisReport": "تقرير تحليل نتائج الطلاب", "programAnalysis": "تحليل البرنامج", "weightageAppliedCannotExclude": "Weightage applied cannot exclude", "seatDeleteWarning": "تم تخصيص هذا المقعد لطالب في جدول قادم. هل ما زلت ترغب في حذفه؟"}, "studentOutcomeAnalysis": {"programAnalysis": {"chooseProgram": "اختر برنامج", "chooseTerm": "اختر المصطلح", "chooseCurriculum": "اختر المنهج", "chooseFramework": "اختر الإطار", "chooseComponent": "اختر المنهج", "noComponentsFound": "لم يتم العثور على مكونات", "noProgramSelected": "لم يتم اختيار برنامج بعد", "chooseAnyProgram": "اختر أي برنامج لعرض تحليل البرنامج", "hide": "يخفي", "unHide": "إظهار", "listOfCourses": "قائمة الدورات ", "insights": "<PERSON>ف<PERSON><PERSON><PERSON>", "totalNoOfCourses": "إجمالي عدد الدورات", "mappedWithSo": "تم تعيينها مع SO", "inDraft": "في المسودة", "barVisualization": "تصور الشريط", "tableVisualization": "تصور الجدول", "componentElements": "العناصر المكونة", "configureExclusion": "تكوين معايير الاستبعاد", "exclusionNote": "<b>ملاحظة:</b> يجب تضمين دورة تدريبية واحدة على الأقل لحساب النتائج.", "viewExcludedList": "عرض قائمة المستبعدين", "chooseRow": "اختر الصف", "yearAndLevel": "السنة والمستوى", "dataVisualizationCourse": "تصور البيانات لـ <b>“{{courseName}}”</b>", "onlyCompletedAnalysis": "التحليلات متاحة فقط للدورات التي تم الانتهاء منها", "overallAvgCalculation": "ويحسب المعدل العام على أساس متوسط ​​جميع السنوات.", "showHideTable": "إظهار / إخفاء الجدول", "relativeAnalysis": "التحليل النسبي", "rebuildReportNote": "يرجى إعادة تشغيل التقرير بسبب التغييرات التي تم إجراؤها أثناء تحليل الدورة التدريبية.", "listOfAllCourses": "قائمة بجميع الدورات لـ"}}, "independentAssessmentAuthoring": {"createAssessment": "إنشاء التقييم", "assessmentCreation": "إنشاء التقييم", "independentAssessmentCreation": "إنشاء تقييم مستقل", "noFiltersApplied": "- لم يتم تطبيق أي مرشحات حتى الآن للعرض -", "noAssessmentCreated": "لم يتم إنشاء أي تقييم حتى الآن في هذه الدورة", "createIndependentAssessment": "إنشاء تقييم مستقل", "noAssessmentsAvailable": "لا توجد تقييمات متاحة", "listOfAssessments": "قائمة التقييمات التي تم إنشاؤها", "itemDetails": "تفاصيل العنصر", "dateAndTime": "التاريخ والوقت", "scheduledDetails": "التفاصيل المجدولة", "dueDate": "تاريخ الاستحقاق", "examStatus": "حالة الامتحان", "createIndependent": "إنشاء تقييم مستقل", "durationMins": "المدة (دقائق)", "duration": "المدة", "mustAnswer": "يجب الإجابة", "subjectTopic": "-- الموضوع / الموضوع --", "cloMapping": "-- رس<PERSON> خرائط CLO --", "taxonomy": "-- التصنيف --", "restrict": "قيد", "choiceShuffling": "اختيا<PERSON> خلط", "itemNavigation": "تنقل العنصر", "choiceAndAnswerKey": "اختيار ومفتاح الإجابة", "addOption": "إضافة خيار", "leftAlign": "محاذاة اليسار", "rightAlign": "محاذاة اليمين", "centerAlign": "محاذاة الوسط", "addItem": "إضافة عنصر", "uploadOldItems": "تحميل العناصر القديمة", "addSection": "إضافة قسم", "equalMark": "علامة متساوية", "customMark": "علامة مخصصة", "sectionCount": "{{current}} ل {{total}}", "restrictNavigation": "قيد التنقل", "cannotDeleteLastItem": "لا يمكن حذف العنصر الأخير", "specifyTotalMarks": "يرجى تحديد مجموع العلامات", "addTotalDuration": "يرجى تحديد المدة الإجمالية", "pleaseFillAllRequiredFields": "يرجى ملء جميع الحقول المطلوبة", "sectionTimeInValid": "يج<PERSON> أن يكون وقت القسم أقل من وقت الامتحان", "sectionMarksInValid": "يجب أن تكون علامات القسم أقل من علامات الامتحان", "itemTotalMarksMismatch": "علامات العنصر لا تتطابق مع مجموع العلامات", "itemTimeInvalid": "يج<PERSON> أن يكون وقت العنصر أقل من وقت القسم", "itemMarksInvalid": "علامات العنصر لا تتطابق مع علامات القسم في القسم رقم {{sectionNo}}", "assessmentPlanSettings": "إعدادات خطة التقييم", "assessmentConductionSettings": "إعدادات إجراء التقييم", "shufflingSettings": "إعدادات الخلط", "totalNoOfSections": "إجمالي عدد الأقسام", "totalNoOfItems": "إجمالي عدد العناصر", "totalDuration": "المدة الكلية", "restrictLateEntry": "قيد الدخول المتأخر", "allowLateEntryTime": "السماح بحد أقصى لوقت الدخول المتأخر كنسبة مئوية (%) من إجمالي مدة الامتحان", "reduceAllowedLateEntry": "تقليل وقت الدخول المتأخر من إجمالي الوقت", "minimumPercentage": "تعيين الحد الأدنى من الوقت كنسبة مئوية (%) يجب على الطالب حضور الامتحان", "allowRearrangeSection": "السماح بإعادة ترتيب القسم", "shuffleSections": "<PERSON>لط الأقسام", "shuffleItems": "<PERSON>لط العناصر", "shuffleItemsWithinGroups": "خلط العناصر داخل المجموعات (.س.م.م، .س.ا، .س.م، .س.ا.ق.ا، م.س.ر)", "mediaSettings": "إعدادات الوسائط", "addTimeForAllSection": "إضافة وقت لجميع الأقسام", "responseType": "نوع الاستجابة", "digital": "مبني على الرقمية", "paper": "ورقي", "selectExamAdmin": "حدد مسؤول الامتحان", "primaryProctor": "المراقب الأساسي", "secondaryProctor": "المراقب الثانوي", "youCanSelectMultipleProctors": "يمكنك تحديد مراقبين متعددين", "examStartConditions": "شروط بدء الامتحان", "allowExamStart": "بدء الاختبار تلقائيًا دون الحاجة إلى إدارة بدء الاختبار", "canStartWithin": "يمكن بدء الاختبار في غضون", "beforeExamStart": "دقا<PERSON><PERSON> قبل بدء الامتحان", "addNew": "إضافة جديد", "systemRequirementIsMissing": "الرجاء تحديد متطلبات النظام للمتابعة", "studentAuthenticationMethodIsMissing": "الرجاء تحديد طريقة مصادقة الطالب للمتابعة", "browsersSelectionIsMissing": "الرجاء تحديد المتصفحات للمتابعة", "assessmentTypeIsMissing": "الرجاء تحديد نوع التقييم للمتابعة", "examTypeIsMissing": "الرجاء تحديد نوع الامتحان للمتابعة", "locationAndProctoringTypeIsMissing": "الرجاء تحديد الموقع ونوع المراقبة للمتابعة", "examCategoryIsMissing": "الرجاء تحديد فئة الامتحان للمتابعة", "attemptTypeIsMissing": "الرجاء تحديد نوع محاولة للمتابعة", "newlyAdded": "م<PERSON><PERSON><PERSON> حديثا", "existing": "مو<PERSON><PERSON><PERSON>", "select": "- ا<PERSON>تار - ", "noStudentGroupFound": "- لم يتم العثور على مجموعة طلاب -", "goToStudentGrouping": "الانتقال إلى تجميع الطلاب", "uploadedStudents": "الطلاب المحملين", "forMaleStudents": "للطلاب <b>الذكور</b>", "forFemaleStudents": "للطلاب <b>الإناث</b>", "chooseTestCenter": "اختر المساهمين الأساسيين (TC) المطلوبين من الجدول أدناه:", "testCenterAllocationNote": "افتراضيًا، يتم تخصيص مراكز الاختبار تلقائيًا حسب السعة الأعلى. يمكنك الاختيار يدويًا عن طريق إلغاء تحديد واختيار مراكز الاختبار المطلوبة.", "filledByPlannedCount": "القدرة الاستيعابية للطلاب/المراكز التدريبية:", "allocatedStudentsPlannedStudents": "الطلاب المخصصون/الطلاب المخطط لهم", "fillOutFields": "يرجى ملء جميع الحقول اللازمة", "roomNumber": "رقم الغرفة", "examEndsBy": "ينتهي الامتحان بحلول", "specifyDateForExam": "تحديد موعد للامتحان", "noTestCenterAddedYet": "- لم تتم إضافة أي مراكز اختبار حتى الآن لإظهارها -", "noVirtualTestCenterAddedYet": "- لم تتم إضافة أي مراكز اختبار عن بعد حتى الآن لإظهارها -", "timePickerPlaceHolder": "سمو : مم أأ", "allocatingStudents": "تخصيص الطلاب", "addOnlyImage": "إضافة صور (فقط إذا لزم الأمر)", "itemCreation": "إنشاء العنصر", "examConfiguration": "تهيئة الامتحان", "assignStudents": "تعيين الطلاب", "scheduleExamAndAllocateTestCenter": "جدولة الامتحان وتخصيص مركز الاختبار", "importStudentList": "استيراد قائمة الطلاب", "selectInstitutionCalender": "حد<PERSON> تقويم المؤسسة", "selectStudentGroup": "<PERSON><PERSON><PERSON> مجموعة الطلاب", "noStudentCount": "<PERSON><PERSON> يو<PERSON>د عدد طلاب", "StudentNotFoundInGroup": "<b><PERSON>ي<PERSON> ق<PERSON>ر على المتابعة!</b> لم يتم العثور على الطلاب في بعض مجموعات الطلاب", "examAdminNote": "لا حاجة لمسؤول الامتحان لبدء الجلسة حيث سيتم بدء الامتحان تلقائيًا بناءً على وقت بدء الامتحان.", "pleaseAddTestCenter": "الرجاء إضافة مركز الاختبار", "pleaseAssignStudentsForAllTc": "يرجى توزيع الطلاب على جميع مراكز الاختبار", "pleaseSelectExamDate": "الرجاء تحديد تاريخ الامتحان", "pleaseSelectExamTime": "ير<PERSON>ى تحديد وقت الامتحان", "noImportedStudents": "تأكد من استيراد جميع الطلاب قبل الاستمرار", "maleCount": "ذكور: {{ assignedCount }}/{{ plannedCount }}", "femaleCount": "أنثى: {{ assignedCount }}/{{ plannedCount }}", "viewOverallDetails": "عرض التفاصيل العامة", "assessmentPublishNote": "يرجى مراجعة التفاصيل بعناية قبل المتابعة. سيتم إرسال إشعار عبر البريد الإلكتروني إلى الطلاب والمراقبين.", "assessmentPlan": "خطة التقييم", "scheduledDateAndTime": "التاريخ والوقت المحددين", "examDateAndTime": "تاريخ ووقت الامتحان", "sectionType": "نوع القسم", "withSection": "مع قسم", "withoutSection": "بدون قسم", "makeDistributionMethod": "طريقة توزيع العلامات", "modeOfConduction": "طريقة إجراء الامتحان", "examAuthentication": "مصادقة الامتحان", "testCenterInvolved": "مركز الاختبار المعني", "equalMarks": "علامات متساوية", "splitMarks": "علامات متساوية", "splitMarksOn": "سيقسم العلامات بالتساوي بين الأقسام والعناصر", "splitMarksOff": "يجب توزيع العلامات يدويًا", "customMarks": "علامات مخصصة", "noGroups": "لا توجد مجموعات", "pleaseSelectExamAdmin": "الرجاء تحديد مسؤول الامتحان", "pleaseSelectPrimaryProctor": "الرجاء تحديد المراقب الرئيسي", "studentOwnedDevice": "ج<PERSON>ا<PERSON> الطالب الخاص", "listOfTestCenters": "قائمة مراكز الاختبار", "allocatedStudents": "الطلاب المخصصون:", "onsiteWithProctor": "في الموقع مع مراقب (CTC); جهاز الطالب الخاص", "remoteWithProctor": "عن بعد مع مراقب (VTC); جهاز الطالب الخاص", "remoteWithoutProctor": "عن بعد بدون مراقب; جهاز الطالب الخاص", "unp": "كلمة المرور", "faceFemale": "الوجه (ف)", "faceMale": "الوجه (م)", "face": "الوجه", "notPublished": "غير منشور", "enablesAt": "يتم تمكينه في:", "confirmTile": "تأكيد التغيير", "clickToProceed": "انقر فوق <b>أ<PERSON>هم</b> للمتابعة.", "iUnderstand": "<PERSON><PERSON><PERSON><PERSON>", "confirmMessage": "بعض مجموعات الطلاب غير متاحة للتكوين المحدد وسيتم إزالتها في <b>الخطوة 3: “تعيين الطلاب”</b>", "confirmAdditionalInfo": "هل أنت متأكد أنك تريد <b>المتابعة</b>؟", "confirmChangePrimaryButton": "نعم، تغيير", "proctorChangeTitle": "تحذير لتغيير الوضع عن بُعد بدون مراقب", "proctorChangeMessage": "سيؤدي التغيير إلى الاختبار عن بعد بدون مراقب (R-P) إلى <b>حذف</b> جميع مراكز الاختبار المضافة ومجموعات الطلاب أيضًا.", "proctorChangePrimaryButton": "نعم، تغيير إلى R-P", "addVirtualTestCenter": "إضافة مركز اختبار افتراضي", "virtualTcName": "اسم TC الافتراضي", "examInProgress": "يُمنع التعديل بعد بدء الامتحانات. يُرجى إلغاء الامتحان من بوابة الامتحانات للتعديل.", "examIsPublished": "تم نشر الامتحان. لإجراء أي تعديلات، يُرجى إلغاء نشره أولًا.", "scheduleRequestCreatedWithdraw": "تم إنشاء طلب الجدولة، إلغاء الطلب للتحرير", "pleaseCompletePreviousSteps": "يرجى استكمال الخطوات السابقة", "facialAndPassword": "الوجه أو كلمة المرور", "assessmentDeleteMessage": "سيؤدي حذف هذا التقييم إلى إزالته نهائيًا. لا يمكن التراجع عن هذا الإجراء.", "scheduleDeleteMessage": "سيؤدي حذف هذا الطلب إلى إزالته نهائيًا. لا يمكن التراجع عن هذا الإجراء.", "restrictRevisitingSection": "قيد إعادة زيارة القسم", "completedAssessmentToEdit": "يقتصر التحرير على التقييمات المكتملة.", "backToIndependentAssessmentPage": "العودة إلى صفحة التقييم المستقل", "studentDeleteConfirmation": "هل أنت متأكد أنك تريد حذف هذا الطالب؟", "deleteMultiStudentsConfirmation": "هل أنت متأكد أنك تريد حذف الطلاب المحددين؟", "plannedCountShouldBeMore": "يجب أن يكون العدد المخطط أكبر أو يساوي عدد الطلاب المستوردين", "searchContent": "ابحث عن نوع الامتحان، وفئة الامتحان، ونوع التقييم، ونوع المحاولة", "uploadInactiveStudents": "يسمح لك تحديد مربع الاختيار هذا بتحميل الطلاب غير النشطين أيضًا.", "itemShouldNotHaveZeroMarks": "يج<PERSON> ألا يكون للعنصر علامات صفرية", "confirmReset": "تأكيد إعادة التعيين والتوزيع المتساوي", "customMarkReset": "سيتم إعادة تعيين جميع العلامات المخصصة المخصصة لكل عنصر وإعادة توزيعها بالتساوي بناءً على إجمالي العلامات.", "resetAndDistributeMsg": "هل أنت متأكد أنك تريد <b>إعادة التعيين</b> و<b>توزيع العلامات بالتساوي</b>؟", "resetAndDistribute": "إعادة التعيين وتوزيع", "maleStudentsAreNotAllocated": "بعض الطلاب الذكور لم يتم تخصيصهم بعد", "maleStudentsAllocatedSuccessfully": "تم تخصيص جميع الطلاب الذكور بنجاح", "femaleStudentsAreNotAllocated": "بعض الطالبات لم يتم تخصيصهن بعد", "femaleStudentsAllocatedSuccessfully": "تم تخصيص جميع الطالبات بنجاح", "publishedBy": "نشر بواسطة", "publishedDate": "تاريخ النشر", "noOfTimesPublished": "عد<PERSON> المرات التي تم نشرها", "uploadOldAssessment": "تحميل التقييم القديم / العناصر الفردية", "attachFromQB": "إرفاق من بنك الأسئلة", "listOfOldAssessment": "قائمة التقييمات القديمة", "attachAssessment": "إرفاق التقييم", "listOfTopics": "قائمة المواضيع", "attachItems": "إرفاق العناصر", "moveToSection": "الانتقا<PERSON> إلى القسم", "uploadItemsAssessments": "تحميل العناصر / التقييمات", "uploadedItemCannotEdit": "لا يمكن تحرير العناصر المحملة", "stemEmpty": "يبدو أن الجذع فارغ في القسم رقم.{{sectionNo}} / Q.No.{{itemNo}} ({{itemTypeCode}})", "withOutSectionStemEmpty": "يبدو أن الجذع فارغ في Q.No.{{itemNo}} ({{itemTypeCode}}).", "addChoicesOrAnswer": "يرجى إضافة الخيارات/مفتاح الإجابة في القسم رقم.{{sectionNo}} / السؤال رقم.{{itemNo}} ({{itemTypeCode}}).", "hotSpotAreaShouldBeDrawn": "يجب رسم منطقة النقاط الساخنة على القسم رقم.{{sectionNo}} / السؤال رقم.{{itemNo}} ({{itemTypeCode}}).", "withoutSectionhotSpotAreaShouldBeDrawn": "يجب رسم منطقة النقاط الساخنة على السؤال رقم {{itemNo}} ({{itemTypeCode}}).", "withOutAddChoicesOrAnswer": "يرجى إضافة الخيارات/مفتاح الإجابة على السؤال رقم {{itemNo}} ({{itemTypeCode}}).", "markAnswerKey": "يرجى وضع علامة على مفتاح الإجابة في القسم رقم.{{sectionNo}} / سؤال رقم{{itemNo}} ({{itemTypeCode}}).", "withOutMarkAnswerKey": "يرجى وضع علامة على مفتاح الإجابة في القسم رقم.{{sectionNo}} /  سؤال رقم{{itemNo}} ({{itemTypeCode}}).", "promptEmpty": "موجه ({{subItemNo}}) فارغ في القسم رقم.{{sectionNo}} /  Q.No.{{itemNo}} ({{itemTypeCode}}).", "subItemStemEmpty": "يبدو أن الجذع فارغ في القسم رقم.{{sectionNo}} /  Q.No.{{itemNo}} ({{itemTypeCode}}) العنصر الفرعي ({{subItemNo}}).", "withoutSectionSubItemStemEmpty": "يبدو أن الجذع فارغ في Q.No.{{itemNo}} ({{itemTypeCode}}) العنصر الفرعي ({{subItemNo}}).", "subItemAddChoicesOrAnswer": "يرجى إضافة الخيارات/مفتاح الإجابة في القسم رقم.{{sectionNo}} / السؤال رقم.{{itemNo}} ({{itemTypeCode}}) العنصر الفرعي ({{subItemNo}}).", "withOutSubItemAddChoicesOrAnswer": "يرجى إضافة الخيارات/مفتاح الإجابة على السؤال رقم {{itemNo}} ({{itemTypeCode}}) العنصر الفرعي ({{subItemNo}}).", "onsiteWithProctorTooltip": "يسمح بإجراء الاختبارات من خلال المراقبة المباشرة في مواقع مرنة وغير مركزية مثل الفصول الدراسية أو المساحات المخصصة للتقييمات ذات المخاطر المنخفضة.", "remoteWithProctorTooltip": "يسمح بإجراء تقييمات عن بعد مع المراقبة المباشرة من خلال أي منصة افتراضية باستخدام رابط مشترك.", "remoteWithoutProctorTooltip": "يسمح بإجراء تقييمات عن بعد دون مراقبة مباشرة من خلال أي منصة افتراضية باستخدام رابط مشترك.", "unPublishAssessmentConfirmation": "تأكيد إلغاء نشر التقييم!", "unPublishNote": "<b>ملاحظة هامة:</b><ul><li>لا يمكن للطلاب حضور الامتحان حتى يتم إعادة نشره.</li><li>عند إلغاء النشر، سيتم إخطار الطلاب والمراقبين ومسؤولي الامتحان عبر البريد الإلكتروني.</li><li>يرجى التأكد من إعادة النشر بمجرد إجراء التغييرات المطلوبة، وستُرسل إشعارات بالتغييرات الأخيرة إلى المستخدمين المعنيين.</li></ul>", "areYouSureToUnPublish": "هل أنت متأكد أنك تريد <b>إلغاء النشر</b>؟", "optionsCannotEqual": "تم الوصول إلى الحد الأقصى من الخيارات: لا يمكنك إضافة أكثر من {{questionCount}} خيارًا.", "mqNoteInfo": "يجب أن يكون عدد الخيارات والأسئلة متساويًا", "configuringTopic": "تكوين الموضوع", "anyNewItemsCreated": "أية عناصر جديدة يتم إنشاؤها ستحتوي أيضًا على هذا الموضوع.", "applyThisSection": "تقدم إلى هذا القسم", "applyAllSections": "تنطبق على جميع الأقسام", "searchTopicSubject": "البحث عن الموضوع/الموضوع", "applyThisTopic": "قم بتطبيق هذا الموضوع على العناصر التي لديها موضوع بالفعل", "setTopicForAllOrSpecificSection": "تعيين موضوع للجميع أو قسم محدد", "addPair": "إضافة زوج", "promptLabel": "السؤال", "answerLabel": "الإجابة", "centralizedInfrastructure": "استخدام البنية التحتية المركزية", "UsingCustomTestCenter": "استخدام مركز الاختبار المخصص", "chooseMethod": "اختر الطريقة", "responseCalculationMethod": "طريقة حساب الاستجابة", "addWeightageToAnswerKey": "إضافة الوزن لكل اختيار", "setTarget": "تحديد الهدف", "noOfCorrectAnswers": "عد<PERSON> الإجابات الصحيحة", "assignTo": "تعيين إلى", "assignReviewer": "تعيين المراجع", "allItemSentForReview": "سيتم إرسال جميع العناصر للمراجعة", "pleaseSelectItemToMoveSection": "الرجاء تحديد عنصر لنقله إلى قسم.", "pleaseSelectItemForAssignItemAuthor": "الرجاء تحديد عنصر لتعيين مؤلف العنصر.", "requestedByAssessmentCreator": "بناءً على طلب منشئ التقييم", "createdByself": "تم إنشاؤه ذاتيًا", "onGoing": "جارٍ", "notClosedYet": "لم يُغلق بعد", "examNotStartedYet": "الاختبار لم يبدأ بعد", "needToStartExam": "يجب بدء الاختبار", "all": "الكل", "createdBySelf": "تم الإنشاء ذاتيًا", "requestedByExamSchedular": "تم الطلب بواسطة مجدول الامتحانات", "scheduleRequest": "طل<PERSON> الجدولة", "requestedByCourseAuthor": "تم الطلب بواسطة مؤلف الدورة", "approveRequest": "موافقة على الطلب", "scheduleRejectReason": "<PERSON><PERSON><PERSON> الطلب", "bothAnswerKeyAndCount": "يجب أن يكون كل من مفتاح الإجابة الإجمالي وعدد الإجابات الصحيحة هو نفسه بالنسبة للقسم رقم {{sectionNo}} / رقم السؤال {{itemNo}} ({{itemTypeCode}}).", "withOutBothAnswerKeyAndCount": "يجب أن يكون كل من مفتاح الإجابة الإجمالي وعدد الإجابات الصحيحة متماثلين بالنسبة للسؤال رقم {{itemNo}} ({{itemTypeCode}}).", "weightageShouldBe100Percentage": "يجب أن تكون النسبة المئوية للأوزان مساوية لـ يجب أن يكون الوزن مساويًا لـ 100 بالمائة على رقم القسم {{sectionNo}} / رقم الطلب {{itemNo}} ({{itemTypeCode}}).", "weightageShouldOnlyAddedForCorrectAnswers": "يجب إضافة الوزن فقط للإجابات الصحيحة في القسم رقم {{sectionNo}} / السؤال رقم {{itemNo}} ({{itemTypeCode}}).", "pleaseScheduleAndContinue": "يرجى الجدولة والمتابعة", "onlyViewAccess": "إمكانية العرض فقط", "proctorDetails": "تفاصيل بروكتور", "questionNotAdded": "لم يتم إضافة السؤال بعد", "optionNotAdded": "لم يتم إضافة الخيار بعد", "studentDetailsIndependentAssessment": "تفاصيل الطالب", "updateNotAllowed": "لا يُسمح بالتحديث بمجرد إرسال التقييم إلى", "withdrawRequestConfirmation": "سيؤدي سحب هذا الطلب إلى إزالة إمكانية الوصول إلى العنصر للمؤلف المعين.", "areYouSureWantToWithdraw": "هل أنت متأكد أنك تريد <b>سحب الطلب؟</b>", "areYouSureWantToCompleteReview": "هل أنت متأكد أنك تريد <b>إكمال المراجعة؟</b>", "yesWithdraw": "نعم، طلب السحب", "areYouSureWantToSendIA": "هل أنت متأكد أنك تريد إرسال هذا التقييم إلى <b>{{author}}</b>؟", "courseSellectionRequired": "الرجاء اختيار الدورة قبل إنشاء التقييم", "regenerateReport": "إعادة إنشاء التقرير", "printAssessment": "طباعة التقييم", "completeAllStepsToPublish": "يرجى استكمال جميع الخطوات لنشر التقييم", "pleaseCompleteAllRequiredFieldsInExamConfiguration": "ير<PERSON>ى إكمال جميع الحقول المطلوبة في تكوين الامتحان.", "pleaseUploadStudentsInStudentDetails": "يرجى تحميل الطلاب في خطوة تفاصيل الطلاب.", "pleaseCompleteScheduledDetails": "يرجى إكمال تفاصيل الجدول الزمني.", "pleaseAssignProctorInProctorDetails": "يرجى تعيين المراقب في خطوة تفاصيل المراقب.", "subItemOptionText": "يرجى تقديم نص الخيار في القسم رقم {{sectionNo}} السؤال رقم {{itemNo}} ({{itemTypeCode}}) العنصر الفرعي ({{subItemNo}}).", "withoutSubItemOptionText": "يرجى تقديم نص الخيار في السؤال رقم {{itemNo}} ({{itemTypeCode}}) العنصر الفرعي ({{subItemNo}}).", "markAnswerKeyForMIT": "يرجى تحديد الإجابة الصحيحة في القسم رقم {{sectionNo}} السؤال رقم {{itemNo}} ({{itemTypeCode}}) العنصر الفرعي ({{subItemNo}}).", "noExamDetails": "لم يتم تقديم تفاصيل الامتحان بعد!", "noItemDetails": "لم يتم توفير تفاصيل العناصر بعد!", "timeInPast": "يرجى اختيار وقت في المستقبل. الوقت المحدد في الماضي.", "sumOfCustomMarks": "يجب أن يكون مجموع العلامات المخصصة مساويًا لـ 100٪.", "mustAddComment": "يجب إضافة تعليق إلى علامة التصحيح المطلوب.", "correctionRequired": "وضع علامة على أن التصحيح مطلوب", "reviewed": "وضع علامة كمراجعة", "completeReview": "مراجعة كاملة", "reviewCompleted": "تمت المراجعة!", "addComment": "أضف تعليق...", "addReply": "أض<PERSON> الرد...", "itemsNotReviewed": "بعض العناصر لم تتم مراجعتها بعد لإكمالها", "withdrawnItems": "تم سحب بعض العناصر", "assignItemsToWithdrawItemAuthor": "لم يتم إرسال العناصر المحددة إلى مؤلف العنصر.", "assignItemsToWithdrawReviewer": "لم يتم إرسال العناصر المحددة إلى المراجع.", "studentGroupsNotAssigned": "لم يتم تحديد مجموعات طلابية في هذا الاختبار! بدلاً من ذلك، تم تحميل الطلاب يدويًا عبر الاستيراد الجماعي كما هو موضح أدناه:", "noGroupsFound": "لم يتم العثور على مجموعات", "answerDescription": "وصف ,الإجابة", "andSetWeightage": "وتعيين الوزن", "setWeightage": "تعيين الوزن (%)", "selectItemsToWithdraw": "يرجى تحديد بعض العناصر التي تريد سحبها.", "noCourseName": "لا يوجد اسم الدورة", "previousSection": "القسم السابق", "nextSection": "القسم التالي", "cannotEditOldItems": "لا يمكن تعديل العنصر القديم", "liveExams": "الاختبارات المباشرة", "examEnded": "انتهى الامتحان", "pendingCorrection": "في انتظار تحديث التصحيحات!", "pendingCorrectionNote": "لا تزال بعض العناصر تحمل علامة <b>تتطلب التصحيح</b> ولم يتم تصحيحها بعد. سيتم وضع علامة <b>تم النشر</b> على جميع هذه العناصر بعد المتابعة.", "rescheduledExamNote": "لقد تم إعادة جدولة هذا التقييم ولم يتم إعادة نشره بعد للعرض.", "contactExamScheduler": "يرجى الاتصال بمقرر الامتحان لاستكمال عملية إعادة الجدولة.", "assessmentCancelled": "لقد تم إلغاء التقييم!", "assessmentCancelledNote": "لقد تم إلغاء هذا التقييم من قبل مقرر الامتحان.", "contactScheduler": "يرجى الاتصال بالمجدول المعني لمزيد من التفاصيل.", "imageLabel": "صورة", "addAnswer": "إضافة إجابات", "itemThemEmpty": "الموضوع يبدو فارغًا في السؤال رقم {{itemNo}} ({{itemTypeCode}}).", "withSectionItemThemEmpty": "الموضوع يبدو فارغًا في القسم رقم {{sectionNo}} / السؤال رقم {{itemNo}} ({{itemTypeCode}}).", "itemStemLeadInEmpty": "المقدمة تبدو فارغة في السؤال رقم {{itemNo}} ({{itemTypeCode}}).", "withSectionItemStemLeadInEmpty": "المقدمة تبدو فارغة في القسم رقم {{sectionNo}} / السؤال رقم {{itemNo}} ({{itemTypeCode}}).", "chooseRightAnswer": "- اختر الإجابة الصحيحة", "typeAnAnswer": "اكتب إجابة", "previewImage": "معاينة الصورة", "updateOption": "خيار التحديث", "oldItemNote": "تحت الاختيار، ({{oldItemCount}}) من العناصر هي عناصر قديمة (مختارة من بنك الأسئلة) ولن يتم إرسالها أو سحبها للمراجعة.", "oldItemIANote": "تحت التحديد، ({{oldItemCount}}) من العناصر هي عناصر قديمة (تم اختيارها من بنك الأسئلة) ولن يتم إرسالها إلى مؤلف العنصر.", "mapItemsNote": "سيتم تخطي العناصر المرفقة، أو القديمة، أو قيد المراجعة. سيتم تجاهل {{cannotMapItemCount}} عنصر فقط.", "switchCourseConfirmation": "سيؤدي التبديل إلى دورة جديدة إلى تحديث المواد والمواضيع الخاصة بالأسئلة الحالية إلى الدورة المحددة حديثًا.", "wouldYouLikeToUpdate": "<b>هل ترغب في تحديث الدورة</b>؟", "needToReviewItemsStatus": "<b>({{count}}) عناصر تحتاج إلى مراجعة</b>", "needToCorrectRequiredItemsStatus": "<b>({{count}}) عناصر تحتاج إلى تصحيح</b>", "inReviewItemsStatus": "<b>({{count}}) عناصر قيد المراجعة</b>", "sendBackAAConfirmationInfo": "لا يمكنك تعديل أي شيء بعد الإرسال.", "withSectionItemAlreadySentToReviewer": "تم إرسال هذه العنصر بالفعل إلى المؤلف أو المراجعين للقسم رقم {{sectionNo}} / السؤال رقم {{itemNo}} ({{itemTypeCode}}).", "withoutSectionItemAlreadySentToReviewer": "تم إرسال هذه العنصر بالفعل إلى المؤلف أو المراجعين للسؤال رقم {{itemNo}} ({{itemTypeCode}})، العنصر الفرعي {{subItemNo}}.", "publishConfirmation": "بعض المراجعين لم يكملوا مراجعاتهم. هل تريد نشر التقييم على أي حال؟", "itemAlreadySentToItemAuthorOrReviewers": "تم بالفعل إرسال بعض العناصر إلى المؤلف أو المراجعين. يرجى سحبها قبل المتابعة.", "itemAlreadySentToReviewers": "تم بالفعل إرسال بعض العناصر إلى المراجعين.", "confirmSaveAsNew": "تأكيد الحفظ كجديد", "confirmSaveAsNewNote": "<b>*ملاحظة:</b> تحرير هذا العنصر سيحفظه كعنصر جديد.", "confirmSaveAttached": "هل أنت متأكد أنك تريد تعديل العنصر المرفق؟", "testCentersBookedSuccessfully": "تم حجز مراكز الاختبار بنجاح.", "itemAuthorNote": "غير مسموح به حيث أن بعض العناصر موجودة مع {{ itemAuthor }}. تابع بعد أن يكملوا البطاقة أو سحب البطاقة من {{ itemAuthor }}.", "scheduleRequestApproved": "تمت الطلب المطلوبة.", "scheduleRequestRejected": "تم رفض الطلب المطلوبة.", "clickToEdit": "اضغط للتعديل", "clickToEditItem": "اضغط للتعديل", "rejectRequestConfirmation": "هل أنت متأكد أنك تريد <b>رفض</b> هذا الطلب؟", "rejectReason": "يرجى تقديم سبب الرفض.", "refreshInfo": "تحديث القائمة لعرض أحدث إمكانية مراكز الاختبار", "scheduleRequestedNotYetApproved": "طلب الجدولة لكنه لم يتم الموافقة عليه بعد", "addExamStartTime": "أضف وقت بدء الاختبار قبل إنشاء مركز الاختبار", "sentToReviewer": "إرسال إلى {{reviewersRoleName}}", "sendToReviewer": "إرسال إلى {{reviewersRoleName}}", "centralizedExamProctorNote": "للاختبارات المركزية، يجب تعيين المراقبين في صفحة تعيين المراقبين،", "goToAssignProctor": "الذهاب إلى تعيين المراقبين", "importViaDigiClass": "استيراد من ديجي كلاس", "importViaDigiAssess": "استيراد من دي جي أسس", "importViaSis": "استيراد من سي إس", "uploadSisStudents": "رفع طلاب سي إس", "importViaExcel": "استيراد من إك<PERSON>ل (طلاب إضافية)", "additionalStudents": "طلاب إضافية", "importViaExcelNote": "قم بتحميل القالب، وأدخل إدخالات الطلاب وقم بتحميل الملف المكتمل.", "confirmResetMapping": "هل أنت متأكد أنك تريد إعادة تعيين التعيين للعناصر المحددة؟", "mappingItems": "عناصر التعيين", "subjectAndTopic": "الموضوع/الموضوع", "courseLearningOutcomes": "نتائج تعلم الدورة (CLO)", "sameExamConfig": "نظرًا لأنه تم إنشاء اختبار بنمط مختلف لهذا النوع من المحاولات، فيرجى إنشاء الاختبار بنوع محاولة مختلف.", "dutyApproach": "نهج الواجب", "showAvailableOnly": "إظهار المتاح فقط", "proctorDutyOnly": "واجب المراقب فقط", "classDutyOnly": "واجب الصف فقط", "filterByStaff": "تصفية حسب توفر الموظفين", "bufferCount": "<PERSON><PERSON><PERSON> الطلاب الإضافية", "pleaseSelectAtLeastOneGroup": "يرجى اختيار على الأقل مجموعة واحدة", "allPlannedStudentsAllocated": "تم تخصيص جميع الطلاب المخططين", "videoProcessing": "يتم معالجة الفيديو، يرجى تحديث الصفحة لاحقًا.", "videoProcessingFailed": "فشل معالجة الفيديو، يرجى تحميله مرة أخرى.", "proctorAssignedCentralized": "المراقب مخصص مسبقًا لهذا الوقت.", "videoAttachment": "مرفق الفيديو", "audioAttachment": "مرفق صوتي", "pdfAttachment": "مرفق قوات الدفاع الشعبي", "matchTheAnswersWithQuestion": "مطابقة الإجابات مع السؤال", "importQuestionsFromExcel": "استيراد الأسئلة من إكسل", "processQuestions": "معالجة الأسئلة", "uploadExcelFile": "تحميل ملف إك<PERSON>ل", "noQuestionsExtracted": "لم يتم استخراج أي أسئلة من الملف المحمل", "processingFile": "يتم معالجة الملف...", "dragDropOrClick": "اسحب وأفلت الملف هنا أو انقر للتصفح", "dropFileHere": "أف<PERSON><PERSON> الملف هنا", "supportedFormats": "التنسيقات المدعومة", "extractedQuestions": "الأسئلة المستخرجة", "uploadMoreQuestions": "تحميل المزيد من الأسئلة", "importQuestions": "استيراد الأسئلة", "importFromExcel": "استيراد من إكسل", "errorReadingFile": "خطأ في قراءة الملف", "successAddedQuestions": "تم إضافة {{ count }} أسئلة جديدة بنجاح", "selectAMaximumOfPointsOnTheImageBelow": "اختر أقصى {{noOfPoints}} نقاط على الصورة أدناه", "answerAlltheQuestionsBelow": "أج<PERSON> عن جميع الأسئلة أدناه", "chooseAnyOneCorrectAnswersForEachQuestions": "اختر أي إجابة واحدة صحيحة لكل سؤال", "supportedItemTypes": "أنواع العناصر المدعومة: .س.م.ا .س.ا.ق", "invalidTemplate": "قالب غير صالح", "duplicateOptionsFound": "تم العثور على وإزالة {{ count }} خيار مكرر أثناء الاستيراد.", "youDontHavePermissionToCreateViewItems": "ليس لديك صلاحية لإنشاء / عرض العناصر"}, "courseInput": {"inputData": "بيانات الإدخال", "pleaseSelectCourse": "يرجى اختيار الدورة", "noSubjectOrTopicAvailable": "- لم يتم إضافة مواضيع أو مواد بعد -", "createNewSubject": "+ إنشاء موضوع جديد", "createNewTopic": "+ إنشاء موضوع جديد", "newSubject": "موضوع جديد", "newTopic": "موضوع جديد", "newContent": "م<PERSON><PERSON><PERSON><PERSON> جديد", "nameOfTheSubject": "اسم الموضوع", "writeDescription": "اكتب وصفًا", "addTopic": "+ إضافة موضوع", "addContent": "+ إضافة محتوى", "writtenContent": "المحتوى المكتوب", "noContent": "لا يوجد محتوى", "topicWithCount": "{{ count }} مواضيع", "noTopics": "لا توجد مواضيع", "courseSpec": "مواصفات الدورة", "addClo": "+ إضافة نتائج التعلم للدورة", "plannedCountShouldBeMore": "يجب أن يكون العدد المخطط أكبر أو يساوي عدد الطلاب المستوردين", "cloByPlo": "ناتج التعلم / ناتج البرنامج", "noteForSelectCurriculum": "لربط نواتج التعلم للسنة التأسيسية، قم باختيار جميع البرامج ضمن الأطر المختلفة التي تتبعها مؤسستك.", "noteForPloAndCloMappingTable": "لربط نواتج التعلم بنواتج البرنامج ، اختر نوع الربط الذي ترغب في استخدامه لربط النواتج.", "selectOption": "- اختر خيارًا -", "pleaseSelectProgramOrCurriculum": "الرجاء تحديد البرنامج أو المنهج للمتابعة", "selectProgramAndFramework": "<PERSON><PERSON><PERSON> البرا<PERSON>ج والإطار", "mapTypeAndOutcomeMapping": "نوع الخريطة ورسم خرائط النتائج", "courseInputs": "مدخلات الدورة", "courseManagement": "إدارة الدورة", "courseInformation": "معلومات الدورة", "hierarchy": "تَسَلسُل", "goToReports": "انتقل إلى التقارير", "chooseReport": "اختر تقريرًا", "reportsNotAvailable": "التقارير غير متوفرة للعام الدراسي المحدد!", "searchSubjectTopic": "البحث عن الموضوع والموضوع", "courseDashboard": "لوحة معلومات الدورة", "conductedInsideApplication": "تم إجراؤها داخل التطبيق", "conductedOutsideApplication": "أجريت خارج التطبيق", "errorReadingExcelFile": "خطأ في قراءة الملف الإكسل. يرجى التحقق من تنسيق الملف.", "mismatchedHeaderData": "تم اكتشاف حالات عدم تطابق في تحميل بيانات الموضوع. يرجى التحقق من العمود المطلوب.", "noValidTopicsFoundInExcelFile": "لم يتم العثور على مواضيع صالحة في الملف الإكسل.", "successfullyImportedTopics": "تم استيراد {{ count }} مواضيع بنجاح."}, "scheduleExamDetail": {"courseDetails": "تفاصيل الدورة", "dateAndTime": "التاريخ والوقت", "examTime": "وقت الامتحان", "groups": "المجموعات", "importedPlanned": "مستورد/مخطط", "proctors": "مراقبون", "assessmentStatus": "حالة التقييم", "scheduleStatus": "حالة الجدول الزمني", "actions": "الإجراءات", "schedule": "جدول", "view": "منظر", "publish": "نشر", "students": "طلاب", "scheduleOn": "جدولة على", "tribleDots": "المزيد من الخيارات", "uploadStudents": "تحميل الطلاب", "downloadTemplate": "تحميل القالب", "reschedule": "إعادة جدولة الامتحان", "cancel": "إلغاء الامتحان", "areYouSureToReschedule": "هل أنت متأكد من إعادة الجدولة؟", "areYouSureToCancel": "هل أنت متأكد من إلغاء الاشتراك؟", "rescheduleExam": "إعادة جدولة الامتحان", "cancelExam": "إلغاء الامتحان", "selectStudentPermitted": "حدد مسموح للطالب", "importedDataIsEmpty": "البيانات المستوردة فارغة", "searchCourse": "ابحث عن اسم الدورة", "testCenters": "مرا<PERSON><PERSON> الاختبار", "listOfTestCenters": "قائمة مراكز الاختبار", "searchTestCenter": "اسم مركز اختبار البحث", "testCenterDetails": "تفاصيل مركز الاختبار", "primaryProctor": "بروكتور الابتدائي", "secondaryProctor": "بروكتور ثانوي", "export": "يصدّر", "notAssignedYet": "لم يتم تعيينه بعد", "seats": "مق<PERSON><PERSON>د", "tc": "ح", "searchStudent": "بحث عن اسم الطالب", "searchProctor": "اسم بروكتور البحث", "editInvigilator": "مراقب التحرير", "goToAssignment": "اذه<PERSON> إلى المهمة", "academicNo": "الرقم الأكاديمي", "studentName": "اسم الطالب", "gender": "جن<PERSON>", "studentGroup": "مجموعة الطلاب", "seatNo": "رقم المقعد", "proctorId": "معرف بروكتور", "proctorName": "اسم بروكتور", "invigilatorDuty": "واجب المراقب", "role": "دور", "primary": "أساسي", "secondary": "ثانوي", "scheduledBy": "مجدولة بواسطة:", "testCentersCount": "المساهمين الأساسيين", "male": "ذكر", "female": "<PERSON>ن<PERSON>ى", "studentProctor": "الطالب/المراقب (نهاية)", "startTime": "وقت البدء", "actualStartTime": "وقت البدء الفعلي", "edit": "ي<PERSON><PERSON><PERSON>", "sessionTime": "وقت الجلسة", "centerlizedExam": "الامتحان المركزي", "independentExam": "الامتحان المستقل", "independentlyScheduled": "مجدولة بشكل مستقل", "centralizedSchedule": "الجدول الزمني المركزي", "assignedRequired": "مُعيَّن/مطلوب", "noStudentsImportedPlanned": "عدد الطلاب المستوردين / عدد الطلاب المخطط لهم", "noProctorsTestcenters": "عدد المراقبين المعينين / عدد مراكز الاختبار", "assessmentAuthors": "مؤلفو التقييم", "publishedAt": "تاريخ النشر"}}