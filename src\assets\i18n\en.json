{"alphabets": {"a": "A", "b": "B", "c": "C", "d": "D", "e": "E", "f": "F", "g": "G", "h": "H", "i": "I", "j": "J", "k": "K", "l": "L", "m": "M", "n": "N", "o": "O", "p": "P", "q": "Q", "r": "R", "s": "S", "t": "T", "u": "U", "v": "V", "w": "W", "x": "X", "y": "Y", "z": "Z"}, "common": {"failedToLoad": "Failed to load", "digivalItSolutionsUAE": "Digival IT Solutions - UAE", "copyright": "Copyright", "pvtLtdIndia": "(in technology partnership with Digival Solutions Pvt Ltd, India v{{appVersion}})", "superAdmin": "Super Admin", "add": "Add", "edit": "Edit", "cancel": "Cancel", "confirm": "confirm", "done": "done", "previous": "Previous", "prev": "Prev", "next": "Next", "save": "save", "publish": "Publish", "unPublish": "Unpublish", "approve": "Approve", "reject": "Reject", "instructions": "Instructions", "analysis": "analysis", "settings": "settings", "notSuitable": "Not suitable", "suitable": "Suitable", "correct": "correct", "unAnswered": "unAnswered", "close": "close", "closeUpperCase": "CLOSE", "wrong": "wrong", "share": "SHARE", "smallExport": "export", "capsExport": "EXPORT", "noResultsFound": "No results found", "showAll": "Show All", "expandAll": "Expand All", "collapseAll": "Collapse All", "another": "another", "assign": "Assign", "noDataFound": "No Data Found", "noStudentsFound": "No Students Found", "Dashboard": "Dashboard", "personalDetails": "Personal Details", "firstName": "First Name", "firstNameRequired": "First name required", "middleName": "Middle Name", "lastName": "Last Name", "lastNameRequired": "Last name required", "universityEmailID": "University Email ID", "personalEmailIDRequired": "Personal Email ID required", "mobileNumber": "Mobile Number", "phoneNumber": "Phone Number", "mobileNumberRequired": "Mobile Number required", "mobileValidation": "Should be 9 digits", "dob": "DOB", "dobRequired": "DOB required", "gender": "Gender", "male": "Male", "female": "Female", "genderRequired": "Gender required", "uploadDocuments": "Upload Documents", "residentIDNationalID": "Resident ID / National ID", "attach": "Attach", "delete": "Delete", "deleteWithCount": "Delete({{count}})", "view": "View", "grade": "Grade", "grade:": "Grade:", "employeeID": "Employee ID", "academicDetails": "Academic Details", "employeeIDAlreadyExist": "Employee ID Already Exists", "employeeIDIsRequired": "Employee ID is required", "designation": "Designation", "correctionRequired": "Correction required", "designationRequired": "Designation required", "residentNationalIdRequired": "Resident ID / National ID required", "residentNationalIdAlreadyExist": "Resident ID / National ID Already Exists", "invalidResidentNationalId": "Invalid Resident ID / National ID", "nationalIDResidentID": "National ID / Resident ID", "pleaseProvideFollowingProfileDetails": "Please provide the following Profile details", "submit": "Submit", "passportNumberShouldBeAlphanumeric": "Passport Number should be alphanumeric", "passportNumberAlreadyExist": "Passport Number Already Exists", "provideNewPassword": "Provide New Password", "passwordIsRequired": "Password is required", "reTypePasswordIsRequired": "Retype Password", "enterNewPassword": "Enter new Password", "attach&Upload": "Attach & Upload", "uploading": "Uploading...", "resetPassword": "Reset Password", "resetPasswordLinkSent": "Reset password link has been sent to", "resetPasswordLinkIsExpired": "Reset password link has been expired", "ok": "Ok", "invalidEmailId": "Invalid Email ID", "emailRequired": "Email is required", "assessment": "Assessment", "assessmentStatus": "assessment status", "selectedCourse": "Selected Course:", "selectProgram": "Select program", "selectCurriculum": "select curriculum", "selectUser": "Select User", "userName": "User Name", "userId": "User ID", "ipAddress": "IP Address", "framework": "Framework", "domainTheme": "domainTheme", "selectYear": "Select Year", "clo": "CLO", "plo": "PLO", "plo##": "PLO ##", "map": "Map", "addPlo": "Add PLO", "noRecordsFound": "No records found", "medicalProgram": "Medical program", "expand": "EXPAND", "customize": "Customize", "no": "NO", "yes": "YES", "archive": "Archive", "year": "Year", "addLevel": "Add level", "required": "required", "description": "Description", "attemptType": "Attempt Type", "resultStatusFilter": "result status filter", "programYearLevelCourse": "Program/YEAR/LEVEL/COURSE", "publishAndShare": "PUBLISH & SHARE", "readyToPublish?": "ready to publish?", "readyToPublish": "Ready to publish", "itemRevision": "item revision", "itemReRevision": "item re-revision", "manualEvaluation": "Manual Evaluation", "youDontHaveAccessToEvaluation": "You don’t have access to evaluation.Please contact your course administrator", "youDontHaveAccessToSchedule": "You don’t have access to schedule.", "currentResults": "Current Results", "history": "History", "reports": "Reports", "proceed": "Proceed", "reset": "reset", "roles": "Roles", "activate": "Activate", "modules": "<PERSON><PERSON><PERSON>", "pages": "Pages", "tabsSubTabs": "Tabs/SubTabs", "createRole": "Create Role", "updateRole": "Update Role", "roleName": "Role Name", "successfully": "successfully", "deactivate": "deactivate", "list": "List", "email": "Email", "shouldBe9Digits": "Should be {{number}} digits", "firstDigitShouldContainNumberBetween5To9": "The first digit should contain number between {{start}} to {{end}}", "mobileNumberAlreadyExist": "Mobile Number Already Exists", "exam": "exam", "date": "date", "time": "time", "marksDetails": "marks details", "marksScored": "Marks Scored", "percentage": "Percentage", "itemTypeWiseMarks": "Item type-wise Marks", "itemType": "Item type", "totalMarks": "Total marks", "totalTime": "Total time", "program": "Program", "programRequired": "Program required", "addressDetails": "Address Details", "buildingNumberStreetName": "Building Number, Street Name", "buildingNumberStreetNameRequired": "Building Number, Street Name required", "regionName": "Region Name", "regionRequired": "Region required", "cityName": "City Name", "cityRequired": "City required", "districtName": "District Name", "districtRequired": "District required", "zipCode": "Zip Code", "zipCodeIsRequired": "Zip code required", "shouldBeValid5Digits": "Should be valid {{number}} digits", "unitCode": "Unit Code", "unitCodeRequired": "Unit code required", "passwordRequired": "Password required", "reTypePasswordRequired": "Retype Password required", "allAnnouncements": "All Announcements", "latest": "Latest", "marks": "Marks", "current": "Current:", "noResultsPublished": "No results published", "course": "course", "day": "day", "sessionTime": "session time", "selectedSessionTime": "selected session time", "informationIsCorrect/Wrong": "Please tell us if the information we have about you is correct or wrong", "tapCorrect/Wrong": "Tap correct or wrong", "academicNumber": "Academic Number", "academicNo": "Academic no.", "enrollmentYear": "Enrollment Year", "studentId": "Student ID", "residentNationalIdIsRequired": "Resident ID / National ID is Required", "correctionRequiredForIdentifiedData": "Correction required for identified data", "enrollmentYearRequired": "Enrollment year required", "enter4DigitValidYear": "Enter {{number}} digit valid year", "academicNumberShouldBe6-9Digits": "Academic Number Should be {{start}}-{{end}} digits", "academicNumberAlreadyExist": "Academic Number Already Exists", "academicNumberRequired": "Academic Number required", "shouldBeLessThanCurrentYear": "Should be less than current year", "noAssessmentsFound": "No Assessments found", "flag": "flag", "noItem": "No items found", "search": "Search", "upload": "Upload", "update": "Update", "to": "to", "from": "from", "send": "SEND", "sNo": "S.No", "academicYear": "Academic year", "pending": "Pending", "evaluated": "Evaluated", "individual": "individual", "theme": "THEME", "all": "All", "unSelectAll": "Unselect all", "status": "Status", "loading": "Fetching data...", "selectCompleteAssessments": "Select complete Assessments or pick Items grouped by topics from Past Assessments", "none": "None", "total": "total", "updatedSuccessfully": "Updated successfully", "name": "Name", "copyTo": "COPY TO", "role": "role", "download": "download", "error": "error", "type": "type", "items": "items", "student": "Student", "typeReason": "Type the reason*", "browse": "Browse", "create": "Create", "programs": "Programs", "newSession": "New Session", "display": "Display", "4Digits": "Should be 4 digits", "subject": "subject", "manage": "manage", "digiAssess": "Digi<PERSON><PERSON><PERSON>", "digiClass": "DigiClass", "actions": "Actions", "archivedSuccessfully": "Archived successfully", "import": "import", "export": "export", "wrongTimeFormat": "Wrong time format", "address": "Address", "exportData": "Export Data", "invigilatorsUnassigned": "Invigilators unassigned", "examManagement": "Exam management", "testCenter360": "Test center 360", "assessmentType": "Assessment Type", "examType": "Exam type", "assessmentAuthor": "Assessment Author", "testCenters": "Test Centers", "noOfStudents": "No. of Students", "primaryInvigilator": "Primary Invigilator", "invigilatorWithIndex": "Invigilator {{index}}", "invigilator2": "Invigilator 2", "courseModule": "Course / Module", "plannedStudentsMale": "Planned No. of Male Students ", "plannedStudentsFemale": "Planned No. of Female Students ", "plannedMale": "Planned Male", "plannedFemale": "Planned Female", "schedulePublishedOn": "Schedule Published On", "studentsImportedMale": "Male Students imported", "studentsImportedFemale": "Female Students imported", "studentsImportedMaleFemale": "Male and female students imported", "importedMale": "Male imported", "importedFemale": "Female imported", "importStudents": "Imported students \n M/F", "assessmentPublishedOn": "Assessment published on", "tableView": "table view", "itemView": "item view", "mappings": "mappings", "clos": "CLO", "studPerf": "stud. perf", "timesUsed": "times used", "lastUsed": "last used", "enrollYear": "Enrollment Year", "nationalIdIqamaId": "Resident ID / National ID", "cardView": "card view", "formatAcademicYear": "{{startYear}}-{{endYear}}", "sessionTiming": "{{startHour}}:{{startMin}} {{startFormat}} to {{endHour}}:{{endMin}} {{endFormat}}", "am": "AM", "pm": "PM", "studentListPreview": "Student list preview", "invigilatorListPreview": "Invigilator List Preview", "staffName": "Staffs Name", "formatTime": "{{hour}}:{{min}}", "noInternetConnection": "Internet is unavailable. Please check your connection", "key": "KEY", "revision": "revision", "finalized": "Finalized", "scheduler": "Scheduler", "createTimetable": "Create TimeTable", "testCenterCapacity": "Test center capacity", "allocated": "Allocated", "somethingWentWrong": "Something went wrong. Please try again later", "errorConnectingServer": "Error connecting server!!! Please try again later", "payloadTooLarge": "The file you're trying to upload is too large. Please reduce the file size to 30 MB and try again.", "timeOutError": "Time-out has occurred", "remove": "remove", "archiveMessage": "You are about to archive this", "question": "Question", "a": "A", "a+": "A+", "b": "B", "c": "C", "d": "D", "e": "E", "f": "F", "g": "G", "q": "Q", "m": "M", "mb": "Mb", "ua*": "UA*", "mg": "MG", "itemAuthor": "<PERSON><PERSON>", "reviewer": "Reviewer", "reviewer1": "Reviewer1", "subjectExpertReviewer": "Subject Expert Reviewer", "medicalEducationist": "Medical educationist", "createItem": "Create <PERSON><PERSON>", "groupCount": "Group Count", "finalize": "Finalize", "notStarted": "Not Started", "published": "Published", "onGoing": "On Going", "readyForFinalization": "Ready for finalization", "reviewCompleted": "Review completed", "approved": "Approved", "selectCourse": "Select Course", "original": "Original", "autoEvaluated": "Auto Evaluated", "final": "Final", "studentResponses": "Student Responses", "version": "Version", "stronglyDisagree": "strongly disagree", "disagree": "Disagree", "neutralDisagree": "Neutral disagree", "neutral": "Neutral", "agree": "Agree", "stronglyAgree": "Strongly agree", "collegeName": "IBN SINA NATIONAL COLLEGE", "collegePlace": "Al - Mahjar, Jeddah", "low": "Low", "medium": "Medium", "high": "High", "fetchingCourses": "fetching courses...", "testCenter": "Test Center", "select": "Select", "answerKey": "Answer Key", "taxonomy": "Taxonomy", "shareReports": "Share Reports", "publishSharesSelectedCourses": "Publish & share reports - selected courses", "dashboard": "Dashboard", "examSchedule": "Exam Schedule", "examResults": "Exam Results", "facultyManagement": "Faculty Management", "forgotPassword": "Forgot Password?", "signUp": "Sign up", "pageNotFound": "Page Not Found", "studentManagement": "Students Management", "mapping": "Mapping", "reportAnalytics": "Reports & Analytics", "survey": "Survey", "facultyVerifyProfileDetails": "Faculty Verify Profile Details", "facultyVerifyProfile": "Faculty Verify Profile", "studentVerifyProfile": "Student Verify Profile", "accountVerify": "Account <PERSON><PERSON><PERSON>", "uploadPermittedStudents": "Upload Permitted Students", "uploadMorePermittedStudents": "Upload more than Permitted students", "assignRolesPermissionStaffsAssigned": "Assign Roles & Permission - Staffs - Assigned", "assessmentSettingsDashBoard": "Assessment (settings & DashBoard)", "examManagementTestCenterCourseGroup": "Exam Management (Test Center & Course Group)", "rolesPermission": "Roles & Permission", "examManagementOnGoingAndPrevious": "Exam Management (OnGoing And Previous)", "studentPortal": "Student Portal", "pastExam": "Past Exam", "login": "<PERSON><PERSON>", "permissionDenied": "Permission denied!", "domain": "Domain", "mins": "<PERSON>s", "proctor": "PROCTOR", "primary": "PRIMARY INVIGILATOR", "secondary": "SECONDARY", "students": "Students", "examCoordinator": "EXAM_COORDINATOR", "impact": "IMPACT", "alignment": "ALIGNMENT", "textualResponse": "TR*", "assistantInvigilator": "AssistantInvigilator", "selectFilter": "Select filter", "outcome": "Outcome", "otherStaffs": "Other Staffs", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "les": "LES", "las": "LAS", "ls": "LS", "es": "ES", "rs": "RS", "oe": "OE", "month": "month", "week": "week", "hour": "hour", "minute": "minute", "second": "second", "years": "years", "months": "months", "weeks": "weeks", "days": "days", "hours": "hours", "minutes": "minutes", "seconds": "seconds", "h": "h", "s": "s", "just_now": "Just Now", "ago": "ago", "o": "o", "r": "R", "p": "P", "+p": "+P", "-p": "-P", "allPrograms": "All Programs", "allYears": "All Years", "first_class": "First Class", "second_class": "Second Class", "saveAsDraft": "Save As Draft", "extraMarks": "Extra Marks", "highestMark": "Highest mark of this assessment {{maximumMarks}} You can give maximum extra marks is {{maximumExtraMark}} for all students", "maximumMarks": "Maximum mark Achieved, <PERSON><PERSON> add this above extra mark for this student", "studentList": "Student List", "maxExtraMarks": "Max Extra Marks", "originalMarks": "Original Marks", "revisedMarks": "Revised Marks", "apply": "Apply", "code": "Code", "selectExamDate": "Select Exam Date", "expandDatePicker": "Expand Date Picker", "scheduled": "Scheduled", "notScheduled": "Not Scheduled", "selectedDate": "Selected date", "overallReports": "Overall reports", "courseCode": "Course Code", "category": "Category", "selectedAcademicNO": "Selected Academic NO", "chooseDate": "<PERSON>ose <PERSON>", "present": "Present", "studentType": "Student Type", "nextItem": "Next Item", "topic": "Topic", "startTime": "Start Time", "endTime": "End Time", "targetBenchMark": "Target Benchmark", "batchAttainment": "Batch Attainment", "studentImprovisationReport": "Student Improvement Report", "staffId": "Staff ID", "studentInMultiSession": "Student in multiple sessions", "selectAssessment": "Select Assessment", "selectAttemptType": "select attempt type", "defaultAssessmentValues": "Default Assessment Values", "absent": "Absent", "supported": "Supported", "denied": "Denied", "authenticationFailed": "Authentication failed", "requested": "Requested", "ploCloDescription": "{{translatedMappingMode}} {{description}}", "ploCloUserManagementNumber": "{{translatedMappingMode}} {{number}}", "taxonomyAddLevelPlaceHolder": "Description", "addLevelPlaceHolder": "Description", "allItems": "All Items", "topics": "topics", "addHotspot": "Add hotspot", "editHotspot": "Edit hotspot", "common": "Common", "mixed": "Mixed", "uploadStudents": "Upload Students", "bulkImports": "Bulk Imports", "individualStudent": "Individual Student", "importStudentGrouping": "Import from student grouping", "getDetails": "Get Details", "show": "Show", "diffIndex": "Diff. Index", "discrIndex": "Discr. Index", "biserial": "Biserial", "inference": "Inference", "acceptabilityCriteria": "Acceptability Criteria", "acceptabilityCriteriaFor": "Acceptability Criteria For", "hideResponse": "Hide Response", "viewResponse": "View Response", "searchByCourse": "Search by course", "downloadTemplate": "download template", "in": "IN", "out": "OUT", "examCategories": "exam categories", "conductedOn": "conducted on", "externalExam": "external exam", "externalExamDetails": "external exam details", "examCategory": "exam category", "selectCategory": "select category", "noColumnsSelected": "No columns selected", "changeSettings": "Change settings", "viewAll": "View all", "mapped": "mapped", "charLimit": "Character Limit", "academic_No": "Academic No", "createAllRequest": "Create All Request", "gradeBook": "Grade book", "author": "author", "draft": "draft", "continue": "continue", "back": "back", "deletedSuccessfully": "Deleted Successfully", "saveAndNext": "Save & Next", "benchmark": "Benchmark", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "goBack": "Go Back", "item": "item", "taggedItem": "tagged item", "taggedItems": "tagged items", "viewMore": "View more", "addedSuccessfully": "Added successfully", "rebuildReport": "Rebuild Report", "onlyNumbersAllowed": "Only numbers allowed", "showPdf": "Show PDF", "hidePdf": "Hide PDF", "viewAttachedPdf": "View Attached Pdf", "reRunReportNote": "Please Re-run the report due to changes made during re-evaluation or re-revision.", "label": "Average of All the {{label}}'s", "component": "Component", "allMaleStudents": "all male students", "allFemaleStudents": "all female students", "excludeAll": "Exclude All", "includeAll": "Include All", "exclude": "Exclude", "include": "Include", "applyAndSave": "Apply & Save", "filter": "Filter", "gradePlay": "GRADEPLAY", "youDontHavePermission": "You don't have permission to access this page", "note": "Note", "optional": "(optional)", "moved": "Moved", "questions": "Questions", "duplicate": "Duplicate", "unassigned": "Unassigned", "stem": "<PERSON><PERSON>", "examAndAssessmentType": "Exam & Assessment type", "dateAndSession": "Date & Session time", "selectExamDetail": "Selected exam details", "bySelf": "By self", "other": "Other", "confirmSelection": "Confirm Selection", "selectStudents": "Selecting Students", "assignEvaluator": "Assigning Eva<PERSON><PERSON>", "courseAuthor": "Course Author", "otherGeneralUser": "Other general user", "chooseUser": "Choose user", "evaluatedMark": "Evaluated Mark", "dateAndTime": "Date & Time", "allFieldsMandatory": "All fields are mandatory", "examPreference": "Exam Preference", "modeOfConduction": "Mode of conduction", "systemRequirement": "System Requirement", "examAuthentication": "Exam authentication", "browser": "Browser", "headSetWithMic": "Headset With Mic", "headSetWithOutMic": "Headset Without Mic", "camera": "Camera", "fullScreenMode": "Full screen mode", "generalUser": "General User", "examStartTime": "Exam start time", "enterName": "Enter a name", "enterDigits": "Enter digits", "enterNo": "Enter No", "enterInstruction": "Enter the instructions", "reAllocateStudents": "Re-allocate Students", "or": "OR", "addNewType": "Add New {{ type }} Type", "nameOfType": "Name of the {{ type }}", "enterCode": "Enter code", "startsBy": "Starts by", "allocateStudents": "Allocate Students", "digiBrowser": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "normalBrowser": "Normal Browser", "startExam": "Start Exam", "goToExam": "Go to Exam", "refresh": "Refresh", "live": "LIVE", "confirmDelete": "Confirm Delete", "yesDelete": "Yes, Delete", "noKeepIt": "No, Keep it", "studentAlreadyAssigned": "Student already assigned", "examMode": "Exam <PERSON>", "windows": "Windows", "mac": "<PERSON>", "iPad": "IPad", "changeItemType": "Change Item Type", "areYouSureYouWantToChangeItemType": "Are you sure you want to change the item type?", "pleaseNote": "Please Note", "notProvided": "Not provided", "upcoming": "Upcoming", "clickToUpload": "Click here to upload students", "searchByAcademicNo": "Search by Academic number...", "noGoBack": "No, Go back!", "table": "Table", "unSupportedAttachmentType": "Unsupported Attachment Type", "unSupportedAudioType": "Unsupported audio type, only wav format is supported", "unSupportedVideoType": "Unsupported video type, only mp4 and webm formats are supported", "notClosed": "Not closed", "notConducted": "Not conducted", "testCenterOwnedDevice": "Test Center Owned Device", "clickOnceCompleted": "Click here once completed", "virtualTestCenter": "Virtual Test Center", "requestedItems": "requested items", "withdrawRequest": "Withdraw Request", "markAsCorrected": "<PERSON> as Corrected", "changeAuthor": "Change author", "genericConfig": "Generic Configuration", "default": "<PERSON><PERSON><PERSON>", "commentSection": "Comment Section", "goToDashboard": "Go To Dashboard", "uploadNewItems": "Upload New Items", "uploadNewItem": "Upload New Item", "alertMessage": "<PERSON><PERSON>", "requestStatus": "Request Status", "replying": "Replying...", "withdraw": "Withdraw", "enterText": "Enter text here...", "today": "Today", "start": "Start", "allLogs": "All Logs", "viewLog": "View Log", "logs": "Logs", "safeExamBrowser": "Safe <PERSON><PERSON>", "tcod": "TCOD", "assignee": "Assignee", "answers": "Answers", "studentMarks": "Student Marks", "viewCourses": "Click to view courses", "viewExams": "View exams", "availableSlots": "Available slots", "availableSlotsFor": "Available slots for", "scheduledExams": "Scheduled Exams", "fileSizeExceeded": "File size should be ≤ {{size}}", "importExcel": "Import Excel"}, "assessment": {"dashboard": {"dashboardTitle": "Dashboard Title", "itemManagement": "Item Management", "attempt": "Attempt", "itemsAdded": "ITEMS ADDED", "newItems": "New Items", "oldItems": "Old Items", "createdBy": "Created by", "requestedBy": "Requested by", "rejectedBy": "Rejected by", "release": "Release", "nameOf": "Name of", "times": "Times", "used": "USED", "last": "last", "timesUsed": "No. of times used", "noOfItems": "No. of Items", "stem": "stem", "stems": "stems", "selectUser": "Select User", "selectItemAuthor": "Select Item author", "selectReviewer": "Select Reviewer", "oldItemQuestion": "Old Items that match", "pastAssessmentTopic": "Pick items from Past Assessments under selected Topic", "createAssessments": "Create Assessments", "itemStatus": "<PERSON><PERSON> and their Status", "assessmentsStatus": "Assessments and their Status", "dueDate": "due date", "createAssessment": "Create Assessment", "selectItems": "Select Items", "monitorItemStatus": "Monitor Item Status", "reviewPublish": "Review & publish", "createReview": "Create / Review", "inReview": "In-Review", "sentToReviewer": "Sent to Reviewer", "prepareForApproval": "Prepare for approval", "reviseForApproval": "Revise for Approval", "inApproval": "In-Approval", "requestFulfilled": "Request fulfilled", "review": "Review", "reviewed": "Reviewed", "inCorrection": "In-Correction", "approve": "Approve", "searchItemAuthor": "Search item author", "reviewsRequired": "Reviews / Required", "reasonForDiscard": "Reason for Discard", "confirmationRequired": "Confirmation Required", "areYouSureWantToSendThisItem": "Are you sure want to send this item?", "onceSendCannotEditDuringReview": "Once sent, you cannot edit it during the review stage.", "clickYesToProceed": "Click 'Yes' to Proceed", "clickViewEquation": "Click to view equation", "modificationRestricted": "Modification restricted", "createItemsBySelf": "Create Items By Self", "areYouGoingToCreateItemsByYourself": "Are you going to create Items by yourself ?"}, "questionBank": {"questionBank": "Question Bank", "itemsHistory": "Items History", "itemsHistoryNotAvaliable": "Item history not available", "allAssessment": "All Assessments", "allReviewedItemBatches": "All Reviewed Item Batches", "pastExam": "pastExam", "term": "term", "curriculum": "curriculum", "level": "level", "rotationGroup": "rotationGroup", "rotationalGroup": "rotational group", "examFilters": "<PERSON><PERSON>", "academicYear": "academic year", "courseFilters": "course filters", "syllabus": "syllabus", "created": "Created", "on": "ON", "reviewed": "Reviewed", "dateSessionFilter": "Date & session filter", "session": "session", "cancelled": "Cancelled", "importTopicItems": "Import Topic Items", "noOfQuestions": "No. of Questions"}, "createItem": {"selectAndRequestItems": "Select & Request Items", "requestItems": "request (new & review) items", "createNewAssessment": "Create New Assessment", "itemsGrouped": "Request New or Select Old from Items Grouped by topics", "extractFullAssessment": "Extract full Assessment", "allSelectedItems": "all selected items", "selectedRequired": "Selected / Required", "old": "Old", "new": "New", "reason": "Reason", "reAttach": "RE-ATTACH", "move": "move", "moveTo": "Move to", "resetSection": "reset section", "section": "Section", "requestCorrection": "request correction", "notAllowedToEdit": "Not allowed to edit since item requested for correction, you can edit once item correction done by item author", "replace": "replace", "taxonomy": "Taxonomy", "viewAllItems": "View all items", "rejected": "Rejected", "respond": "Respond", "createNew": "Create New", "addTwoItems": "Add at least two items to existing mixed item type first", "requestNew": "Request New +", "attachNew": "Attach New", "invalidRequest": "Invalid Request", "requestedCorrection": "Requested Correction", "noItems": "No Items", "addAnItem": "Add an item into the topic by clicking \"+ Create New\" Button above", "addNewOldItem": "Add new/old item into the topic by clicking respective \" + \" buttons above", "assessmentInformation": "Assessment Information", "aboutToArchive": "You are about to archive {{name}} assessment", "archive": "Archive", "noOfItems": "No. of items", "noOfTask": "No. of Tasks", "archiveFile": "Archive file", "finalizeAssessment": "Finalize Assessment", "groupedByItemType": "Grouped by item type", "groupedByNew": "Grouped by new", "groupedByOld": "Grouped by old", "groupedByStatus": "Grouped by status", "completeAssessmentItems": "Complete Assessment Items", "totalReceived": "Total Received", "completeAssessment": "Complete Assessment", "examInstructions": "Exam instructions", "examDetails": "<PERSON><PERSON>", "examPeriod": "Exam Period", "noOfSection": "No. of Sections", "totalItems": "Total items", "instructions": "Answering sequence instructions", "sectionSequence": "Section sequence", "restricted": "Restricted", "fixedOrder": "Answer sections in fixed order", "needToSubmit": "You need to submit an answer to the current question before going to the next one.", "needToSubmitAnswers": "You need to submit answers within the given time, reach time skip to next question", "unrestricted": "Unrestricted", "attendSection": "Answer sections in any order within the exam period.", "itemsBooked": "Items can be bookmarked and revisited.", "submitAnswers": "You need to submit answers within the given exam time", "itemSequence": "Item sequence", "attendItemsFixedOrder": "Answer items in the given order.", "attendItemsAnyOrder": "Answer items in any order", "duringExams": "Note: During Exams", "internetNotAvailable": "Internet will not be available.", "noSoftwareAccess": "All other software will not be accessible.", "readyForApproval": "Ready for Approval", "item": "item", "publishAssessment": "Publish assessment?", "unPublishAssessment": "Unpublish assessment?", "paperBasedQuestionAlert": "If a question requires a written/paper-based response, the shuffle feature will not work for those particular questions, meaning those questions will not be shuffled.", "unPublishAssessmentContent": "Are you sure want to unpublish assessment for course", "sureWantToPublish": "Are you sure want to publish  assessment for course {{courseName}}?", "itemsRequested": "Items Requested", "attachment": "Attachment", "fileAttachment": "This item has attachment", "drawTheHotspot": "Draw the Hotspot", "singleAnswerPoints": "Single answer points", "uploadImage": "Upload Image", "markTheCorrectAnswer": "MARK THE CORRECT ANSWER", "clickToMark": "Click to <PERSON>", "clearPoints": "CLEAR POINTS", "viewPoints": "View Marked Points", "singleHotspot": "Single Hotspot", "chooseItemsCount": "<PERSON><PERSON> Items Count", "mixedItemType": "Mixed Item Type", "counts": "Counts", "allowIAtoAlter": "Allow Item author to alter the item types 'Counts' within the limit of maximum counts", "IACanChange": " *You can change the item type’s counts within the limit of maximum count", "IACannotChange": "*You cannot change the item type’s counts within the limit of maximum count", "itemTypeChanged": "Item Type Changed Successfully", "itemsLinked": "Items linked Successfully", "unlinked": "Unlinked Successfully", "linkItem": "Link <PERSON>", "allowItemLinking": "Allow Item Linking", "itemShow": "Item show to students depends on link", "clickToLink": "Click to <PERSON>", "addItems": "Add Items", "backToAssessment": "Back to assessment author page", "viewChangeScheduling": "View / Change Scheduling", "generatedByHEBA": "Generated by HEBA.ai", "overWritten": "(<PERSON><PERSON><PERSON><PERSON>)", "clearItemTitle": "Confirm Clear Item", "clearItemContent": "Are you sure you want to clear this item? This action cannot be undone.", "showHotspotToStudents": "Show hotspots to students", "disableWeightage": "Disable weightage to enable this method", "provideFullMark": "Provide full mark if", "any": "any", "answerCorrect": "answer(s) are correct", "addWeightage": "Add Weightage", "addWeightageToAnswerKey": "Add Weightage to answer key", "differentMarks": "* Each hotspots can hold different marks", "chooseAnswerKey": "Choose answer key", "weightage": "Weightage %", "targetPassMark": "target pass mark (%)", "targetBenchMark": "target benchmark (%)", "difficultyIndexDiscriminationIndex": "For Difficulty index and discrimination index calculations", "changeItemType": "Changing item type ?", "changingItemTypeStage": "Changing the item type at this stage may remove the previously entered data permanently", "areYouSureYouWantToChange": "Are you sure, you want to change?", "unlinkToChange": "Unlink question to change item type", "selectAnswerKey": "Select Answer key", "noOfCorrectAnswerKey": "No.of correct answers key", "addItemToExisting": "Add item to existing mixed item type first", "maxWeightage": "Max Weightage should be 100", "totalCorrectAnswers": "Total correct answers should not be equal or more than total option", "imageUpload": "Upload image and select a particular area as the answer.", "clearAll": "Clear All", "hotspotsAdded": "Hotspots Added", "hotspotQuestion": "Hotspot Question", "attachedItem": "Attached Item"}, "autoGenerationByHeba": {"chooseTheRandomness": "Choose the randomness for the item to generate*", "chooseTheRandomnessToolTip": "In essence, higher randomness in question genertion can lead to more exploratory and potentially out of the box questions, while lower randomness generate more predictable and focussed questions grounded in provided context"}, "selfItemCreate": {"createNewItem": "CREATE NEW ITEMS", "createNewItemUnderMyTopics": "Create new items - under my topics", "finalizeAll": "FINALIZE ALL", "sendForApproval": "SEND FOR APPROVAL", "sendForReview": "SEND FOR REVIEW", "createNew": "Create New", "turnOffCreateItemsBySelfTurnOffWarning": "If you <b>turn off \"Create by self,\"</b> the existing items will <b>be moved to the Question Bank, and removed from here.</b>", "turnOffCreateItemsBySelfTurnOffInfo": "Would you like to proceed turning off “Create by self” option?", "turnOffCreateItemsBySelfTurnOnWarning": "If you <b>turn on \"Create by self,\"</b> the existing items will <b>be moved to the Question Bank, and removed from here.</b>", "turnOffCreateItemsBySelfTurnOnInfo": "Would you like to proceed turning on “Create by self” option?"}, "itemCreate": {"question": "Question", "notifyVia": "Notify Item authors Via", "subjectName": "Subject {{subjectName}}", "topicsName": "Topics {{topicName}}", "assignedItemAuthor": "Assigned Item Author", "newItems": "New Items", "reviewOld": "Review Old", "totalRequired": "Total Required", "noTopicsHasMultipleAuthors": "No topic has multiple authors!", "item": "<PERSON><PERSON>", "selection": "Selection", "single": "SINGLE", "addNewChoice": "Add New Choice", "qNo": "Q. NO.", "selectionSingle": "Selection : S<PERSON><PERSON>", "choices": "Choices", "addChoiceAnswerKey": "Add a choice to set an answer key", "reply": "REPLY", "clickOnQuestion": "Click on a Question Textbox to select or view the respective", "answerKey": "Answer Key", "feedback": "<PERSON><PERSON><PERSON>", "feedbackForCorrectAnswers": "Feedback for correct Answers", "enterFeedbackCorrectAnswers": "Enter feedback for correct Answers (optional)", "feedbackForWrongAnswers": "Feedback for wrong Answers", "enterFeedbackWrongAnswers": "Enter feedback for wrong Answers (optional)", "true": "True", "false": "False", "questionTheme": "Question theme", "leadIn": "Lead In", "addPrompt": " + Add Prompt", "addQuestion": "+ Add Question", "launch": "launch", "itemHistoryNotAvailable": "Item history not available", "itemsDetails": "Items Details", "initiatedBy": "Initiated By", "assessmentAuthor": "Assessment Author", "itemAuthor": "<PERSON><PERSON>", "subjectExpertReviewer": "Subject expert reviewer", "medicalEducationist": "Medical Educationist", "qualityAssuranceReviewer": "Quality Assurance Reviewer", "createdOn": "Created on", "lastOpened": "Last Opened", "lastModified": "Last Modified", "assessmentDetails": "Assessment Details", "courseName": "Course name", "topicName": "Topic name", "itemMapping": "Item mapping", "addAQuestion": "Add a question", "courseLevelOutcomes": "course level outcomes", "noClosMapped": "No CLOs mapped", "noTaxonomyMapped": "No taxonomy mapped", "addTaxonomyInSettings": "Add Taxonomy in Settings", "chooseAnyOne": "Choose any One", "chooseAnyAnswers": "Choose any {{noOfAnswerKeys}} answers", "chooseAnswer": "Choose answer", "chooseTheCorrectAnswer": "Choose the correct answer", "createNew": "Create New Item", "prompt": "Prompt", "enterChoice": "Enter choice {{index}}", "pickNewItemFromQuestionBankToAttach": "Pick a new item from Question bank to attach", "newItemsThatMatch": "New Items that match", "noViewAccess": "No view access for you", "subject": "Subject", "enterReply": "Enter reply here...", "provideShortAnswer": "PROVIDE A SHORT ANSWER BELOW", "provideAnswerPrompt": "PROVIDE SHORT ANSWERS FOR EACH OF THE PROMPTS BELOW", "typeAnswer": "Type your answer here.", "typeAnswerHere": "Type your answer here. Answer should not exceed <1000> characters.", "answerShouldNotExceedLimit": "Answer should not exceed <1000> characters.", "selectAnswer": "Select answer", "matchTheFollowing": "MATCH THE FOLLOWING WITH CORRECT CHOICES", "maxPromptsReached": "Maximum number of prompts reached", "hotspotDeletePopupTitle": "Deleting hotspots?", "hotspotDeletePopupMessage": "All marker hotspot will lost and this action cannot be undone are you sure want to delete hotspots?", "closeHotspotPopupTitle": "Unsaved Changes Found", "closeHotspotPopupMessage": "This hotspot question contain unsaved changes, kindly consider saving.", "clearAllHotspotTitle": "Clearing all hotspots?", "clearAllHotspotMessage": "All marked hotspot will be lost and this action cannot be undone are you sure want to clear hotspots?", "showHotspotToStudentsTitle": "Showing hotspots to students?", "showHotspotToStudentsContent": "By turing on this method can leads to loss the weightage settings if configured", "showHotspotToStudentsInfo": "If enabled, check the correct answers and set weightage", "canApplyWeightageTitle": "Turning off the toggle?", "canApplyWeightageContent": "By Turning off this toggle can leads to loss the weightage settings if configured", "clickAddHotspot": "*Click <b>“Add hotspot”</b> to add specific area on the image as answer.", "dragAndDrop": "Drag and drop your file to upload", "uploadFile": "Upload file", "deleteImageHotspot": "Delete Image and Hotspots?", "imageHotspotReset": "The image and all created hotspots will be permanently deleted, and this action cannot be undone.", "deleteImageHotspotContent": "<b>Are you sure you want to delete this image with hotspots marked?</b>", "resetWeightage": "Reset Weightage", "courseRequiredForAddItem": "Please select a course before adding item", "duplicateOptionText": "Duplicate option not allowed", "myQuestions": "My Questions", "myReview": "My Review"}, "itemRequest": {"reviewOldItems": "Review Old Items -", "createNewItems": "Create New Items -", "sendToAssessmentAuthor": "send to {{authorName}}", "sentToAssessmentAuthor": "sent to {{authorName}}", "reviewedRequired": "Reviewed / Required", "oldItems": "Old Items", "requestedNewItems": "REQUESTED NEW ITEMS", "createdRequired": "Created / Required", "enterReason": "Enter reason", "itemRequested": "ITEM REQUESTED", "reviewItems": "REVIEW ITEMS", "enterCommentsHere": "Enter comments here...", "discardFaultyitem": "Discard - <PERSON><PERSON><PERSON>", "enterTheReason": "Enter The Reason", "confirmItem": "Confirm Item Extraction", "confirmAssessment": "Confirm Assessment Extraction", "discardSelected": "This Selected item contains {{item}} <b>\"Discard\"</b> items that will be excluded from the extracted copy.", "assessmentDiscard": "This assessment contains {{item}} <b>\"Discard\"</b> items that will be excluded from the extracted copy.", "includeDiscard": "To include all items (with Discard items) in the extracted copy, click Confirm.", "removeDiscard": "Remove - Discard <PERSON>em", "removeDiscardItem": "Are you sure want to remove the discard item?", "createNewItem": "Create new item for this topic", "discardItem": "Discard <PERSON>em", "useItem": "Are you sure you want to use discard item?", "noOfTimesDiscard": "No.of times Discarded"}, "stepWizard": {"inReview": "In Review", "corrections": "Corrections", "inApproval": "In Approval", "confirmation": "Confirmation"}, "sentReview": {"itemSentForReview": "<PERSON>em sent for review", "chooseAlternateReplacement": "<PERSON><PERSON> Alternate as Replacement", "attachPublishedItem": "Attach Published Item", "moveRequestCreateNew": "Move request to create new", "headerName": "Item Management > Item Author > For Review", "courseRequiredForReviewer": "Please select a course before sending to reviewer"}, "itemsReview": {"allItems": "All Items", "pendingItems": "Pending Items", "reviewedItems": "Reviewed Items", "sendToItemAuthor": "Send to item author", "sendToAA": "Send to Assessment Author", "sendToME": "Send to {{ME}}", "itemsReviewInSelectedAssessment": "Items for Review in the Selected Assessment"}, "itemForReview": {"itemForReview": "Item for Review:", "sendForCorrection": "Send For Correction", "checkList": "Checklist", "noteChangesAutoSaved": "*NOTE: Changes will be auto saved", "textEmptyError": "Input field should not be empty", "texonomyMappingEmptyError": "Cannot remove all taxonomy mapping", "attachmentError": "Cannot delete attachment"}, "questionByAssessment": {"questionBank": "Question Bank", "archiveAssessment": "Archive Assessment", "archiveThisAssessment": "Are you sure want to archive this assessment", "questionByAssessment": "Question by Assessment", "questionByTopic": "Question by <PERSON><PERSON>", "archiveItem": "Archive Item", "archiveItemsConfirmation": "Are you sure want to archive {{count}} item?", "confirmArchiveRemainingItems": "Some items may already be archived. Do you want to proceed with archiving the remaining items?", "archiveAllItemsConfirmation": "Are you sure want to archive all items?", "alreadyArchivedAllItems": "All selected items are already archived.", "alreadyArchivedAllItem": "The selected item is already archived.", "createItemOrReview": "Item Create/Review"}, "assessmentCheckList": {"checkList": "CHECKLIST", "done": "Done", "notDone": "Not Done", "notApplicable": "Not Applicable", "selectItem": "Select An item to view its checklist"}, "assessmentCardPopup": {"similarAssessmentIsFoundIn": "A Similar Assessment is found in", "wouldYouLikeToUseThis?": "Would you like to use this?", "if": "If", "proceedExisting": "you can proceed using the existing Assessment and publish it", "assessmentCreation": "a new Assessment will be created and the existing assessment cannot be published", "impact": "Agree if you understand the above impacts", "permanentlyAssign": "Proceeding will permanently assign you as the {{role}} for this assessment ?"}, "assessmentCardGroup": {"assessments": "Assessments", "showAll": "SHOW ALL TASKS", "ongoingWithMe": "Ongoing - With me", "withItemAuthor": "With Item Author", "sentToIA": "Sent to <PERSON><PERSON>", "sentToAA": "Sent to Assessment Author", "itemAuthorCompleted": "Item Author Completed", "inAssessmentAuthor": "In Assessment Author", "sentToItemAuthor": "Sent to <PERSON><PERSON>", "withdrawnItem": "Withdrawn Item from IA", "fulfilledItemAuthor": "Fulfilled - from Item Author", "ongoingItems": "Ongoing items", "inReviewSubjectExpert": "In Review ({{subjectExpertReviewerRoleName}})", "reviewCorrections": "Review / Corrections", "inReviewMedicalEducationist": "In Review ({{medicalEducationistRoleName}})", "approveCorrections": "Approve / Corrections", "inApprovalMedicalEducationist": "In Approval ({{medicalEducationistRoleName}})", "sentAssessmentAuthor": "Sent (to assessment author)", "ongoingInReview": "Ongoing In Review", "sentItemAuthorForCorrections": "<PERSON><PERSON> (Item author) for Corrections", "reviewAfterCorrections": "Review After Corrections"}, "extractBulkOldQuestions": {"oldAssessmentsThatMatch": "Old Assessments that match", "extract": "Extract", "assessmentAuthors": "Assessment Authors", "studentPerformance": "Student Performance", "replace": "Replace"}, "createAssessmentPopup": {"createAssessment": "Create New Assessment", "coursesList": "COURSES LIST", "dueDate": "due date (optional)", "noTopicsFound": "No topics found under this Subject"}, "createNewAssessmentPopup": {"requestItem": "Request for Item Authors to Create New items or Review Old ones", "allTopics": "All Topics", "topicMultipleAuthor": "Topics with Multiple authors", "itemsDueDate": "Items Due Date", "sendRequest": "Send Request", "show": "SHOW"}, "status": {"alternate": "Alternate", "approvedMe": "Approved ({{ME}})", "archived": "Archived", "correctedMe": "Corrected ({{ME}})", "correction": "Correction", "correctionFromMe": "Correction from {{ME}}", "correctionFromSe": "Correction from {{SE}}", "discard": "Discard", "expired": "Expired", "finalizedWr": "Finalized (WR)", "forConfirmation": "For Confirmation", "forReview": "For Review", "fulfilled": "Fulfilled", "imported": "Imported", "invalidRequest": "Invalid Request", "inApprovalHyphen": "In-Approval", "inApprovalMeHyphen": "In-Approval ({{ME}})", "inApproval": "In Approval", "inApprovalMe": "In Approval ({{ME}})", "inReviewHyphen": "In-Review", "inReviewMeHyphen": "In-Review ({{ME}})", "inReviewSeHyphen": "In-Review ({{SE}})", "inReview": "In Review", "inReviewMe": "In Review ({{ME}})", "inReviewSe": "In Review ({{SE}})", "newRequest": "New Request", "notSuitable": "Not Suitable", "ongoing": "Ongoing", "publishedWr": "Published (WR)", "publishedR": "Published (R)", "readyForApproval": "Ready For Approval", "readyForReview": "Ready For Review", "reviewed": "Reviewed", "suitable": "Suitable", "retain": "<PERSON><PERSON>", "requestedCorrection": "Requested Correction", "reviewedMe": "Reviewed ({{ME}})", "reviewedSe": "Reviewed ({{SE}})", "updatedByAA": "Updated By Assessment Author", "onGoingWithAA": "Ongoing With Assessment Author", "facultyNeedCorrection": "Faulty Need Correction"}, "itemsTab": {"questionStem": "Question Stem", "history": "History", "details": "Details", "mapping": "Mapping", "preview": "Preview"}, "title": {"studentVerifyProfileDetails": "Student Verify Profile Details", "addIndividualStudent": "Add Individual Student", "assessmentAuthorDashboard": "Assessment Author Dashboard", "itemAuthorDashboard": "Item creation Dashboard", "reviewerDashboard": "Reviewer Dashboard", "medicalEducationistDashboard": "Medical Educationist Dashboard", "subjectExpertReviewerDashboard": "Subject expert reviewer dashboard", "questionBank": "Question Bank", "itemDetail": "<PERSON><PERSON>", "reviewCreateAssessment": "Review/Create Assessment", "reviewItem": "Review Item", "reviewAssessment": "Review Assessment", "conductExamsSettings": "Conduct exams settings", "remoteExamsSettings": "Remote exams settings"}, "fileDetails": "file details", "matchingQuestion": "Matching question", "extractAssessmentPopup": {"noItemCountSelected": "No item count selected", "difficultyIndexRange": "difficulty index range", "discriminationIndexRange": "discrimination index range", "oldAssessments": "Old Assessments", "oldItems": "Old items", "newItems": "New items", "autoExtractItems": "Auto Extract Items", "selectItemCount": "Select Item Count", "itemType": "Item Type", "requiredCount": "Required Count", "subItemsPerSet": "Sub Items Per Set", "addItemType": "Add Item Type", "biserialIndexRange": "Biserial Index Range", "selectItemCountExtract": "Select Item Count Extract"}}, "settings": {"basicList": {"basicList": "Basic List", "serviceSetting": "Service & Setting", "addBasicList": "Add Basic List", "addDomain": "Add Domain/Theme", "frameworks": "frameworks", "selectFrameworks": "Select Framework", "surveyItemType": "Survey Item type", "basicModule": {"itemsSelectedList": "Items For Selected List", "newProgramName": "New Program Name", "newShortCode": "New Short Code", "shortCode": "Short code", "selectType": "Select type", "programsCreatedSuccessfully": "Programs created successfully", "itemNameRequired": "Item name is required", "itemCodeRequired": "Item code is required", "treeNodeIdMissing": "Tree nodeID is missing, cannot add new", "program": "Program", "programName": "{{name}} Program", "term": "Term", "year": "Year", "curriculum": "Curriculum", "level": "Level", "rotationGroup": "Rotational Group", "course": "Course", "module": "<PERSON><PERSON><PERSON>", "elective": "Elective", "subject": "Subject", "topic": "Session Topic", "subTopic": "Sub Topic", "slo": "Session Learning Outcomes", "topics": "Topics", "programAddedSuccessfully": "Program added successfully", "programUpdatedSuccessfully": "Program updated successfully", "programArchivedSuccessfully": "Program archived successfully", "termAddedSuccessfully": "Term added successfully", "termUpdatedSuccessfully": "Term updated successfully", "termArchivedSuccessfully": "Term archived successfully", "yearAddedSuccessfully": "Year added successfully", "yearUpdatedSuccessfully": "Year updated successfully", "yearArchivedSuccessfully": "Year archived successfully", "curriculumAddedSuccessfully": "Curriculum added successfully", "curriculumUpdatedSuccessfully": "Curriculum updated successfully", "curriculumArchivedSuccessfully": "Curriculum archived successfully", "levelAddedSuccessfully": "Level added successfully", "levelUpdatedSuccessfully": "Level updated successfully", "levelArchivedSuccessfully": "Level archived successfully", "rotationGroupAddedSuccessfully": "Rotation Group added successfully", "rotationGroupUpdatedSuccessfully": "Rotation Group updated successfully", "rotationGroupArchivedSuccessfully": "Rotation Group archived successfully", "courseAddedSuccessfully": "Course added successfully", "courseUpdatedSuccessfully": "Course updated successfully", "courseArchivedSuccessfully": "Course archived successfully", "moduleAddedSuccessfully": "<PERSON><PERSON>le added successfully", "moduleUpdatedSuccessfully": "Module updated successfully", "moduleArchivedSuccessfully": "Module archived successfully", "electiveAddedSuccessfully": "Elective added successfully", "electiveUpdatedSuccessfully": "Elective updated successfully", "electiveArchivedSuccessfully": "Elective archived successfully", "subjectAddedSuccessfully": "Subject added successfully", "subjectUpdatedSuccessfully": "Subject updated successfully", "subjectArchivedSuccessfully": "Subject archived successfully", "topicAddedSuccessfully": "Session topic added successfully", "topicUpdatedSuccessfully": "Session topic updated successfully", "topicArchivedSuccessfully": "Session topic archived successfully", "subTopicArchivedSuccessfully": "Sub topic archived successfully", "sloAddedSuccessfully": "Session learning outcome added successfully", "sloUpdatedSuccessfully": "Session learning outcome updated successfully", "sloArchivedSuccessfully": "Session learning outcome archived successfully", "deleteChildError": "Archive child elements in this parent element to continue this action", "surveyItemTypeAddedSuccessfully": "Survey item type added successfully", "surveyItemTypeUpdatedSuccessfully": "Survey item type updated successfully", "surveyItemTypeArchivedSuccessfully": "Survey item type archived successfully", "cloUpdatedSuccessfully": "Course learning outcome updated successfully", "areYouSureWantToDelete": "Are you sure want to delete?", "invalidShortCode": "Invalid short code", "addTopicContentTitle": "Add a Content to this Topic", "addContent": "Add content", "editContent": "Edit content", "changeDutyApproach": "Are you sure you want to change the Duty approach?", "changeDutyApproachNote": "Changing the duty approach will remove all the proctors assigned by you.Proctors assigned centrally by <PERSON><PERSON> will be retained."}, "itemType": {"itemTypeAddedSuccessfully": "Item type added successfully", "itemTypeUpdatedSuccessfully": "Item type updated successfully"}, "contentUnderTheTopic": "Content Under the Topic of"}, "globalExamSettings": {"header": "Settings > Global Exam Settings", "title": "DigiAssess | Global Exam Settings - {{label}}", "globalExamSettings": "Global Exam Settings", "changingSettings": "Changing settings here will affect multiple functions across the application and will not affect past data.", "detail": "Details For Selected Module", "assessmentTypes": "Assessment Types", "attemptTypes": "Attempt Types", "examTypeWise": "Exam type-wise: <PERSON><PERSON>", "coursesExams": "Courses & Types Of Exams", "examinationTimeSlots": "Examination Time Slots", "examDatesAllotment": "Exam Dates Allotment", "proctorSelection": "Proctor selection settings", "serviceSetting": "Service & Setting > Global Exam Settings", "globalExamSettingsTitle": "DigiAssess | Global Exam Settings - {{label}}", "examTypeStable": {"examName": "Exam Name", "shortForm": "Short Form"}, "academicYear": {"academicYear": "Current Academic Year", "pastAcademicYear": "Past Academic Years", "addEndDateAcademicYear": "Please add start and end date of current academic year", "courseGroupAlreadyGenerated": "Course group already generated, it can't be updated until next academic year", "rangeDatePlaceholder": "Select Start Date - End Date"}, "examinationDateAllotment": {"typeOfExam": "Type of Exam", "startDate": "Start Date", "endDate": "End Date", "pastDatesNotAllowed": "Past dates not allowed"}, "examTypes": {"assessmentType": "Select Assessment Type", "examCategory": "Select Exam Category", "courseModuleGroup": "Course/Module Groups", "examCategoryNotMapped": "Exam category is not mapped with the displayed exam types", "examCategoryNotMappedAssessmentType": "Exam category and exam type is not mapped with the selected assessment type", "o+p": "O + P", "o-p": "O - P", "r-p": "R - P", "r+p": "R + P", "customTestCenter": "O + P (Custom Test Center)", "centralizedTestCenter": "O + P (Centralized Test Center)", "noTestCenter": "R - P (No Test Center)", "virtualTestCenter": "R + P (Virtual Test Center)", "remoteWithProctor": "Remote with proctor", "remoteWithOutProctor": "Remote without proctor", "onsiteWithProctor": "Onsite with proctor", "listOfTaggedExamTypes": "List of tagged exam types"}, "proctorSettings": {"course": "Show 'course' ", "role": "Show 'Role' ", "proctor": "Show 'Times proctored'", "assign": "Show 'Last Assigned' ", "showPhoneNumber": "Show 'Phone Number' ", "proctorStatus": "Number of proctors for each Exam"}, "courseTypesList": {"course": "Course", "configure": "Configure", "activate": "Activate", "mode": "Mode", "tcUsage": "TC Usage", "tcAllocationRequest": "TC Allocation Request", "centralized": "Centralized", "customized": "Customized", "both": "Both", "notApplicableForCustomized": "Not Applicable for Customized Infra Type", "modeInfo": "Select 'IN' if the exam is conducted within DigiAssess; choose 'OUT' if it's outside the platform.", "TcUsageInfo": "Choose 'Centralized' to use existing test centers from Test Center Management, 'Customized' for new ones not listed, or 'Either' if using either option.", "TcAllocationInfo": "For centralized TC usage, Select 'Yes' to send schedule & TC requests for approval before publishing; choose 'No' to publish directly without approval for those who creates the assessment."}}, "assessmentSettings": {"noAssessmentsFound": "No Assessments Found", "itemReviewUniDirectional": "Item Review in Unidirectional", "itemStem": "<PERSON><PERSON>", "itemChoice": "Item Choice", "itemAnswerKey": "Item Answer key", "accessItemStem": "Access For Question", "accessItemChoice": "Access For Choice", "accessItemAnswerKey": "Access For Answer Key", "uniDirectionalNote": "* Note : When Unidirectional “ON” Reviewers Select Send For Corrections Item Will Send To Item Author, Once The Corrections Changed Than Send To Assessment Author", "none": "NONE", "view": "VIEW", "edit": "EDIT", "assessmentSettings": "Assessment Settings", "allSaved": "All Saved", "configurations": "Configurations:", "backToSchedulingPage": "Back to scheduling page", "resetAllAssessmentItems": "Reset all assessment items", "confirmResetAssessmentItems": "Are you sure you want to proceed with resetting all items? Please note that once reset, both new and existing items will be reset to their default state.", "recallCard": "Recall Cards from Item author", "renewCardFromItemAuthor": "Are you sure you want to proceed Renew Item request ? Please note that once Renewed Item, both new and existing items will return.", "renewItem": "Renew Item Request", "resetRequestedCards": "Reset Created or Attached Cards", "requestedCardsReset": "If you change item type related settings all created and attached cards will be reset, are you sure you want to proceed?", "noteUpdateItemType": "Note: If you need to change the item type or settings, first Renew Item from the item author and make your updates.", "noteUpdateTime": "Note: If you need to change the time and mark, first reset card from the section and make your updates.", "filters": "Filters", "changeSettings": "Are you sure want to change settings?", "basicSettingsLabel": "Basic Settings", "advanceSettingsLabel": "Advanced Settings (Optional)", "advanceSetting": "Advance Settings", "basicSettings": {"totalNoOfItems": "Total No. of Items", "totalMarks": "Total Marks", "totalTimeDuration": "Total Time Duration (min)", "reviewerRequired": "Reviewer required", "oldItems": "Old Items must be Reviewed by item author"}, "advanceSettings": {"shufflingOfSections": "Shuffling of Sections", "answeringMethod": "Answering Method", "showSectionsInInstructionPage": "Show sections in instruction page", "showSectionsItemTypesInInstructionPage": "Show item types in instruction page", "sectionReArrangement": "Section Re-arrangement", "shufflingOfChoices": "Shuffle choices", "shufflingItems": "Shuffling of Items Within the Section", "shufflingGroups": "Shuffling of Items Within Groups (Applicable: EMQ, CQ, MQ, CSAQ, MIT)", "allowNavigatingToNextItemWithoutAnswering": "Allow Navigating to Next Item Without Answering", "studentsMandatorySubmitExam": "Mandatory to Answer All Items to Submit Exam", "restrictStudentLateEntry": "Restrict Students Entry After Late Entry Time", "maximumLateEntryDuration": "Set Maximum Late Entry Duration", "minimumTimeStudentMustAttendExam": "Set Minimum Time Student Must Attend the Exam", "reduceLateEntryTotalTime": "Reduce Late Entry Time From Total Time", "onlyAllowedWriteSafeExamBrowser": "Only Allowed To Write In Safe <PERSON><PERSON>er", "revisitingItem": "Revisiting Item", "revisitingSections": "Revisiting Sections", "allowMaximumLateEntryTimePercentageTotalExamDuration": "Allow maximum late entry time as a percentage(%) of total exam duration"}, "examType": {"examTypes": "Exam Types", "general": "General", "customize": "Customize", "custom": "Custom", "selectAssessment": "Select Assessment", "manageConfigurations": "Manage configurations", "noAssessmentSelected": "No Assessment selected / No Exam types matching selected Assessment", "customSettings": "Custom Settings", "course": "Course + Exam Type", "allConfigurations": "All Saved custom Configurations", "addCustomSetting": "+  Add Course-<PERSON>am Specific Settings", "genericConfigurations": "Generic {{examTypeCode}} configurations", "examDesc": "Generate and save your preferred configuration for {{assessmentName}} exams"}, "endOfModule": {"saveAs": "Save As", "customizedSetting": "Switch on the toggle to enable customized assessment setting", "proctorType": "To change proctoring type, go to", "proctorTypeWithStatus": "To change proctoring type ({{proctoringType}}), go to", "examTypeWise": "Exam type-wise: <PERSON><PERSON>", "selectedConfiguration": "Selected Configuration for all such exam type (Only admin can change)", "noVersionsSelected": "No versions selected", "select": "Select", "defaultAssessmentValues": "Default Assessment Values", "itemAuthoring": "Item Authoring", "feedbackRequired": "Fe<PERSON><PERSON> Required", "newItemsCreated": "New Items created by item author must be Reviewed by", "reviewerChecklist": "{{subjectExpertReviewerRoleName}} Checklist Required", "medicalEducationist": "{{medicalEducationistRoleName}} Checklist Required", "itemReview": "Item Review in Unidirectional", "authorHeader": "author > assessment > dashboard >", "itemsRequested": "items Requested", "assessmentAuthoring": "Assessment Authoring", "oldItems": "Old Items must be Reviewed by item author", "totalSize": "Total File Size Limit (MB)", "totalLimit": "Total Time Limit (min)", "totalMarksLimit": "Total Marks Limit", "totalItemsLimit": "Total Items Limit", "itemsWithoutSection": "Items Without Section", "marks": "Marks", "eachItem": "each item:", "timeMin": "time (min)", "itemType": "Item Type", "shuffleOfItems": "Shuffle of Items", "shuffleChoice": "Shuffle Choices - within Items", "answeringSequenceItems": "Answering Sequence of Items", "itemsWithSection": "Items With Section", "commonAllSections": "common all sections", "allSections": "All Sections", "quantity": "Quantity:", "itemsWithin": "Items within", "eachSection": "Each Section:", "shufflingOfSections": "Shuffle sections", "answeringSequenceSection": "Answering Sequence of Section", "sectionItemsWithin": "Section & Items within", "sectionSettings": "section settings:", "numberOfItems": "Number of items within this section", "numberOfGroupsLimit": "Number of Group Limit This section", "totalGroupsItemType": "Total Groups For This ItemType", "marksLimit": "Marks Limit within this Section", "timeLimit": "Time Limit within this Section (min)", "itemSettings": "item settings:", "distributionOfMarks": "Distribution of  <span class=\"digi-mr-5 digi-ml-5 digi-font-500\"> MARKS </span>  across Items within section", "distributionOfTime": "Distribution of  <span class=\"digi-mr-5 digi-ml-5 digi-font-500\"> TIME </span>  across Items within section", "sequenceOfItems": "Sequence of items within a section", "shufflingOfChoices": "Shuffling of <span class=\"digi-mr-5 digi-ml-5 digi-font-500\"> CHOICES </span> within Section (in {{MCQ}},{{CQ}},{{MQ}},{{EMQ}})", "shufflingOfItems": "Shuffling of <span class=\"digi-mr-5 digi-ml-5 digi-font-500\"> ITEMS </span> within Section", "shufflingOfItemGroup": "Shuffling of items within <span class=\"digi-mr-5 digi-ml-5 digi-font-500\"> GROUPS </span> (in {{EMQ}},{{CQ}},{{MQ}},{{CSAQ}}, {{MIT}})", "onsite": "onsite", "remote": "remote", "remote-p": "REMOTE - P", "remote+p": "REMOTE + P", "onsite+p": "ONSITE + P", "onsite-p": "ONSITE - P", "endOfAssessment": "End Of Assessment", "duringAssessment": "During Assessment", "both": "Both", "equal": "Equal", "unEqual": "Unequal", "restricted": "Restricted (No Revisit)", "freeSequence": "Free Sequence (Revisit)", "mins": "MINS", "examTypesNav": "SETTINGS > ASSESSMENT SETTINGS > EXAM TYPES", "totalSessionTimeLimit": "Total time limit and no. of each section time limit doesn't match", "totalSessionMarksLimit": "Total marks limit and no. of each section marks limit doesn't match", "totalSessionItemsLimit": "Total items limit and no. of each section items limit doesn't match", "decimalValue": "Items should not be Decimal values", "invalidTime": "Invalid time in within section / each item within section, please enter valid time", "publishDate": "Publish Date", "itemDecimalValue": "Items / time should not be Decimal values", "anyChanges": "Any changes made to the settings", "saveAsNew": "Save settings as new", "saveAsDraft": "Save settings as drafts", "chooseConfiguration": "Choose Configuration", "examsReport": "Exams - Reports Required", "exams": "<PERSON><PERSON>", "customName": "Custom Name", "descHint": "Onsite, Onsite proctor, with Item Review, With Section, With Survey", "genericType": "Generic Type", "assessmentTitle": "ASSESSMENT SETTINGS > MESSAGE SETTINGS", "itemsLimitError": "Number of items limit should not be greater / less than the total number of item limit", "multipleItemsLimitError": "Group items limit should be more than 1", "minChoiceValidationError": "Item type minimum choice should be more than 1", "marksLimitError": "Marks limit should not be greater / less than the total marks limit", "timeLimitError": "Time limit should not be greater / less than the total time limit", "groupLimitError": "Group limit should not be greater / less than the number of group limit this section", "itemReviewUnidirectional": "Item Review is Unidirectional", "unidirectionalInformation": "* Note: When unidirectional is “ON” the items Reviewers send for corrections will be sent to item author, once the corrections are made, they will be sent to assessment author", "itemChoice": "Item Choice", "generalSettings": "General Settings", "totalTimeLimit": "Total Time Limit (Min)", "giveExtraTimeRestrictedSequence": "Allow To Give Extra Time For Restricted Sequence", "navigateNextItemAnswering": "Navigate To Next Item Without Answering", "studentsMandatorySubmitExam": "For Students Mandatory To Answer The All Items To Submit Exam", "maximumLateEntryDuration": "Maximum Late Entry Duration(% Of Total Exam Time)", "restrictStudentLateEntry": "Restrict Students Entry After Late Entry Time", "minimumTimeStudentMustAttendExam": "Minimum Time Student Must Attend The Exam(% Of Total Exam Time)", "reduceLateEntryTotalTime": "Reduce Late Entry Time From Total Time", "onlyAllowedWriteSafeExamBrowser": "Only Allowed To Write In Safe <PERSON><PERSON>er", "studentAuthMethod": "Student Authentication Method for exam", "skipStudentAuthMethod": "Can Skip Face Authentication Based On Gender", "itemsSelected": "{{length}} items selected ", "itemWithMinimumChoice": "Item With Minimum Choice", "itemWithDefaultChoice": "Item With Default Choice", "itemTypes": "Item Types", "forAllSections": "FOR ALL SECTIONS", "totalNoOfSection": "Total no. of section", "itemsType": "Items Type", "totalGroups": "Total Groups", "totalItems": "Total Items", "sections": "Sections", "separateItemInMultipleSection": "Distribution item in multiple section", "userCanAddMoreSectionsBasedOnItemType": "*User can add more sections based on item type", "noOfGroups": "No.of Groups", "selectGroup": "Select Group", "g": "G", "showSectionsInInstructionPage": "Show sections in instruction page", "showSectionsItemTypesInInstructionPage": "Show section item types in instruction page", "whenRearrangeSectionStarts": "When Rearrange section starts", "notes": "Notes", "whenRearrangeSectionStartsNote1": "The total time required for the rearrange section will be decreased.", "whenRearrangeSectionStartsNote2": "Enabling the select before exam option displays the section item types on the instruction page.", "whenRearrangeSectionStartsNote3": "If the re-arrange section is enabled before the exam starts, late students can still rearrange the section after the exam begins.", "beforeExam": "Before Exam", "afterExam": "After Exam", "showItemTypesInInstructionPage": "Show item types in instruction page", "facial": "Facial", "password": "Password", "surveyType": "survey type", "canChangeDefaultValue": "Assessment author can change default selections & values", "percentageCannotBeMore": "Percentage can not be more than 100", "groupNotMappedError": "Group count missing or group not mapped", "groupLimitOver": "Group limit is over", "groupAlreadySelected": "Group already selected another section", "sectionSplitError": "Cannot split {{item}}  groups or items equally among {{noOfSections}} sections. Please split the groups and items manually.", "enterSectionItemCount": "Please enter item count for this section", "itemCountGroupCountMismatch": "Selected group count and item count for this section is Mismatch", "groupAndItemCountMisMatch": "The number of group count does not match with item count", "requiredItemTypes": "Required Item Types", "doNotAllowToRevisit": "Don’t Allow to Revisit", "allowToRevisit": "Allow to Revisit", "navigateToNextItemWithoutAnswering": "Navigate to next Item without answering", "setMinimumTimeStudentMustAttendExam": "Set minimum time as a percentage(%) student must attend the exam", "assessmentTotalItemCountDoesNotMatchTheSelectedItemTypesCount": "Assessment total item count does not match the selected item types count", "allowAssessmentAuthorToChangeDefaultSelectionsValues": "Allow Assessment Author to change Default Selections & Values", "allowRearrangingSectionOrderBeforeExamStarts": "Allow re-arranging section order during the exam", "allowMaximumLateEntryTimePercentageTotalExamDuration": "Allow maximum late entry time as a percentage(%) of total exam duration", "worksOnlyWithOnsiteExamProctor": "Works only with Onsite Exam + Proctor"}, "allSavedFormativeConfigurations": {"archivingImpact": "Archiving will have impact on Assessment Creation\n", "archive": "Do you still want to archive this?", "impacts": "Impacts:", "noConfiguration": "Currently No assessment is using this configuration"}, "assessmentGlobalSettings": {"configurationList": "Configurations list > View Configuration"}, "assessmentSettingsPopup": {"createCustomSetting": "Create a Custom Assessment Setting", "selectExam": "Select an Exam Type", "selectCourse": "Select a course", "startSelectingProgram": " Start by selecting the program and other drop downs to find the required course"}, "attendanceManagement": {"conductingExams": "Conducting Exams > Assessment Delivery", "attendanceManagement": "Attendance Management"}, "discardItemMarkDistribution": {"title": "Choose how to handle marks from discarded assessment items", "directGraceMarks": "Direct <PERSON>", "directGraceMarksDescription": "Marks from discarded questions are directly added to student total marks.", "Redistribution": "Redistribution of marks to the undiscarded questions", "RedistributionDescription": "Marks from discarded items are distributed to similar question types. Select a redistribution method below:", "distributedSimilar": "Marks to be distributed to similar item types", "distributedSimilarDescription": "Marks are distributed equally to similar question type items (e.g., MCQ to MCQ).", "distributedAll": "Marks to be distributed to all item types", "distributedAllDescription": "Marks are distributed equally across all remaining question types regardless of type.", "assessmentPlanAdjustment": "Assessment Plan Adjustment", "assessmentPlanAdjustmentDescription": "Modify the assessment structure by dynamically reducing the total number of questions and marks when items are discarded (e.g., 60 questions worth 60 marks with 5 removed becomes 55 questions worth 55 marks).", "discardedItemMarkDistribution": "Discarded Item Mark Distribution", "selectDistributionMethod": "Select Distribution Method"}, "conductExamSettings": {"cloudServer": "Cloud Server", "riskMitigation": "Risk mitigation", "internetNotAvailable": "In case Internet is not available at the start or during the exam, students can complete the exam within the grace period of", "hrs": "hrs", "secondaryServer": "Secondary Server", "assessmentDownloads": "Assessments downloads to Secondary Server", "dailyNight": "daily night before scheduled Exam day", "notificationSent": "Notifications will be sent to", "examCoordinator": "Exam Co-ordinator", "addUserRole": "Add user role for which the notifications need to be sent", "chooseOption": "Choose an option", "option": "option", "conductingExamAssessment": "Conducting Exams > Exam Assessment", "startEnd": "Start and End of Exam instructions", "globalSettings": "As per global settings of assessment and its exam type", "abbreviatedCourseCode": " Abbreviated course code", "noOfSections": "No. Of Sections", "restrictedSequence": "Restricted sequence. Answer sections in fixed order.", "submitAnswer": "Need to submit answers in the current section before going to next.", "freeSequence": "Free sequence. You are allowed to go back to previous sections.", "attendNextSessions": "You can answer next sections without submitting the answers for this section.", "restrictedSequenceQuestion": "Restricted sequence. Answer questions in fixed order.", "submitAnswerQuestion": "You need to submit answers for the current question before going to the next one.", "updateContent": "will update the content", "linkExam": "link to Exam schedule calendar"}, "coursesExamSpec": {"coursesExamSpec": "Courses Exam Specification", "defineEditExamSpec": "Define/ Edit Test Specification for Each course and Exam type", "startSelectProgram": "Start by selecting the program & other drop downs to find the required course", "defined": "Defined", "notDefined": "Not Defined", "testSpecGraph": "test specification graph", "itemsDistribution": "items distribution - heat map", "exportTestSpec": "Export Test specification", "searchByCourse": "Search by course"}, "itemUnderEachClo": {"overallClo": "Overall CLO - Items weightage calculated from the data below", "totalItemsAlloted": "Total items needs to be alotted in all item types", "totalItemsMapped": "Total items needs to mapped", "topic": "Topic", "items": "Items", "mcqItems": "MCQ Items"}, "testSpecSetting": {"testSpecSetting": "Test Specification settings", "courseExamSpecification": "Courses Exam Specification", "defineExamSpecification": "Define/Edit Exam specification for Each course and Exam type", "statusNotes": "*Note: Maximum 10 color shades values are available, above 10 the last color values are appiled.", "eachCourseAndExamType": "Each Course And Exam Type", "generalExamSpecification": "general exam specification"}, "courseExamSpecPopup": {"testSpecGraph": "Test Spec Graph", "viewTestSpecGraph": "View Test Spec Graph for by", "itemCountItemTypes": "Item count - Item types", "itemCountOutcomes": "Item count - Outcomes", "outcomeWeightage": "Outcome weightage", "topicWeightage": "Topic weightage", "selectItemType": "SELECT ONE/MULTIPLE ITEM TYPE", "noOfItems": "NO.OF ITEMS", "selectMultipleClo": "SELECT ONE/MULTIPLE CLO", "selectClo": "SELECT CLO", "itemsWeightage": "ITEMS WEIGHTAGE", "selectTopic": "SELECT TOPIC", "selectSubject": "SELECT SUBJECT", "cloDomains": "CLO DOMAINS", "individualClo": "INDIVIDUAL CLO"}}, "messagingSettings": {"messagingSettings": "Messaging Settings", "messagingTypes": "Messaging Types", "smsEmailInApp": "SMS, Email, In app", "eventBasedAlerts": "Event Based Alerts", "timeBasedDueDatesReminders": "Time Based Due Dates and Reminders", "assessmentSettings": "Assessment settings", "messageSettings": "Message Settings", "messagingSettingsAnnouncement": "Messaging Settings > Announcements", "questionBankPermission": "Question Bank > List of permissions", "listPermission": "List of permissions", "admin": "Admin", "notificationType": {"title": "Messaging Settings > Messaging Types", "sms": "SMS", "email": "Email", "notification": "App Notification", "digiassess": "Digiassess"}, "eventBasedAlert": {"title": "Messaging Settings > Event Based Alerts", "creatingAssessment": "Request for creating assessment based on schedule", "finalizedRequest": "Item Author has finalized the requested Batch of Items", "itemAuthor": "<PERSON><PERSON>", "assessmentAuthorRequest": "Assessment Author requested a Batch of Items", "subjectReview": "{{subjectExpertReviewerRoleName}} completed review for a Batch of Items.", "medicalReview": "{{medicalEducationistRoleName}} completed review for a Batch of Items.", "subjectExpertReviewer": "Subject Expert Reviewer", "reviewItems": "Item Author requested review for a Batch of Items.", "medicalEducationalist": "Medical Educationalist", "correctionsItems": "Item Author, has completed corrections for a Batch of Items."}, "messageDueDate": {"title": "Messaging Settings > Time Based Due Dates and Reminders", "dueDates": "DUE DATES", "assessmentPublish": "Assessment Publish Due Date", "gracePeriod": "(No. of days (Grace period) before start date of all exams)", "chooseDays": "Choose Days", "itemRequest": "Item Request Due Date", "beforePublish": "No. of days before Assessment Publish Due Date", "applicable": "Applicable for Item Author, {{subjectExpertReviewerRoleName}}, {{medicalEducationistRoleName}}", "reminders": "REMINDERS", "beforeAssessmentPublish": "No. days before Assessment Publish Due Date", "time": "Time", "repeat": "Repeat", "itemAuthor": "Item Author, {{subjectExpertReviewerRoleName}}, {{medicalEducationistRoleName}}", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "day": "day", "days": "days", "maximumDays": "Repeat days should be maximum {{days}}"}}, "reviewerSetting": {"reviewerSetting": "Reviewer Setting", "reviewerSettings": "Reviewer Settings", "checklist": "Checklist", "title": "Settings > Reviewer Settings > Checklist", "checkListBreadCrum": "Checklist > {{reviewerType}} > {{itemType}}", "subjectExpertReviewerSetting": {"addSection": "+ add section", "sectionTitle": "Section Title text...", "checklist": "Start creating the checklist by adding a section, Click on \"Add Section\" button above", "addItem": "Add Item", "itemContent": "Enter item contents", "archive": "You are about to archive this {{name}}", "subjectExpertReviewer": "Subject Expert Reviewer", "medicalEducationist": "Medical Educationist"}}, "questionBankSettings": {"questionBankSettings": "Question Bank Settings", "importFileFormatsAllowed": "Import file formats allowed", "importItemTemplate": "Import Item Template", "exportFileFormatsAvailable": "Export file formats available", "title": "Settings > Question Bank Settings > Import file formats allowed", "docx": ".docx", "singleItemTypes": "MCQ, SAQ, TF, EQ", "questionBankImportFileFormats": {"importTitle": "Settings > Question Bank Settings > Import File Formats Allowed", "exportTitle": "Settings > Question Bank Settings > Export File Formats Allowed", "importFileFormats": "importFileFormats", "exportFileFormats": "exportFileFormats", "importFormats": "Import Formats", "exportFormats": "Export Formats", "importFile": "Question Bank > Import file formats allowed", "exportFile": "Question Bank > Export file formats allowed", "permissionFile": "Question Bank > List of permissions", "msWordDocument": "MS Word Document", "csvFile": "CSV File", "xmlFile": "XML File", "importTemplateFormat": "MS Word Document (docx) format", "forPermittedCourse": "For Permitted Courses", "itemAuthorUseAnother": "<PERSON><PERSON> Among Authors", "itemSharingPermissionStatus": "Item Sharing Permission Status"}, "questionBankImportItemTemplate": {"title": "Question Bank > Import Item Template", "importingItem": "Template for importing Items", "format": "format", "itemTemplate": "Upload your Item Template", "importItemTemplate": "Settings > Question Bank Settings > Import Item Template", "itemTemplateDownload": "item-template.docx"}, "questionBankContentPopup": {"importItem": "Import Items using MS Word Document", "sampleTemplate": "Upload a Sample template file", "browseFile": "Click to Browse a file", "browse": "Browse"}}, "surveySettings": {"surveySettings": "survey settings", "includeSurvey": "Include General Survey", "inThisCourseSurveyToBeConductedAtThe": "In this course, survey to be conducted at the", "selectExamType": "Select ExamType", "generalSurveyItems": "general survey items", "createSurveyQuestion": "Create a Survey Question", "generalSurveyItem": "Survey Settings > General Survey Items", "generalSettings": "Settings > General Settings", "questionCreated": "Survey question created successfully", "questionArchived": "Survey question archived successfully", "surveyItems": "survey items", "selectedOutcomes": "*Selected Outcomes will be published for the survey", "selectAll": "Select All", "createSurveyItem": "Create Survey Item :", "enterSurveyQuestion": "Enter Survey Question ...", "choice/options": "Choice / Options", "surveyQuestionsSettings": "survey > survey questions settings", "surveySettingTree": {"surveyItems": "{{name}} Survey Items", "nameSurveyItems": "{{name}} Survey Items"}, "likertAgreeScale": "Likert agree scale", "linearScale": "Linear scale", "emotionalScale": "Emotional scale", "ratingScale": "Rating scale", "likertExtentScale": "Likert extent scale", "openEnded": "Open ended", "greatExtent": "Great Extent", "moderateExtent": "Moderate Extent", "someExtent": "Some Extent", "veryLittleExtent": "Very Little Extent", "notAtAll": "Not at all", "conductSurveyAnonymous": "Conduct survey anonymous", "setCharacterLimit": "Set number of characters limit"}, "onsiteConductingExams": {"onsiteConductingExams": "Onsite - Conducting <PERSON><PERSON>", "remoteConductingExams": "Remote - Conducting <PERSON><PERSON>", "maxLateEntryTime": "Maximum Late Entry time", "facialId": "Facial ID ON", "conductingExamsStudents": "Conducting Exams > Students", "globalServiceSettings": "GLOBAL SERVICE SETTINGS", "conductingExamsSettings": "CONDUCTING EXAMS SETTINGS", "remoteExams": "REMOTE EXAMS", "onsiteExams": "ONSITE EXAMS", "managingProctor": {"columnsShownForProctor": "Columns shown for proctor re-selection settings", "showCourse": "Show 'Course'", "showRole": "Show 'Role'", "showTimesProctored": "Show 'No. of times proctored'", "showLastAssigned": "Show 'Last assigned'", "showPhoneNumber": " Show 'Phone Number'"}, "examManagementTestCenter": {"pauseExamTestCenter": "Pause Exam - for Individual / all test centers", "allowPauseExamForIndividualTestCenter": "Allow <PERSON>am Co-ordinator to pause exams for individual test centers", "allowPauseExamForAllTestCenter": "Allow <PERSON>am Co-ordinator to pause exams for all test centers", "limitTimeForPauseExam": "Limit time duration for Pausing <PERSON>am", "maximumTimeForPausingAllTestCenter": "Maximum time for pausing All test centers (in minutes)", "maximumTimeForPauseingIndividualTestCenter": "Maximum time for pausing Individual test centers (in minutes)", "timeDurationForPausing": "Time duration options for Pausing the Exam", "addCustomTime": "Allow Exam Co-ordinator to add custom time", "timeDurationOptions": "time durations options (in minutes)", "reasonForPausingExam": "Reasons for Pausing the Exam", "addCustomReason": "Allow Exam Co-ordinator to add a custom reason", "resetExamIndividualTestCenter": "Allow Exam Co-ordinator to Reset exams for individual test centers", "resetExamAllTestCenter": "Allow <PERSON>am Co-ordinator to Reset exams for all test centers", "resetExamReason": "Reasons for the Exam's reset", "terminateExam": "Terminate Exam", "terminateExamReason": "Reasons for the Exam's termination ", "terminateExamForIndividualTestCenter": "Allow Exam Co-ordinator to Terminate exams for individual test centers", "terminateExamForAllTestCenter": "Allow Exam Co-ordinator to Terminate exams for all test centers", "giveExtraTimeForIndividualTestCenter": "Allow Exam Co-ordinator to give Extra time for individual test centers", "giveExtraTimeForAllTestCenter": "Allow Exam Co-ordinator to give Extra time for all test centers", "reasonForExtraTime": "Reasons for providing Extra time", "giveExtraTime": "Give Extra Time", "maxTimeIndividualTestCenters": "Maximum Extra time to be given for individual test centers (in minutes)", "maxTimeAllTestCenters": "Maximum Extra time to be given for all test centers (in minutes)", "allowToAddCustomTime": "Allow Exam Co-ordinator to add custom time", "reasonOptions": "Reasons Options", "examManagement": "Exam management - for test centers", "conducting": "Conducting Exams > Exam Co-ordinator", "reset": "Reset Exam", "addedTimeDurations": "Added time durations (in minutes)", "addAnotherTimeDuration": "Add another time duration", "enterNewReasonToAddToTheList": "Enter New Reason to add to the list"}, "proctorAttendanceManagement": {"attendanceManagement": "Attendance Management", "maximumLateEntryProctor": "Maximum late entry time for Proctors (in minutes) before the scheduled start time"}, "proctorExamManagement": {"proctorPauseExam": "Allow Proctor to Pause the exam for students", "maximumTimeForPauseingIndividualStudent": "Maximum time for pausing Individual students (in minutes)", "pauseExam": "<PERSON><PERSON>am", "allowToAddCustomTime": "Allow Proctor to add custom time", "allowToAddCustomReason": "Allow Proctor to add custom reason", "conductingExam": "Conducting Exams > Proctor"}, "proctorStudentManagement": {"misbehaviourActivity": "Misbehavior Activity", "tagMisbehaviourActivity": "Allow Proctors to tag Misbehavior activities by students", "reasonForTaggingMisbehaviour": "List of reasons for tagging misbehavior activities by students", "extraTimeForStudents": "Allow Proctor to give Extra time for students", "disturbingOthers": "Disturbing Others", "suspectedCheating": "Suspected cheating", "caughtCheating": "Caught cheating", "aggressiveBehaviour": "Aggressive behavior", "physicalRage": "Physical rage", "addedReasons": "Added reasons"}, "studentAttendanceManagement": {"maximumExtraTIme": "Maximum Extra time be given for Individual students (in minutes)", "listTimeDurationOptions": "List of time duration options", "listReasonForExtraTime": "List of reasons for providing Extra time", "maximumLateEntryStudent": "Maximum late entry time for Students (in minutes) from the scheduled exam's start time", "restrictStudentLateEntry": "Restrict students' entry after late entry time", "attendanceManagement": "Attendance Management"}, "studentAuthentication": {"studentAuthentication": "Student Authentication", "facialAuthentication": "Facial Authentication", "userPassword": "User Password", "facialIdAuthentication": "Facial ID Authentication", "fingerprintAuthentication": "Fingerprint Authentication", "performOnpermiseTitle": "Perform on-permise server facial authentication for on-premise exams", "performOnpermiseDescription": "Note: Facial authentication does not require an internet connection."}, "onsiteMenu": {"examCoordinator": {"examManagement": "Exam Management", "examCoordinator": "Exam Co-ordinator", "managingProctors": "Managing Proctors", "ProctorReselectionSettings": "Proctor re-selection settings", "ExamManagementForTestCenters": "Exam management - for test centers", "examManagementDesc": "Pause exams, Reset exams, Terminate exams, Give extra time for individual / All test centers"}, "proctor": {"proctor": "Proctor", "attendanceManagement": "Attendance Management", "maximumLateEntryTimeForProctors": "Maximum late entry time for Proctors", "pauseExamUploadAnswers": "Pause exam", "authenticationTagMisbehaviourGiveExtraTime": "for authentication, tag misbehavior, Give extra time"}, "student": {"student": "Student", "attendanceManagement": "Attendance Management", "MaximumLateEntryTime": "Maximum Late Entry time", "studentAuthentication": "Student Authentication", "facialIdOnFingerprintON": "Facial ID", "facialId": "Facial ID"}}}, "assessmentItemType": {"newItemType": "New Item Type", "selectShortCode": "Select Short Code", "itemType": "Item Type", "shortCode": "Short code"}, "reportsAnalyticsSettings": {"reportAndAnalyticsSettings": "Reports and Analytics Settings", "settingsImpactExamReports": "Settings that impact Exam reports and Outcome reports", "exportReport": "Export reports", "permissionFormatsLists": "Permissions, Formats, List of reports to be exported", "publishReport": "publish reports", "difficultyIndexDiscriminationIndex": "Difficulty Index, Discrimination Index, Distractors, Permissions", "listOfReportsPublished": "List of reports to be published", "grading": "Grading", "gradeSystem": "Grade System", "outcomeMetricsAchievement": "Outcome Metrics - Achievements", "outcomeMetricsItems": "Outcome Metrics - Items", "TargetAchievementLowAchievementValues": "Target Achievement, Low Achievement Values", "faultyItemsLowQualityItems": "Faulty Items, Low Quality Items", "markOutcomeNotValid": "Percentage of marks required to achieve its respective outcomes is not valid. Please enter a valid number", "outcomeAchievementForSAQ": "Outcome Achievement for SAQ, CSAQ, EQ Item types and External Exam Result", "markRequiredToAchieveOutcome": "Percentage of mark required to achieve its respective outcomes", "permissionActivatedSuccessfully": "Permission activated successfully", "permissionDeactivatedSuccessfully": "Permission deactivated successfully", "weightageNotMatching": "Weightage not matching, it should match 100%", "exportReports": {"studentResultsOriginal": "Student Results - Original", "studentResultsFinalized": "Student Results - Finalized", "studentResponseReportOriginal": "Students Response Report - Original", "studentResponseReportFinalized": "Students Response Report - Finalized", "itemAnalysisReportOriginal": "Item Analysis Report - Original", "itemAnalysisReportFinalized": "Item Analysis Report - Finalized", "detailedItemAnalysisReportOriginal": "Detailed Item Analysis Report - Original", "detailedItemAnalysisReportFinalized": "Detailed Item Analysis Report - Finalized", "exportReports": "Export Reports", "exportFormats": "Export Formats", "allFormats": "All Formats", "individualReportsExported": "Individual students Reports allowed to be exported", "rolesPermission": "Your role's permission will decide if you can modify the below settings.", "moreInformation": "For more information, contact super admin.", "allReports": "All Reports", "csv": "CSV", "xlsx": "XLSX", "pdf": "PDF", "selectTypeOfReports": "Select the types of reports to exported for the selected courses", "selectGender": "Select Gender"}, "publishReports": {"individualGradeReport": "Individual Grade Report", "cloPloAnalysisReport": "CLO & PLO Analysis Report", "consolidatedGradeReport": "Consolidated Grade Report", "outcomeReport": "Outcome Report", "gradeReport": "Grade report", "reportAnalyticsSettings": "Report & Analytics Settings", "publishReports": "Publish Reports", "reportsAfterPublished": "Reports can be shared after Published", "studentViewIndividualGradeReport": "Student can view individual itemwise marks in grade report", "nationalAccreditationReport": "NCAAA Report", "studentOutcomesAnalysis": "SO ABET Analysis", "nationalAccreditationStandards": "National accreditation standards compliance data", "comprehensiveGradeAnalysis": "Comprehensive grade analysis across all courses", "learningOutcomeAssessment": "Learning outcome assessment & achievement metrics", "studentCriteriaEvaluation": "Student outcomes and ABET criteria evaluation", "chooseReport": "Choose Report", "cloAndPloreport": "Course learning outcome and Program learning outcome report", "itemAnalysis": "Item Analysis", "noOfTimesRevised": "No. of times revised", "noOfTimesEvaluated": "No. Of Times Evaluated", "selectOneToSeeDetails": "select one to see details", "reEvaluate": "Re - Evaluate", "reRevise": "Re - Revise", "cancelReRevision": "Cancel Re - Revision", "noTopicsFound": "No topics found", "startRevision": "Start revision"}, "itemRevision": {"itemRevisionForAssessmentResults": "Item Revision for Assessment Results:", "minimumSampleSizeNotValid": "Minimum Sample Size for Discrimination Index is not valid. Please enter a valid number.", "difficultyIndex": "Difficulty Index", "difficultyItem%": "Difficult Item %", "suitableItem%": "Suitable Item %", "easyItem%": "Easy Item %", "excellent": "Excellent", "good": "Good", "poor": "Poor", "worst": "Worst", "discriminationIndex": "Discrimination Index", "showDiscriminationIndex": "Show Discrimination Index,", "sampleSizeAbove": "only when sample size (Total students attending the exam) is above", "distractors": "Distractors", "lowFunctionalStudentResponse": "Low functional or Non-Distractor - Student response (%)", "highFunctionalStudentResponse": "High functional Distractor - Student response above (%)", "allowMarkingFaulty": "Allow marking “Faulty - Needs Correction”, during Item Revision", "allowRevertRevisedResult": "Allow to “Revert Revised results to Original” for individual student before finalize", "permissions": "Permissions", "difficult": "<PERSON><PERSON><PERSON><PERSON>", "suitable": "Suitable", "easy": "Easy", "finalizedToPublishAssessment": "Finalized & Ready to publish for Assessment Results:", "itemRevisionForAssessment": "Item Revision for Assessment Results:", "publishedAssessmentResults": "Published Assessment Results:", "reRevisedAssessmentResults": "Re-revised Assessment Results:", "reevaluationTriggeredSuccessfully": "Reevaluation triggered successfully", "publishCourse": "publish-course", "finalizedReadyToPublish": "Finalized & Ready to publish", "reRevised": "Re-revised", "reEvaluated": "Re-evaluated", "inRevision": "In Revision", "inRevisionMe": "In Revision - ME", "courseAndStatus": "Course & STATUS", "reviseItems": "Revise Items", "gradeDistributionCurve": "Grade Distribution Curve", "itemQualityDistribution": "Item Quality Distribution", "itemsEvaluatedTotalItems": "items evaluated / total items", "afterRevision": "After revision", "beforeRevision": "Before revision", "searchModule": "Search by course/module", "originalResults": "Original Results", "impact": "Impact", "finalResult": "Final Result", "originalResult": "Original Result", "onlyAutoEvaluatedItems": "Only Auto Evaluated Items", "afterManualEvaluation": "After Manual Evaluation", "finalAfterFurtherRevision": "Final After Further Revision", "track": "Track"}, "outcomeMetricAchievement": {"commonValueAchievement": "Common Target Achievement", "target": "Target", "outcomeWiseValueAchievement": "Outcome-wise Target Achievement", "pleaseEnterValidNumber": "Percentage of marks required to achieve its respective outcomes is not valid. Please enter a valid number.", "targetBenchMarkSIR": "Student Improvement Target Benchmark", "courseSIR": "Course/Module", "subjectSIR": "Subject", "taxonomySIR": "Taxonomy", "topicSIR": "Topic"}, "reportGrading": {"alphaNumeric": "Numeric Not Allowed. Kindly Provide Alpha Numeric / Alpha With Special Characters", "activeGradeSystem": "Active Grade System", "addGradeSystem": "+ Add a Grade System", "addNewGrade": "+ Add a new grade", "noGradeSystem": "No Grade System", "editName": "Edit Name", "addGrade": "ADD GRADE", "gradeName": "Grade Name", "editGrade": "EDIT GRADE", "addAGradeSystem": "ADD A GRADE SYSTEM", "gradeSystemName": "Grade System Name", "editGradeSystem": "EDIT GRADE SYSTEM", "studentGradeFail": "Student attaining this grade has Failed", "doYouWishProceedWithoutSaving": "Do you wish to proceed without saving?", "nameAlreadyExist": "Name already exists", "rangeAlreadyExist": "Range already exists", "from": "From (%)", "to": "To (%)", "selectActiveGrade": "Select Active Grade", "selected": "selected", "added": "added", "active": "(Active)", "fail": "(fail)", "archiveErrorMessage": "Can't archive active grade system", "gradeLimitError": "'from' value should less then 'to' value"}}, "frameWorks": {"domainTheme": "Domain/Theme in order for selected framework", "newFrameWorkName": "New Framework Name", "frameWorkCode": "Framework Code", "code": "Code", "addDomainTheme": "{{mode}} {{for}}", "domainThemeTitle": "Domain/Theme"}, "facial": {"faceCapture": "Face Capture", "capture": "Capture", "captureFace": "Capture Face", "autoRenew": "autoRenew", "retake": "retake", "selectedCamera": "SELECTED CAMERA", "webCamera": "Web Camera", "moreThanOneFaceIsDetected": "More than one face is detected", "pleaseRetakeAgain": "Please retake again", "currentPictureDoesNotMatch": "Current Picture does not match with previous picture. Please retake this picture", "uploadFacial": "Upload Facial"}, "specialSetting": {"specialSettings": "special settings", "assessmentResetMessage": "Due date reset, Assessment card reset, Edit assessment Settings", "specialAssessmentSetting": "Special Settings > Assessment Author", "dueDateReset": "due date reset", "assessmentCardReset": "assessment card reset", "editAssessmentSetting": "edit assessment settings", "selectExamType": "select exam type", "changeDueDate": "change due date", "scheduleDate": "schedule date", "cardTakenBy": "card taken by", "cardStatus": "card status", "confirmCourseCode": "To confirm, Type \"{{courseCode}}\" in the text input field below", "dueDateAA": "Due Date for Assessment Author publish assessment", "dueDateIA": "Due Date for Item Author to Create New or Review Old"}, "advanceOutcome": {"title": "Advanced outcome settings", "performanceSettings": "performance level settings", "benchmarkSettings": "benchmark settings", "impactSettings": "impact mapping analysis", "contentSetting": "Content Mapping Analysis", "performanceSettingsDescription": "The performance level feature allows you to set and track performance ranges with unique names, descriptions, and color codes.", "benchmarkSettingsDescription": "The benchmark feature allows you to set minimum performance standards for students and track their progress against these benchmarks.", "impactSettingsDescription": "The impact analysis feature allows users to assess the significance of various elements within a system or process by assigning labels such as high, medium, or low, helping to prioritize impact that has made during matrix mapping.", "contentSettingDescription": "Content mapping aligns course content with learning objectives or standards, ensuring relevance and identifying gaps in the curriculum.", "changeHeader": "change header", "changeDescription": "change description", "institution": "institution", "percentageRange": "Percentage Range", "minPercentage": "<PERSON><PERSON>", "weightage": "weightage", "label": "label", "descriptionOptional": "Description (Optional)", "color": "color", "addLevel": "Add Level", "minimumValue": "Minimum value", "maximumValue": "Maximum value", "componentManagement": "Component management", "addRange": "add range", "max4character": "Maximum 4 characters only", "lettersExceedMax4character": "Letters Exceeds. Maximum 4 characters only", "chooseDifferentColor": "Please choose unique colors for all four values. Duplicate colors are not allowed."}, "componentManagement": {"dynamicComponentsCreation": "Dynamic Components Creation", "systemComponents": "System Components", "componentMapping": "Components Mapping", "createNewComponent": "Create New Component", "dynamicMappingConfiguration": "Dynamic mapping configuration", "programName": "Program name", "curriculumName": "Curriculum name", "coursesMapped": "Courses Mapped", "courseList": "Course list", "giveComponentName": "Give component a name", "labelTheHierarchy": "Label the hierarchy", "soComponents": "SO components", "showThisComponent": "Show this component in item creation area", "chooseFrameworkOptional": "Choose framework (Optional)", "chooseNumbering": "Choose Numbering", "dynamicComponentCreation": "Dynamic component creation", "courseMatrixMapping": "Course matrix mapping", "componentName": "Component name", "curriculum": "curriculum", "sharedCourses": "shared courses", "sharedCourseInfo": "The courses shared with other programs. The CLO’s of the courses which are mapped with multiple PLO’s are considered as a shared courses", "totalCourses": "Total courses", "filters": "filters", "clearAll": "clear all", "level": "Level", "row": "row", "column": "column", "coursesOf": "Courses of", "flowPreview": "Flow preview", "so": "so", "sharedTo": "Shared to", "sharedFrom": "Shared from", "searchCourses": "Search Courses", "noComponentsCreated": "No components were created under the programs to display", "applyFilter": "apply filter", "filterBy": "Filter by", "introductory": "introductory", "reinforcement": "reinforcement", "emphasize": "emphasize", "contentMapping": "content mapping", "courseName": "course name", "heatTransfer": "Heat transfer", "soDescription": "Make reasonably justified assumptions for missing information based on standards or best practices", "nameRequired": "Component name is required", "curriculumRequired": "Curriculum is required", "hierarchyLabel": "Hierarchy label is Required", "threeLevelsRequired": "Minimum 3 levels is required for each hierarchy", "descriptionShouldBeEmpty": "Description should not be empty", "contentMappingConfiguredInSettings": "Please ensure that the Content Mapping is configured in the settings before proceeding.", "doYouWantToProceed": "Would you like to proceed with ending the process or continue? If you prefer to end it, you can configure again at any time.", "goBackToPreviousScreen": "Going back to previous screen ?", "allDataAutoSaved": "All entered data are auto saved. you can continue creating component anytime. Are you sure you want to go back?", "allProgramUnderInstitution": "All programs under this institution can access this component", "thresholdIsRequired": "Please provide threshold value", "weightageWarning": "Please note that applying the weightage will regenerate the analysis report. Once applied, you must complete the analysis process to add the course report to the program level analysis. Would you like to proceed?", "avgOfSubunits": "Avg. of sub-units", "avgOfStudentMarks": "Avg. of student marks", "avgOfStudentMarksInfo": "Average of all student marks for questions tagged under a specific unit for this Metric Performance.", "chooseCalculationMethod": "Choose calculation method:", "calculationMethodRequired": "Calculation method is required"}, "common": {"selectParentWillAppliedToChildren": "What is selected for the parent will automatically be applied to the children.", "selectChildWillNotAppliedToParent": "What is selected for the child will not be applied to the parent or other siblings.", "newItemName": "New Item Name", "availableType": "Available Types", "examTitle": "Legend", "onSite": "onsite Exam", "remote": "Remote Exam", "proctor": " with <PERSON><PERSON>", "notProctor": "without Proctor", "fullScreen": "View in fullscreen", "shortForm": "Short Form", "onsiteProctor": "ONSITE_WITH_PROCTOR", "remoteProctor": "REMOTE_WITHOUT_PROCTOR", "selectExportType": "select export type", "examCategories": "exam categories", "selectExamCategories": "Select Exam Categories", "onsiteExamConductInDigiassess": "Onsite Exam Conduct in Digiassess", "offsiteExamConductInOutSide": "Offsite Exam Conduct in Out Side", "settingsUpdatedSuccessfully": "Settings has been updated successfully", "configurationNotes": "Note: Configuration will work only for byself assessment creation", "applicableFor": "Applicable For"}}, "dashboard": {"userManagement": {"userManagement": "user management", "overallImportStatus": {"title": "Overall Import Status", "totalImportedUsers": "Total Imported users (Students and Staffs)", "validated": "validated", "pendingValidation": "pending validation", "studentsPendingValidation": "Students Pending validation", "requestNotSent": "REQUEST NOT SENT", "dataVerificationPending": "DATA VERIFICATION PENDING", "biometricVerificationPending": "BIOMETRIC VERIFICATION PENDING", "staffsPendingValidation": "Staffs Pending validation"}}, "schedule": {"schedule": "schedule", "ongoingSchedule": {"title": "Ongoing Schedule", "showing": "Showing", "coursesCount": "courses count", "viewDetails": "view details", "permittedStudents": "Permitted Students", "assessmentsPublished": "Assessments published"}, "upcomingScheduledAssessment": {"title": "Upcoming Scheduled Assessments"}}, "exams": {"exams": "exams", "overallAverage": "Overall Average", "examAdmin": "<PERSON><PERSON>", "liveDashboardAllModule": "Live Dashboard - All module", "testCenterManagement": "Test Center Management", "seatAllocation": "Seat Allocation", "macAddress": "MAC Address", "seatNo": "Seat No", "deleteSystem": "Delete System", "confirmDeleteSystem": "Are you sure you want to delete this system?", "courseGroupsManagement": "Course groups Management", "manageActivities": "Manage Activities", "dashboardExams": "Dashboard | Exams", "dashboardSchedule": "Dashboard | Schedule", "dashboardActivityLog": "Dashboard | Activity Log", "dashboardUserManagement": "Dashboard | User Management", "componentElements": "Component Elements", "currentExamSession": {"title": "Current Exam Session", "live": "Live", "sessionTiming": "Session Timing", "session": "Session 1", "noCurrentExamSessionFound": "NO CURRENT EXAM SESSION FOUND", "SessionDetailsOfEachCourse": "Session Details of each course", "courses": "courses", "please": "Please try again", "loading": "Loading..."}, "currentExamSessionProgress": {"testCentres": "Test Centers", "studentsReported": "Students Reported", "allocatedForTheSession": "Allocated for the session", "permittedForTheSession": "Permitted for the session", "total": "Total", "male": "Male", "female": "Female"}, "sessions": {"upcomingSessions": "Upcoming Sessions", "sessions": "sessions", "sessionNum": "Session", "students": "students", "testCentres": "test centers", "courses": "courses", "NoUpcomingExamSessionFound": "NO UPCOMING EXAM SESSION FOUND"}, "notification": {"notifications": "Notifications", "dailyUpdates": "Daily Updates", "markAllAsRead": "Mark all as read", "updatesForToday": "updates for today", "youReadAllTheNotifications": "You have read all the notifications", "by": "by", "reportAnalytics": "report analytics"}, "registeredStudentsCount": {"title": "Registered Students Count", "students": "Students"}, "overAllCountStatus": {"overall": "Overall", "male": "male", "female": "female"}, "studentsProgramCount": {"students": "students", "male": "male", "female": "female"}, "registerStaffCount": {"title": "Registered Staffs Count", "noRolesAssigned": "No roles assigned", "staffs": "Staffs"}, "smileyError": {"uhOh": "Uh oh!", "sorrySomethingWentWrong": "Sorry, something went wrong", "pleaseTryAgain": "Please try again and reload the page", "tryAgain": "try again", "reloadPage": "Reload the page after some time"}, "trackAssessmentsLive": {"title": "Live Track assessments for the current academic year", "program": "PROGRAM", "total": "TOTAL", "scheduled": "SCHEDULED", "assessmentPreparation": "ASSESSMENT PREPARATION", "inProgress": "IN PROGRESS", "currentExamLive": "EXAM LIVE", "resultsReports": "RESULTS & REPORTS", "generated": "GENERATED", "pleaseSelectType": "Please select EXAM TYPE and ATTEMPT TYPE", "noRecordsFound": "No Records Found.", "noExamTypeAndAttemptTypeFound": "No EXAM TYPE and ATTEMPT TYPE Found", "noExamTypeFound": "No EXAM TYPE Found", "noAttemptTypeFound": "No ATTEMPT TYPE Found"}}}, "exams": {"common": {"academicYear": "Academic Year", "addStudentsIndividually": "ADD STUDENTS INDIVIDUALLY", "uploadFiles": "Upload Files", "academicNumber": "Academic Number", "filteredGroupsNotFound": "Filtered groups not found", "roomNo": "Room Number", "for": "for", "scheduleNewRequest": "Schedule New Request", "noRequestScheduledYet": "No Request has been scheduled yet", "pleaseSelectCourse": "Please select a course with hierarchy to create a schedule request.", "scheduleRequestsSentSuccessfully": "Schedule requests sent successfully", "scheduleRequestApprove": "Schedule Request/Approve", "assignQuestionPaperIncharge": "Assign Question Paper Incharge", "approvedSuccessfully": "Approved successfully"}, "sendNotification": {"publishScheduleSendNotification": "Publish Schedule and Send notifications", "customizeMessage": "customize message", "collapseMessage": "collapse message", "AllGroups": "All groups", "notificationsNotSent": "notifications not sent", "sendEmail": "Send Email", "sendSMS": "Send SMS", "sendAppNotification": "Send App notification", "publishTimetable": "Are you sure you want to publish the timetable for the selected scheduled courses?", "selectedCourse": "For the selected courses notifications will be sent", "proctors": "Proctors", "assessmentAuthors": "Assessment authors", "expand": "EXPAND TO VIEW FOR EACH COURSE", "sent": "sent", "notScheduled": "Exam not scheduled yet", "noCourse": "No courses found", "notified": "students notified ", "newTiming": "Enter new timing for exam session", "repeat": "Repeat for upcoming", "sessionDateTime": "SESSION DATE & SESSION TIME", "yearsLevels": "years & levels", "courseGroups": "course groups", "noStatus": "No Status yet.", "createTimetable": "“Create timetable” to get started", "programme": "Program", "scheduledExamDate": "Scheduled Exam Date", "examTiming": "<PERSON><PERSON>", "studentsUploadedLastPublish": "No. of students uploaded since last publish", "studentsUploadedRegistered": "Total No. of Students Uploaded/registered", "studentsSentUploaded": "Notifications to Students Sent/Uploaded", "notificationsProctor": "Notifications to Pro<PERSON>: <PERSON><PERSON>", "notificationsAssessmentAuthor": "Notifications to Assessment Author: <PERSON><PERSON>", "appNotification": "app notification", "editorChangeText": "Dear user<PERSON><PERSON>, {{text}}  \n Thank you,\n adminSignature", "replaceTestCenter": "in Test Center &lt; testCenterName &gt;", "assessmentPublished": "(Assessment Published)", "scheduledExamDateTime": "Scheduled Exam Date & Time", "viewEmailTemplate": "View E - Mail Template", "studentNotifications": "Student Notifications", "proctorNotifications": "Proctor Notifications", "newStudentsUploaded": "New Students Uploaded", "authorNotifications": "Author Notifications", "exactExamStartAndEndTime": "Exact exam start and end time.", "lastNotificationPublish": "Number of students added since last notification publish.", "numberOfStudentsUploadedRegistered": "Total No. of Students Uploaded/registered", "notificationStudents": "Notifications to Students Sent/Uploaded", "notificationProctor": "Notifications to Pro<PERSON>: <PERSON><PERSON>", "notificationAssessmentAuthor": "Notifications to Assessment Author: <PERSON><PERSON>", "allMemberTemplates": "All member templates", "mailMembers": "Members: Proctors, Students, Assessment Authors"}, "examsManagement": {"editInvigiliator": "Edit invigiliator", "deleteTestCenter": "Delete testCenter", "assignInvigilator": "Assign Invigilators", "removeInvigilator": "Remove Invigilators", "areYouSureRemoveInvigilators": "Are you sure to remove invigilators?", "selectTCManually": "Select TC Manually", "selectTC": "Select TC", "ongoing": "Ongoing", "previousExams": "Previous Exams", "areYouSure": "Are you sure?", "areYouSureMessage": "Are you sure want to remove selected students from <b>{{testCenterName}}</b>", "showStudentName": "Show student name", "selectScheduleAction": "Select schedule action", "searchTextExamType": "Search by Exam Name or Code", "courseGroupManagement": {"course": "Create course groups and its short code for each program", "none": "None", "generate": "generate", "courseGroupGeneration": "Course Group Generation", "courseManagementList": {"programList": "Programs List", "programName": "Program name", "course": "Courses list under the selected program", "programGroupName": "Program Name / Group Names", "syllabus": "Syllabus", "noCourse": "No course list available", "shortCodes": "Short codes"}}, "testCentreManagement": {"search": "Search by test center, room number, location ...", "testNumber": " Test Center Number", "max": "Max", "capacity": "Capacity", "location": "Locations", "seat": "Seats Reserved", "number": "Number", "usable": "Usable"}, "pastExam": {"pastExamsData": "Past Exams Data", "examCoordinator": "<PERSON><PERSON> co-ordinator", "viewExam": "View Exam <PERSON>", "previousExamData": "Previously held Examinations Data", "dateConducted": "Date Conducted", "postExamHeader": "POST EXAM", "studentList": "Student List", "activityLog": "Activity Log", "authentication": "Authentication", "search": "Search name or Academic Id", "pastExamActivity": {"noSessionsFound": "No Session found for selected date", "selectExam": "Select Exam", "selectTestCenter": "Select test center", "selectRole": "select role", "pastExamActivity": "Past Exam Activity log for the selected Exam(s)", "students": "STUDENTS PRESENT/TOTAL STUDENTS", "courseExamId": "Course Exam ID", "rolePersonName": "Role / Person Name", "trigger": "<PERSON><PERSON> - Action", "time": "time", "action": "action", "designation": "designation", "timeDate": "Time & Date", "selectAcademicId": "Select Academic ID"}}, "examsManagement": {"examManagementCreateTimetable": "Exam Management - Create timetable", "monitorExam": "Monitor Exam Pre Readiness", "selectExamType": "Select Exam type - attempt type", "createTimetable": "Create Timetable", "publishSchedule": "Publish Schedule", "rescheduleExam": "Reschedule Exam", "cancelExam": "Cancel Exam", "noPermission": "No permission selected for export data.", "selectDateTime": "Select date, time & Allocate test center", "scheduleNow": "Schedule Now", "courseDetails": "Course Details", "plannedStudentCount": "planned student count", "examDateRange": "Exam date range", "examDurationMin": "<PERSON><PERSON> (min)", "reCheck": "Re - check", "chooseTCFromTableBelow": "Choose TC from the table below", "autoSelectTc": "Auto-Select Test Center", "autoSelectConfirmation": "Are you sure you want to auto select testcenters?", "autoSelectAdditionalNote": "This will automatically select the test centers and will create the schedule request", "tcRoomName": "Test Center Name", "available": "Available", "seatsBooked": "Seats Booked", "setAllocationCount": "Set Allocation Count", "checkTestCenterAvailability": "Check Test Center Availability", "viewTcs": "View TC's", "testCenterAvailability": "Test Center Availability", "availableTcs": "Available Test centers listed!", "setDate": "Set date, time & duration, then click “Test Center Availability”", "endsAt": "Ends at", "autoAllocateStudents": "Auto-allocate students", "examDate": "Exam date", "noScheduledExamsForThisDate": "No scheduled exams for this date.", "viewScheduledExams": "View Scheduled Exams", "requestTC": "Request Test Center", "changeSchedule": "Change Schedule", "autoAllocateStudentNote": "Automatically assigns students across the selected test centers based on available capacity"}, "courseModule": {"allCourseGroups": "All Course Groups", "unscheduled": "Unscheduled", "studentsUnfilled": "Students Unfilled", "unpublishedAssessments": "Unpublished Assessments"}, "examTimeTable": {"program": "Program-Term-Year-Curriculum-Level", "noOfCourses": "courses/modules scheduled count", "invigilatorsAssigned": "Invigilators assigned for", "assessmentsPublished": "Assessments published for", "assessmentPlanStatus": "Assessment Plan Status", "studentUploadStatus": "Students Upload status", "downloadTemplateStudents": "Download Template to upload students"}, "testTable": {"invigilator3": "Invigilator 3", "testCenterCapacity": "Test center capacity", "changeTC": "Change test center", "changeTestCenter": "Change Test Center", "selectedStudentsTC": "Selected students are moved one TC to another TC"}}, "uploadPermittedStudents": {"addStudent": "ADD STUDENT", "currentUploads": "Current Uploads", "pastUploads": "Past Uploads", "uploadFilesPermitted": "Upload files with the permitted students for the selected Exams", "uploadStudentList": "Upload the permitted students list to DigiAssess", "pastStudentUploads": "Past Students uploads", "listPastStudent": "List of Past Students uploads for", "downloadTemplate": "DOWNLOAD TEMPLATE", "getTemplate": "GET TEMPLATE", "downloadFileCoursegroupManagement": "DOWNLOAD FILE FROM COURSE GROUP MANAGEMENT ", "removeStudent": "REMOVE STUDENT", "studentList": "Permitted students list in DigiAssess", "viewFiles": "View Files", "viewStudents": "View Students", "startBySelectingExams": "Start by selecting <PERSON><PERSON>", "filesUploadedValidated": "Files uploaded & Validated", "filesUploaded": "Files Uploaded:", "file": "file", "filesNotProcessed": "These files will not be processed. You may need to re - upload them after correction", "errorFound": "Errors found", "noFilesUploaded": "No Files Uploaded", "uploadFiles": "Upload Files", "standardNaming": "Standard naming convention to be strictly followed for the Groups:", "uploadFormat": "2019-2020-MP-RT-5Y-MPCL2.0-9L-RG2-FAM535-M", "courseName": "MP-RT-5Y-MPCL2.0-9L-RG2-FAM535", "uploadCompleted": "Uploaded completed {{completed}} out of {{total}}", "error": "error", "groups": "Groups", "emailID": "Email ID", "dateUploaded": "Date Uploaded", "batch": "<PERSON><PERSON>", "studentsUploaded": "Students Uploaded", "noRecordFound": "No Record found", "addStudentToGroup": "Add a student to the group", "invalidAcademicNumber": "Invalid academic number", "errorAcademicNumber": "Academic Number Should be 4-15 digits", "errorFirstName": "First name is required", "errorMiddleName": "middle name is required", "lastNameRequired": "last name is required", "csvFilesSelectedZero": "No. of (.xlsx) files selected - 0", "csvFilesSelected": "No. of (.xlsx) files selected", "selectStudentPermitted": "Select students permitted to take exam", "examManagementUploadStudent": "Exam management > Upload Permitted students into DigiAssess", "regularTerm": "Regular Term", "year5": "Year 5", "level9": "Level 9", "courseCode": "Course Code", "academicNo": "academic_no", "currentUploadTemplate": "current_upload_template", "allFieldsRequired": "All Fields Required!", "fromValidation": "From Validation", "verifiedSuccessfully": "Verified successfully", "noFileFound": "No file found!", "fileDoesNotHaveMandatoryColumn": "file does not have mandatory column", "pleaseImportValidFile": "Please import valid .csv file", "fileTypeValidation": "File type validation", "examSelected": "<PERSON><PERSON> selected", "uploadedStudentsInfo": " Few students inside this file have already been uploaded previously. Make changes accordingly & re-upload.", "removeFile": "Remove file", "remove&Proceed": "Remove & proceed", "mp": "MP", "rt": "RT", "5y": "5Y", "mp2": "MP2.0", "9l": "9L", "rg": "RG", "fam": "FAM535", "failedStudents": "failed-students", "doNotLeaveSpaceAcademicNumber": "Don't Leave Space In Academic Number", "excel": {"academicNo": "academicNo", "email": "email", "gender": "gender", "message": "message"}, "courseNameMismatch": "Name used in the file is not recognized by the system. Correct the file name and reupload", "dataMismatch": "Mismatches detected in student data upload", "invalidStudentsDetected": "Invalid students detected, Click proceed to update only valid students", "invalidInactiveStudentsDetected": "Invalid/Inactive students detected.", "exportError": "Export Error List", "errorStudentList": "Error Student List", "uploadStudentsError": "The number of students that have been uploaded is greater then the planned capacity", "filled/plannedStudent": "Filled/Planned Students {{gender}}: {{filledStudent}}/{{plannedStudent}}", "currentUploadCount": "Current Uploads: {{count}}", "needCapacity": "Need capacity for remaining {{gender}} students: {{count}}", "selectTestCenter": "Select test center", "notificationHint1": "The scheduler will receive a notification stating that proctors are not available for this course at the selected test center.", "notificationHint2": "The scheduler will receive a notification stating that excess students have been uploaded for this course", "testCenter": "TestCenter", "remaining": "Remaining", "filled": "Filled", "total": "Total", "addTestCenter": "ADD TEST CENTER", "viewLog": "View Log", "dateAndTime": "Date and Time", "username": "Username", "action": "Action", "response": "Response", "proctorNotAssigned": "(Proctor Not Assigned)", "importedStudents": "Imported {{gender}} Students", "showMore": "show more", "showLess": "show less", "studentNameInAnotherTC": "Student name in another TC", "assignedTcs": "Assigned TCs", "alreadyStudentsInAnotherTC": "The listed students are already in Scheduled in another TC for another course on same date and time. So move to that current TC?", "clickProceedOrCancel": "Click 'Proceed' or 'Cancel' to remove from list and try again.", "duplicateStudents": "Duplicate student on the uploaded file", "fileProcessingError": "File processing error"}, "uploadStudentError": {"cannotMoveToThisTC": "Cannot Move to this TC", "cantMoveTestCenter": " The selected test center is for {{selectedTc}} students only. You cannot move {{selectedGender}} students to this test center.", "removeSelection": "Remove Selection:", "removeSelectionHint": "Remove the {{selectedGender}} student's selection and move to this test center.", "mixedTestCenter": "Mixed Test Center:", "mixedTestCenterHint": "Choose a mixed test center to accommodate both genders together.", "testCenterFull": "Test Center Full", "testCenterFullHint": " The test center is already full. To accommodate this student, please remove any student from the center or assign them to another available test center", "allotSeatsForMixedTC": "Allot Seats for Mixed TC", "enterTheMaleAndFemaleSeatsForMixedTC": "Enter the Male and Female seats for Mixed TC", "modifyCapacity": "Modify Capacity", "autoAssignTC": "Auto Assign TC", "withoutMixed": "Are you sure want to assign the students to the test center automatically based on available seats? Only valid/active students will be added.", "uploadedStudents": "Uploaded Students: {{count}}", "totalTCCapacity": "Total TC Capacity : {{upload}}/{{total}}", "withMixedHint": "Remaining students are assign in remaining test center automatically based on available seats", "testCenterLimitExceed": "Test Center Limit Exceed", "testCenterLimitExceedMessage": "Test center Maximum capacity reached. Cannot upload more then {{tcCount}} students", "mixedTcConfirmation": "Decreasing the number of students may result in a reset of the assigned student list, and they will be returned to the default state.It is necessary to reassign the students to the test center.", "importedDataIsEmpty": "The imported data is empty", "mixedTcCountError": "Mixed test center required/planned count mismatch", "testCenterAddedSuccessfully": "Test center added successfully.", "studentUploadDenied": "Student Upload Denied", "studentInActive": "Inactive Students Detected", "inactiveStudentMessage": "The system has detected inactive students in the selected group. Would you like to include or exclude these students?", "inactiveStudents": "Inactive Students", "studentName": "Student Name"}, "assignDateTimeHeader": {"maleStudents": "Male students:", "femaleStudents": "Female students:", "academicYear": "Academic year", "change": "change", "assignDateTime": "Assign date and session time for", "rescheduleDateTime": "Reschedule date and session time for", "dateAssignedSuccess": "Date and time assigned successfully", "examDuration": "<PERSON><PERSON>", "selectStartTime": "Select start time", "defaultTime": "Default time (start time of session)", "enterStartTime": "Enter start time", "duration": "{{duration}} Mins", "selectCommonTestCenter": "Select Common Test Center", "genderWiseSegregate": "Genderwise Segregate", "mixedGender": "Mixed Gender", "uploadStudentsToTc": "Upload Students To Test Center", "autoAssignTC": "Auto Assign TC", "plannedStudents": "Planned Students", "totalTcCapacity": "Total TC Capacity", "selectedTcCapacity": "Selected TC Capacity", "examCode": "Exam : {{examCode}}"}, "assignDateTimePicker": {"freeTc": "Free TC's:", "addSessionTime": "Add Session Time", "timeRepeatedSuccessful": "New session time created successfully", "examManagement": "Exam Management > Scheduler > Create timetable", "availableSeats": "{{totalFemaleTestCenter}}F - {{numberSeatsFemale}} | {{totalMaleTestCenter}}M - {{numberSeatsMale}}", "customTimeError": "The exam cannot be scheduled because the duration of the exam exceeds the selected time slot.", "sessionTimeMsg": "Test center already used in other session ({{startHour}}.{{startMin}} {{startFormat}} - {{endHour}}.{{endMin}} {{endFormat}}) If you want this TC select the above session", "actAs": "Act as {{gender}} Tc", "termsAndCondition": "Terms and Conditions", "agreeToFillMale": "Agree to fill male students first when selecting mixed test center", "mixedTestCenterWarning": "Please agree to the condition for mixed test center", "pleaseSelectMaleTc": "Please select at least one male test center.", "pleaseSelectFemaleTc": "Please select at least one female test center.", "pleaseAllocateSeatForAllTcs": "Please allocate seats for all selected test centers.", "maleSeatAllocation": "Allocated male seats are fewer than the planned count. Please allocate more male seats to proceed.", "femaleSeatAllocation": "Allocated female seats are fewer than the planned count. Please allocate more female seats to proceed.", "maleSeatAllocationExceeds": "Allocated male seats exceed the planned count.", "femaleSeatAllocationExceeds": "Allocated female seats exceed the planned count.", "noSeatsAvailable": "Seats are not available in any testcenters. Refresh list and try again to check available testcenters if any", "allocationExceeds": "Allocated seats exceed the planned count", "autoAllocateStudents": "Auto-allocate students to test centers", "manualAllocateStudents": "Manually allocate students to test centers"}, "assignInvigilatorDialog": {"addInvigilators": "Add Invigilators:", "invigilatorNameRequired": "Invigilator name required", "staffLeastAssigned": "Staff least assigned for Invigilation this academic year", "timesInvigilated": "Times Invigilated", "lastAssigned": "Last Assigned", "noStaffsFound": "No staff found", "belongsToTheSameCourse": "Belongs to the same course", "staffId": "Staff ID", "invigilatorAlreadyAssigned": "Invigilator Already Assigned!", "availability": "Availability", "availabilityDescription": "Proctor availability is determined based on DigiClass schedules and assigned proctors for the selected time slot."}, "createTimeTableHeader": {"examLocationAndProctoringType": "Exam location & Proctoring type", "createCompleteList": "To automatically create the complete list of course groups, please ensure that the type of exams for each course has been declared in", "createTimeTableExam": "Create timetable for the following type of Exam", "selectScheduleRescheduleCourse": "Select the scheduled courses to reschedule the exam", "selectScheduleCancelCourse": "Select the scheduled courses to cancel the exam", "editTimeTableExam": "Edit timetable for the following type of Exam"}, "createTimeTableList": {"invigilator1": "Invigilator 1", "invigilatorAssignedSuccessfully": "Invigilator Assigned Successfully", "plannedStudent": "Planned students successfully updated", "plannedStudentCount": "Planned student count must be greater than 0 for either male or female", "plannedNoOfStudentsMaleFemale": "Planned Students Male & Female", "plannedNoOfStudentsFemale": "Planned no of students female", "plannedNoOfStudentsMale": "Planned no of students male", "courseModule": "Course/Module", "plannedNoOfStudents": "Planned No of students", "selectedSessionTime": "Selected session time", "assignInvigilator": "Assign invigilator", "sureReschedule": "Are you sure you want to reschedule?", "cancelCourseExam": "Cancel Exam for the selected Course and Exam", "addCustomSettings": "Add custom settings", "allocatedCount": "Allocated Count", "secondaryInvigilator": "secondary invigilator", "plannedFemale": "Planned female", "plannedMale": "Planned male"}, "exportPopup": {"selectStaff": "Select staff", "selectDate": "Select Date", "selectSessionTime": "Select session time", "allSession": "All sessions time", "allTestCenter": "All test centers", "maleTestCenter": "Male test centers", "femaleTestCenter": "Female test centers", "exportList": "Export list by Test center and Schedule for", "notScheduled": "Not all the courses are scheduled. Would you like to proceed anyway?", "allInvigilators": "all invigilators", "individualInvigilators": "individual invigilator", "otherInvigilators": "Other invigilators in the same Test Center", "invigilator": "Invigilator", "signature": "signature", "dateExam": "Date of the Exam", "individualInvigilatorList": "Individual Invigilator list preview", "invigilatorType": "invigilator type", "thereIsNoTestCenterAvailable": "There is no test center available in this session time", "noRecordAvailable": "No Record Available"}, "exportPdf": {"individualCourseExport": {"student": {"testCenter": "{{testCenterName}} , RoomNumber , {{roomNumber}}", "testCenterRemote": "{{testCenterName}}", "examDate": "Exam Date : {{date}}", "sessionTime": "Session Time : {{time}}", "startTime": "Start Time : {{time}}", "signature": "Signature", "testCenterCapacity": "Test Center \n Capacity", "noOfStudents": "No.of \nStudents", "studentFileName": "{{examType}}-ALL STUDENT LIST", "allInvigilatorFileName": "{{examType}}-ALL INVIGILATOR LIST", "invigilatorFileName": "{{staffName}}-TESTCENTER LIST", "staffName": "Staff Name : {{staffName}}", "startDate": "Start Date : {{date}}", "endDate": "End Date : {{date}}", "invigilationDuty": "Invigilation Duty", "allCoursesAndAllExamType": "All Courses &  All Exam Type", "testCenters": "Test Centers", "dateOfTheExam": "Date Of The\nExam", "sessionTimeColumn": "Session Time", "examName": "Exam Name", "invigilatorType": "Invigilator Type", "otherInvigilator": "Other Invigilators\nin the same\nTest Center", "nameOfInvigilators": "Name of Invigilators", "signatureOfInvigilators": "Signature of Invigilators", "noOfAbsent": "No of Absent :", "totalPresent": "Total Present :", "total": "Total :", "secondaryInvigilator": "Secondary Invigilator"}}}, "importDigiClass": {"importFromDigiClass": "Import from DigiClass", "importStudentDigiClass": "Import Students from DigiClass", "institutionCalendar": "Institution Calendar", "selectedStudents": "Selected students", "selectedStudentsExceed": "Selected students exceeded planned count", "filledPlannedStudents": "Filled/Planned Students", "foundationGroup": "foundation group", "clearAll": "clear all", "deliveryType": "delivery type", "searchByStudent": "Search by student name, academic no., group", "studentName": "studentName", "deliveryGroup": "delivery group", "deniedStatus": "denied status", "importedFrom": "imported from"}, "studentGrouping": {"noAssessmentAuthorFound": "Please select at least one assessment author to proceed", "noStudentsFound": "Select students from student grouping or upload atleast one student to proceed", "groups": "Groups", "fromTo": "From: <b>{{ from }}</b> To: <b>{{ to }}</b>", "clickToViewGroups": "Click to view groups", "refreshGroups": "Refresh Groups", "groupName": "Group Name", "groupCode": "Group Code", "denialStatus": "Denial Status", "addStudentGroups": "Add Student Groups", "studentGroups": "Student Groups", "addStudentGroup": "Add Student Group", "editStudentGroup": "Edit Student Group", "studentGroupsRequired": "No. of Student Groups Required", "restrictAuthorAccess": "Restrict Author Access", "enterStudentGroupName": "Enter Student Group Name", "studentGroupName": "student Group Name", "chooseGender": "<PERSON>ose <PERSON>", "authorsPrepareAssessments": "Allow only selected Assessment authors can prepare assessments to this group.", "selectedItemAuthorsPrepare": "Allow only selected Item authors can able to prepare items to this group.", "enabledOnlyDesignated": "When enabled, only designated item authors will have access to view and manage item requests for this student group in the Item author’s portal.", "enabledOnlyDesignatedAssessment": "When enabled, only designated assessment authors will have access to view and manage assessment requests for this student group in the assessment author’s portal.", "selectNumberGroups": "Select the number of groups needed for this course.", "firstBatch": "QM First Batch", "importStudents": "Import Students", "createStudentGroup": "Create a Student Group", "downloadImportTemplate": "Download Import Template", "studentGrouping": "Student Grouping", "assignStudent": "Assign Student", "listOfAllGroups": "List of All Groups", "nameOfTheStudentGroup": "Name of The Student group", "noOfImportedStudents": "No. of Imported Students", "studentsUnderFirstBatch": "Students Under the First Batch Students", "symbol": "Symbol", "group": "Group", "allStudentGroup": "All Student Group", "maleGroup": "Male Group", "femaleGroup": "Female Group", "mixedGroup": "Mixed Group", "importTitle": "Import students - {{ name }}", "addStudent": "Add Student", "studentGroup": "Student Group", "maleCount": "Prefilled from DigiClass (Male)", "femaleCount": "Prefilled from DigiClass (Female)", "studentGroupDeleteMessage": "Students are already in this group. Are you sure you want to delete it?", "infoAssignEvaluators": "Use this to allocate multiple manual evaluators to a specific set of students.", "assignManualEvaluator": "Assign <PERSON>", "autoAssignInfo": "Auto-assign will distribute students evenly among the available manual evaluators based on the total student count.", "messageAutoAssignEvaluators": "Are you sure want to assign evaluators automatically?", "totalStudents": "Total Students", "selectedEvaluatorGroup": "Selected Evaluator for this Group", "selectStudents": "Select students", "selectEvaluator": "Select evaluator", "autoAssign": "auto-assign", "selectGroupAndSchedule": "Select Group And Schedule Date And Time", "assignAssessmentAuthor": "Assign assessment author", "assignedAssessmentAuthors": "Assigned assessment authors", "assignedManualEvaluators": "Assigned manual evaluators", "assignAssessmentAuthors": "Assign assessment authors", "assignManualEvaluators": "Assign manual evaluators", "manualEvaluators": "Manual evaluators", "studentGroupingSearch": "Search by name, code", "scheduleForUnselectedGroup": "Schedule for Unselected Group", "assignGroupsSchedule": "Assign Groups & Schedule", "scheduleForUnselectedGroups": "Schedule for Unselected Groups", "refreshStudentList": "Sync student group", "noStudentFound": "No student found", "saveAndNext": "Save & Next", "createGroup": "Create group", "createGroupAndProceed": "Create group & Proceed", "proceedWithoutGroup": "Proceed without group", "doYouWantToProceedWithoutGroup": "Do you want to proceed uploading without Group?", "groupDetails": "Group details", "deliveryGroup": "Delivery Group", "listOfStudentGroups": "List of Student Groups ({{ groupLength }})", "studentGroupingManagement": "Student Grouping Management", "institutionCalendar": "Institution Calendar", "deliveryType": "Delivery Type", "refreshCalendar": "Refresh calendar", "pleaseNoteCourse": "Please note the courses for the following exams:", "addStudentStatus": "Active/ Inactive", "noStudentFoundPleaseRefreshStudentListAndCheckAgain": "No student found, please refresh student list and check again", "assignedEvaluators": "Assigned Evaluators", "resetEvaluators": "Reset Evaluators", "createUnselectedGroup": "Create Unselected Group", "seat": "<PERSON><PERSON>"}, "assignProctor": {"listOfScheduledTestCenters": "List of Scheduled Test Centers", "noScheduledExamsAvailable": "No scheduled exams available", "assigningProctor": "Assign Proctor", "proctorsWillAssignedForTheseTimings": "Proctors will be assigned for these timings", "notAssigned": "Not assigned", "primary": "Primary", "multiple": "Multiple", "contact": "Contact", "selectPrimaryError": "Please select a primary proctor before assigning.", "proctorDutyAllotment": "Proctor Duty Allotment", "weekView": "Week View", "assigned": "Assigned", "assign": "+ Assign", "overlapping": "Overlapping", "editDutyTimings": "Edit Duty Timings", "outOfSlot": "Out of Slot", "notStarted": "Not Started", "live": "Live", "ended": "Ended", "noPrimaryProctorAssigned": "No primary proctor assigned"}, "proctorDutyAllotment": {"dateRange": "Date Range", "noDateRangeAdded": "- No date range added -", "proctorDutyDateRange": "Proctor Duty Date Range", "proctorDutyTimings": "Proctor <PERSON> Timings", "noProctorDutyDateRangeAdded": "- No proctor duty date range added yet -", "slot": "Slot", "proctorDuty": "Proctor duty", "classDuty": "Class duty", "assignDutyExamTiming": "Assign duty for <PERSON><PERSON>", "assignDutyProctorDutySlot": "Assign duty for Proctor Duty Slot", "duties": "Duties", "upcoming": "Upcoming", "past": "Past", "slotDetails": "Slot Details", "noSlotsCreated": "No slots created"}}, "cropImage": {"cropImage": "CROP IMAGE", "cropSave": "Crop & Save"}, "login": {"signIn": "Sign in", "emailNotYetVerified": "Email not yet verified", "reTypePassword": "Retype Password", "logout": "Logout", "password": "Password", "profileInformation": "Profile Information", "digiAssess": "Digi<PERSON><PERSON><PERSON>", "screener": "Screener", "digiAssessDescription": "AI-Powered Comprehensive Assessment Management Platform ensuring exam integrity, accurate and reliable performance analysis", "screenerDescription": "AI-Powered, Globally Compliant Platform designed to run secure entrance, placement, and qualification exams, anytime, anywhere"}, "mappings": {"common": {"noPlo": "no. PLO", "cloDone": "CLO done", "mapPloClo": "Map PLO-CLO", "addClo": "Add CLO", "addPlo": "Add PLO", "cloPloMappingGraph": "CLO - PLO mapping graph", "clo": "CLO"}, "dashboard": {"mapping": "MAPPING", "dashboard": "DASHBOARD", "mappingDashboard": "Mapping - Dashboard", "mappingTaxonomy": "Mapping - Taxonomy", "mappingGraph": "Mapping - Mapping Graph", "mappingStandardRangeSettings": "Mapping - Standard Range Settings", "areYouSure": "Are you sure?", "frameworkAutoDelete": "Once the Framework is changed the previous data under the specific framework will be deleted automatically", "yes": "yes", "manageProgramMapping": "Manage Program mapping - helps to monitor students' performance", "dependency": "Dependency: To start this process the programs, their curriculums, and the Frameworks used in the organization must be defined in the basic data definition section (basic lists).", "addFrameworkManageCloPlo": "Add framework, Manage PLOs & CLOs for each Program's curriculum", "selectProgram": "Select Program", "collapseAll": "Collapse All", "expandAll": "Expand All", "noFrameworkSelected": "No Framework Selected", "addPlo": "Add PLO", "addClo": "Add CLO", "noRecordsFound": "No records found", "mapPloClo": "Map PLO-CLO", "cloPloMappingGraph": "CLO - PLO mapping graph", "noPlo": "no. PLO", "cloDone": "CLO done", "mapType": "map type", "contentMap": "content map", "optional": "OPTIONAL", "required": "REQUIRED", "mapRequired": "MAP REQUIRED", "mapOptional": "MAP OPTIONAL", "selectRange": "select a range of years", "clo": "CLO (Course Level Outcome)", "plo": "PLO (Program Level Outcome)"}, "addPlo": {"addPloTitle": "Add PLO (Program Level Outcome) for the selected course", "selectedCourse": "for the selected course", "programLevelOutcome": {"number": "{{ mode }} NUMBER", "description": "{{ mode }} DESCRIPTION", "addClo": "+ ADD CLO", "addPlo": "+ ADD PLO"}}, "addClo": {"title": "Add CLO (Course Level Outcome) for the selected course"}, "mappingPloClo": {"title": "Map CLOs under PLOs for the selected course", "selectedCourse": "Selected Course:", "selectPrograms": "Select Programs, Framework and Mapping Type", "cloPloMapping": "CLO - PLO Mapping", "tip": "Tip: To Map the CLOs of the", "year": "year, select all Programs under different frameworks your institution follows.", "stepTwo": "Proceed to step 2", "medicalYearTableList": {"plo": "PLO", "cloName": "CLO {{ cloName }}", "l": "L", "m": "M", "h": "H"}}, "contentMapping": {"title": "Content mapping for the selected course", "contentMappingGridTab": {"plo": "PLO ##", "cloName": "CLO {{ cloName }}"}}, "taxonomy": {"taxonomy": "Taxonomy", "listOFTaxonomy": "List of taxonomies and their levels", "addTaxonomy": "ADD TAXONOMY", "listOfTaxonomy": {"noDescriptionFound": "No description found"}, "taxonomyTable": {"levels": "levels", "levelCount": "level", "levelName": "level name", "levelDescription": "level description", "addLevel": "+ ADD LEVEL", "shortCodeLevel": "short code of the level", "descriptionText": "description text in one or more lines", "deleteTaxonomy": "Are you sure you want to archive?"}, "taxonomyPopup": {"edit": "Edit", "add": "Add", "taxonomy": "taxonomy", "level": "level", "taxonomyName": "Taxonomy Name", "levelName": "Level Name", "mappingMethod": "Mapping Method", "addTaxonomy": "Add a taxonomy", "editTaxonomy": "Edit a taxonomy", "addLevel": "Add a level", "editLevel": "Edit a level", "singleMapping": "Single Mapping", "multipleMapping": "Multiple Mapping"}}, "mappingGraph": {"title": "CLO - PLO Mapping matrix for a Program curriculum", "selectProgram": "Select program", "selectCurriculum": "select curriculum", "framework": "framework", "selectedDomainTheme": "SELECTED DOMAIN/THEME", "selectedYear": "selected year", "theme": "THEME", "viewBy": "View by", "compareWithDefined": "compare with defined", "frameworkStandardRange": "Framework standard range", "rangeDefined": "Range defined for", "editRanges": "Edit ranges:", "settings": "settings", "cloMappedPlo": "NO. OF CLO MAPPED WITH PLO", "domainsThemes": "DOMAINS/THEMES", "yearsSelectedProgram": "NUMBER OF YEARS IN THE SELECTED PROGRAM", "selectYear": "Select Year", "selectDomainTheme": "Select Domain/theme", "noOfClo": "Number of CLO aligned with PLO for selected", "noOfCourses": "Number of Courses:", "themeLevelOutcome": "theme level outcome", "programLevelOutcome": "program level outcome", "frameworkMustBeSelected": "A Framework must be selected for the program's curriculum to generate the mapping graph", "cloPloNeedsToBeDone": "CLO - PLO mapping needs to be done for all the courses to generate the mapping graph", "programLevelOutcomeTable": {"edit": "EDIT", "total": "total"}, "themeGraph": {"theme": "THEME", "year": "YEAR"}}, "standardRangeSettings": {"mappingSettingsStudentCurriculumOutcome": "Mapping settings > Student Curriculum Outcome", "outcomeStandardRangeSettings": "Outcome standard range settings", "note": "Under each framework, for each program curriculum, define the maximum and minimum number of CLOs required to be mapped with PLOs under each domain/theme.", "frameworkDefinition": "Standard range of the framework defined within the framework may be applied to all the Program's curriculum that follows it.", "selectNatureOfProgram": "select nature of the program", "yearProgram": "YEAR PROGRAM {{year}}", "specificTo": "specific to:", "followingDefaultValues": "following default Values", "higherLimit": "Higher Limit", "lowerLimit": "Lower Limit", "settingSuccessMsg": "Settings updated successfully.", "standardRange": "Standard Range", "standardRangeSettings": "Standard Range Settings"}, "alignmentImpactMapping": {"contentMappingForSelectedCourse": "Content mapping for the selected course", "medicalProgram": "Medical Program", "yearLevel": "Year 2 - Level 3", "course": "Course 1", "introductoryProficientAdvanced": "I - Introductory; P - Proficient; A - Advanced", "i": "I", "p": "P", "a": "A", "mc": "MC", "mappingContent": "MC - Mapping Content", "introductory": "I - Introductory", "proficient": "P - Proficient", "advanced": "A - Advanced", "lowImpactMediumImpactHighImpact": "L - Low impact; M - Medium impact; H - High Impact", "l": "L - Low impact", "m": "M - Medium impact", "h": "H - High Impact"}, "typeOfMapping": {"typeOfLevelsAndTheirDescription": "Type of levels and their description", "nameOfLevel": "Name of level", "nameIs": "Name is", "shortCodeToBeUsedWhileAligningInTable": "Short code to be used while aligning in the table", "shortCodeIs": "Short code is", "noLevelsAddedYet": "No levels added yet"}, "contentMappingGridTab": {"plo": "PLO ##", "cloName": "CLO {{ cloName }}"}, "questionMappingSetting": {"headerName": "MAPPING GRAPH", "activeHeader": "SETTINGS"}}, "otp": {"otp": "otp", "enterOtp": "Enter OTP", "otpSentToYourMobileNumber": "OTP sent to your mobile Number", "otpSentToYourEmail": "OTP send to your email", "otpIsRequired": "OTP is required", "invalidOtp": "Invalid OTP", "expiresIn": "Expires in", "resendOtp": "Resend OTP"}, "pageNotFound": {"backToPrevious": "Back to previous", "goToHome": "Go to home"}, "reportingAndAnalytics": {"assessmentResult": {"publishResultsBySelectingSingle/MultipleCourses": "Publish results by selecting single or multiple courses", "itemsRevisionRecommended": "Items Revision Recommended", "allResults": "All Results", "pendingResults": "Pending Results", "publishedResults": "Published Results", "reEvaluate": "RE-EVALUATE", "reRevise": "RE-REVISE", "cancelRe": "Cancel re", "groupDiscard": "One or more item has been discarded in this group", "startRevision": "Start revision", "cancelReRevision": "Cancel re revision", "evaluateManuallyAlignmentWithAccreditation": "This module allows the user to evaluate manually in alignment with accreditation standards.", "startManualEvaluation": "Start Manual Evaluation", "startRevisionConfirmation": "Are You Sure You Want To Start Item Revision?", "startRevisionAdditionalInfo": "This will take you to the Item Revision page.", "startManualEvaluationConfirmation": "Are You Sure You Want To Start Manual Evaluation?", "startManualEvaluationAdditionalInfo": "This will take you to the Manual Evaluation page."}, "consolidateGradeReport": {"consolidateGradeReport": "consolidated grade report", "selectColumnAction": "select column action"}, "reportAndAnalytics": {"reportsAnalytics2020-2021": "Reports & Analytics: 2020 - 2021", "contentWillBeSoon": "Content will be soon"}, "revisionPublished": {"reRevisionForPublishedResults2020-2021": "Re-revision for published results: 2020 - 2021", "resultStatusFilter": "result status filter", "13Nov": "13 Nov", "cancelReRevision": "cancel re-revision"}, "showPublishedReport": {"showingPublishedReportsForMpRegularTerm": "Showing Published reports for MP - Regular term"}, "studentPerformanceTab": {"step2": "“Step 2” is skipped since Manual Evaluation is completed for the selected course", "youMayDoItemRevision&FinalizeResults.": "You may do the Item Revision and finalize the results.", "manualEvaluation": "manual evaluation", "beforeManualEvaluation": "(before manual evaluation)", "afterManualEvaluation": "(after manual evaluation)", "finalizedResult": "finalized result", "finalizeResult": "finalize result", "doneRevision": "Done Revision", "doneReRevision": "Done Re-Revision", "result": "result", "reviewResult": "review published result", "reviewOriginal": "review original result", "reRevision": "Re-revision", "gradeRevision": "Grade Revision", "manualEvaluationItemsPending": "This report includes only items that require manual evaluation"}, "analysisReportTabs": {"viewReport": "View published reports for the selected academic year", "originalStudent": "originalStudent", "consolidate": "consolidate", "cloPloAnalysis": "cloPloAnalysis", "studentReports": "student reports", "individualGrade": "INDIVIDUAL GRADE REPORTS", "consolidatedGrade": "CONSOLIDATED GRADE REPORTS", "cloPloAnalysisReports": "CLO & PLO ANALYSIS REPORTS", "studentImprovisationReports": "Student improvement Reports", "courseReport": "Course Reports", "numberOfTimesCourseReportGenerated": "Number of times course report generated"}, "batchItemPerformanceTabs": {"batchPerformance": "Batch performance", "itemResponses": "Item-wise responses"}, "cloPloAnalysisReport": {"searchCourse": "Search Course", "cloPloAnalysisReports": "clo & Plo Analysis Report", "selectCourse": "Select the course to view the CLO - PLO analysis and compare the reports visually", "coursePublishProgram": "The values shown below are no. of courses published/total number of courses for each program", "cloPloDetailsTab": {"cloPloAnalysis": "CLO - PLO Analysis for:", "dataBasedOnResult": "The data shown below are based on original result and before the curve up", "ploAnalysis": "PLO ANALYSIS", "multiplePrograms": "Since this course is aligned with multiple programs, select a program to list its students", "cloAnalysis": "CLO ANALYSIS ", "reportsAnalyticsAssessment": "Reports & Analytics > Assessment Results", "allPublishedReports": "All Published Reports", "selectAnalysis": "Select analysis", "marksPercentage": "Marks Percentage: {{percentage}}", "percentage": "Percentage", "cloAverage": "CLO Average: {{percentage}}", "ploAverage": "PLO Average: {{percentage}}", "collegeName": "IBN SINA NATIONAL COLLEGE", "collegePlace": "Al - Mahjar, Jeddah", "cloPloAnalysisChartAccordion": {"graphShow": "Graph shown for selected", "target": "Target Benchmark", "student": "All Students achievement", "select": "Selected"}, "tooltipTemplate": {"targetBenchMark": "Target Benchmark", "inference": "Inference", "achievement": "Achievement", "reliability": "Test Reliability", "gapWithClass": "Class Achievement Gap", "gapWithTarget": "Target Achievement Gap", "low": "Low", "high": "High", "achieved": "Achieved", "classAchievement": "Class Achievement", "standardErrorOfMeasurement": "Standard Error of Measurement"}}, "common": {"na": "NA", "studentPerformance": "Student Performance", "overall": "Overall", "overallMale": "Overall Male", "overallFemale": "Overall Female", "exam": "All Exams Aggregate", "achievement": "Achievement"}}, "consolidateGradeTable": {"cumulativeGrade": "Cumulative Grade", "cumulativePercentage": "Cumulative Percentage", "unMerge": "unmerge", "unGroup": "ungroup", "mergedColumn": "<PERSON><PERSON><PERSON>", "groupedColumn": "Grouped Column", "tooltipUnMerge": "Click to unmerge columns", "tooltipUnGroup": "Click to ungroup columns", "noOfTimeCourseReportGenerated": "Number of times course report generated", "courseMeter": "Course Meter"}, "currentReportDashboard": {"manageResult": "Manage publishing results for ongoing exams: Academic Year", "evaluate": "Evaluate Items > analyze and revise items > publish results", "reportAnalytical": "Reports & Analytics", "assessmentResult": "Assessment Results", "allReports": "All Published Reports"}, "distractorTable": {"item": "Item #: ", "itemType": "Item type:", "topic": "Topic", "description": "description:", "plo": "PLO", "all": "All", "options": "Options", "theme": "THEME:", "leadIn": "Lead In", "question": "Question:", "studentPercentageScores": " Number of students/percentage achieving scores for each option:", "show": "SHOW", "upper": "upper 1/3rd.", "lower": "lower 1/3rd.", "middle": "middle 1/3rd.", "media": "Media in choice", "attachment": "Attachment", "passPercentage": "Pass percentage", "benchmarkPercentage": "Benchmark percentage", "achievedWeightage": "Achieved Weightage"}, "exportReportsPopup": {"exportCourse": "Select the types of reports to be exported for the selected courses", "report": "Types of Reports", "exportReport": "Export reports", "resultOriginal": "Student Results - Original", "resultFinal": "Student Results - Finalized", "responseOriginal": "Students Response Report - Original", "responseFinal": "Students Response Report - Finalized", "itemAnalysisOriginal": "Item Analysis Report - Original", "itemAnalysisFinal": "Item Analysis Report - Finalized", "detailItemAnalysisOriginal": "Detailed Item Analysis Report - Original", "detailItemAnalysisFinal": "Detailed Item Analysis Report - Finalized", "selectSectionWise": "Select Section Wise"}, "finalizeStudentsPerformance": {"revertOriginal": "Revert All Decreased To Last Published", "showGender": "show by gender", "studentData": "Student Data", "itemData": "item Data", "increased": "Increased", "decreased": "Decreased", "same": "Same", "originalResult": "ORIGINAL RESULT", "showRevisedResult": "Show By Revised Result Status", "individualStudentPopup": {"individualStudent": "Individual Student Responses"}, "itemRevision": "ITEM REVISION", "itemReRevision": "ITEM RE-REVISION", "original": "Original", "lastPublished": "Last Published", "revised": "Revised", "after": "After", "after_grade_revision": "After Grade Revision", "currentItemRevision": "CURRENT ITEM REVISION", "withHold": "With Hold", "undoWithHold": "Undo With Hold"}, "functionalTable": {"highDistractor": "High functional distractor", "nonFunctionalDistractor": "nonfunctional distractor", "correction": "Needs Correction:", "discarded": "discarded", "itemAnalysis": "inference of item analysis", "capitalizeUnacceptable": "Unacceptable", "capitalizeBorderline": "Borderline", "capitalizeAcceptable": "Acceptable", "capsUnacceptable": "UNACCEPTABLE", "capsBorderline": "BORDERLINE", "capsAcceptable": "ACCEPTABLE", "retain": "<PERSON><PERSON>", "discardLow": "Discard - Low Quality Item", "discardFaulty": "Discard", "faultyNeedsCorrection": "Faulty Needs Correction", "answerKeyQuestion": "ANSWER KEY FOR QUESTION", "answerKeyQuestionGroup": "ANSWER KEY FOR GROUP QUESTION", "changeAnswerKey": "Change answer key", "oldAnswerKey": "Old answer key", "confirmAnswerKey": "Confirm answer key change", "areYouSureChangeAnswerKey": "Are you sure you want to change the answer key for this item?", "changingAnswer": "Changing the answer key will affect the results of any future evaluations and may impact student scores.", "changingAnswerMq": "Changing the answer key will affect the another item of the group, future evaluations and may impact student scores.", "changeAnswerKeySuccess": "Answer key changed successfully", "updatingAnswerKey": "Updating answer key", "pleaseWait": "Please wait", "answerKeyUpdating": "Answer key updating...", "reset": "Reset", "resetOptions": "To change the answer key, click reset option", "changeAnswerKeyWeightageTitle": "Confirm Answer Key Weightage Change", "weightageShouldProvided": "Weightage should be provided for the selected answer key", "changeAnswerKeyWeightageContent": "Are you sure you want to change the <b>Answer Key Weightage</b> for this item? </br></br>Changing the answer key weightage will afftect the results of any future evaluations and may impact student scores"}, "individualBatchStudent": {"responseSpeed": "Response Speed (seconds)", "responseStatus": "Response Status", "incorrect": "Incorrect", "filterResponse": "filter by response status"}, "individualGradeReport": {"courseName": "Course name ", "totalItem": "Total Items", "cumulativePerformance": "Cumulative Performance", "student": "Total Students", "studentPassFailed": "No. of Students Passed/Failed", "gradeDistribution": "Grade Distribution", "individualGradeReports": "Individual Grade Reports"}, "itemQualityExpansion": {"itemQuality": "Item Quality", "distribution": "distribution", "discriminative": "Discriminative Index", "quantification": "QUANTIFICATION", "insufficientData": "Insufficient data for calculation", "studentAssessment": "students must have attended the assessment", "atLeast": "At least", "gradeDistribution": "Grade Distribution Outcome", "item": "ITEM", "analysis": "ANALYSIS"}, "itemWiseResponses": {"itemNumber": "item number", "textualResponse": "TR - TEXTUAL RESPONSES ; MP - MARK POINTS ; UA - UNANSWERED ; NS - NOT STARTED ; AB - ABSENT", "studentData": "Student Data", "itemData": "item Data"}, "multiAxisPloAnalysis": {"sessionDuration": "Session Duration", "pageView": "Page Views"}, "pendingTaskTable": {"programTerm": "program - term", "publishCourse": "published courses", "finishPublish": "finalized & ready to publish", "totalPublish": "total published", "evaluateCourse": "re-evaluated courses", "revisedCourse": "re-revised courses"}, "performanceProgressPopup": {"performanceProgress": "Performance progress for", "exam": "EXAMS", "highestPerformance": "highest performance", "overallPercentage": "overall percentage", "totalStudent": "total students", "passFail": "pass/fail "}, "publishedDetails": {"publishDetail": "published details", "resultPublish": "result publish", "publish": "Published & shared with", "role": "Role", "doneBy": "done by"}, "publishPopup": {"assessmentAuthor": " Apart from the Assessment Authors of the course, share reports with", "sharedWith": "Shared with", "reportStudent": "Also share the reports with Students", "shareSpecificStaff": "share with the other specific staff"}, "publishReportsPopup": {"publishStaff": "Publish to other staff", "publishResultAssessment": "Would you like to publish the results to the respective Assessment Author of the course?", "reportStudent": "Also share the reports with students", "gradeReport": "Grade report", "outcomeReport": "Outcome report"}, "reportHistory": {"publishResult": "Published results for the previous academic years", "publishedResultsForAcademicYear": "Published results for the previous academic years", "selectExamType": "Select the Exam type and the Academic year to start viewing the results"}, "reportingExamTabs": {"exam": "All exams:", "quickLink": "Quick links", "reEvaluate": "Re-evaluation ", "reRevision": "Re-revision", "publishResults": "publish results", "requireProgramTrackSelect": " For required programs, track and select pending tasks for publishing results"}, "reportOutcomeExpansion": {"reportOutcome": "report & Outcome details", "beforeIr": "Before IR", "afterIr": "After IR", "maximumScore": "maximum score", "minimumScore": "minimum score", "rangeScore": "Range of score", "meanScore": "mean score", "medianScore": "median score", "standardDeviation": "standard deviation", "outcome": "Outcome analysis", "selectClo": "select CLO", "itemNumbers": "Item numbers", "itemWeightage": "item weightage", "achievementClo": "achievement of CLO", "achievementTarget": "Achievement Target", "achievementGap": "achievement gap", "achievementInference": "Achievement Inference"}, "reportTables": {"answeredItem": "answered items", "correctItems": "correct items", "incorrectItems": "incorrect items", "unansweredItems": "unanswered items", "reviseResultStatus": "Revised Result Status", "originalMarks": "Original Marks", "item": "item", "originalPercentage": "original percentage", "originalGRADE": "original GRADE", "finalResultStatus": "Final Result Status", "revert": "Revert to", "notAllowedToRevert": "You are not allowed to Revert Marks. Please contact admin.", "originalResults": "Original Results", "itemRevisionResults": "Current Item Revision Results", "gradeRevisionResults": "Current Grade Revision Results", "publishedResults": "Published Results", "marks": "Marks", "percentage": "Percentage", "grade": "Grade", "lastPublishedResults": "Last Published Results", "exceededMark": "Exceeded Mark", "studentStatus": "Student Status"}, "studentReport": {"markBasedAnalysis": {"depthAnalysis": "select exam for in-depth analysis", "itemWisePerformance": "Item-wise performance", "selectItemType": "Select Item type", "itemStem": "<PERSON><PERSON>", "studentAnswer": "Student's answer", "marksAchieved": "Marks achieved", "leadIn": "Lead In", "paragraph": "Paragraph", "totalPercentage": "total percentage of marks", "achievementGap": "achievement gap - class achievement", "incorrect": "Incorrectly answered", "correct": "<PERSON>rrectly answered", "unanswered": "Unanswered", "lowToHigh": "Marks low to high", "highToLow": "Marks high to low"}, "ns": "NS", "ab": "AB", "f": "F", "absent": "ABSENT", "notStarted": "NOT STARTED", "complete": "COMPLETE", "inComplete": "INCOMPLETE", "withHold": "WITH HOLD"}, "manualEvaluationCourseDashboard": {"manualEvaluation": {"selectAssessment": "Select an Assessment and evaluate student typed in responses for items"}, "manualEvaluationCourse": {"manualEvaluationCoursesDashboard": "Manual evaluation courses dashboard", "manualEvaluationAssessment": "Manual evaluation assessment", "manualEvaluationItems": "Manual evaluation items", "noOfCourses": "No. of courses for manual evaluation", "cancelRe-evaluation": "cancel re-evaluation", "manualEvaluationResult": "Manual Evaluation for Assessment Results", "revaluationResults": "Re-evaluation for published results", "searchModule": "Search by course/module", "inEvaluation": "In Evaluation", "evaluated": "Evaluated", "evaluatedItem": "evaluated / total items", "itemEvaluated": "items {{evaluatedType}}evaluated / total items", "evaluation": "Evaluation", "reEvaluation": "Re Evaluation", "reEvaluated": "Re Evaluated", "notStarted": "NOT STARTED", "Evaluated": "EVALUATED", "inReEvaluation": "IN RE EVALUATION", "evaluationNotStarted": "Evaluation not started, please start it through manual evaluation dashboard"}, "manualEvaluationItem": {"selectItem": "Select an item and evaluate", "exportAll": "export all", "reportsZip": "reports.zip", "saq": "SAQ", "eq": "EQ", "completedEvaluation": "Completed Evaluation", "completedReEvaluation": "Completed Re-Evaluation", "evaluatedBy": "evaluated by", "assignEvaluator": "Assign <PERSON><PERSON><PERSON>", "reAssignEvaluator": "Re-Assign <PERSON>", "assignEvaluators": "Assign Evalu<PERSON>", "selectedItems": "Selected Items", "selectEvaluator": "Please select one of the following options for assigning evaluator", "assignCommonEvaluator": "Assign a common evaluator", "allStudents": "Applicable to all students", "assignEvaluatorByStudent": "Assign evaluator by student-wise segregation", "whoShouldEvaluate": "Who should evaluate", "readyforEvaluation": "Ready for evaluation", "previewManualEvaluator": "Assign/Preview Manual Evaluator", "selectStudent": "Select Students", "selectStudentContent": "Do you want to select all ({{total}}) students or only the ({{limit}}) students listed on this page ?", "selectAllUnAssignedStudentContent": "Do you want to select all ({{limit}}) Unassigned students?", "selectAssignedStudentContent": "Do you want to select all ({{limit}}) Assigned students?", "selectUnassignedListedStudent": "Select Unassigned Listed Students ({{limit}})", "selectAssignedListedStudent": "Select Assigned Listed Students ({{limit}})", "selectTotalStudent": "Select All Students ({{total}})", "manualEvaluation": "Manual Evaluator", "selectedStudent": "Selected students", "assignEvaluatorValidationErrorContent": "Please assign evaluator for all students and items.", "selectItemsForEvaluation": "Choose any items to assign evaluator", "manualEvaluationConfiguration": "Configure Item Evaluation Access", "selectConfigurationOption": "Please select one of the following options for item evaluation:", "anyOneCanEvaluate": "Allow Anyone from the Course or General Users to be Selected for Evaluation", "itemAuthorShouldOnlyEvaluate": "Item Author should only evaluate", "exclusionOfAuthorsForEvaluation": "Exclusion of authors for evaluator selection list", "exclusionItemAuthorFromEvaluation": "Exclude item author from evaluation", "exclusionAssessmentAuthorFromEvaluation": "Exclude assessment author from evaluation", "exclusionBothFromEvaluation": "Exclude both item author and assessment author from evaluation", "assessmentAuthorShouldOnlyEvaluate": "Assessment Author should only evaluate", "assessmentAuthorAndItemAuthorShouldOnlyEvaluate": "Assessment Author and Item Author should only evaluate", "selectAccessToShow": "Check the boxes to hide student details during evaluation:", "yourChosenOption": "You’ve chosen the option", "wantToProceed": "Are you sure you want to proceed?", "listOfStudentsAssigned": "List of students Assigned", "assignedItems": "Assigned items", "noEvaluatorsAssigned": "No evaluators assigned", "listOfStudent": "List of students and their responses ({{studentCount}})", "selectStudentToProceedNextStep": "Select students and proceed to the next step.", "confirmRemoveStudentTitle": "Confirm Remove", "confirmRemoveStudentContent": "Are you sure want to remove ({{studentCount}}) selected students?", "evaluationCompletedSuccessfully": "Evaluation completed successfully"}, "manualEvaluationItemTable": {"displayItemNo": "Display NO (Item No)", "itemNo": "Item No", "statusMedia": "Status & Media", "statusStemMedia": "STATUS - STEM & MEDIA", "stem": "stem", "evaluate": "evaluate", "evaluating": "Evaluating", "notAddedYet": "Not Added Yet", "group": "Group", "inferenceItemAnalysis": "Inference of Item Analysis", "itemDetail": "<PERSON><PERSON>", "subjectByTopic": "Subject/Topic", "assignedEvaluator": "Assigned evaluator", "studentStatus": "Student Status", "unassignedEvaluatorsErrorContent": "For Item No.<b> {{errorMessage}} </b> no evaluator has been assigned yet. Until assigned, nobody can evaluate the item. Please take action wisely!", "notAssigned": "- Not assigned -"}, "manualEvaluationTable": {"assessmentName": "ASSESSMENT NAME", "items": "ITEMS", "examDate": "EXAM DATE", "lastUpdate": "LAST UPDATED"}, "studentResponse": {"outOf": "out of", "noResponse": " No responses available for applied filter", "unAnswer": "UA* - Unanswered", "evaluateMark": "Evaluate and enter marks for each student response:", "exportStudentResponse": "Export all student responses", "typeAnswerKey": "Type in the Answer Key", "studentResponses": "student responses", "importMarks": "import marks", "markOutOf": "mark out of", "studentDetails": "Student Details", "itemStem": "<PERSON><PERSON>", "assessmentDetails": "Assessment Details", "marks": "marks", "academicId": "Academic ID", "studentId": "Student ID", "filePasswordProtected": "File is password protected. Please remove password lock to import marks", "maxCharcLimit": "*Max Character limit is 2000", "evaluatedMarks": "Evaluated Marks"}, "invalidMarksStudentsList": {"studentIdInvalidMark": "The following student IDs have empty or invalid marks:", "importFileAgain": "Please correct the marks for these students and import the file again."}}, "studentItemWiseTable": {"overAllAssessment": "Over all Direct assessment %", "finalAssessment": "Final Indirect assessment %"}, "common": {"dataVisualization": "data visualization", "diffIndex": "Diff. Index", "discrmIndex": "Disc. Index", "correctAnswer": "Correct answer", "answerStatus": "Answer Status", "incorrectAnswer": "Incorrect answer", "totalNoOfCourse": "Total no. of Course", "totalNoOfExams": "Total no. of Exams Conducted for Courses", "totalNoOfStudentsAttended": "Total No. of Students attended", "totalNoOfStudents": "Total No. of Students survey respondents", "cumulativeStudents": "Cumulative Students Performance", "noOfStudent": "NO. OF STUDENTS", "academicId": "academic ID", "studentName": "Student name", "totalCourses": "Total courses", "total": "Total", "courses": "courses", "autoEvaluation": "auto evaluation", "noOfCourses": "No. of courses published in each program", "publishReport": "publish reports", "mentionUserName": "Mention the other @usernames for whom you need to publish ", "itemStatus": "Item Status", "prompt": "prompts", "answerKey": "Answer key", "studentResponse": "Student Response", "mark": "mark", "enterMark": "<PERSON><PERSON>", "overallStudent": "Overall Students", "courseName": "Course Name", "totalExamConducted": "Exams Conducted", "totalStudents": "Total Students", "cummulativePerformance": "Cummulative  Performance", "passFail": "No. of Passed/Failed", "performanceProgress": "Performance Progress across different exams", "passMark": "pass mark", "individualStudent": "individual student", "examData": "Exam Data", "reportsAnalyticsDashboard": "reports & analytics dashboard", "itemRevisionForCourse": "Item Revision for course", "itemRevisionForPublishedCourse": "Item Revision for published course", "gradeReportItemRevisionForCourse": "grade report Item Revision for course", "cloPloAnalysisReports": "Clo Plo analysis reports", "studentReportAnalysis": "student report analysis", "publishCourse": "publish course", "viewAttachment": "View Attachment", "individualAchievement": "Individual Achievement", "systemNumber": "System Number", "detailedReport": "Detailed-Report", "authenticationStatus": "Authentication Status", "status": "Status", "filterOriginalResults": "This filter will only apply to the student's Original Exam Result", "filterGradeRevisionResults": "This filter will only apply to the student's Grade Revision Result", "reportsNotAvailableWithoutOutcomeMappings": "Reports are not available because exams are updated without outcome mappings", "assessmentResults": "Assessment Results", "courseReports": "Course Reports - NCAAA", "courseAnalysisDashboard": "Course Analysis Dashboard", "studentGroup": "Student Group", "selectStudentGroups": "Select Student Groups"}, "surveyReport": {"courseEvaluationSurveyReport": "Course Evaluation Survey Reports", "courseEvaluationReportFor": "Course Evaluation Survey Reports for", "surveyItemWise": "survey - item-wise", "surveyStudentWise": "survey - student-wise ", "insightsOutcomeWise": "insights - outcome-wise", "surveyDashboard": "survey Dashboard", "courseEvaluationReport": "course evaluation reports", "surveyReportSettings": "Survey report settings", "surveyQuestionsSettings": "Survey questions settings", "itemWise": {"generalSurvey": "General Survey", "outComeSurvey": "Outcome Survey", "openEndedSurvey": "Open Ended Survey", "export": "Export", "itemType": "item type", "option": "option", "values": "values", "male": "male", "female": "female", "totalResponse": "Total Response", "maleCount": "male ({{count}})", "femaleCount": "female ({{count}})", "totalCount": "total ({{count}})", "malePercentage": "male %", "femalePercentage": "female %", "totalPercentage": "total %", "noExamType": "No exam results published under selected attempt type", "readMore": "read more", "readLess": "read less", "maleResponse": "MALE STUDENT's </br> RESPONSE", "feMaleResponse": "FEMALE STUDENT's </br> RESPONSE", "exportWithStudentId": "With student academic id", "exportWithoutStudentId": "Without student academic id", "studentResponseForOESurvey": "Student response for open ended survey"}}, "studentItemWise": {"student": "All students", "selectClo": "Select CLO", "cloDomain": "CLO domains", "individualClo": "INDIVIDUAL CLO", "selectQuestion": "Select Question", "outcomesNumber": "Outcomes Number", "directAssessment": "Direct Assessment", "indirectAssessment": "Indirect Assessment", "dataCloDomain": "data shown for Selected CLO domains", "dataSelectedStudent": "Data shown for selected student", "markBased": "Marks Based ANALYSIS", "outcomeBased": "Outcome Based ANALYSIS", "selectTerm": "Select term", "selectAndView": "Select the Student and open each course to view the report", "studentReports": "student Reports", "reportsAnalytics": "Reports & Analytics > Assessment Results", "studentCourseListed": "For each student inside each program, courses will be listed for which the student has appeared for.", "describeAnatomical": "describe the different anatomical parts of the human body", "header": "INDIVIDUAL STUDENT ANALYSIS:"}, "studentSurvey": {"describeDifferentAnatomicalPart": " To what extent can you describe the different anatomical parts of the human body?"}, "surveyRatingTable": {"detailDescribe": "To what extent you can describe the different anatomical parts of the human body?", "likertScale": "Likert Scale", "average": "Average"}, "surveySetting": {"surveySetting": "Survey Setting", "studentSkip": "Students can skip questions or entire survey", "startSurvey": "Start survey", "whenSurveyStart": "When should survey start", "whenSurveyStartLate": "When Student comes late attend the survey after the exam", "note": "Note: “Don't Allow” will affect the students exam", "beforeExam": "Before Exam", "afterExam": "After Exam", "skipSurvey": "Skip Survey", "skipEntireSurvey": "<PERSON><PERSON> En<PERSON>re Survey", "skipSurveyQuestions": "Skip Survey Questions", "allow": "Allow", "dontAllow": "Don't Allow"}, "insightWiseTable": {"studentWiseAnalysis": "Student-Wise CLO Analysis", "overallDirectAssessment": "Over all Direct assessment %", "overallIndirectAssessment": "Final indirect assessment %", "totalOverAll": "Total Over all %"}, "studentImprovisation": {"studentImprovisationReportOverall": "Student Improvement  Report - Overall", "individualReports": "individual reports", "needFocus": "need focus", "doingGreat": "doing great", "better": "better", "studentAttainment": "Student Attainment", "detailedReport": "Detailed Report", "examTypeInfo": "Filters should have default values based on Published results", "selectExamReports": "Select Exam Reports", "gradeReports": "Grade Reports", "studentImprovisationReports": "Student Improvement Reports", "view": "View", "viewReportToolTip": "Report won't be visible if student absent/ scored  '0' mark"}, "sirIndividual": {"sirReportIndividual": "student Improvement report - individual", "belowBenchmark": "below benchmark", "aboveBenchmark": "above benchmark", "belowBatchAttainment": "below batch attainment", "aboveBatchAttainment": "above batch attainment"}, "individualReportShare": {"shareWithStudent": "Share with students", "shareWithStaff": "Share with staffs"}, "componentAnalysisReport": {"title": "Component analysis report", "createNewAnalysis": "create new analysis", "analysisThroughMatrix": "Analysis can be done by connecting the flows through matrix", "startConfiguration": "start configuration", "continueConfiguration": "continue configuration", "editConfiguration": "edit configuration", "creatingNewAnalysis": "Creating new analysis", "nameOfTheAnalysis": "Name of the analysis", "courseAnalysis": "course analysis", "analysisMatrix": "Start creating an analysis that can be done by connecting the flows through matrix", "editAnalysis": "edit analysis", "analysisAlreadyCreated": "Analysis already created", "allStudentsAbsent": "This exam can't be used for course analysis due to the absence of all students"}, "dynamicMappingConfiguration": {"componentExamSelection": "Component and exam selection", "matrixMapping": "Matrix Mapping", "dataVisualization": "Data visualization", "componentSelection": "Component selection", "selectComponent": "Select component", "examSelection": "Exam selection", "internal": "internal", "external": "external", "selected": "Selected", "onlyRelatedComponents": "Only related components of this course is listed", "chosenComponent": "Chosen component", "giveName": "Give a name here...", "noExam": "no exam", "listOfAnalysis": "list of analysis", "internalExams": "internal exams", "externalExams": "external exams", "weightageApplied": "weightage applied", "benchmarkApplied": "benchmark applied", "subAnalysisNote": "Exams created under any sub-analysis cannot be selected.", "createdUnder": "Also created under", "notScheduledForAllSelectedGroups": "Not scheduled for all selected groups!", "selectAll": "Select All Exams for “{{examName}}”", "unSelectAll": "Unselect All Exam for “{{examName}}”", "numberOfExamsFound": "({{coursesLength}}) Exams found in <b>“{{examName}}”</b> under Exams conducted for single/multiple student groups individually, To select this exam, please select all related exams which has all student groups involved.", "unselectingExams": "Unselecting <b>“{{examName}}”</b> under <PERSON><PERSON> conducted for single/multiple student groups individually, will unselect all related exams. Are you sure you want to unselect?", "studentGroupsInvolved": "Student Groups Involved", "unableToSelect": "Unable to select if not scheduled for all selected student groups", "allSelectedGroupsInvolved": "All selected groups involved", "selectTheExamsType": "Select the exams type", "unableToMerge": "Unable to merge the groups since one of the groups is already used in a different analysis."}, "taggingComponentPopup": {"taggingComponent": "Tagging components with exams", "applyTagging": "apply tagging", "chooseExam": "Choose exam", "itemNumber": "item number", "taggedComponents": "Tagged components", "clearAll": "Clear all", "tagWith": "tag with", "tagAtlLeastOneItem": "You need to tag with at least one item", "taggingUpdated": "Tagging applied successfully"}}, "studentManagement": {"studentProfileDataUpdate": "Student Profile Data Update", "studentDashboardAnnouncements": "Student Dashboard | Announcement", "studentDashboardPastExams": "Student Dashboard | Past Exams", "studentDashboardExamResults": "Student Dashboard | Exam Results", "studentDashboardUpcomingExams": "Student Dashboard | Upcoming Exams"}, "basicAlert": {"misMatchInvalidHeader": "Mismatched / Invalid header Data"}, "sideNav": {"youAre": "you are", "on": "on", "autoSave": "auto save", "profile": "Profile", "verify": "Verify", "verifyProfileForm": "Verify profile form", "notifications": {"notifications": "Notifications", "markAllAsRead": "Mark all as read", "markAsRead": "<PERSON> as read", "due": "due", "fetchingNotifications": "Fetching notifications...", "noNotificationsFound": "No notifications found", "createRequest": "Create Request", "create/Review/Request": "Create/Review Request", "reviewRequest": " Review Request", "finalizeRequest": "Finalize Request", "createPending": "Create Pending", "reviewPending ": "Review Pending", "dueToday": "Due Today", "arrivedTillNow": "Arrived Till Now", "courseId": "course id"}}, "textEditor": {"dear": "Dear", "studentFirstName": "Student First Name", "visitLink": "Please visit the following link and login for online registration with DA", "completeRegistration": "You are requested to complete registration and finish verifying your profile data by", "3Days": "{{date}} (3 days from sending this email)", "thankYou": "Thank you", "deanUniversity": "Dean of university", "facultyFirstName": "Faculty First Name", "examControlOffice": "Exam Control Office"}, "confirmDialogBox": {"contentTitle": "You are about to permanently delete this", "messageText": "Test centers and Invigilators assigned for the selected date and session time will be lost.", "messageInfo": "You have to assign Invigilators again to all the test centers, once the new date and session time has been picked.", "cancelExam": "Proceeding to cancel exam will be permanent.", "confirmCancelExam": "Are you sure want to proceed?", "assessmentConfiguration": "The Assessment configuration is currently active; you cannot archive it now. Select a configuration not in use or replace the active configuration you want to archive", "okay": "Okay", "okay!": "Okay!", "notRecover": "You will not be able to recover this", "deletePermanently": "Delete permanently", "archiveThis": "Are you sure you want to archive this ({{name}})?", "cardStatusChange": "Card status will change from", "ongoingWithMe": "to Ongoing with me", "removeSession": "Remove Session", "removeSessionMessage": "Are you sure you want to remove the session? Once removed, the data cannot be retrieved.", "removeAuthorTitle": "Remove Author", "removeAuthorMsg": "Are you sure want to unassign the author ?"}, "conflictDialog": {"importBanner": "Import DB from Banner"}, "createDialog": {"addTestCenter": "Add test center", "addVirtualTestCenter": "Add virtual test center", "testCenterName": "Test Center Name", "testCenterRequired": "Test center name required", "invalidTestCenter": "Invalid test center name", "selectGenderTestCenter": "Select Gender reserved for Test center", "genderRequire": "Gender required", "roomNumberRequire": "Room number required ", "location": "Location ", "locationRequire": "Location required", "capacity": "Maximum Capacity", "capacityRequire": "Capacity required ", "reserve": "Reserved", "reserveRequire": "Reserved required ", "reason": " Reason", "reasonRequire": "Reason is required", "testCenterDelete": "Test center deleted successfully", "clickAddTestCenter": "Click on “+add test center” to get started", "testCenterCreatedSuccess": "Test center created successfully", "testCenterUpdateSuccess": "Test center updated successfully", "maxCapacityExceeded": "Maximum capacity limit exceeded", "totalReservedCapacity": "Total reserved capacity should be less than maximum capacity", "testCenterCapacity": "Test Center Capacity", "tcName": "Test Center {{tcName}}", "rescheduleNecessaryCapacity": "Please reschedule the following exam and make the necessary capacity", "rescheduleTheExam": "Please reschedule the following exam", "course": "Course", "examType": "Exam type", "attemptType": "Attempt type", "scheduledDate": "Scheduled Date", "commonTcNote": "*Note: Common test center can act as genderwise or mixed based on selection of schedule needs", "shouldBeAtleastOne": "Capacity should be atleast one", "reserveNotZero": "Reserve should not be zero", "reserveReasonNotEmpty": "Reserve reason should not be empty", "reserveCountNotEmpty": "Reserve value should not be empty"}, "userManagement": {"student": {"studentManagement": "Student Management", "profileUpdateTitle": "Student Management > Profile Update", "studentCorrectionTitle": "Student Management > Pending > Profile Data Verification > Correction", "title": "Students Management", "importStudent": "import student", "importStudentsDb": "Import Students DB from Banner", "csvData": "student.csv file data", "allStudents": "All Students ({{ count }})", "ValidatedStudents": "Validated ({{ count }})", "pendingStudents": "Pending Validation ({{ count }})", "studentSelectedPage": "{{count}} student selected on this page", "facultySelectedPage": "{{count}} faculty selected on this page", "facultiesSelectedPage": "{{count}} faculties selected on this page", "studentSelected": "student selected on this page", "studentTemplate": "STUDENT_TEMPLATE", "studentProfileUpdate": "Student Profile Update", "studentResponseXlsx": "student-responses.xlsx", "enterAcademicNo": "Enter Academic No (optional)", "assignProgramName": "assign program", "detailMarks": {"selectCourse": "Result Details of selected Course:", "examDetails": "exam details", "itemTypeWiseMarks": "Item type-wise Marks"}, "studentBasicDetailsUpdate": {"profileDetail": "Please provide the following Profile details"}, "studentPortalTab": {"detailExamResult": {"detailsOfSelectedCourse": "Details of selected course:", "detailedCourseList": "Detailed list of results grouped by each course", "levelErrMessage": "Selected year / level not available", "selectAttemptType": "Select attempt type"}, "scheduleExamDetails": {"scheduleExamDetails": "Schedule exam details", "schedule": "Schedule", "fetchingExams": "Fetching your scheduled exams...", "doNotHaveScheduledExams": "You do not have any scheduled exams", "tc": "tc", "coursesLink": "courses & link to its outcomes", "notYetPublished": "not yet published", "notShared": "not shared", "lauchUsingSEB": "Launch Exam Portal Using SEB", "launchExam": "Launch Exam Portal", "downloadSEBOptions": "Download Safe <PERSON><PERSON>er"}, "notificationTitle": "Notification Title", "notificationContent": "Notification Content", "info": "Info", "results": "Results", "downloadResultTemplate": "Download Result Template", "examStarts": "<PERSON><PERSON>s", "examSchedule": "Exam Schedule", "link": "Link", "tc": "Test Center"}, "studentVerifyDetails": {"information": "Please tell us if the information we have about you, is correct or wrong", "correctOrWrong": "Tap \"correct\" or \"wrong\"", "verifyData": "Verify Data"}}, "faculty": {"importFaculty": "import faculty", "importFacultyData": "Import Faculty Data", "testInputValue": "Test input value", "allFaculties": "All Faculties", "facultySelectedPage": "{{count}} Faculties selected on this page", "facultyTemplate": "FACULTY_TEMPLATE", "facultyUpdateProfile": "Faculty Update Profile", "adminFacultyProfileUpdate": {"facultyProfileUpdate": "Faculty Profile Update", "headerName": "Faculty Management > Profile Update"}, "facultyAdminUpdate": {"correctionRequired": "Correction required for identified data", "headerName": "Faculty Management > Pending > Profile Data Verification > Correction"}, "reallocateTopicsPopup": {"reallocateResponsibility": " Reallocate active responsibilities before deactivating the user", "responsibilityRequirement": "*Some of the responsibilities do not have an alternate user for reallocating. Add users with the required permissions first.", "toSaveReallocation": "To save the reallocations and deactivate later,", "understandReallocation": " I understand once reallocations are saved, they cannot be changed", "saveReallocations": " Save reallocations", "rolesResponsibilities": "Roles & Responsibilities", "reassignUser": "Re-assign User", "selectUser": "Select User", "noAlternateUsersFound": "No Alternate Users found", "reallocated": "Reallocated", "noRolesAssigned": "No roles assigned", "deactivateProfile": "deactivate < {{ userName }} >'s profile", "activateProfile": "activate < {{ userName }} >'s profile"}}, "assignRoleTopic": {"staffsAssigned": "Staffs - Assigned ({{ count }})", "staffsUnassigned": "Staffs - Unassigned ({{ count }})", "headerTitle": "Assign Roles & Permission", "assignedRoleSuccessfully": "Assigned role successfully", "assignedTopicSuccessfully": "Assigned topic successfully", "pleaseSelectFacultyAssign": "Please select a faculty to assign", "programDetailsAssignedSuccessfully": "Program details assigned successfully", "noProgramTypesAvailable": "There is no program types available, to continue this action just save on it", "roleChangedSuccessfully": "Role changed successfully", "assignRolesPermission": "DigiAssess | Assign Roles & Permission - {{ textLabel }}", "assignFaculty": {"staffAssign": "Pick a staff to assign", "noFacultiesFound": "No faculties found"}, "assignTopic": {"selectRoleToAssign": "Select a role to assign", "selectRoleAsDefault": "Make Selected Role As De<PERSON>ult", "noRoleData": "No role data found", "assignSubjectAndTopics": "Pick and Assign the subject and its topics to the selected role", "selectProgram": "Start by selecting the program and other drop downs to find the required subject and its topics"}, "roleTable": {"searchText": "Search by faculty name", "manageRole": "Manage Roles & Subjects Permissions", "defaultRole": "Default Role", "noRolesAssigned": "No roles assigned", "assign": "assign", "fullName": "Full Name", "topic": "Topic", "level": "Level", "curriculum": "Curriculum", "term": "Term", "facultyName": "Faculty Name", "defaultRoleUpdated": "Default role updated successfully"}, "role": {"describeRole": "Describe role", "selectUiElement": "Select UI Elements", "selectPermission": " Select Permissions", "permissionForParentElement": "Permissions given to a parent UI element gets automatically applied to all the children. But a permission applied to a child UI element does not get applied automatically to the parent.", "noPermissionFound": "No Permissions Found", "editPermission": "EDIT PERMISSIONS", "permissionList": {"restore": "restore", "deSelectAll": "Deselect All"}}}, "rolesAndModules": {"rolesPermission": {"selectRole": "Select role(s) to import permissions from", "selectAtLeast1": "must select at least 1", "chooseCategory": "Choose a category", "deleteRole": "Delete Role", "areYouSureToDelete": "Are you sure you want to delete this role", "fromEntry?": " from entry?", "thisCannotBeUndone": "This cannot be undone.", "selectRoles": "Select role(s)", "noCategory": "No category selected", "rolesPermission": "roles Permission", "roles&Permissions": "Roles & Permissions", "clearCategory": "Clear Category", "pleaseFillOutThisField": "Please fill out this field", "deActivatingRole": {"deActivatingRole": "Deactivating a role", "deactivate": "You are about to deactivate", "noFaculties": "No faculty assigned to this role.", "defaultRoles": "The following list of staff are assigned as “default” for this role.", "changeDefaultRole": "Kindly change their default role before deactivating", "followingStaffRole": "The following staff will lose access to DigiAs<PERSON><PERSON> as they don't have any alternative role or they don't have any active roles to assign"}, "deActivated": {"the": "the", "roleDeactivated": "role has been deactivated/updated by the Admin", "cannotProceed": "You may not be able to proceed further with this role.", "in": "in", "seconds": "seconds", "youWillBeLoggedOut": "You will be logged out automatically", "youWillBeRedirectedToDefaultRole": "You will be redirected to your default role <{{role}}>'s homepage automatically"}}, "page": "Page", "tab": "Tab", "subTab": "SubTab", "assessmentAuthor": "assessment author", "itemAuthor": "item author", "medicalEducationist": "medical educationist", "subjectExpertReviewer": "subject expert reviewer"}, "common": {"exportStudent": "Export Student", "selectRegistrationStatus": "Select Registration Status", "selectStatus": "Select Status", "search": "Search by academic id, name, email", "userStatus": "State", "dob": "Date of Birth", "passportNumber": "Passport Number", "nationalId": "National ID / Iqama no.", "enrollYear": "Enrolled Year", "enrollYearBr": "Enrolled Year", "profileVerification": "Profile Data verification", "requestSent": "Request Sent", "biometricVerification": "Biometric Verification", "sendMail": "SEND EMAIL", "verificationRequestPending": "Verification Request Pending", "profileVerificationPending": "Profile Verification Pending", "biometricVerificationPending": "Biometric Verification Pending", "selectAll": "Select All", "cancelSelectAll": "Cancel Select All", "deSelectAll": "Deselect All", "active": "Active", "deactivate": "deactivate", "activate": "activate", "inactive": "Inactive", "blocked": "Blocked", "nationalIdBr": "National ID /", "iqamaNo": "Iqama no.", "academic": "Academic", "number": "Number", "importStudentData": "Import Student Data", "testInputValue": "Test input value", "errorMsgGettingStudents": "Error getting all students data from the server.", "sendingEmail": "Sending email...", "nameOfFile": "Name of the file", "provideDetail": "Provide Your Detail", "dashboard": "DASHBOARD", "examSchedule": "EXAM SCHEDULE", "examResults": "EXAM RESULTS", "logo": "LOGO", "errGettingFromServer": "Error getting data from the server.", "enterTimeDuration": "Enter time duration to add to the list (in minutes)", "valid": "<PERSON><PERSON>", "pending": "Pending", "taskPending": "task pending", "correctionRequired": "correction required", "inValidFileType": "You have uploaded an invalid file type. Please upload with a valid file type. \n File Format: CSV", "templateMismatchFaculty": "Template mismatch on faculty import", "dataVerifiedSuccessfully": "Data Verified Successfully", "templateMismatchStudent": "Template mismatch on student import", "mismatchedHeaderData": "Mismatched / Invalid header Data", "errorGetAllStudent": "Error getting All Students", "errorOnImportStudent": "Error importing Students", "fileSizeLimit": "File size has exceeded the max. limit of 6MB. Please upload a smaller file", "errorFileType": "You have uploaded an invalid file type. Please upload a valid file type \n File Format: PNG, JPG, TIFF", "uploadedSuccessfully": "Uploaded successfully", "imageDeletedSuccessfully": "Image deleted successfully", "markedSuccessfully": "Marked Successfully", "upcomingExams": "UPCOMING EXAMS", "pastExams": "PAST EXAMS", "announcements": "ANNOUNCEMENTS"}}, "assessmentStatus": {"onGoing": "on going", "notStarted": "not started", "completed": "Completed", "ongoingWithAssessmentAuthor": "ongoing with assessment author", "ongoingWithItemAuthor": "ongoing with item author", "published": "published", "cancelled": "cancelled", "revisionCompleted": "revision completed", "inRevision": "in revision", "recommended": "Recommended", "notRequired": "Not Required", "curveLeft": "<PERSON><PERSON><PERSON> Skewed left", "curveRight": "<PERSON><PERSON>ve Skewed right", "withdrawn": "Withdrawn", "correctionDone": "Correction Done"}, "itemTypes": {"comprehensiveQuestions": "Comprehensive Questions", "multipleChoiceQuestion": "Multiple Choice Question", "trueOrFalse": "True Or False", "shortAnswerQuestions": "Short Answer Questions", "extendedMatchingQuestions": "Extended Matching Questions", "explanatoryQuestions": "Explanatory Questions", "comprehensiveShortAnswerQuestion": "Comprehensive Short Answer Question", "matchingQuestions": "Matching Questions", "hotspotQuestions": "Hotspot Questions", "mixedQuestions": "Mixed Questions", "multipleRightAnswer": "Multiple Right Answer Question", "clinicalPracticalExam": "Clinical Practical Exam", "orderingQuestion": "Ordering Question", "dragAndDropSequencing": "Drag And Drop Sequencing", "cq": "CQ", "mcq": "MCQ", "tf": "TF", "saq": "SAQ", "mq": "MQ", "emq": "EMQ", "eq": "EQ", "csaq": "CSAQ", "hq": "HQ", "mst": "MIT", "mit": "MIT", "mra": "MCQ-MRA", "cpe": "CPE", "oq": "OQ", "ddq": "DDQ"}, "profileSettings": {"personalSettings": "Personal Settings", "changeYourLogo": "Change your Logo", "uploadYourLogo": "Upload your Logo", "collegeName": "College Name", "enterCollegeName": "Enter college name", "enterCollegeAddress": "Enter college address", "language": "Language", "defaultTimeZone": "Default Time zone", "fileShouldBeLess": "The file should be less than 1MB and format can be .jpeg .PNG", "selectDefaultLanguage": "Select default language", "selectDefaultTimezone": "Select default timezone"}, "assessmentSavePopUp": {"total": "Total", "sections": "Sections", "count": "Count", "time": "Time", "marks": "Marks", "withReview": "With Review", "withoutReview": "Without Review", "equal": "Equal", "unEqual": "Unequal", "none": "None", "withoutSection": "Without Section", "plannedCount": "Planned Count", "totalMinutes": "Total Minutes", "totalMarks": "Total Marks", "itemCount": "<PERSON><PERSON>"}, "403": {"permissionDenied": "Permission denied!", "permissionDeniedMessage": "You do not have permission to access the requested data. kindly add permission or switch role to continue", "backToHome": "back to home"}, "testSpecPopup": {"testSpec": "Define/Edit Test Specification", "selectedCourse": "Selected Course", "selectExam": "Select Exam type & define Items count for each Item type", "itemsDistribution": "Items Distribution", "itemsUnderClo": "No. of Items under each CLO", "tasksUnderClo": "No. of Tasks under each CLO", "assessmentSettings": "Assessment Settings", "subjectsTopics": "Subjects/topics", "total": "Total", "itemWeightage": "Item Weightage", "taskWeightage": "Task Weightage", "mcq": "MCQ", "saq": "SAQ", "eq": "EQ", "tOrF": "T or F", "mq": "MQ", "emq": "EMQ", "cq": "CQ", "totalGroups": "Total Groups", "totalStations": "Total Stations", "totalItems": "Total Items", "totalTasks": "Total Tasks", "overallItemsCount": "Overall Items count", "overallTaskCount": "Overall Task count", "listOfExam": "List of Exam with attempt type", "quiz": " > Quiz (Regular)", "selectedConfiguration": "Selected Configuration for all such exam type (Only admin can change)", "listExam": "List of Exam with attempt type", "activeVersion": "Active Version", "testSpecification": "Test Specification", "enableDisable": "Enable/Disable Test Specification", "saveAsOverwrite": "Save as overwrite", "overrideNote": "Note: “It will not affect the scheduled exams”", "overrideDraftNote": "Note: “It will Change the existing Drafts settings”", "mismatchError": "count mismatch in {{itemTypeCode}} item type", "chooseTestSpecification": "Choose Test Specification", "createNew": "Create New", "item": "<PERSON><PERSON>", "task": "Task", "group": "Group", "station": "station", "drafts": "Drafts", "manualCloTitle": "Are you sure you want to checked / unchecked ?", "manualCloMessage": "All of the data that you are mapping in CLO is reset if the checkbox is checked/unchecked.", "enterCloManually": "Enter CLO Manually", "enterBelowTotalItems": "value should be less than total number of items", "selectGroupEachSection": "please select at least one group in each section", "withSelectItemType": "Cannot proceed without selecting '{{code}}' itemType", "noOfQuestion": "No. of questions", "newVersionCreateMessage": "New version successfully created", "ossaiExam": "(OSSAI EXAM)"}, "itemDistribution": {"itemDistributionHeatMap": "Item Distribution - Heat Map", "overallExamwise": "Overall Examwise", "topics": "Topics", "noOfQuestion": "No. of questions", "individualExam": "Individual Exam", "regular": "Regular", "special": "Special", "countMismatchError": "Total count mismatch.kindly insert the number lesser then the given total count", "addGroup": "Add Another Group", "addStations": "Add Another Stations", "minimumTwoRequired": "Minimum two item required", "groupMismatchError": "Total group number mismatch mapped group", "stationGroupMismatchError": "Total group number mismatch with station group", "groupCountMismatch": "Total group count and subjet group count mismatch", "moveSectionTitle": "Do you want to proceed with this change?", "moveSectionMessage": "You are changing the item type in Section {{currentSection}} to {{changeItemType}}. This will swap the current {{currentItemType}} in Section {{currentSection}} to Section {{changeSection}}."}, "testSpecPdf": {"fileDetails": "File Details", "reportName": "Report Name", "exportDate": "Export Date", "exportTime": "Export Time", "courseDetails": "Course Details", "academicYear": "Academic Year", "courseCode": "Course Code", "courseName": "Course Name", "selectedSubject": "Selected Subject", "selectedItemType": "Selected Item type"}, "ossai": {"ossai": "OSSAI", "viewSettings": "View Settings", "editSettings": "Edit Settings", "createOssaiExam": "Create o<PERSON><PERSON>", "createExam": "Create Exam", "ossaiExamSettings": "OSSAI Exam <PERSON>tings", "stationDuration": "Station Duration", "minutes": "Minutes", "stations": "Stations", "station": "Station", "task": "Task", "mark": "<PERSON>", "ossaiExam": "ossai exam", "resetExam": "reset exam", "examSettings": "<PERSON><PERSON>", "emptyStationMarks": "Total station mark should not be empty", "emptyDuration": "Duration should not be empty", "emptyStationTask": "Station Task should not be empty", "notMatchStationMarks": "Station task marks not match with total marks", "enterTotalMark": "Enter total mark", "enterStationDuration": "Enter station duration", "examOssai": "{{ossai}} exam", "novToOssaiExam": "Go to the {{ossai}} Quick Links Exam Page to access exams and resources.", "ossaiDashboard": "{{ossai}} dashboard"}, "passwordValidatorErrors": {"minLength": "Password should be at least 8 characters long.", "uppercase": "Password should contain at least one uppercase letter.", "lowercase": "Password should contain at least one lowercase letter.", "digit": "Password should contain at least one digit.", "specialCharacter": "Password should contain at least one special character."}, "externalMarks": {"externalMarks": "external marks", "listOfCourses": "list of Courses for external exam", "importantInformation": "important information", "importantMessage": "This page is exclusively for adding marks from exams conducted outside DigiAssess.", "importantAssessmentMessage": "This assessment does not have a items so cannot do item revision, if you want item revision please create item in external exam for this course", "courseAuthors": "course authors", "addMore": "add more", "addAuthors": "add authors", "initiateBy": "initiate by", "addDetails": "add details", "noExamCreated": "No External Exams are Created", "createNewExternalExam": "create new external exam", "testCenterDetails": "Test Center Details", "externalInvigilator": "External Invigilator", "staffName": "Staff Name", "place": "Place", "addAnotherInvigilator": "Add another invigilator", "importTemplate": "Import from template", "addIndividualStudent": "Add individual Student", "sNo": "S No.", "qNo": "Q No.", "addQuestionWiseMarks": "Add question wise marks", "pleaseAddAssessment": "Please add a assessment details to add a total mark for the individual students", "viewAssessmentDetails": "Click to Add / View Assessment Details", "importMarksInfo": "While importing leave the marks field empty or give - if the student is absent", "floorName": "Floor Name", "addMoreStudents": "Add more students", "outcomeMetrics": "Outcome Metrics", "addSessionDetails": "add session details", "addStudents": "add students", "addMarks": "add marks", "addSession": "add session", "legend": "legend", "markAllPresent": "<PERSON> all as Present", "markAllAbsent": "<PERSON> all as Absent", "topics": "Topics", "subjects": "Subjects", "importResults": "Import Results", "addCourseAuthors": "Add Course Authors", "allStaffs": "All Staffs", "courseStaffs": "Course staffs", "moveToanotherSession": "Move to another session", "completeEvaluation": "Complete Evaluation", "addToReport": "Add to Report", "compareExam": "compare exam", "showAllExam": "show all exam", "mergeColumn": "merge column", "groupColumn": "group column", "goToReport": "Go to report", "pleaseSelectStudent": "Please select students", "importStudents": "Import Students", "importDigiClass": "Import from Digiclass", "importPrevious": "Import students from previous exams", "confirmTitle": "Confirmation: Adding to Report", "confirmMessage": "Are you sure you want to add this to the report? Once added, you cannot change the data here.", "totalMarksExceeds": "Total marks exceeds.Please enter valid total marks", "addedToReport": "Added to report", "noSessionsFound": "No sessions are found to move students into another session", "itemCountMisMatch": "Required Items count mismatch", "itemMarksMisMatch": "Required Items mark mismatch", "emptyDataFound": "Marks should not be empty", "emptyDateFound": "Session date not found please enter valid date", "emptyTCNameFound": "Please enter test center name", "itemsRequired": "Items Required", "marksRequired": "Marks Required", "deleteConfirmation": "Do you want to delete this course?", "studentCourseGrouping": "Student Course Grouping", "assessmentDetails": {"assessmentDetails": "Assessment Details", "viewAttachment": "View Attachment", "extractFullAssessment": "Extract Full Assessment", "qno": "QNo.", "topic": "Topic", "itemType": "Item Type", "stem": "<PERSON><PERSON>", "mappingTaxonomy": "Mapping / Taxonomy", "marks": "Marks", "addNewItem": "Add new item", "attachFrom": "<PERSON>ta<PERSON> from", "questionBank": "Question Bank", "mediaOrDocument": "Media or Document", "documentType": "(.pdf, .doc, .jpg)", "viewMoreContent": "Tapping or clicking anywhere on and shows the complete stem.", "viewMore": "VIEW MORE ...", "viewStemAttachment": "View Attachment", "uploading": "Uploading", "previousPage": "Back to previous page"}, "addExternalInvigilatorPopup": {"addExternalInvigilator": "Add External Invigilator", "editExternalInvigilator": "Edit External Invigilator", "addInternalStaff": "Data from user management (Add internal staff)", "enterStaffName": "Enter Staff Name", "enterEmailId": "Enter Email Id", "enterMobileNumber": "Enter Mobile Number", "place": "place"}, "addViewAssessmentDetails": {"addAssessmentDetails": "Add Assessment Details", "editAssessmentDetails": "Edit Assessment Details", "splitUpBasedOnTotalNumberOfItemsEntered": "Split-up based on total number of items entered", "totalMarks": "Enter total no.of Marks", "totalItems": "Enter total no.of items", "overall": "overall", "splitUp": "split-up", "previousPage": "Back to previous page", "addExternalInvigilatorPopup": {"addExternalInvigilator": "Add External Invigilator", "editExternalInvigilator": "Edit External Invigilator", "addInternalStaff": "Data from user management (Add internal staff)", "enterStaffName": "Enter Staff Name", "enterEmailId": "Enter Email Id", "enterMobileNumber": "Enter Mobile Number", "place": "place"}, "itemsRequiredMsg": "The required assessment item count/marks does not match the created item count. Please review and make changes if necessary.", "itemsRequiredTitle": "Required Assessment Item Count/Marks Mismatch"}, "excel": {"sNo": "S.no", "academicId": "Academic Id", "studentName": "Student Name", "status": "Status", "totalMarks": "Total Marks({{totalMarks}})", "grade": "Grade", "percentage": "Percentage", "error": "File contains invalid data, Please do necessary actions and Re-upload it again."}, "questionExportPdfPopup": {"educationalServices": "Deanship of Educational Services", "instructor": "instructor", "studentName": "Student Name", "room": "room", "section": "section", "generalInstruction": "general instruction", "messageOne": "Read and follow the instructions for each question carefully.", "messageTwo": "Mobiles and smart devices should be switched off and placed on the table assigned by the invigilator during the exam", "messageThree": "Non-Programable calculators are allowed.", "messageFour": "To get a full credit of the question you have to show all steps leading to the final answer", "messageFive": "Cheating by any means will be seriously dealt with according to the University regulations", "answerAllQuestions": "answer all questions", "part": "part", "page": "page", "preview": "preview", "setNumberOfLines": "Set Number of lines for Student response (Paper exam)"}, "withData": "With data", "withoutData": "Without data", "abToPErrorMessage": "Marks must be entered for all present students during reevaluation", "pToABErrorMessage": "Marks cannot be updated for an absentee students  during revaluation", "noRecordsFoundRefineSearch": "No records found. Please refine your search criteria and try again."}, "learningOutcomeReports": {"reportsAndAnalytics": "Reports and Analytics", "learningOutcome": "Learning outcome reports", "comparisonAnalysis": "Comparison analysis", "frameWork": "Frame work", "filters": "Filters", "clearAll": "Clear all", "applyFilters": "Apply Filters", "trendAnalysis": "Trend analysis", "outcomeAchievements": "Outcome Achievements", "filterBy": "Filter by", "fullName": "Course name", "achievementStatus": "Achievement status", "notAchieved": "Not achieved", "achieved": "Achieved", "overwriteStatus": "Overwrite status", "taggedExams": "Tagged <PERSON><PERSON>", "fes": "FES", "domainWeightAge": "Domain weightage %", "overallDomainAchievement": "Overall Domain Achievement %", "moreDetails": "More details", "outcomesTagged": "Outcomes", "knowledge": "Knowledge", "cognitiveBased": "Cognitive based", "interpersonal": "Interpersonal", "communication": "Communication", "professionalism": "Professionalism", "avg": "Avg", "exclusionCriteria": "Exclusion criteria", "configuringWeightage": "Configuring weightage", "weightedAverage": "Weighted average", "impactWeightage": "Impact weightage", "choose": "<PERSON><PERSON>", "weightage": "Weightage", "chooseWeightage": "Choose to add weightage", "excluding": "Excluding", "excluded": "Excluded", "hideUnhide": "Hide / Unhide", "excludeIncludeItems": "Exclude / Include items from the list?", "excludeIncludeItemSelections": "Exclude / Include items from the list will modify the report as per the selections", "sureWantExcludeInclude": "Are you sure want to Exclude / Include items ?", "hideItem": "Hide item", "unhideItem": "Unhide item", "excellent": "Excellent", "good": "Good", "satisfactory": "Satisfactory", "needsAttention": "Needs attention", "selectYear": "Select year", "selectLevel": "Select level", "selectProgramPlaceholder": "Please select any program to display details", "weightagePlaceHolder": "Choose all the fields to start configuring", "outcomeWiseTooltip": "Data represents average of all the exams", "itemTypeWiseTooltip": "Data represents average of all the item types", "itemWiseTooltip": "Data represents average of all the items within the item type", "excludedItem": "This is excluded, cannot add weighted average", "weightedAverageApplied": "Weightage average applied cannot exclude", "confirmExclude": "Cannot exclude a component that has a weighted average. To proceed remove weighted average and exclude", "weightedAverageInfo": "Exceeds Limit: The allocation must sum up to 100% for each level", "performanceLevels": "Performance Levels", "sharedTo": "Shared to", "sharedFrom": "Shared from", "alsoSharedToMultiplePrograms": "Also shared to multiple programs", "outcome": "Outcome", "mapOutcome": "Map outcome", "unMapOutcome": "Unmap outcome", "mapDesc": "This is Out Exam Made By without items and mapping type as overall, Do you want to proceed mapping", "unMapDesc": "Are you sure you want to un map this outcome", "clearTag": "Cleared tag", "listOfAllExams": "List of All exams for", "equivalenceWeightage": "Equivalence Weightage", "weightageAllocated": "Weightage will be allocated within each level of the hierarchy, excluding the SO, KPI and KPIE levels. The allocation must sum up to 100% for each level.", "studentAvgPerformance": "STUDENT Avg. PERFORMANCE", "warning": "Warning", "warningMessage": "Proceeding will reset the entire applied weightage of all items. Are you sure you want to proceed without applying the Weighted Average?", "weightedAverageApply": "Weighted average applied", "benchmarksApplied": "Benchmarks applied", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "thresholdContent": "To determine acceptable threshold levels and identify deviations for SO level.", "yesInActive": "Yes, Inactive", "thresholdWarningHeader": "Confirm Threshold Inactivation", "thresholdWarningContent": "Are you sure you want to <b>Inactive</b> the threshold?", "achievementPercentage": "Achievement (%)", "thresholdPercentage": "<PERSON><PERSON><PERSON><PERSON> (%)", "thresholdAchieved": "<PERSON><PERSON><PERSON><PERSON>d", "thresholdNotAchieved": "Thr<PERSON>old Not Achieved", "notUsed": "Not used", "appliedSuccessfully": "Applied successfully", "studentOutcomeAnalysisReport": "Student Outcome Analysis Report", "programAnalysis": "Program Analysis", "weightageAppliedCannotExclude": "Weightage applied cannot exclude", "seatDeleteWarning": "This seat has been assigned to a student in an upcoming schedule. Do you still want to proceed to delete it?"}, "studentOutcomeAnalysis": {"programAnalysis": {"chooseProgram": "Choose program", "chooseTerm": "Choose term", "chooseCurriculum": "Choose curriculum", "chooseFramework": "Choose framework", "chooseComponent": "Choose component", "noComponentsFound": "No components found", "noProgramSelected": "No Program Selected Yet", "chooseAnyProgram": "Choose any program to view program analysis", "hide": "<PERSON>de", "unHide": "Unhide", "listOfCourses": "List of courses", "insights": "Insights", "totalNoOfCourses": "Total No. of Courses", "mappedWithSo": "Mapped with SO’s", "inDraft": "In-draft", "barVisualization": "Bar Visualization", "tableVisualization": "Table Visualization", "componentElements": "Component Elements", "configureExclusion": "Configuring Exclusion Criteria", "exclusionNote": "<b>Note:</b> At least one course should be included for Outcome calculation.", "viewExcludedList": "View Excluded List", "chooseRow": "Choose row", "yearAndLevel": "Year & Level", "dataVisualizationCourse": "Data Visualization for <b>“{{courseName}}”</b>", "onlyCompletedAnalysis": "Only completed analysis will be listed here.", "overallAvgCalculation": "The overall average is calculated based on the average of all the years.", "showHideTable": "Show/Hide Table", "relativeAnalysis": "Relative Analysis", "rebuildReportNote": "Please Re-run the report due to changes made during course analysis.", "listOfAllCourses": "List of All Courses"}}, "independentAssessmentAuthoring": {"createAssessment": "Create Assessment", "assessmentCreation": "Assessment Creation", "independentAssessmentCreation": "Independent Assessment Creation", "noFiltersApplied": "- No filters applied yet to show -", "noAssessmentCreated": "No Assessments created yet in this course", "createIndependentAssessment": "Create Independent Assessment", "noAssessmentsAvailable": "No assessments available", "listOfAssessments": "List of assessments created", "itemDetails": "<PERSON><PERSON>", "dateAndTime": "Date & Time", "scheduledDetails": "Scheduled Details", "dueDate": "Due Date", "publishStatus": "Publish Status", "examStatus": "Exam Status", "createIndependent": "Creating Independent Assessment", "durationMins": "Duration (mins)", "duration": "Duration", "mustAnswer": "Must answer", "subjectTopic": "-- Subject / Topic -- ", "cloMapping": "-- CLO Mapping --", "taxonomy": "-- Taxonomy --", "restrict": "Restrict", "choiceShuffling": "Choice Shuffling", "itemNavigation": "Item Navigation", "choiceAndAnswerKey": "Choices & Answer Key:", "addOption": "Add Option", "leftAlign": "Left Align", "rightAlign": "Right Align", "centerAlign": "Center Align", "addItem": "Add Item", "uploadOldItems": "Upload Old Items", "addSection": "Add Section", "equalMark": "Equal Mark", "customMark": "Custom Mark", "sectionCount": "{{current}} of {{total}}", "restrictNavigation": "Restrict Item Navigation", "cannotDeleteLastItem": "Cannot delete the last item", "specifyTotalMarks": "Please specify the total marks", "addTotalDuration": "Please specify a total duration", "pleaseFillAllRequiredFields": "Please fill all the required fields", "sectionTimeInValid": "Section time does not match the total time", "sectionMarksInValid": "Section marks does not match the total marks", "itemTotalMarksMismatch": "Item marks does not match the total marks", "itemTimeInvalid": "Item time does not match the section time", "itemMarksInvalid": "Item marks does not match the section marks in Section No.{{sectionNo}}", "assessmentPlanSettings": "Assessment Plan Settings", "assessmentConductionSettings": "Assessment Conduction Settings", "shufflingSettings": "Shuffling Settings", "totalNoOfSections": "Total no. of sections", "totalNoOfItems": "Total no. of items", "totalDuration": "Total duration (min)", "restrictLateEntery": "Restrict late entry", "allowLateEntryTime": "Allow maximum late entry time as a percentage(%) of total exam duration", "reduceAllowedLateEntry": "Reduce late entry time from the total time", "minimumPercentage": "Set minimum time as a percentage(%) student must attend the exam", "allowRearrangeSection": "Allow re-arranging section order", "shuffleSections": "Shuffle sections", "shuffleItems": "Shuffle items", "shuffleItemsWithinGroups": "Shuffle items within Groups (EMQ, CQ, MQ, CSAQ, MIT)", "mediaSettings": "Media Settings", "addTimeForAllSection": "Add time for all sections", "responseType": "Response type", "digital": "Digital-based", "paper": "Paper-based", "selectExamAdmin": "Select an Exam <PERSON>", "primaryProctor": "Primary Proctor", "secondaryProctor": "Secondary Proctor", "youCanSelectMultipleProctors": "You can select multiple proctors at once.", "examStartConditions": "Exam start Conditions", "allowExamStart": "Auto Start Exam Without Start of Exam Admin", "canStartWithin": "Can start within", "beforeExamStart": "mins before the exam starts.", "addNew": "Add New", "applicableFor": "Applicable for", "systemRequirementIsMissing": "Please select a System Requirement to proceed", "studentAuthenticationMethodIsMissing": "Please select a Student Authentication Method to proceed", "browsersSelectionIsMissing": "Please select at least one Browser to proceed", "assessmentTypeIsMissing": "Please select an Assessment Type to proceed", "examTypeIsMissing": "Please select an Exam Type to proceed", "locationAndProctoringTypeIsMissing": "Please select a Location and Proctoring Type to proceed", "examCategoryIsMissing": "Please select an Exam Category to proceed", "attemptTypeIsMissing": "Please select an Attempt Type to proceed", "newlyAdded": "Newly Added", "existing": "Existing", "select": "- Select -", "noStudentGroupFound": "No student groups found!", "goToStudentGrouping": "Go to Student Grouping.", "setPlannedCount": "Set a planned count and upload students via <b>bulk import</b>:", "uploadedStudents": "Uploaded students", "forMaleStudents": "For <b>Male</b> Students", "forFemaleStudents": "For <b>Female</b> Students", "chooseTestCenter": "Choose required TC(s) from the table below:", "testCenterAllocationNote": "By default, test centers are auto-allocated by highest capacity. You can manually select by unchecking and choosing the required test centers.", "filledByPlannedCount": "Filled Students/TC(s) Capacity:", "allocatedStudentsPlannedStudents": "Allocated Students/Planned Students", "fillOutFields": "Please fill out all the necessary fields", "roomNumber": "Room No", "examEndsBy": "<PERSON><PERSON> ends by", "specifyDateForExam": "Specify a date for exam", "noTestCenterAddedYet": "- No test centers added yet to show -", "noVirtualTestCenterAddedYet": "- No virtual test centers added yet to show -", "timePickerPlaceHolder": "HH : MM AM", "examAdminNote": "No need for <PERSON><PERSON> to start the session as exam will be started automatically based on the exam start time.", "allocatingStudents": "Allocating Students", "addOnlyImage": "Add Images (Only if needed)", "itemCreation": "Item creation", "examConfiguration": "Exam configuration", "assignStudents": "Assign students", "scheduleExamAndAllocateTestCenter": "Schedule exam & Allocate test center", "assignExamAdminAndProctor": "Assign <PERSON>am <PERSON>min & Proctor", "importStudentList": "Imported Students List", "selectInstitutionCalender": "Select Institution Calender", "selectStudentGroup": "Select Student Group", "noStudentCount": "No student count set", "StudentNotFoundInGroup": "<b>Unable to proceed Next!</b> Student are not found in some student group", "pleaseAddTestCenter": "Please add test center", "pleaseAssignStudentsForAllTc": "Please assign students to all test centers", "pleaseSelectExamDate": "Please select a exam date", "pleaseSelectExamTime": "Please set an exam start time", "noImportedStudents": "Ensure all students are imported before continuing", "maleCount": "Male: {{ assignedCount }}/{{ plannedCount }}", "femaleCount": "Female: {{ assignedCount }}/{{ plannedCount }}", "viewOverallDetails": "View Overall Details", "assessmentPublishNote": "Please review the details carefully before proceeding. Email notification will be sent to students and proctors.", "assessmentPlan": "Assessment Plan", "scheduledDateAndTime": "Scheduled Date & Time", "examDateAndTime": "Exam Date & Time", "sectionType": "Section Type", "withSection": "With Section", "withoutSection": "Without Section", "makeDistributionMethod": "Mark Distribution Method", "modeOfConduction": "Mode of Conduction", "examAuthentication": "<PERSON><PERSON>", "testCenterInvolved": "Test Center Involved", "equalMarks": "Equal Marks", "splitMarks": "Split marks equally", "splitMarksOn": "Turning this on will split the marks equally among the sections and items", "splitMarksOff": "Turning this off you have to manually distribute the marks", "customMarks": "Custom Marks", "noGroups": "no groups", "pleaseSelectExamAdmin": "Please select <PERSON>am admin", "pleaseSelectPrimaryProctor": "Please select Primary proctor", "studentOwnedDevice": "Student owned device", "listOfTestCenters": "List of Test centers", "allocatedStudents": "Allocated students:", "onsiteWithProctor": "O+P", "remoteWithProctor": "R+P", "remoteWithoutProctor": "R-P", "onsiteProctor": "Onsite With Proctor", "remoteProctor": "Remote With Proctor", "withoutRemoteProctor": "Remote Without Proctor", "centralizedTestCenter": "Centralized Test Center", "customTestCenter": "Custom Test Center", "unp": "Password", "faceFemale": "Face (F)", "faceMale": "Face (M)", "face": "Facial", "notPublished": "Not published", "enablesAt": "Enables at:", "confirmTile": "confirm change", "clickToProceed": "Click '<b>I Understand</b>' to proceed.", "iUnderstand": "I Understand", "confirmMessage": "Some student groups are unavailable for the selected configuration and will be removed in <b>Step 3: “Assign Students”</b>", "confirmAdditionalInfo": "Are you sure you want to <b>proceed</b>?", "confirmChangePrimaryButton": "Yes, Change", "examInProgress": "Editing is restricted once exams are started. Please cancel exam in exam portal to edit.", "examIsPublished": "Exam is already published. To make changes, please unpublish it first.", "scheduleRequestCreatedWithdraw": "Schedule Request Created, Withdraw Request to edit", "proctorChangeTitle": "Warning to change Remote W/O Proctor", "proctorChangeMessage": "Changing to Remote Without Proctor (R-P) will <b>delete</b> all the added Test Centers and also Student Groups.", "proctorChangePrimaryButton": "Yes, Change to R-P", "addVirtualTestCenter": "Add virtual test center", "virtualTcName": "Virtual TC Name", "skipFacial": "Skip facial for (Opt):", "skipFacialGender": "Selected gender uses Password if facial skipped", "pleaseCompletePreviousSteps": "Please complete the previous steps", "facialAndPassword": "Facial or Password", "assessmentDeleteMessage": "Deleting this assessment will permanently remove it. This action cannot be undone.", "scheduleDeleteMessage": "Deleting this schedule request will permanently remove it. This action cannot be undone.", "restrictRevisitingSection": "Restrict revisiting of section", "completedAssessmentToEdit": "Editing is restricted for Completed Assessments.", "backToIndependentAssessmentPage": "Back to Independent Assessment Page", "studentDeleteConfirmation": "Are you sure you want to Delete this Student <b>{{academicNo}}</b> ?", "deleteMultiStudentsConfirmation": "Are you sure you want to Delete setected students?", "plannedCountShouldBeMore": "Planned count should be more or equal to imported students count", "searchContent": "Search Exam Type, Exam Category, Assessment Type & Attempt Type", "searchCourseContent": "Search course name", "uploadInactiveStudents": "Selecting this checkbox allows you to upload inactive students also.", "itemShouldNotHaveZeroMarks": "<PERSON><PERSON> should not have zero marks", "confirmReset": "Confirm Reset and Equal Distribution", "customMarkReset": "All custom marks assigned to each item will be reset and redistributed equally based on the total marks.", "resetAndDistributeMsg": "Are you sure you want to <b>reset</b> and <b>distribute the marks equally</b>?", "resetAndDistribute": "Reset and Distribute", "maleStudentsAreNotAllocated": "Some male students are not allocated yet", "maleStudentsAllocatedSuccessfully": "All Male students allocated successfully", "femaleStudentsAreNotAllocated": "Some female students are not allocated yet", "femaleStudentsAllocatedSuccessfully": "All female students allocated successfully", "publishedBy": "Published By", "publishedDate": "Published Date", "noOfTimesPublished": "No. of times published", "uploadOldAssessment": "Upload Old Assessment / Individual Items", "attachFromQB": "Attach from Question Bank", "listOfOldAssessment": "List of old assessments", "attachAssessment": "Attach Assessment", "listOfTopics": "List of Topics", "attachItems": "Attach Items", "moveToSection": "Move to a Section", "uploadItemsAssessments": "Upload Item / Assessments", "uploadedItemCannotEdit": "Uploaded item, cannot edit", "stemEmpty": "Stem seems to be Empty on Section No.{{sectionNo}} / Q.No.{{itemNo}} ({{itemTypeCode}}).", "withOutSectionStemEmpty": "Stem seems to be Empty on Q.No.{{itemNo}} ({{itemTypeCode}}).", "addChoicesOrAnswer": "Please add choices/answer key on Section No.{{sectionNo}} / Q.No.{{itemNo}} ({{itemTypeCode}}).", "hotSpotAreaShouldBeDrawn": "Hotspot area should be drawn on Section No.{{sectionNo}} / Q.No.{{itemNo}} ({{itemTypeCode}}).", "withoutSectionhotSpotAreaShouldBeDrawn": "Hotspot area should be drawn on Q.No.{{itemNo}} ({{itemTypeCode}}).", "withOutSectionPromptEmpty": "Prompt ({{subItemNo}}) is Empty on Q.No.{{itemNo}} ({{itemTypeCode}}).", "withOutAddChoicesOrAnswer": "Please add choices/answer key on Q.No.{{itemNo}} ({{itemTypeCode}}).", "markAnswerKey": "Please mark answer key on Section No.{{sectionNo}} / Q.No.{{itemNo}} ({{itemTypeCode}}).", "withOutMarkAnswerKey": "Please mark answer key on Q.No.{{itemNo}} ({{itemTypeCode}}).", "promptEmpty": "Prompt ({{subItemNo}}) is Empty on Section No.{{sectionNo}} / Q.No.{{itemNo}} ({{itemTypeCode}}).", "subItemStemEmpty": "Stem seems to be Empty on Section No.{{sectionNo}} / Q.No.{{itemNo}} ({{itemTypeCode}}) Sub item ({{subItemNo}}).", "withoutSectionSubItemStemEmpty": "Stem seems to be Empty on Q.No.{{itemNo}} ({{itemTypeCode}}) Sub item ({{subItemNo}}).", "subItemAddChoicesOrAnswer": "Please add choices/answer key on Section No.{{sectionNo}} / Q.No.{{itemNo}} ({{itemTypeCode}}) Sub item ({{subItemNo}}).", "withOutSubItemAddChoicesOrAnswer": "Please add choices/answer key on Q.No.{{itemNo}} ({{itemTypeCode}}) Sub item ({{subItemNo}}).", "onsiteWithProctorTooltip": "Allows to conduct exams with live proctoring in flexible, non-centralized locations like classrooms or custom spaces for low-stakes assessments.", "remoteWithProctorTooltip": "Allows to conduct remote assessments with live proctoring through any virtual platform using a shared link.", "remoteWithoutProctorTooltip": "Allows to conduct remote assessments without live proctoring through any virtual platform using a shared link.", "unPublishAssessmentConfirmation": "Unpublish Assessment Confirmation!", "unPublishNote": "<b>Important Note:</b><ul><li>Students cannot attend the exam until it is re-published.</li><li>Upon unpublishing, students, proctors, and exam admins will be notified via email.</li><li>Please make sure to publish again once the required changes are made and notifications will be sent about the recent changes to the assigned users.</li></ul>", "areYouSureToUnPublish": "Are you sure you want to <b>unpublish</b>?", "optionsCannotEqual": "Maximum Options Reached: You cannot add more than {{questionCount}} options.", "mqNoteInfo": "No. of options and questions must be equal", "configuringTopic": "Configuring topic", "anyNewItemsCreated": "Any new items created will also have this topic.", "applyThisSection": "Apply to this section", "applyAllSections": "Apply to all sections", "searchTopicSubject": "Search for topic/subject", "applyThisTopic": "Apply this topic to items that already have a topic", "setTopicForAllOrSpecificSection": "Set topic for all or specific section", "addPair": "Add Pair", "promptLabel": "Prompt", "answerLabel": "Answer", "centralizedInfrastructure": "Using centralized Infrastructure", "UsingCustomTestCenter": "Using custom test center", "chooseMethod": "Choose Method", "responseCalculationMethod": "Response Calculation Method", "addWeightageToAnswerKey": "Add weightage to each choices", "setTarget": "Set Target", "noOfCorrectAnswers": "No. of Correct Answers", "assignTo": "Assign to", "sentToReviewer": "Sent to {{reviewersR<PERSON>Name}}", "sendToReviewer": "Send to {{reviewersRoleName}}", "assignReviewer": "Assign Reviewer", "allItemSentForReview": "All Items will be sent for review", "pleaseSelectItemToMoveSection": "Please select an item to move to a section.", "pleaseSelectItemForAssignItemAuthor": "Please select an item for assign item author", "requestedByAssessmentCreator": "Requested by Assessment Creator", "createdByself": "Created By-self", "onGoing": "On-Going", "notClosedYet": "Not closed yet", "examNotStartedYet": "Exam not started yet", "needToStartExam": "Need to start exam", "all": "All", "scheduleRequest": "Schedule Request", "requestedByCourseAuthor": "Requested by Course Author", "approveRequest": "Approve Request", "approver": "Approver", "scheduleRejectReason": "Reject Reason", "createdBySelf": "Created By-self", "requestedByExamSchedular": "Requested by <PERSON>am <PERSON>r", "bothAnswerKeyAndCount": "Both total answer key and correct answer counts should be same for Section No.{{sectionNo}} / Q.No.{{itemNo}} ({{itemTypeCode}}).", "withOutBothAnswerKeyAndCount": "Both total answer key and correct answer counts should be same for Q.No.{{itemNo}} ({{itemTypeCode}}).", "weightageShouldBe100Percentage": "Weightage should be equal to 100 percent on Section No.{{sectionNo}} / Q.No.{{itemNo}} ({{itemTypeCode}}).", "weightageShouldOnlyAddedForCorrectAnswers": "Weightage should only be added for the correct answers on Section No.{{sectionNo}} / Q.No.{{itemNo}} ({{itemTypeCode}}).", "pleaseScheduleAndContinue": "Please schedule and continue", "onlyViewAccess": "Only View Access", "proctorDetails": "Proctor details", "questionNotAdded": "Question not added yet", "optionNotAdded": "Option not added yet", "studentDetailsIndependentAssessment": "Student details", "updateNotAllowed": "Update not allowed once assessment sent to", "reviewCompletedNote": "Updates are not allowed after review completion", "withdrawRequestConfirmation": "Withdrawing this request will remove item access for the assigned author.", "areYouSureWantToWithdraw": "Are you sure you want to <b>withdraw request?</b>", "areYouSureWantToCompleteReview": "Are you sure you want to <b>Complete Review?</b>", "yesWithdraw": "Yes, Withdraw Request", "areYouSureWantToSendIA": "Are you sure you want to send this  Assessment to <b>{{author}}</b>?", "courseSellectionRequired": "Please choose a course before creating an assessment", "regenerateReport": "Re-generate Report", "printAssessment": "Print Assessment", "completeAllStepsToPublish": "Complete all the Steps to Publish", "pleaseCompleteAllRequiredFieldsInExamConfiguration": "Please complete all the required fields in the exam configuration.", "pleaseUploadStudentsInStudentDetails": "Please upload the students in the student details step.", "pleaseCompleteScheduledDetails": "Please complete the schedule details.", "pleaseAssignProctorInProctorDetails": "Please assign  proctor in the proctor details step.", "subItemOptionText": "Please provide option text on Section No.{{sectionNo}} Q.No.{{itemNo}} ({{itemTypeCode}}) Sub item ({{subItemNo}}).", "withoutSubItemOptionText": "Please provide option text on Q.No.{{itemNo}} ({{itemTypeCode}}) Sub item ({{subItemNo}}).", "markAnswerKeyForMIT": "Please mark answer key on Section No.{{sectionNo}} Q.No.{{itemNo}} ({{itemTypeCode}}) Sub item ({{subItemNo}}).", "withOutMarkAnswerKeyForMIT": "Please mark answer key on Q.No.{{itemNo}} ({{itemTypeCode}}) Sub item ({{subItemNo}}).", "noExamDetails": "Exam details not provided yet!", "noItemDetails": "Item details not provided yet!", "timeInPast": "Please select a future time. The selected time is in the past", "sumOfCustomMarks": "Sum of all custom marks provided", "noCourseName": "No Course Name", "mustAddComment": "Must add comments to <PERSON> as Correction Required", "correctionRequired": "Mark as Correction Required", "reviewed": "<PERSON> as Reviewed", "completeReview": "Complete Review", "reviewCompleted": "Review Completed!", "addComment": "Add a comment...", "addReply": "Add reply...", "itemsNotReviewed": "Some Items are not reviewed yet to Complete", "withdrawnItems": "Some items are withdrawn", "assignItemsToWithdrawItemAuthor": "Selected items have not been sent to the <PERSON>em author.", "assignItemsToWithdrawReviewer": "Selected items have not been sent to the Reviewer.", "studentGroupsNotAssigned": "No student groups selected in this Exam! Instead, manually uploaded students via bulk import as shown below:", "noGroupsFound": "No groups Found", "answerDescription": "Answer Description", "andSetWeightage": "and set weightage", "setWeightage": "Set Weightage (%)", "totalAttachmentSize": "Attachments size should be ≤ 15 MB", "previousSection": "Previous section", "nextSection": "Next section", "cannotEditOldItems": "Old Item cannot be edited", "selectItemsToWithdraw": "Please select some items to Withdraw.", "liveExams": "Live Exams", "examEnded": "<PERSON><PERSON>", "pendingCorrection": "Pending Corrections Update!", "pendingCorrectionNote": "Some items are still marked as <b>Correction Required</b> and have not been corrected yet. All such items will be marked as <b>Published</b> after proceeding.", "rescheduledExamNote": "This assessment has been rescheduled and is not yet re-published to show.", "contactExamScheduler": "Please contact the exam scheduler to complete the rescheduling process.", "assessmentCancelled": "Assessment has been cancelled!", "assessmentCancelledNote": "This assessment has been cancelled by the Exam Scheduler.", "contactScheduler": " Kindly contact the respective scheduler for further details.", "imageLabel": "Image", "addAnswer": "Add answers", "itemThemEmpty": "Them seems to be Empty on Q.No.{{itemNo}} ({{itemTypeCode}}).", "withSectionItemThemEmpty": "Them seems to be Empty on Section No.{{sectionNo}} / Q.No.{{itemNo}} ({{itemTypeCode}}).", "itemStemLeadInEmpty": "Lead-in seems to be Empty on Q.No.{{itemNo}} ({{itemTypeCode}}).", "withSectionItemStemLeadInEmpty": "Lead-in seems to be Empty on Section No.{{sectionNo}} / Q.No.{{itemNo}} ({{itemTypeCode}}).", "chooseRightAnswer": "Choose right answer", "typeAnAnswer": "Type an answer", "previewImage": "Preview Image", "updateOption": "Update Option", "oldItemNote": "Under the selection,({{oldItemCount}}) items are old <b>(chosen from the question bank)</b> and will not be sent or withdrawn for review.", "oldItemIANote": "Under the selection,({{oldItemCount}}) items are old <b>(chosen from the question bank)</b> and will not be sent for Item Author.", "mapItemsNote": "Items that are already attached, old, or under review will be skipped. Only {{cannotMapItemCount}} items will be ignored.", "MaleStudentsFirst": "Male students first when selecting mixed test center", "switchCourseConfirmation": "Switching to a new course will update the subjects and topics of the existing questions to the newly selected course.", "wouldYouLikeToUpdate": "<b>Would you like to update them?</b>", "needToReviewItemsStatus": "<b>({{count}}) items needs review</b>", "needToCorrectRequiredItemsStatus": "<b>({{count}}) items needs correct required</b>", "inReviewItemsStatus": "<b>({{count}}) items are in review</b>", "sendBackAAConfirmationInfo": "You cannot modify anything after sending.", "withSectionItemAlreadySentToReviewer": "This item has already been sent to the author or reviewers for Section No. {{sectionNo}} / Q.No. {{itemNo}} ({{itemTypeCode}}).", "withoutSectionItemAlreadySentToReviewer": "This item has already been sent to the author or reviewers for Q.No. {{itemNo}} ({{itemTypeCode}}), Sub-item {{subItemNo}}.", "publishConfirmation": "Some reviewers have not completed their reviews.Do you still want to publish the assessment?", "itemAlreadySentToItemAuthorOrReviewers": "Some items have already been sent to the item author or reviewers. Please withdraw them before proceeding.", "itemAlreadySentToReviewers": "Some items have already been forwarded to the reviewers", "confirmSaveAsNew": "Confirm Save as New Item", "confirmSaveAsNewNote": "<b>*Note:</b> Editing this item will save it as a new item.", "confirmSaveAttached": "Are you sure you want to edit the attached item?", "testCentersBookedSuccessfully": "Test centers booked successfully.", "itemAuthorNote": "Not allowed to as some items are with the {{ itemAuthor }}. Proceed after they complete the card or withdraw the card from the {{ itemAuthor }}.", "scheduleRequestApproved": "The requested schedule has been approved.", "scheduleRequestRejected": "The requested schedule has been rejected.", "clickToEdit": "Click to edit", "clickToEditItem": "Click to edit <PERSON><PERSON>", "rejectRequestConfirmation": "Are you sure you want to <b>reject</b> this request?", "rejectReason": "Please provide a reason for rejection.", "refreshInfo": "Refresh the list to display the most current test center availability", "scheduleRequestedNotYetApproved": "Schedule requested but not yet approved", "addExamStartTime": "Add Exam start time before creating test center", "centralizedExamProctorNote": "For centralized exam, proctors need to be assign in asign proctor page,", "goToAssignProctor": "Go to As<PERSON> Proctor", "importViaDigiClass": "Import via DigiClass", "importViaDigiAssess": "Import via DigiAssess", "importViaSis": "Import via SIS", "uploadSisStudents": "Upload SIS Students", "importViaExcel": "Import via Excel (Additional Students)", "additionalStudents": "Additional Students", "importViaExcelNote": "Download the template, fill in the student entries and upload the completed file.", "confirmResetMapping": "Are you sure you want to reset the mapping for selected items?", "mappingItems": "Mapping Items", "subjectAndTopic": "Subject/ Topic", "courseLearningOutcomes": "Course Learning Outcomes (CLO)", "sameExamConfig": "Since an exam with different mode has already been created for this attempt type, please create the exam in a different attempt type.", "dutyApproach": "Duty Approach", "showAvailableOnly": "Show Available Only", "proctorDutyOnly": "Proctor Duty  Only", "classDutyOnly": "Class Duty Only", "filterByStaff": "Filter by staff availability", "bufferCount": "Buffer Count", "pleaseSelectAtLeastOneGroup": "Please select at least one group", "allPlannedStudentsAllocated": "All planned students allocated", "videoProcessing": "Video is being processed,please refresh after sometime.", "videoProcessingFailed": "Video processing failed, please upload again.", "proctorAssignedCentralized": "Proctor already assigned for this duty time.", "videoAttachment": "Video Attachment", "audioAttachment": "Audio Attachment", "pdfAttachment": "PDF Attachment", "matchTheAnswersWithQuestion": "Match the Answers with question", "importQuestionsFromExcel": "Import Questions from Excel", "processQuestions": "Process Questions", "uploadExcelFile": "Upload Excel File", "noQuestionsExtracted": "No questions extracted from the uploaded file", "processingFile": "Processing file...", "dragDropOrClick": "Drag and drop your file here or click to browse", "dropFileHere": "Drop your file here", "supportedFormats": "Supported formats", "extractedQuestions": "Extracted Questions", "uploadMoreQuestions": "Upload More Questions", "importQuestions": "Import Questions", "importFromExcel": "Import from Excel", "errorReadingFile": "Error reading file", "successAddedQuestions": "Successfully added {{ count }} new questions", "selectAMaximumOfPointsOnTheImageBelow": "Select a maximum of {{noOfPoints}} points on the image below", "answerAlltheQuestionsBelow": "Answer all the questions below", "chooseAnyOneCorrectAnswersForEachQuestions": "Choose any one correct answers for each questions", "supportedItemTypes": "Supported item types: MCQ, SAQ", "invalidTemplate": "Invalid template", "duplicateOptionsFound": "Found and removed {{ count }} duplicate option(s) during import.", "youDontHavePermissionToCreateViewItems": "You don't have permission to create / view items"}, "courseInput": {"inputData": "Input Data", "pleaseSelectCourse": "please select course", "noSubjectOrTopicAvailable": "- No subject or Topics added yet -", "createNewSubject": "+ Create New Subject", "createNewTopic": "+ Create New Topic", "newSubject": "New subject", "newTopic": "New topic", "newContent": "New content", "nameOfTheSubject": "Name of the Subject", "writeDescription": "Write a description", "addTopic": "+ Add Topic", "addContent": "+ Add Content", "writtenContent": "Written Content", "noContent": "No Content", "topicWithCount": "{{ count }} Topics", "noTopics": "No Topics", "courseSpec": "Course Spec", "addClo": "+ Add CLO", "plannedCountShouldBeMore": "Planned count should be more or equal to imported students count", "cloByPlo": "CLO / PLO", "noteForSelectCurriculum": "To Map the CLOs of the foundation year, select all Programs under different frameworks your institution follows.", "noteForPloAndCloMappingTable": "To Map the CLO - PLO, Select the “Map Type“ you want to  map for the Outcome Mapping.", "selectOption": "- Select Option - ", "pleaseSelectProgramOrCurriculum": "Please select program or curriculum to proceed", "selectProgramAndFramework": "Select Programs & Framework", "mapTypeAndOutcomeMapping": "Map Type & Outcome Mapping", "courseInputs": "Course Inputs", "courseManagement": "Course Management", "courseInformation": "course", "hierarchy": "hierarchy", "goToReports": "Go to Reports", "chooseReport": "Choose a Report", "reportsNotAvailable": "Reports not available on selected academic year!", "searchSubjectTopic": "Search subject and topic", "courseDashboard": "Course Dashboard", "conductedInsideApplication": "Conducted inside the application", "conductedOutsideApplication": "Conducted outside the application", "errorReadingExcelFile": "Error reading Excel file. Please check the file format.", "mismatchedHeaderData": "Mismatched / Invalid header Data. Expected columns: name, code", "noValidTopicsFoundInExcelFile": "No valid topics found in the Excel file.", "successfullyImportedTopics": "Successfully imported {{ count }} topics."}, "scheduleExamDetail": {"courseDetails": "Course Details", "dateAndTime": "Scheduled Date & Time", "examTime": "<PERSON>am <PERSON>", "groups": "Groups", "importedPlanned": "Imported/ Planned", "proctors": "Proctors", "assessmentStatus": "Assessment Status", "scheduleStatus": "Schedule Status", "actions": "Actions", "schedule": "schedule", "view": "view", "publish": "publish", "students": "Students", "scheduleOn": "Schedule On", "tribleDots": "More Options", "uploadStudents": "Upload Students", "downloadTemplate": "Download Template", "reschedule": "Reschedule exam", "cancel": "Cancel exam", "areYouSureToReschedule": "Are you sure to reschedule?", "areYouSureToCancel": "Are you sure to cancel?", "rescheduleExam": "Reschedule Exam", "cancelExam": "Cancel Exam", "selectStudentPermitted": "Select Student Permitted", "importedDataIsEmpty": "Imported data is empty", "searchCourse": "Search course name", "testCenters": "Test centers", "listOfTestCenters": "List of Test Centers", "searchTestCenter": "search test center name", "testCenterDetails": "Test Center Details", "primaryProctor": "Primary Proctor", "secondaryProctor": "Secondary Proctor", "export": "Export", "notAssignedYet": "Not assigned yet", "seats": "seats", "tc": "TC", "searchStudent": "search student name", "searchProctor": "search proctor name", "editInvigilator": "Edit Invigilator", "goToAssignment": "Go to assignment", "academicNo": "Academic No", "studentName": "Student Name", "gender": "Gender", "studentGroup": "Student Group", "seatNo": "Seat No", "proctorId": "Proctor <PERSON>d", "proctorName": "Proctor Name", "invigilatorDuty": "Invigilator Duty", "role": "Role", "primary": "primary", "secondary": "secondary", "scheduledBy": "Scheduled by:", "testCentersCount": "TC's", "male": "Male", "female": "Female", "studentProctor": "Student/Proctor (End)", "startTime": "Start Time", "actualStartTime": "Actual Start Time", "edit": "Edit", "sessionTime": "Session Time", "centerlizedExam": "Centralized Exam", "independentExam": "Independent Exam", "independentlyScheduled": "Independently scheduled", "centralizedSchedule": "Centralized schedule", "assignedRequired": "Assigned / Required", "noStudentsImportedPlanned": "No. of Students Imported / No. of Students Planned", "noProctorsTestcenters": "No. of Proctors assigned / No. of Test centers", "assessmentAuthors": "Assessment Authors", "publishedAt": "Published At"}}