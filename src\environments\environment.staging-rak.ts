const baseUrl = 'https://daapi-staging.rak.digi-val.com'
const v1 = `${baseUrl}/api/v1`
const gradeBookBaseURL = 'https://gradebook.gcp.digivalitsolutions.com/'
const gradeBookParams = 'noAuth=false&loadedFrom=iframe&product=DA'
const gradeBookUrl = `${gradeBookBaseURL}auth/login?${gradeBookParams}`
const ossaiUrl = 'https://osce.gcp.digivalitsolutions.com/staging-osce/osce/'
const cloudUrl = v1
const uploadAttachmentBaseUrl = 'https://da-unify-staging.rak.digi-val.com/api/v1'
const helpDeskURL = 'https://helper.gcp.digivalitsolutions.com'

export const environment = {
  production: true,
  baseUrl,
  cloudUrl,
  gradeBookBaseURL,
  gradeBookParams,
  gradeBookUrl,
  ossaiUrl,
  endPoint: v1,
  otpService: 'email',
  digiExamUrl: 'https://digiexam-staging.rak.digi-val.com',
  language: 'en',
  countryCode: '966',
  languageSwitch: false,
  isTwoFactorAuthDisabled: true,
  canEnableSms: true,
  canViewForgotPassword: false,
  canLimitChar: false,
  measurementId: '',
  clientConfig: {
    canAllowPaperExam: true,
    helpDeskClientCode: 'rak',
    enableReCaptcha: true
  },
  canShowSessionTime: true,
  uploadAttachmentBaseUrl,
  sso: {
    isEnabled: false,
    loginUrl: 'https://accounts.rak.digi-val.com'
  },
  helpDeskURL,
  canEnableSis: true
}
