// This file can be replaced during build by using the fileReplacements array.
// ng build --prod replaces environment.ts with environment.prod.ts.
// The list of file replacements can be found in angular.json.

const baseUrl = 'http://localhost:5000'
const v1 = `${baseUrl}/api/v1`
const gradeBookBaseURL = 'http://localhost:3000/'
const gradeBookParams = 'noAuth=false&loadedFrom=iframe&product=DA'
const gradeBookUrl = `${gradeBookBaseURL}auth/login?${gradeBookParams}`
const ossaiUrl = 'https://osce.gcp.digivalitsolutions.com/staging-osce/osce'
const cloudUrl = v1
const uploadAttachmentBaseUrl = 'https://api.digivalitsolutions.com/api/v1'
const helpDeskURL =
  'https://digival-staging-nginx-ds-yk25kmkzeq-el.a.run.app/staging-digival-helper/'

export const environment = {
  production: false,
  baseUrl,
  cloudUrl,
  gradeBookBaseURL,
  gradeBookParams,
  gradeBookUrl,
  ossaiUrl,
  endPoint: v1,
  otpService: 'slack',
  digiExamUrl: 'http://localhost:4201',
  language: 'en',
  countryCode: '966',
  languageSwitch: false,
  isTwoFactorAuthDisabled: true,
  canEnableSms: false,
  canViewForgotPassword: true,
  canLimitChar: true,
  measurementId: 'G-JL8Y25KY70',
  clientConfig: {
    canAllowPaperExam: true,
    helpDeskClientCode: '',
    enableReCaptcha: true
  },
  canShowSessionTime: true,
  uploadAttachmentBaseUrl,
  sso: {
    isEnabled: false,
    loginUrl: 'https://accounts.scd.digi-val.com'
  },
  helpDeskURL,
  canEnableSis: true
}

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as zone.run, zoneDelegate.invokeTask.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
