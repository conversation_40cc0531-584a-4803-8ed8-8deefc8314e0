@import url('./style/theme.css');
@import url('./style/spacing.css');
@import url('./style/font.css');

:root {
  --digi-checkbox-state-layer-size: 20px;
}

html,
body {
  height: 100%;
  min-height: 100%;
}

body {
  margin: 0;
  font-family: inherit, 'Helvetica Neue', sans-serif;
  font-size: 16px;
}

[lang='ar'] * {
  font-family: 'Noto Naskh Arabic', 'Roboto' !important;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

[lang='ar'] mat-icon,
.material-icons {
  font-family: 'Material Icons' !important;
}

[lang='ar'] h1,
[lang='ar'] h2,
[lang='ar'] .digi-title,
[lang='ar'] .digi-menu-title,
[lang='ar'] .digi-head,
[lang='ar'] .digi-sub-nav-sub-title,
[lang='ar'] .digi-font-500,
[lang='ar'] .digi-fw-500,
[lang='ar'] .digi-table-title,
[lang='ar'] th {
  font-weight: 600 !important;
}

.mat-mdc-tab .mdc-tab__text-label {
  color: #ffffff !important;
}

.mdc-tab {
  opacity: 0.6;
  color: #ffffff;
}

.mat-mdc-tab .mdc-tab-indicator__content--underline {
  border-color: #ffffff !important;
  margin-bottom: 5px;
  width: 95%;
}

.mat-mdc-dialog-title {
  padding: 0 !important;
  margin-bottom: 15px !important;
}

.mdc-dialog__title::before {
  height: auto !important;
}

.mat-mdc-dialog-container {
  padding: 24px;
}

.mat-mdc-dialog-container .mdc-dialog__surface {
  background-color: transparent !important;
}

#time-picker-wrapper #time-picker .time-picker-header .time-picker-selected-time {
  unicode-bidi: plaintext;
}

[dir='rtl'] #time-picker-wrapper #time-picker .time-picker-footer {
  display: flex;
  flex-direction: row-reverse;
}

.example-form {
  min-width: 150px;
  max-width: 500px;
  width: 100%;
}

.example-full-width {
  width: 100%;
}

/* width */
::-webkit-scrollbar {
  width: 7px;
}

/* Track */
::-webkit-scrollbar-track {
  border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #c7c4c4;
}

fieldset {
  border: none;
}

fieldset {
  border: none;
  margin: 0px;
  padding: 0px;
}

a {
  font-family: inherit;
}

button {
  font-family: inherit;
  border: none;
  background: none;
  outline: none;
}

/* TODO(mdc-migration): The following rule targets internal classes of dialog that may no longer apply for the MDC version. */
mat-dialog-actions {
  margin-top: 50px;
}

.body {
  width: auto;
  min-height: 100%;
  height: auto;
  padding: 15px;
  box-sizing: border-box;
}

::ng-deep.mat-mdc-dialog-container {
  height: auto;
}

.body mat-card {
  padding: 5px 25px 25px;
  display: block;
  width: 100%;
  max-width: 415px;
  background: white;
  box-sizing: border-box;
  border-radius: 16px;
  box-shadow: 1px 1px 6px 3px #aaa !important;
}

.body mat-card-title {
  padding: 0;
  width: 100%;
  font-family: inherit;
  font-style: normal;
  font-weight: normal;
  font-size: 24px;
  line-height: 100%;
  letter-spacing: 0.15px;
  color: #000000;
}

.body mat-card-content {
  overflow: hidden;
}

.body mat-card-footer {
  padding: 20px;
  color: #000000;
}

/* .body mat-form-field{
    padding: 0;
    margin-top: 10px;
    border-bottom: 1px solid #1F000000;
} */
@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

.spinner:before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  margin-left: -10px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  border-top-color: #000000;
  animation: spinner 0.8s linear infinite;
}

mat-hint {
  color: red !important;
}

.otphint {
  color: gray !important;
}

.body .submit {
  width: 100%;
  left: 0%;
  right: 0%;
  bottom: 0%;
  /* margin-top: 10px; */
  background: #3e95ef;
  border-radius: 4px;
  color: white;
  padding: 12px;
  cursor: pointer;
  text-transform: uppercase;
  line-height: 20px;
  font-family: inherit;
  font-style: normal;
  font-size: 16px;
}

.body .disabled {
  opacity: 0.5;
  width: 100%;
  left: 0%;

  right: 0%;
  justify-content: center;
  text-align: center;
  bottom: 0%;
  /* margin-top: 10px; */

  background: #3e95ef;
  border-radius: 4px;
  color: white;
  padding: 12px;
  cursor: not-allowed;
  line-height: 20px;
}

.body .submit-new {
  /* width: 30%; */
  /* left: 0%;
      right: 0%;
      bottom: 0%; */
  margin-top: 20px;
  background: #3e95ef;
  border-radius: 4px;
  color: white;
  /* padding: 6px; */
  cursor: pointer;
  text-transform: uppercase;
}

.body .cancel-button {
  /* padding: 6px; */
  margin-top: 20px;

  border-radius: 4px;
  cursor: pointer;
  text-transform: uppercase;
}

.body .app {
  background: white;
  opacity: 0.5;
}

.body .disabled-new {
  opacity: 0.5;
  /* width: 30%;
      left: 0%;
      right: 0%;
      justify-content: center;
      text-align: center;
      bottom: 0%; */
  /* margin-top: 20px; */
  background: rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  color: white;
  /* padding: 6px; */
  cursor: not-allowed;
}

.body .disabled-new:disabled {
  opacity: 0.5;
  /* width: 30%;
        left: 0%;
        right: 0%;
        justify-content: center;
        text-align: center;
        bottom: 0%; */
  /* margin-top: 20px; */
  background: rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  color: white;
  /* padding: 6px; */
  cursor: not-allowed;
}

.body .forget {
  font-family: inherit;
  color: #0064c8;
  font-size: 15px;
  display: block;
  cursor: pointer;
  position: relative;
  margin-bottom: 35px;
}

.body mat-form-field {
  padding: 0;
  margin-bottom: 33px;
  border-bottom: 1px solid #1f000000;
}

.body .example-form {
  min-width: 150px;
  max-width: 500px;
  width: 100%;
}

.body .example-full-width {
  width: 100%;
}

.body .example-full-width1 {
  width: 100%;
  padding: 0;
  margin: -10px 0 0 0;
  border: none;
}

body mat-placeholder {
  justify-content: center;
  align-items: center;
}

.body input {
  color: black;
}

.body .inputborder {
  border: none;
  /* color: black;
  border: 1px solid black;
  padding: 10px;   */
}

.body button {
  border: none;
  background: none;
  font-family: inherit;
  font-size: inherit;
}

.body input::-webkit-outer-spin-button,
.body input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.body .closeicon {
  font-size: 30px;
  color: red;
}

.body .closewhite {
  margin-left: auto;
  margin-right: auto;
  font-size: 20px;
  color: white;
}

.body .doneicon {
  margin-left: auto;
  margin-right: auto;
  font-size: 20px;
  color: white;
}

.body .verify_not_icon {
  margin-left: auto;
  margin-right: auto;
  font-size: 20px;
  color: white;
}

/* Firefox */
input[type='number'] {
  -moz-appearance: textfield;
}

/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
.mat-form-field-appearance-legacy .mat-form-field-label {
  color: #000000;
}

.body .labletext {
  font-family: inherit;
  font-style: normal;
  font-weight: normal;
  font-size: 15px;
  line-height: 19px;
  color: black;
}

.body .inputborder1 {
  /* border: none; */
  background-color: #ebebeb;
  border: 1px solid #c4c4c4;
  padding: 10px;
  margin: 0;
  font-size: 15px;
}

/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
.no-line .mat-form-field-underline {
  display: none;
}

.body .greentick {
  opacity: 0.4;
  align-items: center;
  border-radius: 8px;
  display: inline;
  background: #27ae60;
  margin-left: 10px;
  margin-right: 10px;
}

.body .greentick_enable {
  align-items: center;
  border-radius: 8px;
  display: inline;
  background: #27ae60;
  margin-left: 10px;
  margin-right: 10px;
}

.body .redclose {
  opacity: 0.4;
  align-items: center;
  border-radius: 8px;
  display: inline;
  background: #eb5757;
  margin-left: 10px;
  margin-right: 10px;
}

.body .redclose_enable {
  align-items: center;
  border-radius: 8px;
  display: inline;
  background: #eb5757;
  margin-left: 10px;
  margin-right: 10px;
}

.iconbutton {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.body .datasubmit {
  background: #3e95ef;
  font-family: inherit;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  align-items: center;
  text-align: center;
  letter-spacing: 0.0125em;
  text-transform: uppercase;
  color: #ffffff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.16);
  border-radius: 8px;
  height: 40px;
}

.body .canceldata {
  margin: 0 16px;
  border: 1px solid #3e95ef;
  border-radius: 8px;
  font-family: inherit;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  height: 40px;
  /* identical to box height, or 150% */

  display: flex;
  align-items: center;
  text-align: center;
  letter-spacing: 0.0125em;
  text-transform: uppercase;

  /* Secondary / Dark */

  color: #0064c8;
}

.mail-merge-variable {
  background: rgba(0, 0, 0, 0.12);
  border-radius: 17px;
  padding: 0px;
  text-align: center;
  cursor: pointer;
  margin-right: 5%;
  border: 1px solid #2f80ed;
  font-size: 14px;
}

/* for custome pagination start */
.mat-mdc-paginator-range-label {
  margin: 0 32px 0 24px;
  min-width: 100px;
}

.mat-mdc-paginator-container {
  width: 93% !important;
}

/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
mat-checkbox {
  color: #418f7f !important;
}

.search1 {
  margin: 10px;
  outline: none !important;
  background: none;
  border: 0px solid gray !important;

  font-family: inherit;
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 21px;
  /* identical to box height */

  color: rgba(0, 0, 0, 0.38);
}

.search1::placeholder {
  color: rgba(0, 0, 0, 0.54);
}

.searchimg {
  width: 10px;
  height: 10px;
  /* margin: 10px; */
}

.logo-img {
  width: 100px;
  height: 100px;
  object-fit: contain;
}

/* for custome pagination end */

/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
::ng-deep .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background,
.mat-checkbox-checked.mat-accent .mat-checkbox-background,
.mat-radio-button.mat-accent .mat-radio-inner-circle {
  background: #418f7f !important;
}

/* TODO(mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version. */
.mat-mdc-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
  border-color: #418f7f !important;
}

/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
::ng-deep .mat-checkbox-checked .mat-checkbox-background,
.mat-checkbox-indeterminate .mat-checkbox-background {
  background-color: #008dff !important;
}

/* // overwrite the ripple overlay on hover and click */
/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
::ng-deep .mat-mdc-checkbox:not(.mat-checkbox-disabled) .mat-checkbox-ripple .mat-ripple-element {
  background-color: #008dff !important;
}

.height-90vh .mat-mdc-dialog-container {
  height: 90vh !important;
}

.dialog-padding-top-none .mat-mdc-dialog-container {
  height: auto;
  padding: 0 24px 12px 24px !important;
}

.dialog-padding-top-bottom-none .mat-mdc-dialog-container {
  height: auto;
  padding: 0 24px 0px 24px !important;
}

.digi-confirm-popup-padding-none .mat-mdc-dialog-container {
  padding: 15px;
}

.dialog-fix .mat-mdc-dialog-container {
  height: auto;
  padding: 12px 24px 12px 24px !important;
  background: #ffffff !important;
}

.dialog-fix .mat-mdc-dialog-container .mdc-dialog__surface {
  background-color: #ffffff !important;
}

.dialog-bg-grey .mat-mdc-dialog-container {
  height: auto;
  padding: 0px 24px 12px 24px !important;
  background: #eff0f2 !important;
}

.student-dialog .mat-mdc-dialog-container {
  height: auto;
  width: 800px;
}

.student-dialog-copy .mat-mdc-dialog-container {
  height: auto;
  width: 875px;
}

.head-top {
  background: linear-gradient(
    36.76deg,
    rgba(26, 123, 220, 0.85) 13%,
    rgba(86, 184, 255, 0.85) 107.28%
  ) !important;
  border: white;
}

/* .body .lab-head
{
  color: white;
} */
.search {
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.12);
  box-sizing: border-box;
  border-radius: 8px;
  height: 40px;
  padding-right: 10px;
}

.topicSearch {
  margin: 10px;
  outline: none !important;
  background: none;
  border: 0px solid gray !important;
}

.search2 {
  height: 35px;

  background: white;
  border: 1px solid #bfbfbf !important;
  width: 100%;
}

.blueBox {
  border: 1px solid #008dff;
  box-sizing: border-box;
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.25);
}

.mat-drawer-container {
  height: 100%;
}

.footer-detail {
  /* top: 285px;
  position: relative; */
  color: white;
  font-family: inherit;
}

/* .foot-data
{
    position: absolute;
} */
/* .logo-img
{
  width: 100px;
    position: relative;
    height: 100px;
    bottom: 20px; */
/* } */
/* for custome pagination end */
.digi-transition-text {
  color: #3e95ef;
  text-decoration: underline;
}

.body .sign-color {
  font-family: inherit;
  font-style: normal;
  font-weight: normal;
  font-size: 32px;
  line-height: 125%;
  color: #0064c8;
  position: relative;
  top: 32px;
}

.body .input-name {
  font-family: inherit;
  font-style: normal;
  font-weight: normal;
  font-size: 20px;
  line-height: 100%;
  letter-spacing: 0.015em;
  color: rgba(0, 0, 0, 0.87);
}

.body .mob-num {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.87);
  font-weight: 500;
  top: 5px;
  position: relative;
}

.title-head {
  font-weight: bold;
  font-size: 24px;
  line-height: 24px;
}

.content-data {
  margin: 0px 169px;
  min-height: calc(100vh - 90px);
}

.sticky {
  position: sticky;
  top: 0;
  background: #e5e5e5;
  z-index: 9999;
}

.img-data {
  width: 200px;
}

.otp-sent {
  margin-bottom: 30px;
  font-family: inherit;
}

.resend-button {
  margin-top: 40px;
}

.subm-button {
  position: relative;
  top: 30px;
}

.hidden {
  visibility: hidden;
}

.digi-assessment-card-popup .mat-mdc-dialog-container {
  height: auto;
  background-color: #f2f2f2;
}

.digi-assign-topic-pop .mat-mdc-dialog-container {
  background-color: #eff0f2;
  margin: 0px 15px;
}

.isDisabled {
  color: currentColor;
  cursor: not-allowed;
  opacity: 0.5;
  text-decoration: none;
  pointer-events: none;
}

@media (min-width: 650px) and (max-width: 800px) {
  .subm-button {
    position: relative;
    top: 75px;
    left: 110px;
  }
}

@media (max-width: 450px) {
  .content-data {
    margin: 0px;
    padding: 20px 5px;
  }

  .body mat-card {
    max-width: 350px;
  }

  .body .input-name {
    font-size: 16px;
  }

  .body .sign-color {
    font-size: 20px;
  }
}

.disable-button {
  background: #e1e1e1 !important;
  border-radius: 8px;
  padding: 8px 16px;
  font-weight: 500;
  font-size: 16px;
  text-transform: uppercase;
  color: white !important;
  pointer-events: none;
}

.search-icon {
  position: relative;
  top: 10px;
}

.current-data {
  font-family: inherit;
  font-style: normal;
  font-weight: bold;
  font-size: 24px;
  line-height: 28px;

  color: #000000;
}

/* //vinoth
 */

.mat-mdc-tab-header {
  background: linear-gradient(
    36.76deg,
    rgba(26, 123, 220, 0.85) 13%,
    rgba(86, 184, 255, 0.85) 107.28%
  ) !important;
}

/* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
.mat-mdc-tab {
  font-family: inherit;
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
  line-height: 24px;
  /* identical to box height, or 133% */

  letter-spacing: 0.0125em;

  /* White / High Emphasis */

  color: rgba(255, 255, 255, 0.87);
}

/* Styles for the active tab label */
/* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
.mat-mdc-tab.mdc-tab--active {
  background-color: transparent;
  color: white;
  opacity: 1;
}

/* Styles for the ink bar */
.mat-mdc-tab-group.mat-primary .mat-ink-bar,
.mat-mdc-tab-nav-bar.mat-primary .mat-ink-bar {
  background-color: white;
  margin-bottom: 5px;
}

.digi-btn-attach {
  font-family: inherit;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  align-items: center;
  text-align: center;
  letter-spacing: 0.0125em;
  text-transform: uppercase;
  border: 2px solid #8fbff0;
  border-radius: 10px;
  padding: 10px;
  justify-content: center;
  color: #0064c8;
}

.digi-email-container {
  font-family: inherit;
  max-height: 55vh;
  border: 1px solid #008dff;
  border-radius: 8px;
}

.digi-email-header {
  background-color: #aad4ff;
  padding: 10px 0;
}

.digi-input-password[type='password'] {
  font-size: 20px !important;
  font-weight: 500;
}

@-moz-document url-prefix() {
  .digi-input-password[type='password'] {
    font-size: 12px !important;
    letter-spacing: 1px;
  }
}

.digi-email-action-buttons {
  font-family: inherit;
  background-color: white;
  margin: 5px;
  border-radius: 5px;
  padding: 10px;
}

.digi-table-fit {
  width: 100%;
}

/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
.mat-checkbox-label {
  font-family: inherit;
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 125%;
  display: flex;
  align-items: center;
  letter-spacing: 0.0125em;
  text-transform: capitalize;
  color: #000000;
}

.digi-title {
  font-family: inherit;
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
  line-height: 24px;
  text-transform: capitalize;
  letter-spacing: 0.0125em;
  color: #000000;
}

.ml-auto {
  margin-left: auto;
}

.digi-font-12 {
  font-size: 12px !important;
}

.digi-font-16 {
  font-size: 16px;
}

.mat-tree-node,
.mat-nested-tree-node {
  font-family: inherit;
}

.digi-popup .mat-mdc-dialog-container {
  overflow: auto;
  height: auto;
}

.ml-16 {
  margin-left: 16px;
}

.mb-10 {
  margin-bottom: 10px;
}

.digi-pd-auto {
  padding: 0px !important;
}

.digi-pd-5 {
  padding: 0px 5px;
}

.digi-pd-4 {
  padding: 0px 4px;
}

.digi-text-capitalize {
  text-transform: capitalize;
}

.digi-margin-20 {
  margin: 20px;
}

.placeholder {
  color: black;
  opacity: 0.5;
}

.mat-focused .placeholder {
  color: #0064c8;
  opacity: unset;
}

.mat-form-field-invalid .placeholder {
  color: red;
  opacity: unset;
}

.dialog-table-fix .mat-mdc-dialog-container {
  height: auto;
}

.digi-invigilator-popup .mat-mdc-dialog-container {
  overflow: auto;
  height: auto;
  background: white;
}

.digi-bg-image {
  background-color: #328dd9;
  background-image:
    linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.05) 25%,
      transparent 25%,
      transparent 75%,
      rgba(255, 255, 255, 0.05) 75%,
      rgba(255, 255, 255, 0.05)
    ),
    linear-gradient(
      -45deg,
      rgba(255, 255, 255, 0.05) 25%,
      transparent 25%,
      transparent 75%,
      rgba(255, 255, 255, 0.05) 75%,
      rgba(255, 255, 255, 0.05)
    );
  height: 100vh;
}

/* TODO(mdc-migration): The following rule targets internal classes of autocomplete that may no longer apply for the MDC version. */
.mat-autocomplete-panel.mat-autocomplete-visible {
  margin-top: 5px;
}

/* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
.mat-mdc-tab-body-content {
  height: auto !important;
}

.digi-button-disabled {
  background: #e1e1e1 !important;
  border-radius: 8px;
  font-weight: 500;
  font-size: 16px;
  border: none;
  text-transform: uppercase;
  color: white !important;
}

.digi-cursor {
  cursor: pointer;
}

.digi-cursor-move {
  cursor: move;
}

.digi-cursor-default {
  cursor: default !important;
}

.digi-text-field {
  width: 100% !important;
  border: 1px solid rgba(0, 0, 0, 0.32) !important;
  margin: 15px auto;
  outline: 0px solid !important;
  font-size: 16px !important;
  padding: 12px 16px 25px !important;
  box-sizing: border-box !important;
  border-radius: 8px !important;
}

.digi-focus:focus {
  outline: none;
}

/* question status */
.digi-delete {
  color: #ff0000;
  cursor: pointer;
}

.digi-status {
  background: lightgrey;
  border-radius: 8px;
  padding: 2px 16px;
}

.digi-status.green {
  background: #b1ffd2;
}

.digi-blue {
  color: #0064c8;
}

.digi-danger {
  color: #ff0000;
}

.digi-bg-white {
  background: white !important;
}

.digi-assessment-tabs > mat-tab-header mat-ink-bar {
  display: none;
}

.digi-assessment-tabs > mat-tab-header {
  border-bottom: 0px;
}

.digi-assessment-tabs > mat-tab-header .mat-mdc-tab-labels {
  background: #eff0f2 !important;
  padding: 10px;
  box-sizing: border-box;
}

.digi-assessment-tabs > mat-tab-header .mat-mdc-tab {
  padding: 0 10px;
  box-sizing: border-box;
  color: #828282;
  text-transform: uppercase;
  font-size: 14px;
  min-width: auto;
}

.digi-assessment-tabs > mat-tab-header .mat-mdc-tab,
.digi-assessment-tabs > mat-tab-header .mat-mdc-tab:hover {
  opacity: 1;
}

.digi-assessment-tabs > mat-tab-header .mat-mdc-tab.mdc-tab--active {
  color: #333333 !important;
  background: #ffffff !important;
  border-radius: 16px !important;
}

.digi-red {
  background: rgba(255, 155, 155, 0.67);
}

/* TODO(mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version. */
.mat-select-disabled,
.mat-select-disabled .mat-select-trigger {
  opacity: 0.9;
  color: black;
  cursor: not-allowed !important;
}

.digi-no-data {
  padding: 10px 0;
  font-size: 18px;
  color: black;
  opacity: 0.5;
  text-align: center;
}

.digi-full-width {
  width: 100% !important;
}

.digi-full-height {
  height: 100% !important;
}

.digi-font-18 {
  font-size: 18px !important;
}

.digi-font-24 {
  font-size: 24px !important;
}

.digi-font-wgt-500 {
  font-weight: 500 !important;
}

.digi-notified-list-dialog .mat-mdc-dialog-container {
  height: auto;
  padding: 10px 10px 20px 20px !important;
}

.digi-color-blue {
  color: #3e95ef;
}

.digi-color-grey {
  color: rgba(0, 0, 0, 0.38);
}

.digi-color-green {
  color: #008000;
}

.digi-color-orange {
  color: #ffa500 !important;
}

.digi-font-500 {
  font-weight: 500 !important;
}

.digi-font-400 {
  font-weight: 400 !important;
}

.digi-gray-bg .mat-mdc-dialog-container {
  background: #f0f0f0;
}

.digi-create-new-assessment-popup .mat-mdc-dialog-container {
  background: #f5f5f5;
}

.digi-font-normal {
  font-weight: normal !important;
}

.digi-black {
  color: rgba(0, 0, 0, 0.87) !important;
}

.digi-disabled {
  color: rgba(0, 0, 0, 0.54) !important;
  cursor: not-allowed;
}

.digi-chevron_right-arrow {
  color: #c4c4c4;
}

.digi-lite-black {
  color: #4f4f4f !important;
}

.digi-word-break {
  white-space: normal;
}

.digi-none {
  display: none !important;
}

.digi-chevron-right-arrow {
  color: #c4c4c4;
}

.digi-text {
  font-family: inherit;
  line-height: 125%;
}

/* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version. */
mat-checkbox.digi-word-break .mat-checkbox-layout {
  white-space: normal;
}

.digi-dark-black {
  color: #000000;
}

.digi-lightblue {
  color: #2f80ed !important;
}

.digi-white {
  color: white;
}

.digi-text-upper,
.digi-text-uppercase {
  text-transform: uppercase !important;
}

.digi-light-grey {
  color: rgba(0, 0, 0, 0.54);
}

.digi-table-detail:hover {
  background: #d1f4ff;
}

.digi-text-transform-none {
  text-transform: none !important;
}

.digi-faculty-valid-panel button.mat-mdc-menu-item span {
  font-size: 14px;
}

.digi-faculty-valid-panel button.mat-mdc-menu-item:hover {
  background: #d1f4ff;
  color: #0064c8;
}

.digi-font-red {
  color: #ff0000ff !important;
}

.mat-mdc-select-panel.digi-course-list-dropdown {
  min-width: calc(100% + 300px) !important;
  max-width: none !important;
}

.mat-mdc-select-panel:not(.digi-full-select-panel) {
  max-width: max-content !important;
}

.mat-mdc-select-panel.digi-width-fit-content {
  min-width: fit-content !important;
}

.digi-fit-content {
  min-width: fit-content !important;
}

.mat-mdc-select-panel.digi-max-content {
  min-width: max-content !important;
}

.digi-text-center {
  text-align: center;
}

[dir='ltr'] .digi-text-left {
  text-align: left !important;
}

[dir='rtl'] .digi-text-left {
  text-align: right !important;
}

.digi-text-align-end {
  text-align: end;
}

.digi-margin-left-15px {
  margin-left: 15px;
}

/* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
mat-tab-group.digi-tab-head-remove-bg > .mat-mdc-tab-header {
  background: transparent !important;
}

/* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
mat-tab-group.digi-tab-head-remove-border > .mat-mdc-tab-header {
  border: none;
}

/* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
mat-tab-group.digi-tab-remove-mat-link > .mat-mdc-tab-header mat-ink-bar {
  display: none;
}

.digi-student-report-card-top-tab {
  display: flex;
  justify-content: space-between;
}

.toastr-link {
  color: #54a7ef !important;
  margin-left: 5px;
}

.toastr-message {
  color: rgba(0, 0, 0, 0.54) !important;
  margin-bottom: 10px;
}

.digi-collapse-table,
.digi-collapse-table .td {
  background-color: #f2f2f2;
}

.digi-expand-table,
.digi-expand-table .td {
  background-color: white;
}

.digi-without-child,
.digi-without-child .td {
  background: white;
}

.digi-sticky-part {
  position: sticky;
  top: 0px;
  z-index: 999;
  background: white;
}

.apexcharts-tooltip-series-group.apexcharts-active {
  text-transform: uppercase;
}

[dir='rtl'] .digi-flip-img {
  -webkit-transform: scaleX(-1);
  transform: scaleX(-1);
}

.digi-border-radius-15 {
  border-radius: 15px;
}

/* TODO(mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version. */
[dir='ltr'] .mat-radio-label-content {
  padding-left: 8px;
}

/* TODO(mdc-migration): The following rule targets internal classes of slide-toggle that may no longer apply for the MDC version. */
[dir='rtl'] .digi-slide-toggle .mat-slide-toggle-thumb {
  margin-right: 3px;
}

[dir='ltr'] .digi-chips mat-icon {
  margin-left: 8px;
}

[dir='rtl'] .digi-chips mat-icon {
  margin-right: 8px;
}

[dir='rtl'] .digi-align-justify {
  margin-right: auto;
  margin-left: initial !important;
}

[dir='rtl'] .digi-right-curve-content {
  margin-right: 0px !important;
  padding: 18px !important;
  margin-left: 10px !important;
}

[dir='rtl'] .digi-left-common-bdr {
  border-bottom-left-radius: 0px !important;
  border-top-left-radius: 0px !important;
  border-top-right-radius: 22px;
  border-bottom-right-radius: 22px;
}

[dir='rtl'] .digi-right-common-bdr {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 22px;
  border-top-left-radius: 22px;
}

[dir='rtl'] .digi-search-container {
  padding-right: 15px;
}

[dir='rtl'] .digi-document-right {
  margin: 15px 0 0 0px !important;
}

[dir='rtl'] .digi-right-btn {
  margin-right: 10px;
}

[dir='rtl'] .digi-status-header {
  white-space: break-spaces;
  padding: 0 0 0 10px;
}

[dir='rtl'] .digi-active-config-right {
  text-align: right;
}

.digi-notification-popup {
  position: fixed !important;
  right: 25px;
  top: 40px;
  left: auto;
  bottom: 0px;
}

.digi-notification-popup .mat-mdc-dialog-container {
  padding: 0px !important;
  border-radius: 0;
  background-color: transparent;
  box-shadow: none;
}

.digi-break-word {
  word-break: break-word;
}

.digi-border-radius-8 {
  border-radius: 8px;
}

.digi-tos-group-select-dropdown-option .mat-pseudo-checkbox-disabled {
  cursor: default;
  background: #e5e5e5;
  color: rgba(0, 0, 0, 0.87);
  border: 2px solid transparent;
}

.digi-tos-group-select-dropdown-option .mat-pseudo-checkbox-disabled::after {
  top: 2.4px;
  left: 1px;
  width: 8px;
  height: 3px;
  border-left: 2px solid white;
  transform: rotate(-45deg);
  opacity: 1;
  box-sizing: content-box;
}

.digi-student-report-exam-types {
  background: white;
  padding: 5px 10px;
  border-radius: 5px;
}

.digi-margin-0 {
  margin: 0;
}

.digi-w-15p {
  width: 15px;
}

.digi-w-32p {
  width: 32px;
}

.digi-white-space-normal {
  word-break: break-word;
  white-space: normal;
}

.digi-color-light-grey {
  color: #7e7e7f;
}

.full-screen-modal .mat-mdc-dialog-container {
  width: 100%;
  height: auto;
}

.digi-course-exam-spec-menu-container.mat-mdc-menu-panel {
  min-width: 315px;
  max-width: 315px;
}

.digi-schedule-upload-student-menu-dropdown {
  max-width: 600px !important;
}

.digi-schedule-upload-student-menu-dropdown .mat-mdc-menu-item {
  height: auto;
}

.digi-scheduler-create-timetable-list-menu-dropdown {
  max-width: 600px !important;
  min-width: 200px !important;
}

.digi-scheduler-create-timetable-list-menu-dropdown .mat-mdc-menu-item {
  height: auto;
}

.cup-icon {
  color: #d97706;
  font-size: 20px;
}

.digi-min-width-10 {
  min-width: 10px;
}

.digi-no-list {
  list-style-type: none;
}

.digi-transform-0-8 {
  transform: scale(0.8);
}

.digi-overflow-auto {
  overflow: auto;
}

.digi-overflow-visible {
  overflow: visible !important;
}

.digi-bg-grey {
  background: #f5f5f5;
}

.digi-flex-1 {
  flex: 1;
}

.digi-individual-report-popup .mat-mdc-dialog-container {
  padding: 0px 20px 20px !important;
}

.ck.ck-balloon-panel.ck-balloon-panel_visible {
  display: none !important;
}

.digi-select-none {
  user-select: none;
}

.ck.ck-balloon-panel.ck-balloon-panel_visible {
  display: none !important;
}

.digi-block {
  display: block;
}

button.mat-mdc-menu-item.digi-menu-item-option {
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.0125em;
  color: #000000;
}

button.mat-mdc-menu-item.digi-menu-item-option:hover {
  background: #d1f4ff;
}

.digi-consolidate-tabs > mat-tab-header {
  background: none !important;
}

.digi-r-5 {
  border-radius: 5px;
}

.digi-r-10 {
  border-radius: 10px;
}

.digi-r-15 {
  border-radius: 15px;
}

.digi-r-20 {
  border-radius: 20px;
}

.digi-r-25 {
  border-radius: 25px;
}

.digi-r-full {
  border-radius: 100%;
}

.digi-assessment-title-container {
  border-radius: 16px;
}

.digi-conducted-tag {
  padding: 2px 5px;
  min-width: 35px;
  display: block;
  text-align: center;
  box-sizing: border-box;
  color: white;
  border-radius: 15px;
  overflow: hidden;
}

.digi-conducted-in {
  background-color: #219653;
}

.digi-conducted-out {
  background-color: #f2994a;
}

.digi-consolidated-grade-menu {
  background-color: black;
  min-width: 250px !important;
}

.digi-text-gray {
  color: #757575;
}

.digi-mixed-blink {
  animation: blinker 2s linear infinite;
}

@keyframes blinker {
  50% {
    opacity: 0;
  }
}

.digi-dot-ts {
  height: 5px;
  width: 5px;
  background-color: #9ca3af;
  border-radius: 50%;
  display: inline-block;
  vertical-align: middle;
}

.digi-tooltip-no-max-width {
  max-width: none !important;
}

.digi-tooltip-multiline-left {
  max-width: none !important;
  white-space: pre-line !important;
  text-align: left !important;
}

.digi-width-fit-content {
  width: fit-content;
}

.digi-test-spec-graph-popup {
  max-width: 95vw !important;
}

.digi-heatmap-tooltip {
  max-width: none !important;
}

.digi-side-panel-container.dialog-fix .mat-mdc-dialog-container {
  padding: 0 !important;
  border-radius: 0 !important;
}

.digi-grey-neutral {
  color: #4b5563;
}

.digi-exclusion-criteria-container .mat-mdc-dialog-container,
.digi-configuring-weight-container .mat-mdc-dialog-container {
  padding: 0;
  background-color: #ffffff;
}

.digi-exclusion-criteria-container .mat-mdc-dialog-container .digi-container,
.digi-configuring-weight-container .mat-mdc-dialog-container .digi-container {
  padding: 24px;
  box-sizing: border-box;
}

.digi-underline {
  text-decoration: underline;
}

.digi-schedule-group-select {
  position: relative;
  left: 20px;
  min-width: calc(100% - -30px) !important;
}

.mat-mdc-select-panel.digi-overflow-option-container {
  min-width: 100% !important;
  overflow: hidden !important;
}

.mat-mdc-select-panel.digi-overflow-option-container .digi-nested-label-container {
  height: auto !important;
}

.mat-select-panel.digi-overflow-option-container mat-option {
  max-width: 500px !important;
}

.mat-select-panel.digi-overflow-option-container.digi-dropdown mat-option div {
  display: inline;
}

.mat-mdc-select-panel .mat-mdc-option.digi-location-dropdown {
  line-height: 1em;
}

.mat-mdc-select-panel .mat-mdc-option.digi-sub-item-dropdown {
  height: auto;
}

.mat-mdc-select-panel .mat-mdc-option.digi-sub-item-dropdown:hover {
  height: auto;
  background-color: #f3f4f6;
}

.digi-custom-dropdown-label {
  font-size: 13px;
  letter-spacing: 1px;
  text-transform: capitalize;
  color: #6b7280;
}

.digi-custom-dropdown-select {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
  border-radius: 4px;
  text-transform: capitalize;
  margin: 0;
  background: #ffffff;
  border: 1px solid #d1d5db;
}

.mat-mdc-select-panel .mat-mdc-option.digi-student-learning-option {
  padding-bottom: 10px;
}

.digi-italic {
  font-style: italic;
}

.digi-add-student-groping-input {
  border: 0;
  outline: none;
}

.digi-overflow-hidden {
  overflow: hidden !important;
}

.cdk-drag-preview
  digi-ck-text-editor
  .digi-no-border
  ckeditor
  .ck.ck-editor__main
  > .ck-editor__editable {
  border-bottom: 1px solid #d1d5db;
  display: flex;
  align-items: center;
}

.digi-hidden-toolbar .ck.ck-toolbar.ck-toolbar_grouping {
  display: none;
}

.digi-hidden-toolbar .ck.ck-editor {
  display: flex !important;
  flex-direction: column-reverse !important;
}

.custom-select-panel {
  padding: 10px !important;
}

.digi-creation-editor digi-ck-text-editor,
.digi-creation-option digi-ck-text-editor {
  overflow: visible !important;
}

.digi-text-grey {
  color: #6b7280;
}

.digi-background-color-grey {
  background-color: #f4f5fa;
}

.digi-dark-grey {
  color: #9ca3af;
}

.mat-mdc-dialog-container .mdc-dialog__surface {
  box-shadow: none !important;
}

.mat-mdc-dialog-container .mdc-dialog__content {
  padding: 0 !important;
}

.mat-mdc-dialog-container,
.mat-mdc-dialog-container .mdc-dialog__container,
.mat-mdc-dialog-container .mdc-dialog__surface {
  min-width: auto !important;
  max-width: none !important;
  min-height: auto !important;
  max-height: none !important;
}

div.mat-mdc-select-panel {
  padding-top: 0px !important;
}

.mdc-switch:enabled .mdc-switch__track::before,
.mdc-switch:enabled .mdc-switch__track::after {
  background: #e0e0e0 !important;
}

.mdc-switch.mdc-switch--selected:enabled .mdc-switch__handle::after {
  background: #2196f3 !important;
}

.mat-mdc-option .mat-pseudo-checkbox-minimal {
  display: none !important;
}

.export-list-test-center-popup .mdc-dialog__surface {
  overflow: visible;
}

.digi-student-assign-program {
  max-height: 200px !important;
}

.digi-list-topic-color {
  color: #374151;
}

.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(
    .mat-mdc-option-multiple
  ) {
  background-color: #0000001a !important;
}

.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) .mdc-list-item__primary-text {
  color: var(--mat-option-label-text-color) !important;
}

.digi-schedule-ongoing-attempt-type-dropdown .mat-mdc-option .mdc-list-item__primary-text {
  font-size: 0.8rem;
}

.digi-mandatory {
  color: #eb5757;
}

.digi-light-blue-bg {
  background: #add8e6;
}

.digi-group-menu.mat-menu-panel {
  max-height: 300px;
  overflow-y: auto;
}

.digi-word-nowrap {
  white-space: nowrap;
}

.digi-padding-none .mat-mdc-dialog-container {
  padding: 0px !important;
}

.marker-green {
  background-color: #62f962;
}

.digi-choose-user .mdc-list-item__primary-text {
  display: block;
  width: 100%;
}

button.mat-menu-item.digi-no-background {
  background: none !important;
}

button.mat-menu-item.digi-no-background:hover {
  background: rgba(0, 0, 0, 0.04) !important;
}

.digi-flex {
  display: flex;
}

.mat-mdc-select-panel.digi-select-panel-full-cover {
  max-width: calc(100% - 15px) !important;
}

mat-checkbox.digi-checkbox-no-padding .mdc-checkbox {
  padding: calc((var(--digi-checkbox-state-layer-size) - 18px) / 2);
  margin: calc((var(--digi-checkbox-state-layer-size) - var(--digi-checkbox-state-layer-size)) / 2);
}

mat-checkbox.digi-checkbox-no-padding .mdc-checkbox .mdc-checkbox__native-control {
  top: calc((var(--digi-checkbox-state-layer-size) - var(--digi-checkbox-state-layer-size)) / 2);
  right: calc((var(--digi-checkbox-state-layer-size) - var(--digi-checkbox-state-layer-size)) / 2);
  left: calc((var(--digi-checkbox-state-layer-size) - var(--digi-checkbox-state-layer-size)) / 2);
  width: var(--digi-checkbox-state-layer-size);
  height: var(--digi-checkbox-state-layer-size);
}

mat-checkbox.digi-checkbox-no-padding .mdc-checkbox .mdc-checkbox__background {
  top: calc((var(--digi-checkbox-state-layer-size) - 18px) / 2);
  left: calc((var(--digi-checkbox-state-layer-size) - 18px) / 2);
}

.digi-assessment-topic-option {
  min-width: 120px;
}

.digi-assignee-tooltip {
  background-color: #f3f4f6 !important;
  min-width: 200px !important;
  box-shadow: 0px 1px 3px 0px #11182733;
  max-width: 350px !important;
  word-break: break-word !important;
}

.digi-attachment-tooltip {
  min-width: fit-content !important;
}

.digi-proctor-tooltip {
  background-color: #f3f4f6 !important;
  min-width: 200px !important;
  box-shadow: 0px 1px 3px 0px #11182733;
}

.digi-session-tooltip {
  background-color: #1f2937 !important;
}

.digi-duty-tooltip {
  background-color: #f3f4f6 !important;
  min-width: 140px !important;
  box-shadow: 0px 1px 3px 0px #11182733;
  word-break: break-word !important;
}

.digi-course-tooltip {
  padding: 10px !important;
  min-width: 350px !important;
}

ngx-mat-timepicker-dialog {
  background-color: #ffffff !important;
  display: block;
}

.digi-assessment-note-popup .mat-mdc-dialog-container .mdc-dialog__surface {
  border-radius: 0 !important;
}

.digi-pointer-auto {
  pointer-events: auto !important;
}

.digi-popup-border-top-blue-line .mat-mdc-dialog-container .mdc-dialog__container {
  border-top: 3px solid #008dff !important;
  border-radius: 3px !important;
}

.digi-popup-border-top-blue-line {
  border-radius: 5px;
  overflow: hidden;
}

.digi-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.mat-mdc-slider .mdc-slider__value-indicator {
  opacity: 1 !important;
}

.digi-correction-menu .mat-mdc-menu-item {
  min-height: 35px !important;
}

.digi-ngx-extended-pdf-viewer .body {
  height: 70vh !important;
}

.digi-ngx-extended-pdf-viewer .toolbar {
  max-width: 990px !important;
}

.digi-ngx-extended-pdf-viewer #toolbarViewerRight pdf-print,
pdf-open-file,
pdf-download,
pdf-highlight-editor,
pdf-text-editor,
pdf-stamp-editor,
pdf-draw-editor,
pdf-toggle-secondary-toolbar {
  display: none !important;
}

.digi-select div.mat-mdc-select-panel {
  max-width: 100px !important;
}

.mat-mdc-select-panel.digi-course-dropdown-container {
  max-height: none !important;
}

.digi-assessment-clo-dropdown-panel .mat-mdc-option.mdc-list-item {
  align-items: flex-start !important;
}

.digi-assessment-clo-dropdown-panel .mat-mdc-option .mat-pseudo-checkbox-full {
  margin-top: 4px !important;
}

.digi-text-truncate-ellipsis {
  --text-line-clamp: 1;
  max-width: 100%;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: var(--text-line-clamp);
  white-space: normal;
}

.digi-dropdown-text-two-line {
  max-width: 100%;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  white-space: normal;
  font-size: 1rem;
  line-height: 1.2em;
  max-height: 2.4em;
}

.digi-text-clip {
  --text-width: 200px;
  max-width: var(--text-width);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.digi-assessment-item-author-dropdown {
  max-height: none !important;
}

.digi-custom-dropdown-panel-class {
  max-height: none !important;
}

.digi-notification-table-hierarchy-text > span {
  display: inline !important;
  margin: 0 !important;
}
